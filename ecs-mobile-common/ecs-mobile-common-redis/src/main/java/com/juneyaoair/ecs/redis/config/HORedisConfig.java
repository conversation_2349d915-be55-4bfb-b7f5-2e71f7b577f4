package com.juneyaoair.ecs.redis.config;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/26 16:33
 */
@Configuration
public class HORedisConfig {
    @Autowired
    private RedisProperties redisProperties;
    @Autowired
    private APIRedisProperties apiRedisProperties;
    @Autowired
    private DefaultRedisProperties defaultRedisProperties;

    /**
     * lettucePool 连接配置
     *
     * @return
     */
    public LettucePoolingClientConfiguration lettucePoolConfig() {
        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        poolConfig.setMaxTotal(redisProperties.getMaxActive());
        poolConfig.setMinIdle(redisProperties.getMinIdle());
        poolConfig.setMaxIdle(redisProperties.getMaxIdle());
        poolConfig.setMaxWaitMillis(redisProperties.getMaxWait());
        poolConfig.setTestOnCreate(redisProperties.getTestOnCreate());
        poolConfig.setTestOnBorrow(redisProperties.getTestOnBorrow());
        poolConfig.setTestOnReturn(redisProperties.getTestOnReturn());
        poolConfig.setTestWhileIdle(redisProperties.getTestWhileIdle());
        poolConfig.setNumTestsPerEvictionRun(redisProperties.getNumTestsPerEvictionRun());
        poolConfig.setTimeBetweenEvictionRunsMillis(redisProperties.getTimeBetweenEvictionRunsMillis());
        poolConfig.setMinEvictableIdleTimeMillis(redisProperties.getMinEvictableIdleTimeMills());

        return LettucePoolingClientConfiguration.builder()
                .poolConfig(poolConfig)
                .commandTimeout(Duration.ofSeconds(redisProperties.getCommandTimeout()))
                .shutdownTimeout(Duration.ofMillis(redisProperties.getShutdownTimeout()))
                .build();
    }

    /**
     * 默认的redis配置
     * @return
     */
    public RedisSentinelConfiguration defaultRedisSentinelConfiguration() {
        RedisSentinelConfiguration sentinelConfig = new RedisSentinelConfiguration();
        sentinelConfig.setMaster(defaultRedisProperties.getMaster());
        Set<RedisNode> sentinels = new HashSet<>();
        final String[] nodes = defaultRedisProperties.getCluster().split(",");
        for (String redisHost : nodes) {
            String[] item = redisHost.split(":");
            String ip = item[0].trim();
            String port = item[1].trim();
            sentinels.add(new RedisNode(ip, Integer.parseInt(port)));
        }
        sentinelConfig.setSentinels(sentinels);
        sentinelConfig.setDatabase(defaultRedisProperties.getDatabase());
        sentinelConfig.setPassword(RedisPassword.of(defaultRedisProperties.getPassword()));
        return sentinelConfig;
    }

    /**
     * @return
     */
    @Primary
    @Bean(value = "defaultRedisConnectionFactory")
    public RedisConnectionFactory defaultRedisConnectionFactory() {
        final LettuceConnectionFactory factory = new LettuceConnectionFactory(defaultRedisSentinelConfiguration(), lettucePoolConfig());
        factory.afterPropertiesSet();//必须初始化实例
        return factory;
    }

    /**
     * redis哨兵配置
     *
     * @return
     */
    public RedisSentinelConfiguration customRedisSentinelConfiguration0() {
        RedisSentinelConfiguration sentinelConfig = new RedisSentinelConfiguration();
        sentinelConfig.setMaster(apiRedisProperties.getMaster());
        Set<RedisNode> sentinels = new HashSet<>();
        final String[] nodes = apiRedisProperties.getCluster().split(",");
        for (String redisHost : nodes) {
            String[] item = redisHost.split(":");
            String ip = item[0].trim();
            String port = item[1].trim();
            sentinels.add(new RedisNode(ip, Integer.parseInt(port)));
        }
        sentinelConfig.setSentinels(sentinels);
        sentinelConfig.setDatabase(apiRedisProperties.getDatabase0());
        sentinelConfig.setPassword(RedisPassword.of(apiRedisProperties.getPassword()));
        return sentinelConfig;
    }

    /**
     * @return
     */
    @Bean(value = "customRedisConnectionFactory0")
    public RedisConnectionFactory customRedisConnectionFactory0() {
        final LettuceConnectionFactory factory = new LettuceConnectionFactory(customRedisSentinelConfiguration0(), lettucePoolConfig());
        factory.afterPropertiesSet();//必须初始化实例
        return factory;
    }


    /**
     * redis哨兵配置
     *
     * @return
     */
    public RedisSentinelConfiguration customRedisSentinelConfiguration1() {
        RedisSentinelConfiguration sentinelConfig = new RedisSentinelConfiguration();
        sentinelConfig.setMaster(apiRedisProperties.getMaster());
        Set<RedisNode> sentinels = new HashSet<>();
        final String[] nodes = apiRedisProperties.getCluster().split(",");
        for (String redisHost : nodes) {
            String[] item = redisHost.split(":");
            String ip = item[0].trim();
            String port = item[1].trim();
            sentinels.add(new RedisNode(ip, Integer.parseInt(port)));
        }
        sentinelConfig.setSentinels(sentinels);
        sentinelConfig.setDatabase(apiRedisProperties.getDatabase1());
        sentinelConfig.setPassword(RedisPassword.of(apiRedisProperties.getPassword()));
        return sentinelConfig;
    }

    /**
     * @return
     */
    @Bean(value = "customRedisConnectionFactory1")
    public RedisConnectionFactory customRedisConnectionFactory1() {
        final LettuceConnectionFactory factory = new LettuceConnectionFactory(customRedisSentinelConfiguration1(), lettucePoolConfig());
        factory.afterPropertiesSet();//必须初始化实例
        return factory;
    }

    /**
     * redisTemplate配置
     *
     * @param
     * @return
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Bean(name = "redisTemplateZero")
    public RedisTemplate<String, String> redisTemplate0() {
        RedisTemplate template = new RedisTemplate();
        template.setConnectionFactory(customRedisConnectionFactory0());
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringRedisSerializer);
        template.setValueSerializer(stringRedisSerializer);
        template.setHashKeySerializer(stringRedisSerializer);
        template.setHashValueSerializer(stringRedisSerializer);
        template.afterPropertiesSet();
        return template;
    }

    /**
     * redisTemplate配置
     *
     * @param
     * @return
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Bean(name = "redisTemplateOne")
    public RedisTemplate<String, String> redisTemplate1() {
        RedisTemplate template = new RedisTemplate();
        template.setConnectionFactory(customRedisConnectionFactory1());
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringRedisSerializer);
        template.setValueSerializer(stringRedisSerializer);
        template.setHashKeySerializer(stringRedisSerializer);
        template.setHashValueSerializer(stringRedisSerializer);
        template.afterPropertiesSet();
        return template;
    }


}
