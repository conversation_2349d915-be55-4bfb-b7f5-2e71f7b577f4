package com.juneyaoair.ecs.redis.service;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 常用redis库操作 1号库
 * @date 2023/5/8 14:09
 */
@Service
public class PrimaryRedisService extends AbstractRedisService {
    @Resource(name = "redisTemplateOne")
    private RedisTemplate<String,String> redisTemplate;
    /**
     * RedisTemplate 模板操作类
     *
     * @return
     */
    @Override
    protected RedisTemplate<String,String> initRedisTemplate() {
        return redisTemplate;
    }


}
