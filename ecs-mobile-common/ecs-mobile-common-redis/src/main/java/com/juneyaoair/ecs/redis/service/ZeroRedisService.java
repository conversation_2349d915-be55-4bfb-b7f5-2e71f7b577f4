package com.juneyaoair.ecs.redis.service;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 0号库redis操作服务
 * @date 2023/5/8 13:59
 */
@Service
public class ZeroRedisService extends AbstractRedisService {
    @Resource(name = "redisTemplateZero")
    private RedisTemplate redisTemplate;
    /**
     * RedisTemplate 模板操作类
     *
     * @return
     */
    @Override
    protected RedisTemplate initRedisTemplate() {
        return redisTemplate;
    }
}
