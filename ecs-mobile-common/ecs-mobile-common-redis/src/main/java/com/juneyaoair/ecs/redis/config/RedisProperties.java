package com.juneyaoair.ecs.redis.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @description redis数据源通用配置
 * @date 2023/4/26  16:14
 */
@Component
@Data
public class RedisProperties {
    /**
     * redis pool配置
     */
    @Value("${spring.redis.lettuce.pool.max-idle:10}")
    private Integer maxIdle;
    /**
     * 定义池中维护的最小空闲连接数。 此设置只有在正值时才有效果。
     */
    @Value("${spring.redis.lettuce.pool.min-idle:1}")
    private Integer minIdle;
    /**
     * 可以分配的最大连接数。 使用负值为无限制。
     */
    @Value("${spring.redis.lettuce.pool.max-active:20}")
    private Integer maxActive;
    /**
     * 池中“空闲”连接的最大数量。 使用负值来表示无限数量的空闲连接。
     */
    @Value("${spring.redis.lettuce.pool.max-wait:10}")
    private Integer maxWait;
    /**
     * 一次最多evict的pool里的jedis实例个数
     */
    @Value("${spring.redis.lettuce.pool.numTestsPerEvictionRun:10}")
    private Integer numTestsPerEvictionRun;
    /**
     *test idle 线程的时间间隔
     */
    @Value("${spring.redis.lettuce.pool.timeBetweenEvictionRunsMillis:6000}")
    private Integer timeBetweenEvictionRunsMillis;
    /**
     *连接池中连接可空闲的时间,毫秒
     */
    @Value("${spring.redis.lettuce.pool.minEvictableIdleTimeMills:60000}")
    private Integer minEvictableIdleTimeMills;
    /**
     *创建连接时检查
     */
    @Value("${spring.redis.lettuce.pool.test-on-create:false}")
    private Boolean testOnCreate;
    /**
     *在获取连接的时候检查有效性
     */
    @Value("${spring.redis.lettuce.pool.test-on-borrow:true}")
    private Boolean testOnBorrow;
    /**
     *当调用return Object方法时，是否进行有效性检查
     */
    @Value("${spring.redis.lettuce.pool.test-on-return:false}")
    private Boolean testOnReturn;
    /**
     * 在空闲时检查有效性
     */
    @Value("${spring.redis.lettuce.pool.test-while-idle:true}")
    private Boolean testWhileIdle;
    /**
     *
     */
    @Value("${spring.redis.lettuce.pool.commandTimeout:60}")
    private Integer commandTimeout;
    @Value("${spring.redis.lettuce.pool.shutdownTimeout:100}")
    private Integer shutdownTimeout;
}
