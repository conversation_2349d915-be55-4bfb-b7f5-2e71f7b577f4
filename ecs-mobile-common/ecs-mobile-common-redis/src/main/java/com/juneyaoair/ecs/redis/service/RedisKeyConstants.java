package com.juneyaoair.ecs.redis.service;

import com.juneyaoair.ecs.utils.SystemUtil;

/**
 * 用于redis key组装
 */
public class RedisKeyConstants {

    public static final String REDIS_PREFIX = "Manage:";

    /**
     * 城市基础信息 hash
     */
    public static final String REDIS_CITY_INFO_HASH = "API:" + SystemUtil.envRedisDir() + "cityInfoHash";
    /**
     * 机场基础信息 hash
     */
    public static final String REDIS_AIRPORT_INFO_HASH = "API:" + SystemUtil.envRedisDir() + "airportInfoHash";
    /**
     * 航线信息缓存 hash
     */
    public static final String REDIS_AIRLINE_INFO_HASH = "API:" + SystemUtil.envRedisDir() + "airLineInfoHash";

    public static final String REDIS_AIRPORT_INFO_CD = "API:" + "airportInfoJson";
    public static final String AIRLINE_UPG_RULE_REDIS = "API:" + "*" + "airline_upg_rule_redis:segments" + "*";
    /** 升舱航线缓存 */
    public static final String REDIS_UPG_SEGMENT = REDIS_PREFIX + "UPG:SEGMENT:";
    /**
     * 航线信息
     */
    public static final String REDIS_DEP_ARR_AIRLINE = SystemUtil.envRedisDir() + "common:deparrAirline:";
}
