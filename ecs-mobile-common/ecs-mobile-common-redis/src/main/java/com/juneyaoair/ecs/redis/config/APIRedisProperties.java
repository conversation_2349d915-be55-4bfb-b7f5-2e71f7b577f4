package com.juneyaoair.ecs.redis.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 该数据源主要针对API相关配置
 * @date 2024/4/29 9:12
 */
@Component
@Data
public class APIRedisProperties {
    @Value("${spring.redis1.password:}")
    private String password;
    @Value("${spring.redis1.sentinel.cluster:}")
    private String cluster;
    @Value("${spring.redis1.sentinel.master:}")
    private String master;
    @Value("${spring.redis1.sentinel.database0:0}")
    private int database0;
    @Value("${spring.redis1.sentinel.database1:1}")
    private int database1;
}
