package com.juneyaoair.ecs.redis.service;

import cn.hutool.core.util.StrUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.alibaba.fastjson2.JSON;
import org.apache.commons.lang3.StringUtils;
import cn.hutool.core.util.StrUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description redis 常用抽象类方法，提供基础的调用
 * @date 2023/5/8 13:51
 */
public abstract class AbstractRedisService {
    /**
     * RedisTemplate 模板操作类
     *
     * @return
     */
    protected abstract RedisTemplate<String,String> initRedisTemplate();

    public void put(String key,String value,long timeOut){
        if (StrUtil.isBlank(key)) {
            throw new HoServiceException("key must not be null");
        }
        ValueOperations<String, String> ope = initRedisTemplate().opsForValue();
        ope.set(key,value,timeOut, TimeUnit.MILLISECONDS);
    }

    public Map<String, String> getMapData(final String key) {
        HashOperations<String, Object, Object> ops = initRedisTemplate().opsForHash();
        // 获取所有的key-value值
        Map<Object, Object> maps = ops.entries(key);
        HashMap map = new LinkedHashMap();
        for (Object curkey : maps.keySet()) {
            map.put(curkey, maps.get(curkey));
        }
        return map;
    }

    /**
     * 读取缓存
     *
     * @param key
     * @return
     */
    public String get(final String key) {
        if (StrUtil.isBlank(key)) {
            throw new HoServiceException("key must not be null");
        }
        ValueOperations<String, String> operations = initRedisTemplate().opsForValue();
        return operations.get(key);
    }

    public void expire(String key,long timeOut, TimeUnit timeUnit){
        if (StrUtil.isBlank(key)) {
            throw new HoServiceException("key must not be null");
        }
        ValueOperations<String, String> ope = initRedisTemplate().opsForValue();
        ope.getAndExpire(key,timeOut,timeUnit);
    }

    /**
     * 读取指定类型数据
     * @param key
     * @param c
     * @return
     * @param <T>
     */
    public <T> T getObject(final String key, Class<T> c) {
        String redisStr = get(key);
        if (StringUtils.isBlank(redisStr)) {
            return null;
        }
        return JSON.parseObject(redisStr, c);
    }

    /**
     * 批量删除包含指定key
     * @param keyPattern
     */
    public void removeAllKey(String keyPattern){
        Set keys = initRedisTemplate().keys(keyPattern);
        if (CollectionUtils.isEmpty(keys)){
            return;
        }
        initRedisTemplate().delete(keys);
    }

    /**
     * 写入JSON数据
     * @param key
     * @param value
     * @param expireTime
     * @return
     */
    public boolean setJSON(final String key, Object value, Long expireTime) {
        try {
            ValueOperations<String, String> operations = initRedisTemplate().opsForValue();
            String json = JSON.toJSONString(value);
            operations.set(key, json);
            initRedisTemplate().expire(key, expireTime, TimeUnit.SECONDS);
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    public Long delete(Collection<String> keys) {
        return initRedisTemplate().delete(keys);
    }

    public Boolean delete(String key) {
        return initRedisTemplate().delete(key);
    }

    public Long removeHashData(String key, String hashKey) {
        HashOperations operations = initRedisTemplate().opsForHash();
        return operations.delete(key,hashKey);
    }

    /**
     * 获取指定数据
     * @param key
     * @param hashKey
     * @param c
     * @param <T>
     * @return
     */
    public <T> T getHashValue(final String key, final String hashKey, Class<T> c) {
        T result = null;
        Object obj = initRedisTemplate().opsForHash().get(key, hashKey);
        if (null != obj) {
            result = JSON.parseObject((String) obj, c);
        }
        return result;
    }
}
