<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c2014.mapper.Channel2014Mapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c2014.entity.Channel2014PO">
            <id property="channelCode" column="CHANNEL_CODE" jdbcType="VARCHAR"/>
            <result property="channelName" column="CHANNEL_NAME" jdbcType="VARCHAR"/>
            <result property="delflag" column="DELFLAG" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        CHANNEL_CODE,CHANNEL_NAME,DELFLAG
    </sql>

    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c2014.entity.Channel2014PO">
        update T_CHANNEL
        <set>
            <if test="channelCode != null and channelCode != ''">
                CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="channelName != null and channelName != ''">
                CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
            </if>
            <if test="delflag != null and delflag != ''">
                DELFLAG = #{delflag,jdbcType=CHAR},
            </if>
        </set>
        <where>
            <if test="channelCode != null and channelCode != ''">
                CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR}
            </if>
        </where>
    </update>
</mapper>
