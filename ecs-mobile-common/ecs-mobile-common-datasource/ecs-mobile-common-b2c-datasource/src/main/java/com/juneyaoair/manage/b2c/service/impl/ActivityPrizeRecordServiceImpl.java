package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.ecs.manage.dto.activity.request.passport.PassportRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.passport.PassportInformation;
import com.juneyaoair.manage.b2c.entity.activity.ActivityPrizeRecordPO;
import com.juneyaoair.manage.b2c.mapper.ActivityPrizeRecordMapper;
import com.juneyaoair.manage.b2c.service.ActivityPrizeRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【T_ACTIVITY_PRIZE_RECORD(积分找回奖品领取表)】的数据库操作Service实现
 * @createDate 2025-04-02 22:00:10
 */
@Service
public class ActivityPrizeRecordServiceImpl extends ServiceImpl<ActivityPrizeRecordMapper, ActivityPrizeRecordPO>
        implements ActivityPrizeRecordService {

    @Resource
    private ActivityPrizeRecordMapper activityPrizeRecordMapper;

    @Override
    public List<PassportInformation> toCatchActivityPrizeRecords(PassportRequest passportRequest) {
        return activityPrizeRecordMapper.toCatchActivityPrizeRecords(passportRequest);
    }
}




