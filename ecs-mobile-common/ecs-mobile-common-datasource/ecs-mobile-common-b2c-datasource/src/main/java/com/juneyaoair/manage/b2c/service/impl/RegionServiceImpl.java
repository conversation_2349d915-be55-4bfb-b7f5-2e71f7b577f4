package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.ecs.manage.dto.region.RegionReqDTO;
import com.juneyaoair.manage.b2c.service.IRegionService;
import com.juneyaoair.manage.b2c.entity.RegionPO;
import com.juneyaoair.manage.b2c.mapper.TRegionMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class RegionServiceImpl extends ServiceImpl<TRegionMapper, RegionPO> implements IRegionService {
    @Resource
    private TRegionMapper regionMapper;

    @Override
    public List<RegionPO> searchList(RegionReqDTO regionReqDTO) {
        return regionMapper.selectByAll(toTRegionPO(regionReqDTO));
    }

    private RegionPO toTRegionPO(RegionReqDTO regionReqDTO) {
        RegionPO ret = new RegionPO();
        ret.regionCode = regionReqDTO.regionCode;
        ret.regionName = regionReqDTO.regionName;
        ret.regionEName = regionReqDTO.regionEName;
        ret.createTime = regionReqDTO.createTime;
        ret.createUser = regionReqDTO.createUser;
        ret.lastUpdateTime = regionReqDTO.lastUpdateTime;
        ret.lastUpdateUser = regionReqDTO.lastUpdateUser;
        return ret;
    }

    @Override
    public int add(RegionReqDTO regionReqDTO) {
        return regionMapper.insertSelective(toTRegionPO(regionReqDTO));
    }

    @Override
    public int update(RegionReqDTO regionReqDTO) {
        return regionMapper.updateByPrimaryKeySelective(toTRegionPO(regionReqDTO));
    }

    @Override
    public int delete(String regionCode) {
        return regionMapper.deleteByPrimaryKey(regionCode);
    }
}
