package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.ActivityBaseInfoPO;
import com.juneyaoair.manage.b2c.service.IActivityBaseInfoService;
import com.juneyaoair.manage.b2c.mapper.ActivityBaseInfoMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_ACTIVITY_BASEINFO】的数据库操作Service实现
* @createDate 2023-11-16 13:57:56
*/
@Service
public class ActivityBaseInfoServiceImpl extends ServiceImpl<ActivityBaseInfoMapper, ActivityBaseInfoPO>
    implements IActivityBaseInfoService {

    @Autowired
    private ActivityBaseInfoMapper activityBaseInfoMapper;

    @Override
    public List<ActivityBaseInfoPO> toGainAllRecords(ActivityBaseInfoPO activityBaseInfoPO) {
        return activityBaseInfoMapper.searchAllRecords(activityBaseInfoPO);
    }

    @Override
    public ActivityBaseInfoPO selectOneByCondition(ActivityBaseInfoPO activityBaseInfoPO) {
        List<ActivityBaseInfoPO> activityBaseInfoPOS = activityBaseInfoMapper.selectAllById(activityBaseInfoPO);
        if (CollectionUtils.isNotEmpty(activityBaseInfoPOS)) {
            return activityBaseInfoPOS.get(0);
        } else {
            return null;
        }
    }
}




