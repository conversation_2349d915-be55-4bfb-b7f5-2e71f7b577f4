package com.juneyaoair.manage.b2c.entity.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.juneyaoair.ecs.manage.dto.activity.common.DbBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.persistence.Id;

/**
 * <AUTHOR>
 * @date ：Created in 2024-07-09 16:32
 * @description： 奖品信息表
 * @modified By：
 * @version: $
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "T_ACTIVITY_PRIZE_ENTITY")
public class ActivityPrizeEntityPO extends DbBase {

    @Id
    @ApiModelProperty(value = "奖品编码")
    private String prizeCode;

    @ApiModelProperty(value = "奖池编码")
    private String prizePoolCode;

    @ApiModelProperty(value = "奖品名称")
    private String prizeName;

    @ApiModelProperty(value = "奖品总数")
    private Integer totalAmount;

    @ApiModelProperty(value = "奖品已发放数")
    private Integer sendAmount;

    @ApiModelProperty(value = "奖品图片URL")
    private String iconUrl;

    @ApiModelProperty(value = "状态 Y:有效 N:无效 D:已删除")
    private String status;

}
