package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.CommonLotteryPoolPO;
import com.juneyaoair.manage.b2c.service.CommonLotteryPoolService;
import com.juneyaoair.manage.b2c.mapper.CommonLotteryPoolMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_PRIZE_POOL】的数据库操作Service实现
* @createDate 2023-05-22 15:48:54
*/
@Service
public class CommonLotteryPoolServiceImpl extends ServiceImpl<CommonLotteryPoolMapper, CommonLotteryPoolPO>
    implements CommonLotteryPoolService {

    @Autowired
    private CommonLotteryPoolMapper commonLotteryPoolMapper;

    @Override
    public List<CommonLotteryPoolPO> toGainAllPoolRecords(CommonLotteryPoolPO commonLotteryPoolPO) {
        return commonLotteryPoolMapper.toGainAllPoolRecords(commonLotteryPoolPO);
    }

}




