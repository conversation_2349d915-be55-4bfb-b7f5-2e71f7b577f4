package com.juneyaoair.manage.b2c.service;

import com.juneyaoair.manage.b2c.entity.activity.ActivityBaseInfoPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【T_ACTIVITY_BASEINFO】的数据库操作Service
 * @createDate 2023-11-16 13:57:56
 */
public interface IActivityBaseInfoService extends IService<ActivityBaseInfoPO> {

    /**
     * @param activityBaseInfoPO
     * @return java.util.List<com.juneyaoair.manage.b2c.entity.activity.ActivityBaseInfoPO>
     * <AUTHOR>
     * @Description 查询符合条件的所有记录
     * @Date 9:56 2023/11/17
     **/
    List<ActivityBaseInfoPO> toGainAllRecords(ActivityBaseInfoPO activityBaseInfoPO);

    ActivityBaseInfoPO selectOneByCondition(ActivityBaseInfoPO activityBaseInfoPO);

}
