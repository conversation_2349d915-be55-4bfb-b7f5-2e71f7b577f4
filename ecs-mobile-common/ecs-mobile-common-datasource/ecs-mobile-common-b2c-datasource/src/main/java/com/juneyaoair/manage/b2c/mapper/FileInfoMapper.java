package com.juneyaoair.manage.b2c.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.juneyaoair.ecs.manage.dto.activity.response.FileInfo;
import com.juneyaoair.manage.b2c.entity.FileInfoPO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【TP_FILE_INFO】的数据库操作Mapper
 * @createDate 2023-06-02 16:53:10
 * @Entity generator.domain.FileInfo
 */
public interface FileInfoMapper extends BaseMapper<FileInfoPO> {

    int updateFileInfo(FileInfoPO file);

    void insertFileInfo(FileInfoPO file);

    List<FileInfoPO> selectByAll(FileInfoPO fileInfoPO);

    List<FileInfo> searchAllByCondition(FileInfo fileInfo);

    int updateFileInfoV2(FileInfo fileInfo);

    void insertFileInfoV2(FileInfo file);


}




