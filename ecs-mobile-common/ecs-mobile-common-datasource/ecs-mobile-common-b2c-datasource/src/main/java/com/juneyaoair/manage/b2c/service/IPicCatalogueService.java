package com.juneyaoair.manage.b2c.service;


import com.juneyaoair.ecs.manage.dto.picture.DelPicCatalogueReqDTO;
import com.juneyaoair.ecs.manage.dto.picture.GetCatalogueListReqDTO;
import com.juneyaoair.ecs.manage.dto.picture.IndexInfoDTO;
import com.juneyaoair.ecs.manage.dto.picture.UpsertPicCatalogueReqDTO;
import com.juneyaoair.manage.b2c.entity.PicCataloguePO;

import java.util.List;

public interface IPicCatalogueService {
    List<IndexInfoDTO> searchPage(GetCatalogueListReqDTO request);

    boolean add(UpsertPicCatalogueReqDTO request);

    boolean update(UpsertPicCatalogueReqDTO request);

    boolean delete(DelPicCatalogueReqDTO request);

    PicCataloguePO selectById(GetCatalogueListReqDTO param);
}
