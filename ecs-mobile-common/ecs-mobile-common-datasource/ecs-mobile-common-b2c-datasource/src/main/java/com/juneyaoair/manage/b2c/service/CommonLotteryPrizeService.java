package com.juneyaoair.manage.b2c.service;

import com.juneyaoair.manage.b2c.entity.activity.CommonLotteryPrizePO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_PRIZE_ENTITY】的数据库操作Service
* @createDate 2023-05-24 08:59:46
*/
public interface CommonLotteryPrizeService extends IService<CommonLotteryPrizePO> {
    /**
     * 获取符合条件的奖品记录
     * @param commonLotteryPrizePO
     * @return
     */
    List<CommonLotteryPrizePO> toGainAllPrizeRecords(CommonLotteryPrizePO commonLotteryPrizePO);

    /**
     * 更新奖品信息
     * @param commonLotteryPrizePO
     * @return
     */
    int toUpdatePrizeInfo(CommonLotteryPrizePO commonLotteryPrizePO);

    int toUpdatePrizeRecord(CommonLotteryPrizePO commonLotteryPrizePO);
}
