package com.juneyaoair.manage.b2c.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.ecs.manage.dto.airline.AirLineLabelReqDTO;
import com.juneyaoair.ecs.manage.dto.airline.AirLineRequestDTO;
import com.juneyaoair.ecs.manage.dto.airline.AirlineDTO;
import com.juneyaoair.ecs.manage.dto.airline.AirlineLabelDTO;
import com.juneyaoair.ecs.manage.enums.YorNEnum;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.manage.b2c.entity.AirlineAPO;
import com.juneyaoair.manage.b2c.entity.AirlineLabelPO;
import com.juneyaoair.manage.b2c.mapper.TAirlineAMapper;
import com.juneyaoair.manage.b2c.mapper.TAirlineLabelMapper;
import com.juneyaoair.manage.b2c.service.IAirlineService;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AirlineServiceImpl extends ServiceImpl<TAirlineAMapper, AirlineAPO> implements IAirlineService {

    @Resource
    TAirlineAMapper airLineMapper;
    @Resource
    private TAirlineLabelMapper airLineLabelMapper;

    @Override
    public List<AirlineDTO> searchList(AirLineRequestDTO airlineRequest) {
        List<AirlineDTO> ret;
        List<AirlineAPO> allEnableAirline;
        if (StringUtils.isNotBlank(airlineRequest.getAirLineLabel())) {
            allEnableAirline = airLineMapper.selectByLabelName(airlineRequest.getAirLineLabel());
        } else {
            AirlineAPO airline = conver2PO(airlineRequest);
            allEnableAirline = airLineMapper.selectByAll(airline);
        }
        if (CollectionUtil.isEmpty(allEnableAirline)) {
            return null;
        }
        ret = allEnableAirline.stream().map(i -> {
            AirlineDTO airlineDTO = new AirlineDTO();
            BeanUtils.copyProperties(i, airlineDTO);
            return airlineDTO;
        }).collect(Collectors.toList());

        List<String> airLineIds = allEnableAirline.stream().map(AirlineAPO::getAirlineId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(airLineIds)) {
            return ret;
        }
        List<AirlineLabelPO> allAirlineLabels = airLineLabelMapper.selectByAirlineIdin(airLineIds);
        if (CollectionUtil.isEmpty(allAirlineLabels)) {
            return ret;
        }
        ret.forEach(i -> {
            i.setListLabel(allAirlineLabels.stream().filter(it -> StringUtils.equals(it.airlineId, i.airlineId))
                    .sorted(Comparator.comparing(po -> StrUtil.isNotBlank(po.getSequence()) ? po.getSequence() : "1000"))
                    .map(p -> {
                        AirlineLabelDTO label = new AirlineLabelDTO();
                        label.setAirlineId(p.getAirlineId());
                        label.setSequence(p.getSequence());
                        label.setLabelType(p.getLabelType());
                        label.setLabelName(p.getLabelName());
                        label.setUrl(p.getUrl());
                        label.setId(p.getId());
                        return label;
                    }).collect(Collectors.toList()));
        });
        return ret;
    }

    @Override
    public List<AirlineLabelPO> selectAirLineLabel(AirlineLabelPO para) {
        return airLineLabelMapper.selectByAll(new AirlineLabelPO());
    }

    @Override
    @DSTransactional
    public boolean add(AirlineAPO airLine, boolean needBack) {
        airLine.setCreateTime(new Date());
        airLine.setAirlineId(HOStringUtil.newGUID());
        airLine.setCreateMan(SecurityUtils.getUsername());
        airLineMapper.insert(airLine);
        //添加返程
        if (needBack) {
            airLine.setAirlineId(HOStringUtil.newGUID());
            //交换城市 机场
            String tempCity = airLine.getDepCity();
            airLine.setDepCity(airLine.getArrCity());
            airLine.setArrCity(tempCity);
            String tempAirport = airLine.getDepAirport();
            airLine.setDepAirport(airLine.getArrAirport());
            airLine.setArrAirport(tempAirport);
            airLineMapper.insert(airLine);
        }
        return true;
    }

    @Override
    public boolean delete(AirlineAPO airLine) {
        return airLineMapper.deleteByAirlineId(airLine.airlineId) > 0;
    }

    @Override
    public boolean update(AirlineAPO airLine) {
        //因为没有更改人字段故无法更改
        return airLineMapper.updateById(airLine) > 0;
    }

    @Override
    public List<AirlineAPO> search(AirlineAPO airLine) {
        return airLineMapper.selectByAll(airLine);
    }

    @Override
    public boolean addLablel(AirLineLabelReqDTO labelDTO) {
        AirlineLabelPO airlineLabelPO = new AirlineLabelPO();
        airlineLabelPO.id = HOStringUtil.newGUID();
        BeanUtils.copyNotNullProperties(labelDTO, airlineLabelPO);
        airlineLabelPO.setFitFlightDateStr(JSONObject.toJSONString(labelDTO.getFitFlightDateList()));
        return airLineLabelMapper.insert(airlineLabelPO) > 0;
    }

    @Override
    public boolean updateLablel(AirLineLabelReqDTO labelReqDTO) {
        AirlineLabelPO airlineLabelFinalPO = new AirlineLabelPO();
        BeanUtils.copyNotNullProperties(labelReqDTO, airlineLabelFinalPO);
        airlineLabelFinalPO.setFitFlightDateStr(JSONObject.toJSONString(labelReqDTO.getFitFlightDateList()));
        return airLineLabelMapper.updateByPrimaryKeySelective(airlineLabelFinalPO) > 0;
    }

    @Override
    public boolean deleteLablel(String labelId) {
        return airLineLabelMapper.deleteByPrimaryKey(labelId) > 0;
    }

    @Override
    public AirlineLabelPO selectLabelById(String labelId) {
        LambdaQueryWrapper<AirlineLabelPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AirlineLabelPO::getId, labelId);
        return airLineLabelMapper.selectOne(wrapper);
    }

    private static AirlineAPO conver2PO(AirLineRequestDTO airlineAPO) {
        AirlineAPO airlinePO = new AirlineAPO();
        airlinePO.setDelflag(YorNEnum.N.getStr());
        if (StringUtils.isNotBlank(airlineAPO.getDepCity())) {
            airlinePO.setDepCity(airlineAPO.getDepCity());
        }
        if (StringUtils.isNotBlank(airlineAPO.getArrCity())) {
            airlinePO.setArrCity(airlineAPO.getArrCity());
        }
        if (StringUtils.isNotBlank(airlineAPO.getAddonRemark())) {
            airlinePO.setAddonRemark(airlineAPO.getAddonRemark());
        }
        if (StringUtils.isNotBlank(airlineAPO.getDepAirport())) {
            airlinePO.setDepAirport(airlineAPO.getDepAirport());
        }
        if (StringUtils.isNotBlank(airlineAPO.getArrAirport())) {
            airlinePO.setArrAirport(airlineAPO.getArrAirport());
        }
        if (StringUtils.isNotBlank(airlineAPO.getIsHoLine())) {
            airlinePO.setIsHoLine(airlineAPO.getIsHoLine());
        }
        if (StringUtils.isNotBlank(airlineAPO.getIsTransit())) {
            airlinePO.setIsTransit(airlineAPO.getIsTransit());
        }
        return airlinePO;
    }


}
