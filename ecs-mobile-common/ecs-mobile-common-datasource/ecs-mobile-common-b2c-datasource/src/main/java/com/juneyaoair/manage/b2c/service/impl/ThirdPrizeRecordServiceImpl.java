package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.ecs.manage.dto.activity.request.coupon.ThirdPartPrizeRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.commonprizesend.ThirdPartPrizeResponse;
import com.juneyaoair.manage.b2c.entity.activity.ThirdPrizeRecordPO;
import com.juneyaoair.manage.b2c.mapper.ThirdPrizeRecordMapper;
import com.juneyaoair.manage.b2c.service.IThirdPrizeRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【T_ACTIVITY_THIRD_PRIZE_RECORD(第三方奖品发放流水表)】的数据库操作Service实现
 * @createDate 2025-03-31 21:10:49
 */
@Service
@SuppressWarnings("all")
public class ThirdPrizeRecordServiceImpl extends ServiceImpl<ThirdPrizeRecordMapper, ThirdPrizeRecordPO>
        implements IThirdPrizeRecordService {

    @Resource
    private ThirdPrizeRecordMapper thirdPrizeRecordMapper;

    @Override
    public void toBatchInsert(Set<ThirdPrizeRecordPO> thirdPrizeRecordPOSet) {
        thirdPrizeRecordMapper.insertBatch(thirdPrizeRecordPOSet);
    }

    @Override
    public List<ThirdPartPrizeResponse> toCatchThirdPrizeList(ThirdPartPrizeRequest thirdCouponRequest) {
        return thirdPrizeRecordMapper.searchAllByCondition(thirdCouponRequest);
    }

    @Override
    public void toTakeBatchInsert(Set<ThirdPrizeRecordPO> thirdPrizeRecordPOSet) {
        saveBatch(thirdPrizeRecordPOSet);
    }
}




