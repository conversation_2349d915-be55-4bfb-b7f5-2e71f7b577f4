package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.AppVersionInfoPO;
import com.juneyaoair.manage.b2c.mapper.AppVersionInfoMapper;
import com.juneyaoair.manage.b2c.service.IAppVersionInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description 针对表【APP_VERSION_INFO】的数据库操作Service实现
 */
@Service
public class AppVersionInfoServiceImpl extends ServiceImpl<AppVersionInfoMapper, AppVersionInfoPO>
        implements IAppVersionInfoService {
    @Resource
    AppVersionInfoMapper appVersionInfoMapper;

    @Override
    public List<AppVersionInfoPO> checkAppVersion(AppVersionInfoPO info) {
        return appVersionInfoMapper.checkAppVersion(info);
    }

    @Override
    public int addAppVersion(AppVersionInfoPO appVersionManage) {
        return appVersionInfoMapper.insertAppVersion(appVersionManage);
    }

    @Override
    public int deleteAppVersion(AppVersionInfoPO appVersionManage) {
        return appVersionInfoMapper.deleteAppVersion(appVersionManage);
    }


}




