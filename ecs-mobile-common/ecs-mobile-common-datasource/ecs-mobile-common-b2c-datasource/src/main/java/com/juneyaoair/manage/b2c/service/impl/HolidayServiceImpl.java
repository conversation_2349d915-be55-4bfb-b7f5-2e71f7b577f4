package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.HolidayPO;
import com.juneyaoair.manage.b2c.service.HolidayService;
import com.juneyaoair.manage.b2c.mapper.HolidayMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【T_HOLIDAY(假日管理)】的数据库操作Service实现
* @createDate 2024-01-24 16:32:26
*/
@Service
public class HolidayServiceImpl extends ServiceImpl<HolidayMapper, HolidayPO>
    implements HolidayService {

}




