package com.juneyaoair.manage.b2c.service;

import com.juneyaoair.ecs.manage.dto.activity.request.ActivityChildInfo;
import com.juneyaoair.manage.b2c.entity.activity.ActivityChildInfoPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【T_ACTIVITY_CHILD_INFO】的数据库操作Service
 * @createDate 2023-11-17 10:58:47
 */
public interface IActivityChildInfoService extends IService<ActivityChildInfoPO> {

    /**
     * @param activityChildInfoPO
     * @return java.util.List<com.juneyaoair.manage.b2c.entity.activity.ActivityChildInfoPO>
     * <AUTHOR>
     * @Description 查询符合条件的所有记录
     * @Date 11:14 2023/11/17
     **/

    List<ActivityChildInfoPO> toGainAllChildRecords(ActivityChildInfoPO activityChildInfoPO);

    ActivityChildInfoPO toGainChildInfoById(ActivityChildInfoPO activityChildInfoPO);

    /**
     * @param activityChildInfoPO
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.activity.request.ActivityChildInfo>
     * <AUTHOR>
     * @Description 查询符合条件的所有子记录
     * @Date 15:59 2023/12/11
     **/
    List<ActivityChildInfo> toGainAllChildRecords(ActivityChildInfo activityChildInfoPO);

    List<ActivityChildInfoPO> toGainAllRecordsWithNonIds(ActivityChildInfoPO activityChildInfoPO);

    /**
     * <AUTHOR>
     * @Description 更新子活动信息
     * @Date 8:34 2024/1/5
     * @param activityChildInfoPO
     * @return int
     **/
    int updateActivityChildInfo(ActivityChildInfoPO activityChildInfoPO);


}
