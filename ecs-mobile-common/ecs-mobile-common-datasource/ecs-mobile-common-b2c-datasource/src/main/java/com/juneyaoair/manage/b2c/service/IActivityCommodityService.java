package com.juneyaoair.manage.b2c.service;

import com.juneyaoair.manage.b2c.entity.activity.ActivityCommodityPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_ACTIVITY_COMMODITY】的数据库操作Service
* @createDate 2023-12-08 15:20:53
*/
public interface IActivityCommodityService extends IService<ActivityCommodityPO> {

    boolean removeById(ActivityCommodityPO activityCommodityPO);

    List<ActivityCommodityPO> toGainAllById(ActivityCommodityPO activityCommodityPO);

}
