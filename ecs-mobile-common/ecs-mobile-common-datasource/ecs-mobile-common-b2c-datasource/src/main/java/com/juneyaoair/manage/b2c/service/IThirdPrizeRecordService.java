package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.ecs.manage.dto.activity.request.coupon.ThirdPartPrizeRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.commonprizesend.ThirdPartPrizeResponse;
import com.juneyaoair.manage.b2c.entity.activity.ThirdPrizeRecordPO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【T_ACTIVITY_THIRD_PRIZE_RECORD(第三方奖品发放流水表)】的数据库操作Service
 * @createDate 2025-03-31 21:10:49
 */
@SuppressWarnings("all")
public interface IThirdPrizeRecordService extends IService<ThirdPrizeRecordPO> {

    /**
     * <AUTHOR>
     * @Description 批量插入第三方奖品信息
     * @Date 21:12 2025/4/1
     **/
    void toBatchInsert(Set<ThirdPrizeRecordPO> thirdPrizeRecordPOSet);

    /**
     * @param thirdCouponRequest
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.activity.response.commonprizesend.ThirdCouponResponse>
     * <AUTHOR>
     * @Description 第三方奖品列表查询
     * @Date 22:09 2025/4/1
     **/
    List<ThirdPartPrizeResponse> toCatchThirdPrizeList(ThirdPartPrizeRequest thirdCouponRequest);

    /**
     * @param thirdPrizeRecordPOSet
     * @return void
     * <AUTHOR>
     * @Description 批量插入第三方奖品信息
     * @Date 14:45 2025/4/2
     **/
    void toTakeBatchInsert(Set<ThirdPrizeRecordPO> thirdPrizeRecordPOSet);

}
