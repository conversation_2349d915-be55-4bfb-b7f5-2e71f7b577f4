package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.*;
import com.juneyaoair.manage.b2c.service.CommonLotteryResultService;
import com.juneyaoair.manage.b2c.mapper.CommonLotteryResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_PRIZE_WINNING_RECORD】的数据库操作Service实现
* @createDate 2023-05-30 10:46:08
*/
@Service
public class CommonLotteryResultServiceImpl extends ServiceImpl<CommonLotteryResultMapper, CommonLotteryResultReq>
    implements CommonLotteryResultService {

    @Autowired
    private CommonLotteryResultMapper commonLotteryResultMapper;

    @Override
    public List<CommonLotteryResultResp> toGainAllPrizeRecords(CommonLotteryResultReq commonLotteryResultReq) {
        return commonLotteryResultMapper.toGainAllLotteryRecords(commonLotteryResultReq);
    }
}




