package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.ecs.manage.dto.citymanage.*;
import com.juneyaoair.manage.b2c.entity.CityInfoPO;
import com.juneyaoair.manage.b2c.entity.DstWtInfoPO;
import com.juneyaoair.manage.b2c.entity.CityLabelInfoPO;

import java.util.List;
import java.util.Map;

public interface ICityInfoService extends IService<CityInfoPO> {
    List<CityInfoRespDTO> searchList(CityInfoReqDTO param);

    int add(CityManageDTO param);

    boolean update(CityManageDTO param);

    boolean setStatus(CityManageDTO param);

    boolean deleteByCityCode(String cityCode);

    List<CityInfoRespDTO> searchCity(CityInfoPO po);

    List<CityInfoRespDTO> searchByCityCode(String cityCode);

    DstWtInfoPO searchDstById(String id);

    boolean addDst(DstWtInfoPO param, String cityCode);

    boolean updateCityLabel(List<CityLabelInfoPO> param, String cityCode);

    boolean updateCityWarn(List<CityWarnReqDTO> params, String cityCode);

    boolean deleteCityWarn(CityWarnReqDTO param, String cityCode);

    boolean updateDst(DstWtInfoPO param, String cityCode);

    boolean switchHotStatus(String cityCode);

    boolean switchOftenStatus(String cityCode);

    boolean switchHotRegionStatus(String cityCode);

    List<CityInfoPO> selectAll(String status);

    /**
     * 城市信息
     * @return
     */
    Map<String, CityInfoPO> getCityMap();

}
