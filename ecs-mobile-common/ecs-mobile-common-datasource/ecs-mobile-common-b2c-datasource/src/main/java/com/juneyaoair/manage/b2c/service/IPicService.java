package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.manage.b2c.entity.PicturePO;
import com.juneyaoair.manage.b2c.entity.dict.DicValueWithDicType;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Set;


public interface IPicService extends IService<PicturePO> {
    List<PicturePO> getList(PicturePO picture);

    PicturePO getPicture(HttpServletRequest request, PicturePO picture);

    boolean addPicture(PicturePO picture, Set<String> imageTypeSet);

    boolean updatePic(PicturePO picture, Set<String> imageTypeSet);

    List<DicValueWithDicType> getChannelList(String dtCode);
}
