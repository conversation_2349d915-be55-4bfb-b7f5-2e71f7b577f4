package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.ecs.manage.dto.modular.ModularSyncDTO;
import com.juneyaoair.manage.b2c.entity.ModularPO;
import com.juneyaoair.manage.b2c.mapper.ModularMapper;
import com.juneyaoair.manage.b2c.service.IModularService;
import com.juneyaoair.manage.b2c.service.IModularVersionManageService;
import com.juneyaoair.ecs.manage.dto.datadict.ModularVersion;
import com.juneyaoair.ecs.manage.dto.modular.ModularDTO;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
@Service
public class ModularServiceImpl extends ServiceImpl<ModularMapper, ModularPO> implements IModularService {
    @Autowired
    private ModularMapper modularMapper;

    @Autowired
    private IModularVersionManageService modularVersionManageService;

    /**
     * 查询服务信息
     *
     * @return
     */
    @Override
    public List<ModularDTO> selectModularList(ModularPO modularPO) {
        return modularMapper.selectModularList(modularPO);
    }

    /**
     * 根据条件查询服务名称
     *
     * @param modularPO
     * @return
     */
    @Override
    public List<ModularPO> queryName(ModularPO modularPO) {
        return modularMapper.queryName(modularPO);
    }

    /**
     * 初始化新版本号
     */
    @Override
    public String initNewVersion(String oldVersion, String newVersion) {
        Set<String> versions = new HashSet<>();
        if (StringUtils.isNotEmpty(oldVersion)) {
            versions.addAll(Arrays.asList(oldVersion.split(",")));
        }
        if (StringUtils.isNotEmpty(newVersion)) {
            versions.addAll(Arrays.asList(newVersion.split(",")));
        }
        return String.join(",", versions);
    }

    /**
     * 插入模块版本信息
     */
    @Override
    public void insertModularVersion(ModularPO modularPO) {
        // 删除旧版本信息
        ModularVersion modularVersion = new ModularVersion();
        modularVersion.setModularId(modularPO.getId());
        modularVersionManageService.deleteModularVersion(modularVersion);

        // 添加新版本信息
        addModularVersion(modularPO, "ios", StringUtils.split(modularPO.getIos(), ","));
        addModularVersion(modularPO, "android", StringUtils.split(modularPO.getAndroid(), ","));
        addModularVersion(modularPO, "harmony", StringUtils.split(modularPO.getHarmony(), ","));
    }

    /**
     * 添加模块版本
     */
    @Override
    public void addModularVersion(ModularPO modularPO, String versionType, String[] versions) {
        if (ArrayUtils.isNotEmpty(versions)) {
            for (String version : versions) {
                ModularVersion mv = new ModularVersion();
                mv.setVersionType(versionType);
                mv.setModularId(modularPO.getId());
                mv.setModularBm(modularPO.getModularBm());
                mv.setVersionNo(version);
                int buildId = Integer.parseInt(version.replace(".", "")) * 100;
                mv.setInnerId(String.valueOf(buildId));
                modularVersionManageService.addModularVersion(mv);
            }
        }
    }

    /**
     * 新增模块
     */
    @Override
    @DSTransactional
    public boolean saveModularWithVersion(ModularPO modularPO) {
        boolean success = this.save(modularPO);
        if (success) {
            insertModularVersion(modularPO);
        }
        return success;
    }

    /**
     * 更新模块
     */
    @Override
    @DSTransactional
    public boolean updateModularWithVersion(ModularPO modularPO) {
        boolean success = this.updateById(modularPO);
        if (success) {
            insertModularVersion(modularPO);
        }
        return success;
    }

    /**
     * 同步版本号
     */
    @Override
    @DSTransactional
    public boolean synchronousWithVersion(ModularSyncDTO syncDTO) {
        ModularPO modularPOForQuery = new ModularPO();
        // 设置当前版本号和新版本号
        modularPOForQuery.setIos(syncDTO.getIos());
        modularPOForQuery.setAndroid(syncDTO.getAndroid());
        modularPOForQuery.setHarmony(syncDTO.getHarmony());
        List<ModularPO> modularList = this.queryName(modularPOForQuery);
        if (modularList == null || modularList.isEmpty()) {
            return false;
        }

        for (ModularPO modularPO : modularList) {
            modularPO.setId(modularPO.getId());
            modularPO.setModularBm(modularPO.getModularBm());

            // 处理Android版本
            if (StringUtils.isNotEmpty(modularPO.getAndroid())) {
                modularPO.setAndroid(this.initNewVersion(modularPO.getAndroid(), syncDTO.getNewAndroid()));
            } else {
                modularPO.setAndroid(syncDTO.getNewAndroid());
            }

            // 处理iOS版本
            if (StringUtils.isNotEmpty(modularPO.getIos())) {
                modularPO.setIos(this.initNewVersion(modularPO.getIos(), syncDTO.getNewIos()));
            } else {
                modularPO.setIos(syncDTO.getNewIos());
            }

            // 处理Harmony版本
            if (StringUtils.isNotEmpty(modularPO.getHarmony())) {
                modularPO.setHarmony(this.initNewVersion(modularPO.getHarmony(), syncDTO.getNewHarmony()));
            } else {
                modularPO.setHarmony(syncDTO.getNewHarmony());
            }
            modularPO.setUpdateTime(new Date());
            boolean success = this.updateById(modularPO);
            if (!success) {
                return false;
            }
            this.insertModularVersion(modularPO);
        }
        return true;
    }

    /**
     * 删除模块
     */
    @Override
    @DSTransactional
    public boolean deleteModularWithVersion(ModularPO modularPO) {
        // 更新模块状态为不可用
        boolean success = this.updateById(modularPO);
        if (success) {
            // 删除相关的版本信息
            ModularVersion modularVersion = new ModularVersion();
            modularVersion.setModularId(modularPO.getId());
            modularVersionManageService.deleteModularVersion(modularVersion);
        }
        return success;
    }

    /**
     * 查询单个模块详情
     */
    @Override
    public ModularDTO queryModifyModular(String id) {
        return modularMapper.queryModifyModular(id);
    }
}
