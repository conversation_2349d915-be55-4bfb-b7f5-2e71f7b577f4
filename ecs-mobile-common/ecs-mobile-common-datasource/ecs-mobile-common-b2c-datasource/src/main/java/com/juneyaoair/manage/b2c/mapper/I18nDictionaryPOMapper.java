package com.juneyaoair.manage.b2c.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.juneyaoair.manage.b2c.entity.I18nDictionaryPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface I18nDictionaryPOMapper extends BaseMapper<I18nDictionaryPO> {

    /**
     * 查询字典列表（支持PageHelper分页）
     * @param dictionaryName 字典名称
     * @param dictionaryType 字典类型
     * @return 字典列表
     */
    List<I18nDictionaryPO> queryDictionaries(@Param("dictionaryName") String dictionaryName,
                                             @Param("dictionaryType") String dictionaryType);
}