package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.ecs.manage.dto.activity.response.DICTValue;
import com.juneyaoair.manage.b2c.entity.dict.DictvaluePO;

import java.util.List;

public interface IDictvalueService extends IService<DictvaluePO> {

    List<DictvaluePO> findDICTValueList(DictvaluePO value);

    List<DICTValue> findDICTValueListV2(DICTValue value);
}
