package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.ActivityIssueLicensePO;
import com.juneyaoair.manage.b2c.service.ActivityIssueLicenseService;
import com.juneyaoair.manage.b2c.mapper.ActivityIssueLicenseMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【T_ACTIVITY_ISSUE_LICENSE(发放非固定数量奖品的许可表)】的数据库操作Service实现
* @createDate 2025-04-02 22:01:08
*/
@Service
public class ActivityIssueLicenseServiceImpl extends ServiceImpl<ActivityIssueLicenseMapper, ActivityIssueLicensePO>
    implements ActivityIssueLicenseService {

}




