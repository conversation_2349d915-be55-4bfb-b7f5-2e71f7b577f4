package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.manage.b2c.entity.activity.CommonLotteryResultReq;
import com.juneyaoair.manage.b2c.entity.activity.CommonLotteryResultResp;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_PRIZE_WINNING_RECORD】的数据库操作Service
* @createDate 2023-05-30 10:46:08
*/
public interface CommonLotteryResultService extends IService<CommonLotteryResultReq> {
    List<CommonLotteryResultResp> toGainAllPrizeRecords(CommonLotteryResultReq commonLotteryResultReq);
}
