package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.TripTransactionPO;
import com.juneyaoair.manage.b2c.service.TripTransactionPOService;
import com.juneyaoair.manage.b2c.mapper.TripTransactionPOMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【T_TRIP_TRANSACTION(旅客行程流水表)】的数据库操作Service实现
* @createDate 2025-06-29 11:46:22
*/
@Service
public class TripTransactionPOServiceImpl extends ServiceImpl<TripTransactionPOMapper, TripTransactionPO>
    implements TripTransactionPOService{

}




