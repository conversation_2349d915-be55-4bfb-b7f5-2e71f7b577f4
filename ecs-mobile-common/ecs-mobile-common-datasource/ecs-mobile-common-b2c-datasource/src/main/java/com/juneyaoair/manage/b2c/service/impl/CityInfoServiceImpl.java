package com.juneyaoair.manage.b2c.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import com.juneyaoair.ecs.manage.dto.citymanage.*;
import com.juneyaoair.ecs.manage.dto.country.CountryDTO;
import com.juneyaoair.ecs.manage.enums.YorNEnum;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.manage.b2c.entity.*;
import com.juneyaoair.manage.b2c.mapper.*;
import com.juneyaoair.manage.b2c.service.ICityInfoService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CityInfoServiceImpl extends ServiceImpl<TCityInfoMapper, CityInfoPO> implements ICityInfoService {
    @Resource
    TCityInfoMapper cityInfoMapper;
    @Resource
    TpFileInfoMapper tpFileInfoMapper;
    @Resource
    TDstWtInfoMapper tDstWtInfoMapper;
    @Resource
    TCityLabelInfoMapper tCityLabelInfoMapper;
    @Resource
    TCityWarnInfoMapper cityWarnInfoMapper;
    @Resource
    TEpidemicInfoMapper epidemicInfoMapper;
    @Resource
    TProvinceMapper provinceMapper;
    @Resource
    TCountryMapper countryMapper;

    private final static String TO_SHOW_YES = "Y";


    @Override
    public List<CityInfoRespDTO> searchList(CityInfoReqDTO dto) {
        LambdaQueryWrapper<CityInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        if (null != dto) {
            if (StringUtils.isNotEmpty(dto.getIsHotCity()) && TO_SHOW_YES.equals(dto.getIsHotCity())) {
                queryWrapper.orderByAsc(CityInfoPO::getCityHotOrder);
            }
            if (StringUtils.isNotEmpty(dto.getIsHotRegion()) && TO_SHOW_YES.equals(dto.getIsHotRegion())) {
                queryWrapper.orderByAsc(CityInfoPO::getZoneHotOrder);
            }
            if (StringUtils.isNotBlank(dto.getCityCode())) {
                queryWrapper.eq(CityInfoPO::getCityCode, dto.getCityCode());
            }
            if (StringUtils.isNotBlank(dto.getCityName())) {
                queryWrapper.like(CityInfoPO::getCityName, "%" + dto.getCityName() + "%");
            }
            if (StringUtils.isNotBlank(dto.getIsInternational())) {
                queryWrapper.eq(CityInfoPO::getIsInternational, dto.getIsInternational());
            }
            if (StringUtils.isNotBlank(dto.getIsHotCity())) {
                queryWrapper.eq(CityInfoPO::getIsHotCity, dto.getIsHotCity());
            }
            if (StringUtils.isNotBlank(dto.getIsOftenCity())) {
                queryWrapper.eq(CityInfoPO::getIsOftenCity, dto.getIsOftenCity());
            }
            if (StringUtils.isNotBlank(dto.getIsHotRegion())) {
                queryWrapper.eq(CityInfoPO::getIsHotRegion, dto.getIsHotRegion());
            }
            if(StringUtils.isNotBlank(dto.getStatus())){
                queryWrapper.eq(CityInfoPO::getStatus, dto.getStatus());
            }
        }
        List<CityInfoPO> records = cityInfoMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(records)) {
            return null;
        }
        List<List<String>> cityCodePartition = Lists.partition(records.stream()
                .filter(i -> i != null && StrUtil.isNotBlank(i.cityCode))
                .map(i -> i.cityCode)
                .collect(Collectors.toList()), 900);
        LambdaQueryWrapper<FileInfoPO> wrapperFile = new LambdaQueryWrapper<>();
        wrapperFile.and(CollUtil.isNotEmpty(cityCodePartition), i -> {
            for (List<String> strings : cityCodePartition) {
                i.or(it ->
                        it.in(FileInfoPO::getLinkid, strings)
                );
            }
        });
        Map<String, List<FileInfoPO>> fileMap = tpFileInfoMapper.selectList(wrapperFile).stream()
                .collect(Collectors.groupingBy((i -> i.getLinkid()), Collectors.toList()));
        LambdaQueryWrapper<CityLabelInfoPO> wrapperCityLabel = new LambdaQueryWrapper<>();
        wrapperCityLabel.and(CollUtil.isNotEmpty(cityCodePartition), i -> {
            for (List<String> strings : cityCodePartition) {
                i.or(it ->
                        it.in(CityLabelInfoPO::getCityCode, strings)
                );
            }
        });
        Map<String, List<CityLabelInfoPO>> labelMap = tCityLabelInfoMapper.selectList(wrapperCityLabel).stream().collect(Collectors.groupingBy(
                (i -> i.cityCode), Collectors.toList()));
        List<List<String>> dstWtIdPartition = Lists.partition(records.stream().map(i -> i.dstWtId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList()), 900);
        LambdaQueryWrapper<DstWtInfoPO> wrapperDstWtInfo = new LambdaQueryWrapper<>();
        wrapperDstWtInfo.and(CollUtil.isNotEmpty(dstWtIdPartition), i -> {
            for (List<String> strings : dstWtIdPartition) {
                i.or(it ->
                        it.in(DstWtInfoPO::getId, strings)
                );
            }
        });

        Map<String, List<DstWtInfoPO>> dstMap = tDstWtInfoMapper.selectList(wrapperDstWtInfo).stream()
                .collect(Collectors.groupingBy((i -> i.id), Collectors.toList()));
        Map<String, CountryJoinRegionPO> countryMap = countryMapper.selectJoinRegoinByAll(null).stream().collect(Collectors.toMap(k -> k.countryCode, v -> v));
        return records.stream().map(
                i -> {
                    CityInfoRespDTO cityInfoRespDTO = new CityInfoRespDTO();
                    BeanUtil.copyProperties(i, cityInfoRespDTO);
                    FileInfoPO fileInfoPO = fileMap.getOrDefault(i.cityCode, new ArrayList<>()).stream().findFirst().orElse(null);
                    if (fileInfoPO != null) {
                        FileInfoDTO fileInfoDTO = new FileInfoDTO();
                        BeanUtil.copyProperties(fileInfoPO, fileInfoDTO);
                        cityInfoRespDTO.fileInfo = fileInfoDTO;
                    }
                    List<CityLabelInfoPO> cityLabelInfoPOS = labelMap.get(i.cityCode);
                    if (CollectionUtil.isNotEmpty(cityLabelInfoPOS)) {
                        cityInfoRespDTO.listCityLabel = cityLabelInfoPOS.stream().map(it -> {
                            CityLabelInfoDTO labelInfoDTO = new CityLabelInfoDTO();
                            BeanUtil.copyProperties(it, labelInfoDTO);
                            return labelInfoDTO;
                        }).collect(Collectors.toList());
                    }
                    DstWtInfoPO dstWtInfoPO = dstMap.getOrDefault(i.dstWtId, new ArrayList<>()).stream().findFirst().orElse(null);
                    if (dstWtInfoPO != null) {
                        DstWtInfoDTO dstWtInfoDTO = new DstWtInfoDTO();
                        BeanUtil.copyProperties(dstWtInfoPO, dstWtInfoDTO);
                        dstWtInfoDTO.dstDateInfos = JsonUtil.jsonToList(dstWtInfoPO.dstMsg, new TypeToken<List<DstDateInfo>>() {
                                }.getType()
                        );

                        cityInfoRespDTO.dstWtInfo = dstWtInfoDTO;
                    }
                    CountryJoinRegionPO countryJoinRegionPO = countryMap.get(i.countryCode);
                    if (countryJoinRegionPO != null) {
                        cityInfoRespDTO.countryDTO = convertCountry(countryJoinRegionPO);
                    }
                    cityInfoRespDTO.provinceName = i.provinceName;
                    cityInfoRespDTO.cityPinYin = HOStringUtil.capitalizeFirstLetter(i.cityPinYin);
                    cityInfoRespDTO.cityPinYinAbb = HOStringUtil.capitalizeFirstLetter(i.cityPinYinAbb);
                    return cityInfoRespDTO;
                }
        ).collect(Collectors.toList());
    }

    private CountryDTO convertCountry(CountryJoinRegionPO countryJoinRegionPO) {
        CountryDTO ret = new CountryDTO();
        ret.countryCode = countryJoinRegionPO.countryCode;
        ret.countryName = countryJoinRegionPO.countryName;
        ret.englishName = countryJoinRegionPO.englishName;
        ret.currency = countryJoinRegionPO.currency;
        ret.createDatetime = countryJoinRegionPO.createDatetime;
        ret.creatorId = countryJoinRegionPO.creatorId;
        ret.countryTelCode = countryJoinRegionPO.countryTelCode;
        ret.hotCountry = countryJoinRegionPO.hotCountry;
        ret.sequence = countryJoinRegionPO.sequence;
        ret.regionCode = countryJoinRegionPO.regionCode;
        ret.regionName = countryJoinRegionPO.regionName;
        ret.regionEName = countryJoinRegionPO.regionEName;
        return ret;
    }

    @Override
    @DSTransactional
    public int add(CityManageDTO param) {
        CityInfoPO cityInfoPO = new CityInfoPO();
        BeanUtil.copyProperties(param, cityInfoPO);
        cityInfoPO.setCityPinYin(param.getCityPinYin());
        cityInfoPO.setCityPinYinAbb(param.getCityPinYinAbb());
        cityInfoPO.isHotCity = YorNEnum.N.getStr();
        if (param.status == null) {
            cityInfoPO.status = "0";
        }
        cityInfoPO.setIsTopCity(" ");
        cityInfoPO.setCreateDatetime(new Date());
        cityInfoPO.setCreateId(SecurityUtils.getUserId());
        cityInfoMapper.insertSelective(cityInfoPO);
        FileInfoDTO fileInfo = param.fileInfo;
        if (fileInfo != null) {
            FileInfoPO fileInfoPO = new FileInfoPO();
            BeanUtil.copyProperties(fileInfo, fileInfoPO);
            fileInfoPO.setLinkid(param.getCityCode());
            fileInfoPO.setFileid(IdUtil.randomUUID());
            tpFileInfoMapper.insertSelective(fileInfoPO);
        }
        return 1;
    }

    @Override
    @DSTransactional
    public boolean update(CityManageDTO param) {
        CityInfoPO cityInfoPO = new CityInfoPO();
        BeanUtil.copyProperties(param, cityInfoPO);
        cityInfoPO.setCityPinYin(HOStringUtil.capitalizeFirstLetter(param.getCityPinYin()));
        cityInfoPO.setCityPinYinAbb(HOStringUtil.capitalizeFirstLetter(param.getCityPinYinAbb()));
        if (param.provinceId != null && param.provinceId > 0) {
            ProvincePO provincePO = provinceMapper.selectByPrimaryKey((long) param.provinceId);
            if (provincePO != null && StringUtils.isNotBlank(provincePO.provinceName)) {
                cityInfoPO.provinceName = provincePO.provinceName;
            }
        }
        cityInfoMapper.updateByPrimaryKey(cityInfoPO);
        //修改城市图片
        if (param.getFileInfo() != null) {
            FileInfoPO fileInfoPO = new FileInfoPO();
            fileInfoPO.setLinkid(param.getCityCode());
            List<FileInfoPO> listExistFile = tpFileInfoMapper.selectAllByLinkid(fileInfoPO.getLinkid());
            //存在则进行修改，不存在则进行添加
            if (listExistFile.size() > 0) {
                FileInfoPO existFilePO = listExistFile.get(0);
                existFilePO.setFileName(param.getFileInfo().getFileName());
                existFilePO.setFileUrl(param.getFileInfo().getFileUrl());
                existFilePO.setFileSize(param.getFileInfo().getFileSize());
                existFilePO.setModifyTime(new Date());
                existFilePO.setRealname(param.getFileInfo().getRealname());
                existFilePO.setImgserviceurl(param.getFileInfo().getImgserviceurl());
                existFilePO.setFilePath(param.getFileInfo().getFilePath());
                existFilePO.setFileType(param.getFileInfo().getFileType());
                tpFileInfoMapper.updateByPrimaryKeySelective(existFilePO);
            } else {
                FileInfoPO newFileInfoPO = new FileInfoPO();
                newFileInfoPO.setFileid(IdUtil.randomUUID());
                newFileInfoPO.setFileUrl(param.getFileInfo().getFileUrl());
                newFileInfoPO.setImgserviceurl(param.getFileInfo().getImgserviceurl());
                newFileInfoPO.setLinkid(param.getCityCode());
                tpFileInfoMapper.insertSelective(newFileInfoPO);
            }
        } else {
            FileInfoPO fileInfoPO = new FileInfoPO();
            fileInfoPO.setLinkid(param.cityCode);
            tpFileInfoMapper.delByLinkid(fileInfoPO.getLinkid());
        }
        return true;
    }

    @Override
    public boolean setStatus(CityManageDTO param) {
        return cityInfoMapper.updateStatusByCityCode(param.status, param.cityCode) > 0;
    }

    @Override
    public boolean deleteByCityCode(String cityCode) {
        return cityInfoMapper.deleteByCityCode(cityCode) > 0;
    }

    @Override
    public List<CityInfoRespDTO> searchCity(CityInfoPO po) {
        LambdaQueryWrapper<CityInfoPO> cityQueryWrapper = getCityInfoPOLambdaQueryWrapper(po);
        List<CityInfoPO> cityInfoPOS = cityInfoMapper.selectList(cityQueryWrapper);
        if (CollectionUtil.isEmpty(cityInfoPOS)) {
            return null;
        }

        List<List<CityInfoPO>> partition = Lists.partition(cityInfoPOS.stream().collect(Collectors.toList()), 900);
        LambdaQueryWrapper<FileInfoPO> fileWrapper = new LambdaQueryWrapper<FileInfoPO>();
        fileWrapper.and(i -> {
            for (List<CityInfoPO> pos : partition) {
                i.or(it -> it.in(FileInfoPO::getLinkid, pos.stream().map(s -> s.cityCode).filter(StringUtils::isNotBlank).collect(Collectors.toList())));
            }
        });
        List<FileInfoPO> fileInfoPOS = tpFileInfoMapper.selectList(fileWrapper);
        Map<String, List<FileInfoPO>> fileMap = fileInfoPOS.stream().collect(Collectors.groupingBy(i -> i.getLinkid(), Collectors.toList()));

        LambdaQueryWrapper<CityLabelInfoPO> cityLabelWrapper = new LambdaQueryWrapper<CityLabelInfoPO>();
        cityLabelWrapper.and(i -> {
            for (List<CityInfoPO> pos : partition) {
                i.or(it -> it.in(CityLabelInfoPO::getCityCode, pos.stream().map(s -> s.cityCode).filter(StringUtils::isNotBlank).collect(Collectors.toList())));
            }
        });
        Map<String, List<CityLabelInfoPO>> labelMap = tCityLabelInfoMapper.selectList(cityLabelWrapper).stream().collect(Collectors.groupingBy(
                (i -> i.cityCode), Collectors.toList()));

        LambdaQueryWrapper<DstWtInfoPO> dstWrapper = new LambdaQueryWrapper<DstWtInfoPO>();
        dstWrapper.and(i -> {
            for (List<CityInfoPO> pos : partition) {
                i.or(it -> it.in(DstWtInfoPO::getId, pos.stream().map(s -> s.dstWtId).filter(StringUtils::isNotBlank).collect(Collectors.toList())));
            }
        });
        Map<String, List<DstWtInfoPO>> dstMap = tDstWtInfoMapper.selectList(dstWrapper).stream()
                .collect(Collectors.groupingBy((i -> i.id), Collectors.toList()));

        LambdaQueryWrapper<EpidemicInfoPO> epidemicWrapper = new LambdaQueryWrapper<EpidemicInfoPO>();
        epidemicWrapper.and(i -> {
            for (List<CityInfoPO> pos : partition) {
                i.or(it -> it.in(EpidemicInfoPO::getCityCode, pos.stream().map(s -> s.getCityCode()).filter(StringUtils::isNotBlank).collect(Collectors.toList())));
            }
        });
        Map<String, EpidemicInfoPO> epidemicMap = epidemicInfoMapper.selectList(epidemicWrapper).stream()
                .collect(Collectors.toMap(EpidemicInfoPO::getCityCode, i -> i));

        LambdaQueryWrapper<CityWarnInfoPO> warnWrapper = new LambdaQueryWrapper<CityWarnInfoPO>();
        warnWrapper.and(i -> {
            for (List<CityInfoPO> pos : partition) {
                i.or(it -> it.in(CityWarnInfoPO::getCityCode, pos.stream().map(CityInfoPO::getCityCode).filter(StringUtils::isNotBlank).collect(Collectors.toList())));
            }
        });
        Map<String, List<CityWarnInfoPO>> warnMap = cityWarnInfoMapper.selectList(warnWrapper).stream().collect(Collectors.groupingBy(
                (i -> i.cityCode), Collectors.toList()));

        Map<String, CountryJoinRegionPO> countryMap = countryMapper.selectJoinRegoinByAll(null).stream().collect(Collectors.toMap(k -> k.countryCode, v -> v));
        return cityInfoPOS.stream().map(
                i -> {
                    CityInfoRespDTO cityInfoRespDTO = new CityInfoRespDTO();
                    BeanUtil.copyProperties(i, cityInfoRespDTO);
                    FileInfoPO fileInfoPO = fileMap.getOrDefault(i.cityCode, new ArrayList<>()).stream().findFirst().orElse(null);
                    if (fileInfoPO != null) {
                        FileInfoDTO fileInfoDTO = new FileInfoDTO();
                        BeanUtil.copyProperties(fileInfoPO, fileInfoDTO);
                        cityInfoRespDTO.fileInfo = fileInfoDTO;
                    }
                    List<CityLabelInfoPO> cityLabelInfoPOS = labelMap.get(i.cityCode);
                    if (CollectionUtil.isNotEmpty(cityLabelInfoPOS)) {
                        cityInfoRespDTO.listCityLabel = cityLabelInfoPOS.stream().map(it -> {
                            CityLabelInfoDTO labelInfoDTO = new CityLabelInfoDTO();
                            BeanUtil.copyProperties(it, labelInfoDTO);
                            return labelInfoDTO;
                        }).collect(Collectors.toList());
                    }
                    DstWtInfoPO dstWtInfoPO = dstMap.getOrDefault(i.cityCode, new ArrayList<>()).stream().findFirst().orElse(null);
                    if (dstWtInfoPO != null) {
                        DstWtInfoDTO dstWtInfoDTO = new DstWtInfoDTO();
                        BeanUtil.copyProperties(dstWtInfoPO, dstWtInfoDTO);
                        cityInfoRespDTO.dstWtInfo = dstWtInfoDTO;
                    }
                    if (epidemicMap != null) {
                        CityEpidemicDTO cityEpidemicDTO = new CityEpidemicDTO();
                        EpidemicInfoPO epidemicInfoPO = epidemicMap.get(i.cityCode);
                        BeanUtil.copyProperties(epidemicInfoPO, cityEpidemicDTO);
                        cityInfoRespDTO.cityEpidemic = cityEpidemicDTO;
                    }
                    List<CityWarnInfoPO> cityWarnInfoPOS = warnMap.get(i.cityCode);
                    if (CollectionUtil.isNotEmpty(cityWarnInfoPOS)) {
                        cityInfoRespDTO.cityWarnDTO = cityWarnInfoPOS.stream().map(it -> {
                            CityWarnDTO warnDTO = new CityWarnDTO();
                            BeanUtil.copyProperties(it, warnDTO);
                            return warnDTO;
                        }).collect(Collectors.toList());
                    }
                    CountryJoinRegionPO countryJoinRegionPO = countryMap.get(i.countryCode);
                    if (countryJoinRegionPO != null) {
                        cityInfoRespDTO.countryDTO = convertCountry(countryMap.get(i.countryCode));
                    }
                    return cityInfoRespDTO;
                }
        ).collect(Collectors.toList());
    }

    private static LambdaQueryWrapper<CityInfoPO> getCityInfoPOLambdaQueryWrapper(CityInfoPO po) {
        LambdaQueryWrapper<CityInfoPO> cityQueryWrapper = new LambdaQueryWrapper<>();
        if (po != null) {
            cityQueryWrapper.eq(po.cityCode != null, CityInfoPO::getCityCode, po.cityCode);
            cityQueryWrapper.eq(po.cityName != null, CityInfoPO::getCityName, po.cityName);
            cityQueryWrapper.eq(po.cityEName != null, CityInfoPO::getCityEName, po.cityEName);
            cityQueryWrapper.eq(po.countryCode != null, CityInfoPO::getCountryCode, po.countryCode);
            cityQueryWrapper.eq(po.cityPinYin != null, CityInfoPO::getCityPinYin, po.cityPinYin);
            cityQueryWrapper.eq(po.cityPinYinAbb != null, CityInfoPO::getCityPinYinAbb, po.cityPinYinAbb);
            cityQueryWrapper.eq(po.provinceId != null, CityInfoPO::getProvinceId, po.provinceId);
            cityQueryWrapper.eq(po.provinceName != null, CityInfoPO::getProvinceName, po.provinceName);
            cityQueryWrapper.eq(po.isHotCity != null, CityInfoPO::getIsHotCity, po.isHotCity);
            cityQueryWrapper.eq(po.isTopCity != null, CityInfoPO::getIsTopCity, po.isTopCity);
            cityQueryWrapper.eq(po.officeAddress != null, CityInfoPO::getOfficeAddress, po.officeAddress);
            cityQueryWrapper.eq(po.officeTel != null, CityInfoPO::getOfficeTel, po.officeTel);
            cityQueryWrapper.eq(po.createDatetime != null, CityInfoPO::getCreateDatetime, po.createDatetime);
            cityQueryWrapper.eq(po.createId != null, CityInfoPO::getCreateId, po.createId);
            cityQueryWrapper.eq(po.cityTimeZone != null, CityInfoPO::getCityTimeZone, po.cityTimeZone);
            cityQueryWrapper.eq(po.nameAbb != null, CityInfoPO::getNameAbb, po.nameAbb);
            cityQueryWrapper.eq(po.englishNameAbb != null, CityInfoPO::getEnglishNameAbb, po.englishNameAbb);
            cityQueryWrapper.eq(po.isInternational != null, CityInfoPO::getIsInternational, po.isInternational);
            cityQueryWrapper.eq(po.baidumappoint != null, CityInfoPO::getBaidumappoint, po.baidumappoint);
            cityQueryWrapper.eq(po.officeFax != null, CityInfoPO::getOfficeFax, po.officeFax);
            cityQueryWrapper.eq(po.delflag != null, CityInfoPO::getDelflag, po.delflag);
            cityQueryWrapper.eq(po.url != null, CityInfoPO::getUrl, po.url);
            cityQueryWrapper.eq(po.cityHotOrder != null, CityInfoPO::getCityHotOrder, po.cityHotOrder);
            cityQueryWrapper.eq(po.zoneHotOrder != null, CityInfoPO::getZoneHotOrder, po.zoneHotOrder);
            cityQueryWrapper.eq(po.iconUrl != null, CityInfoPO::getIconUrl, po.iconUrl);
            cityQueryWrapper.eq(po.dstWtId != null, CityInfoPO::getDstWtId, po.dstWtId);
            cityQueryWrapper.eq(po.cityKoName != null, CityInfoPO::getCityKoName, po.cityKoName);
            cityQueryWrapper.eq(po.cityJpName != null, CityInfoPO::getCityJpName, po.cityJpName);
            cityQueryWrapper.eq(po.cityThName != null, CityInfoPO::getCityThName, po.cityThName);
            cityQueryWrapper.eq(po.cityTcName != null, CityInfoPO::getCityTcName, po.cityTcName);
            cityQueryWrapper.eq(po.cityKeyWords != null, CityInfoPO::getCityKeyWords, po.cityKeyWords);
            cityQueryWrapper.eq(po.isHotRegion != null, CityInfoPO::getIsHotRegion, po.isHotRegion);
            cityQueryWrapper.eq(po.status != null, CityInfoPO::getStatus, po.status);
            cityQueryWrapper.eq(po.longitude != null, CityInfoPO::getLongitude, po.longitude);
            cityQueryWrapper.eq(po.latitude != null, CityInfoPO::getLatitude, po.latitude);
            cityQueryWrapper.eq(po.isOftenCity != null, CityInfoPO::getIsOftenCity, po.isOftenCity);
        }
        return cityQueryWrapper;
    }


    @Override
    public List<CityInfoRespDTO> searchByCityCode(String cityCode) {
        CityInfoPO cityInfoPO = cityInfoMapper.selectByPrimaryKey(cityCode);
        if (cityInfoPO == null) {
            return null;
        }
        List<CityInfoPO> records = Arrays.asList(cityInfoPO);
        Map<String, List<FileInfoPO>> fileMap = tpFileInfoMapper.selectByLinkidIn(records.stream().map(i -> i.cityCode).collect(Collectors.toList())).stream()
                .collect(Collectors.groupingBy((i -> i.getLinkid()), Collectors.toList()));
        Map<String, List<CityLabelInfoPO>> labelMap = tCityLabelInfoMapper.selectByCityCodeIn(records.stream().map(i -> i.cityCode).collect(Collectors.toList())).stream().collect(Collectors.groupingBy(
                (i -> i.cityCode), Collectors.toList()));
        Map<String, List<DstWtInfoPO>> dstMap = tDstWtInfoMapper.selectbyidIn(records.stream().map(i -> i.dstWtId).collect(Collectors.toList())).stream()
                .collect(Collectors.groupingBy((i -> i.id), Collectors.toList()));
        EpidemicInfoPO epidemicInfoPO = epidemicInfoMapper.selectByPrimaryKey(cityCode);
        CityWarnInfoPO cityWarnInfoPOReq = new CityWarnInfoPO();
        cityWarnInfoPOReq.cityCode = cityCode;
        Map<String, List<CityWarnInfoPO>> warnMap = cityWarnInfoMapper.selectByAll(cityWarnInfoPOReq).stream().collect(Collectors.groupingBy(
                (i -> i.cityCode), Collectors.toList()
        ));

        return records.stream().map(
                i -> {
                    CityInfoRespDTO cityInfoRespDTO = new CityInfoRespDTO();
                    BeanUtil.copyProperties(i, cityInfoRespDTO);
                    FileInfoPO fileInfoPO = fileMap.getOrDefault(i.cityCode, new ArrayList<>()).stream().findFirst().orElse(null);
                    if (fileInfoPO != null) {
                        FileInfoDTO fileInfoDTO = new FileInfoDTO();
                        BeanUtil.copyProperties(fileInfoPO, fileInfoDTO);
                        cityInfoRespDTO.fileInfo = fileInfoDTO;
                    }
                    List<CityLabelInfoPO> cityLabelInfoPOS = labelMap.get(i.cityCode);
                    if (CollectionUtil.isNotEmpty(cityLabelInfoPOS)) {
                        cityInfoRespDTO.listCityLabel = cityLabelInfoPOS.stream().map(it -> {
                            CityLabelInfoDTO labelInfoDTO = new CityLabelInfoDTO();
                            BeanUtil.copyProperties(it, labelInfoDTO);
                            return labelInfoDTO;
                        }).collect(Collectors.toList());
                    }
                    DstWtInfoPO dstWtInfoPO = dstMap.getOrDefault(i.dstWtId, new ArrayList<>()).stream().findFirst().orElse(null);
                    if (dstWtInfoPO != null) {
                        DstWtInfoDTO dstWtInfoDTO = new DstWtInfoDTO();
                        BeanUtil.copyProperties(dstWtInfoPO, dstWtInfoDTO);
                        dstWtInfoDTO.dstDateInfos = JsonUtil.jsonToList(dstWtInfoPO.dstMsg, new TypeToken<List<DstDateInfo>>() {
                                }.getType()
                        );
                        cityInfoRespDTO.dstWtInfo = dstWtInfoDTO;
                    }
                    if (epidemicInfoPO != null) {
                        CityEpidemicDTO cityEpidemicDTO = new CityEpidemicDTO();
                        BeanUtil.copyProperties(epidemicInfoPO, cityEpidemicDTO);
                        cityInfoRespDTO.cityEpidemic = cityEpidemicDTO;
                    }
                    List<CityWarnInfoPO> cityWarnInfoPOS = warnMap.get(i.cityCode);
                    if (CollectionUtil.isNotEmpty(cityWarnInfoPOS)) {
                        cityInfoRespDTO.cityWarnDTO = cityWarnInfoPOS.stream().map(it -> {
                            CityWarnDTO warnDTO = new CityWarnDTO();
                            BeanUtil.copyProperties(it, warnDTO);
                            if (StringUtils.isNotBlank(it.depStartDate) && StringUtils.isNotBlank(it.getDepEndDate())) {
                                String concat = it.getDepStartDate().concat(" - ").concat(it.getDepEndDate());
                                warnDTO.setDepShowDate(concat);
                                warnDTO.setArrOrDept("dept");
                            }
                            if (StringUtils.isNotBlank(it.getArrStartDate()) && StringUtils.isNotBlank(it.getArrEndDate())) {
                                String concat = it.getArrStartDate().concat(" - ").concat(it.getArrEndDate());
                                warnDTO.setArrShowDate(concat);
                                warnDTO.setArrOrDept("arr");
                            }
                            return warnDTO;
                        }).collect(Collectors.toList());
                    }
                    return cityInfoRespDTO;
                }
        ).collect(Collectors.toList());

    }

    @Override
    public DstWtInfoPO searchDstById(String id) {
        return tDstWtInfoMapper.selectByPrimaryKey(id);
    }

    @DSTransactional
    @Override
    public boolean addDst(DstWtInfoPO param, String cityCode) {
        String dstId = IdUtil.simpleUUID();
        CityInfoPO cityInfoPO = new CityInfoPO();
        DstWtInfoPO dstWtInfoPO = new DstWtInfoPO();
        BeanUtil.copyProperties(param, dstWtInfoPO);
        dstWtInfoPO.setId(dstId);
        cityInfoPO.setCityCode(cityCode);
        cityInfoPO.setDstWtId(dstId);
        tDstWtInfoMapper.insertSelective(dstWtInfoPO);
        cityInfoMapper.updateByPrimaryKeySelective(cityInfoPO);
        return true;
    }

    @DSTransactional
    @Override
    /**
     * tCityLabelInfoPOs和param
     * 1重复—>更新
     * 2tCityLabelInfoPOs有 param没有->删除
     * 3tCityLabelInfoPOs没有param有->新增
     */
    public boolean updateCityLabel(List<CityLabelInfoPO> param, String cityCode) {
        CityLabelInfoPO cityLabelInfoPO = new CityLabelInfoPO();
        cityLabelInfoPO.cityCode = cityCode;
        List<CityLabelInfoPO> cityLabelInfoPOS = tCityLabelInfoMapper.selectByAll(cityLabelInfoPO);
        if (CollectionUtil.isEmpty(param)) {
            //旧的标签全删
            if (CollectionUtil.isNotEmpty(cityLabelInfoPOS)) {
                tCityLabelInfoMapper.deleteBatchIds(cityLabelInfoPOS.stream().map(i -> i.cityLabelId).collect(Collectors.toList()));
            }
        } else {
            if (CollectionUtil.isNotEmpty(cityLabelInfoPOS)) {
                //旧标签要删的部分
                List<CityLabelInfoPO> toDeletes = cityLabelInfoPOS.stream()
                        .filter(i -> CollectionUtil.isEmpty(param) || param.stream().noneMatch(p -> p != null && p.cityLabelId != null &&
                                p.cityLabelId.equals(i.cityLabelId))).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(toDeletes)) {
                    tCityLabelInfoMapper.deleteBatchIds(toDeletes.stream().map(i -> i.cityLabelId).collect(Collectors.toList()));
                }
                //旧标签没有的部分要新增
                List<CityLabelInfoPO> toAdds = param.stream().filter(i -> i != null && i.cityLabelId == null)
                        .map(i -> {
                            CityLabelInfoPO copied = JsonUtil.fromJson(JsonUtil.objectToJson(i), CityLabelInfoPO.class);
                            copied.cityCode = cityCode;
                            copied.createtime = new Date();
                            copied.createUser = SecurityUtils.getUsername();
                            copied.updatetime = new Date();
                            copied.updateUser = SecurityUtils.getUsername();
                            return copied;
                        }).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(toAdds)) {
                    toAdds.forEach(i -> tCityLabelInfoMapper.insert(i));
                }
                //旧标签要更改的部分
                List<CityLabelInfoPO> toUpdates = param.stream().filter(i -> i != null && i.cityLabelId != null)
                        .map(i -> {
                            CityLabelInfoPO copied = JsonUtil.fromJson(JsonUtil.objectToJson(i), CityLabelInfoPO.class);
                            copied.cityCode = cityCode;
                            copied.updatetime = new Date();
                            copied.updateUser = SecurityUtils.getUsername();
                            return copied;
                        }).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(toUpdates)) {
                    toUpdates.forEach(i -> tCityLabelInfoMapper.updateByPrimaryKey(i));
                }
            } else {
                List<CityLabelInfoPO> toAdds = param.stream().map(i -> {
                    CityLabelInfoPO copied = JsonUtil.fromJson(JsonUtil.objectToJson(i), CityLabelInfoPO.class);
                    copied.cityCode = cityCode;
                    copied.createtime = new Date();
                    copied.createUser = SecurityUtils.getUsername();
                    copied.updatetime = new Date();
                    copied.updateUser = SecurityUtils.getUsername();
                    return copied;
                }).collect(Collectors.toList());
                toAdds.forEach(i ->
                        tCityLabelInfoMapper.insert(i)
                );
            }
        }
        return true;
    }

    @Override
    @DSTransactional
    public boolean updateCityWarn(List<CityWarnReqDTO> params, String cityCode) {
        CityWarnInfoPO tCityLabelInfoPO = new CityWarnInfoPO();
        tCityLabelInfoPO.cityCode = cityCode;
        List<CityWarnInfoPO> tCityLabelInfoPOs = cityWarnInfoMapper.selectByAll(tCityLabelInfoPO);
        if (CollectionUtil.isEmpty(params)) {
            //全删
            if (CollectionUtil.isNotEmpty(tCityLabelInfoPOs)) {
                cityWarnInfoMapper.deleteBatchIds(tCityLabelInfoPOs.stream().map(i -> i.id).collect(Collectors.toList()));
            }
        } else {
            //对已有标签改造
            if (CollectionUtil.isNotEmpty(tCityLabelInfoPOs)) {
                //删除部分
                List<CityWarnInfoPO> toDeletes = tCityLabelInfoPOs.stream()
                        .filter(i -> params.stream().noneMatch(p -> p != null && p.id != null &&
                                p.id.equals(i.id))).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(toDeletes)) {
                    cityWarnInfoMapper.deleteBatchIds(toDeletes.stream().map(i -> i.id).collect(Collectors.toList()));
                }
                //新增部分
                List<CityWarnInfoPO> toAdds = params.stream().filter(i -> i != null && i.id == null)
                        .map(i -> {
                            CityWarnInfoPO copied = new CityWarnInfoPO();
                            BeanUtils.copyNotNullProperties(i, copied);
                            copied.cityCode = cityCode;
                            if (!StrUtil.isBlank(i.getDepShowDate()) && i.getDepShowDate().length() > 13) {
                                copied.depStartDate = i.getDepShowDate().substring(0, 10);
                                copied.depEndDate = i.getDepShowDate().substring(13, 23);
                            }
                            if (!StrUtil.isBlank(i.getArrShowDate()) && i.getArrShowDate().length() > 13) {
                                copied.arrStartDate = i.getArrShowDate().substring(0, 10);
                                copied.arrEndDate = i.getArrShowDate().substring(13, 23);
                            }
                            copied.id = HOStringUtil.newGUID();
                            copied.createTime = new Date();
                            copied.createUser = SecurityUtils.getUsername();
                            copied.updateTime = new Date();
                            copied.updateUser = SecurityUtils.getUsername();
                            return copied;
                        }).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(toAdds)) {
                    cityWarnInfoMapper.insertList(toAdds);
                }
                List<CityWarnInfoPO> toUpdates = params.stream().filter(i -> i != null && i.id != null)
                        .map(i -> {
                            CityWarnInfoPO copied = new CityWarnInfoPO();
                            BeanUtils.copyNotNullProperties(i, copied);
                            if (StringUtils.isNotBlank(i.getDepShowDate())) {
                                copied.depStartDate = i.getDepShowDate().substring(0, 10);
                                copied.depEndDate = i.getDepShowDate().substring(13, 23);
                            }
                            if (StringUtils.isNotBlank(i.getArrShowDate())) {
                                copied.arrStartDate = i.getArrShowDate().substring(0, 10);
                                copied.arrEndDate = i.getArrShowDate().substring(13, 23);
                            }
                            copied.cityCode = cityCode;
                            copied.id = i.id;
                            copied.updateTime = new Date();
                            copied.updateUser = SecurityUtils.getUsername();
                            return copied;
                        }).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(toUpdates)) {
                    toUpdates.forEach(i -> cityWarnInfoMapper.updateByPrimaryKey(i));
                }
            } else {
                //没有现有标签情况
                List<CityWarnInfoPO> toAdds = params.stream().map(i -> {
                    CityWarnInfoPO copied = new CityWarnInfoPO();
                    BeanUtils.copyNotNullProperties(i, copied);
                    copied.cityCode = cityCode;
                    if (StringUtils.isNotBlank(i.getDepShowDate())) {
                        copied.depStartDate = i.getDepShowDate().substring(0, 10);
                        copied.depEndDate = i.getDepShowDate().substring(13, 23);
                    }
                    if (StringUtils.isNotBlank(i.getArrShowDate())) {
                        copied.arrStartDate = i.getArrShowDate().substring(0, 10);
                        copied.arrEndDate = i.getArrShowDate().substring(13, 23);
                    }
                    copied.id = HOStringUtil.newGUID();
                    copied.createTime = new Date();
                    copied.updateTime = new Date();
                    copied.updateUser = SecurityUtils.getUsername();
                    copied.createUser = SecurityUtils.getUsername();
                    return copied;
                }).collect(Collectors.toList());
                for (CityWarnInfoPO toAdd : toAdds) {
                    cityWarnInfoMapper.insert(toAdd);
                }
            }
        }
        return true;
    }

    @Override
    public boolean deleteCityWarn(CityWarnReqDTO param, String cityCode) {
        return cityWarnInfoMapper.deleteByPrimaryKey(param.id) > 0;
    }

    @Override
    @DSTransactional
    public boolean updateDst(DstWtInfoPO param, String cityCode) {
        if (StringUtils.isNotBlank(param.id)) {
            if (CollectionUtil.isEmpty(param.dstDateInfos)) {
                //删除夏令时
                DstWtInfoPO dstWtInfoPO = new DstWtInfoPO();
                dstWtInfoPO.setId(param.getId());
                tDstWtInfoMapper.deleteByPrimaryKey(dstWtInfoPO.id);
                CityInfoPO cityInfoPO = new CityInfoPO();
                cityInfoPO.setCityCode(cityCode);
                cityInfoPO.setDstWtId("");
                cityInfoMapper.updateByPrimaryKeySelective(cityInfoPO);
            } else {
                //修改夏令时
                DstWtInfoPO dstWtInfoPO = new DstWtInfoPO();
                dstWtInfoPO.setId(param.getId());
                dstWtInfoPO.setDstOffset(param.getDstOffset());
                dstWtInfoPO.setDstMsg(JsonUtil.objectToJson(param.dstDateInfos));
                dstWtInfoPO.setLastUpdateTime(new Date());
                dstWtInfoPO.setLastUpdateUser(SecurityUtils.getUsername());
                tDstWtInfoMapper.updateByPrimaryKeySelective(dstWtInfoPO);
            }

        } else {
            //添加夏令时
            String dstId = IdUtil.simpleUUID();
            DstWtInfoPO dstWtInfoPO = new DstWtInfoPO();
            dstWtInfoPO.setId(dstId);
            dstWtInfoPO.setCreateTime(new Date());
            dstWtInfoPO.setCreateUser(SecurityUtils.getUsername());
            dstWtInfoPO.setDstMsg(JsonUtil.objectToJson(param.dstDateInfos));
            dstWtInfoPO.setDstOffset(param.getDstOffset());
            tDstWtInfoMapper.insertSelective(dstWtInfoPO);
            CityInfoPO cityInfoPO = new CityInfoPO();
            cityInfoPO.setCityCode(cityCode);
            cityInfoPO.setDstWtId(dstId);
            cityInfoMapper.updateByPrimaryKeySelective(cityInfoPO);
        }
        return true;
    }

    @Override
    public boolean switchHotStatus(String cityCode) {
        CityInfoPO cityInfoPO = cityInfoMapper.selectByPrimaryKey(cityCode);
        if (YorNEnum.Y.getStr().equalsIgnoreCase(cityInfoPO.isHotCity)) {
            cityInfoPO.isHotCity = YorNEnum.N.getStr();
        } else {
            cityInfoPO.isHotCity = YorNEnum.Y.getStr();
        }
        cityInfoMapper.updateById(cityInfoPO);
        return true;
    }

    @Override
    public boolean switchOftenStatus(String cityCode) {
        CityInfoPO cityInfoPO = new CityInfoPO();
        cityInfoPO.setCityCode(cityCode);
        cityInfoPO = cityInfoMapper.selectByPrimaryKey(cityCode);
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(cityInfoPO.getIsOftenCity(), YorNEnum.Y.getStr())) {
            cityInfoPO.setIsOftenCity(YorNEnum.N.getStr());
        } else {
            cityInfoPO.setIsOftenCity(YorNEnum.Y.getStr());
        }
        cityInfoMapper.updateByPrimaryKeySelective(cityInfoPO);
        return true;
    }

    @Override
    public boolean switchHotRegionStatus(String cityCode) {
        CityInfoPO cityInfoPO = new CityInfoPO();
        cityInfoPO.setCityCode(cityCode);
        cityInfoPO = cityInfoMapper.selectByPrimaryKey(cityCode);
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(cityInfoPO.getIsHotRegion(), YorNEnum.Y.getStr())) {
            cityInfoPO.setIsHotRegion(YorNEnum.N.getStr());
        } else {
            cityInfoPO.setIsHotRegion(YorNEnum.Y.getStr());
        }
        cityInfoMapper.updateByPrimaryKeySelective(cityInfoPO);
        return true;
    }

    @Override
    public List<CityInfoPO> selectAll(String status) {
        List<CityInfoPO> cityInfoPOList = cityInfoMapper.selectByAll(new CityInfoPO());
        //只返回启用状态的城市
        return cityInfoPOList.stream().filter(cityInfoPO -> status.equals(cityInfoPO.getStatus())).collect(Collectors.toList());
    }

    @Override
    public Map<String, CityInfoPO> getCityMap() {
        List<CityInfoPO> cityInfoList = cityInfoMapper.selectByAll(new CityInfoPO());
        Map<String, CityInfoPO> cityMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(cityInfoList)) {
            return cityMap;
        }
        cityInfoList.forEach(cityInfo -> cityMap.put(cityInfo.cityCode, cityInfo));
        return cityMap;
    }
}
