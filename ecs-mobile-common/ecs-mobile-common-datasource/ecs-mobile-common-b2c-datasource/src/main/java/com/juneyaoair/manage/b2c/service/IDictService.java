package com.juneyaoair.manage.b2c.service;

import com.juneyaoair.ecs.manage.dto.notice.DICTValue;

import java.util.List;

/**
 * @ClassName IDictService
 * @Description
 * <AUTHOR>
 * @Date 2023/12/21 13:12
 * @Version 1.0
 */
public interface IDictService {

    List<DICTValue> findDICTValueList(DICTValue dv);

    List<DICTValue> getChannels(DICTValue dv);

    List<DICTValue> getDICTValue(DICTValue dictValue);

}
