package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.ecs.manage.dto.activity.common.ActivityAirLine;
import com.juneyaoair.manage.b2c.entity.activity.ActivityAirlinePO;
import com.juneyaoair.manage.b2c.mapper.ActivityAirlineMapper;
import com.juneyaoair.manage.b2c.service.IActivityAirlineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【T_ACTIVITY_AIRLINE】的数据库操作Service实现
 * @createDate 2023-11-17 12:17:44
 */
@Service
public class ActivityAirlineServiceImpl extends ServiceImpl<ActivityAirlineMapper, ActivityAirlinePO>
        implements IActivityAirlineService {

    @Autowired
    private ActivityAirlineMapper activityAirlineMapper;


    @Override
    public List<ActivityAirlinePO> toGainAllById(ActivityAirlinePO activityAirlinePO) {
        return activityAirlineMapper.searchAllById(activityAirlinePO);
    }

    @Override
    public boolean removeById(ActivityAirLine activityAirlinePO) {
        int result = activityAirlineMapper.delByChildInfoId(activityAirlinePO);
        return result > 0;
    }
}




