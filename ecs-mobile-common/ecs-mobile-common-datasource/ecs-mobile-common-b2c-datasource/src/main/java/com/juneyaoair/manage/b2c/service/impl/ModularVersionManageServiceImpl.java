package com.juneyaoair.manage.b2c.service.impl;

import com.juneyaoair.ecs.manage.dto.datadict.ModularVersion;
import com.juneyaoair.manage.b2c.mapper.WxModularVersionManageMapper;
import com.juneyaoair.manage.b2c.service.IModularVersionManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class ModularVersionManageServiceImpl implements IModularVersionManageService {
    
    @Autowired
    private WxModularVersionManageMapper modularVersionManageMapper;

    @Override
    public int deleteModularVersion(ModularVersion modularVersion) {
        return modularVersionManageMapper.deleteModularVersion(modularVersion);
    }

    @Override
    public int addModularVersion(ModularVersion modularVersion) {
        modularVersion.setId(UUID.randomUUID().toString());
        return modularVersionManageMapper.addModular(modularVersion);
    }
} 