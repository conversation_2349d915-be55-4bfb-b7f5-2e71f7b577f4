package com.juneyaoair.manage.b2c.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.juneyaoair.ecs.manage.dto.airport.AirportInfoDTO;
import com.juneyaoair.ecs.manage.dto.airport.AirportLabelInfoDTO;
import com.juneyaoair.ecs.manage.dto.airport.AirportWarnDTO;
import com.juneyaoair.ecs.manage.dto.airport.ParamAirportPage;
import com.juneyaoair.ecs.manage.enums.YorNEnum;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.manage.b2c.entity.AirportInfoPO;
import com.juneyaoair.manage.b2c.entity.AirportJoinCityPO;
import com.juneyaoair.manage.b2c.entity.AirportJoinLabelWarnPO;
import com.juneyaoair.manage.b2c.entity.AirportLabelInfoPO;
import com.juneyaoair.manage.b2c.entity.AirportWarnInfoPO;
import com.juneyaoair.manage.b2c.entity.CityInfoPO;
import com.juneyaoair.manage.b2c.mapper.TCityInfoMapper;
import com.juneyaoair.manage.b2c.service.IAirportService;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.manage.b2c.mapper.TAirportInfoMapper;
import com.juneyaoair.manage.b2c.mapper.TAirportLabelInfoMapper;
import com.juneyaoair.manage.b2c.mapper.TAirportWarnInfoMapper;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AirportServiceImpl extends ServiceImpl<TAirportInfoMapper, AirportInfoPO> implements IAirportService {

    @Resource
    private TAirportInfoMapper airportInfoMapper;
    @Resource
    private TAirportLabelInfoMapper airportLabelMapper;
    @Resource
    private TAirportWarnInfoMapper airportWarnMapper;
    @Resource
    private TCityInfoMapper cityInfoMapper;

    @Override
    @DSTransactional
    public PageInfo<AirportInfoDTO> searchPage(PageDomain pageDomain, ParamAirportPage param) {
        LambdaQueryWrapper<AirportInfoPO> queryChainWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getAirportCode())) {
            queryChainWrapper.eq(AirportInfoPO::getAirportCode, param.getAirportCode());
        }
        if (StringUtils.isNotBlank(param.getCityCode())) {
            queryChainWrapper.eq(AirportInfoPO::getCityCode, param.getCityCode());
        }
        if (StringUtils.isNotBlank(param.getStatus())) {
            queryChainWrapper.eq(AirportInfoPO::getStatus, param.getStatus());
        }
        if (StringUtils.isNotBlank(param.getAirportName())) {
            queryChainWrapper.like(AirportInfoPO::getAirportName, param.getAirportName());
        }
        Page<AirportInfoPO> records = (Page<AirportInfoPO>) airportInfoMapper.selectList(queryChainWrapper);
        if (CollectionUtil.isEmpty(records)) {
            return records.toPageInfo(i -> new AirportInfoDTO());
        }
        List<String> airportCodes = records.stream().map(AirportInfoPO::getAirportCode).collect(Collectors.toList());
        List<AirportLabelInfoPO> airportLabelInfoPOS = airportLabelMapper.selectByAirportCodeIn(airportCodes);
        AirportWarnInfoPO airportWarnInfoPO = new AirportWarnInfoPO();
        airportWarnInfoPO.airportCode = param.getAirportCode();
        airportWarnInfoPO.deleteFlat = YorNEnum.N.getStr();
        List<AirportWarnInfoPO> airportWarnInfoPOS = airportWarnMapper.selectByAll(airportWarnInfoPO);
        return records.toPageInfo(i -> {
            AirportInfoDTO airportInfoDTO = new AirportInfoDTO();
            BeanUtil.copyProperties(i, airportInfoDTO);
            //机场对应标签信息
            airportInfoDTO.listAirportLabel = Optional.ofNullable(airportLabelInfoPOS).map(
                    it -> it.stream().filter(label -> label != null && i.airportCode.equalsIgnoreCase(label.airportCode)).map(label -> {
                        AirportLabelInfoDTO airportLabelInfoDTO = new AirportLabelInfoDTO();
                        BeanUtil.copyProperties(label, airportLabelInfoDTO);
                        return airportLabelInfoDTO;
                    }).collect(Collectors.toList())
            ).orElse(null);
            //机场对应重要告示
            airportInfoDTO.listAirportWarn = Optional.ofNullable(airportWarnInfoPOS).map(
                    it -> it.stream().filter(warn ->
                            warn != null && i.airportCode.equalsIgnoreCase(warn.airportCode)
                    ).map(warnInfoPO -> {
                        AirportWarnDTO warnDTO = new AirportWarnDTO();
                        BeanUtil.copyProperties(warnInfoPO, warnDTO);
                        warnDTO.setAirportWarnId(warnInfoPO.getId());
                        warnDTO.setDepArrFlag(warnInfoPO.getDepArrFlat());
                        warnDTO.setSortOrder(String.valueOf(warnInfoPO.getSortOrder()));
                        warnDTO.setDepWarnTitle(warnInfoPO.getWarnTitle());
                        warnDTO.setDepWarnContent(warnInfoPO.getWarnContent());
                        if (null != warnInfoPO.getStartDate() && null != warnInfoPO.getEndDate()) {
                            String s = DateUtil.convertDate2Str(warnInfoPO.getStartDate(), "yyyy-MM-dd");
                            String s1 = DateUtil.convertDate2Str(warnInfoPO.getEndDate(), "yyyy-MM-dd");
                            String concat = s.concat(" - ").concat(s1);
                            warnDTO.setDepShowDate(concat);
                        }
                        return warnDTO;
                    }).collect(Collectors.toList())
            ).orElse(null);
            return airportInfoDTO;
        });
    }

    @Override
    public boolean add(AirportInfoPO param) {
        param.setAirportPinyin(HOStringUtil.capitalizeFirstLetter(param.getAirportPinyin()));
        param.setPinyinAbb(HOStringUtil.capitalizeFirstLetter(param.getPinyinAbb()));
        return airportInfoMapper.insertSelective(param) > 0;
    }

    @Override
    public boolean delete(String airportCode) {
        return airportInfoMapper.deleteByPrimaryKey(airportCode) > 0;
    }

    @Override
    public boolean update(AirportInfoPO param) {
        param.setAirportPinyin(HOStringUtil.capitalizeFirstLetter(param.getAirportPinyin()));
        param.setPinyinAbb(HOStringUtil.capitalizeFirstLetter(param.getPinyinAbb()));
        return airportInfoMapper.updateByPrimaryKeySelective(param) > 0;
    }

    @Override
    public boolean setStatus(AirportInfoPO param) {
        AirportInfoPO airportInfoPO = new AirportInfoPO();
        airportInfoPO.setAirportCode(param.getAirportCode());
        airportInfoPO.setStatus(param.getStatus());
        return airportInfoMapper.updateByPrimaryKeySelective(airportInfoPO) > 0;
    }

    @Override
    @DSTransactional
    public boolean updateAirportLabel(List<AirportLabelInfoDTO> params, String airportCode) {
        List<AirportLabelInfoPO> existListLabel = airportLabelMapper.selectByAirportCodeIn(Collections.singletonList(airportCode));
        if (CollectionUtil.isNotEmpty(existListLabel)) {
            //删除params里没有的标签
            for (int i = existListLabel.size() - 1; i >= 0; i--) {
                AirportLabelInfoPO existPO = existListLabel.get(i);
                //删除
                if (params.stream().noneMatch(it -> it.getAirportLabelId() != null && it.getAirportLabelId() == existPO.getAirportLabelId().intValue())) {
                    airportLabelMapper.deleteByPrimaryKey(existPO.airportLabelId);
                }
            }
        }
        for (AirportLabelInfoDTO airportLabelInfoDTO : params) {
            if (null != airportLabelInfoDTO.getAirportLabelId()) {
                //修改
                AirportLabelInfoPO airportLabelInfoPO = new AirportLabelInfoPO();
                airportLabelInfoPO.setAirportLabelId((long) airportLabelInfoDTO.getAirportLabelId());
                airportLabelInfoPO.setAirportCode(airportCode);
                airportLabelInfoPO.setAirportLabelName(airportLabelInfoDTO.getAirportLabelName());
                airportLabelInfoPO.setAirportLabelUrl(airportLabelInfoDTO.getAirportLabelUrl());
                airportLabelInfoPO.setUpdatetime(new Date());
                airportLabelInfoPO.setUpdateUser(SecurityUtils.getUsername());
                airportLabelInfoPO.setLabelIntroduce(airportLabelInfoDTO.getLabelIntroduce());
                airportLabelMapper.updateByPrimaryKeySelective(airportLabelInfoPO);
            } else {
                //添加
                AirportLabelInfoPO airportLabelInfoPO = new AirportLabelInfoPO();
                airportLabelInfoPO.setAirportCode(airportCode);
                airportLabelInfoPO.setAirportLabelName(airportLabelInfoDTO.getAirportLabelName());
                airportLabelInfoPO.setAirportLabelUrl(airportLabelInfoDTO.getAirportLabelUrl());
                airportLabelInfoPO.setUpdatetime(new Date());
                airportLabelInfoPO.setUpdateUser(SecurityUtils.getUsername());
                airportLabelInfoPO.setLabelIntroduce(airportLabelInfoDTO.getLabelIntroduce());
                airportLabelInfoPO.setCreatetime(new Date());
                airportLabelInfoPO.setCreateUser(SecurityUtils.getUsername());
                airportLabelMapper.insert(airportLabelInfoPO);
            }
        }

        return true;
    }

    @Override
    public Map<String, List<AirportInfoPO>> searchAirport() {
        List<AirportInfoPO> airportInfoPOS = airportInfoMapper.selectByAll(null);
        return Optional.ofNullable(airportInfoPOS)
                .map(i -> i.stream().collect(Collectors.groupingBy(AirportInfoPO::getAirportCode)))
                .orElse(null);
    }

    @Override
    @DSTransactional
    public boolean updateCityWarn(List<AirportWarnDTO> param, String airportCode) {
        AirportWarnInfoPO airportWarnInfoPO = new AirportWarnInfoPO();
        airportWarnInfoPO.setAirportCode(airportCode);
        airportWarnInfoPO.setDeleteFlat(YorNEnum.N.getStr());
        String depStartDate = "";
        String depEndDate = "";
        List<AirportWarnInfoPO> select = airportWarnMapper.selectByAll(airportWarnInfoPO);
        for (int i = select.size() - 1; i >= 0; i--) {
            boolean flag = true;
            AirportWarnInfoPO existPO = select.get(i);
            for (AirportWarnDTO paramDTO : param) {
                if (paramDTO.getAirportWarnId() != null && paramDTO.getAirportWarnId().equals(existPO.getId())) {
                    flag = false;
                }
            }
            //删除
            if (flag) {
                existPO.setDeleteFlat(YorNEnum.Y.getStr());
                airportWarnMapper.updateByPrimaryKeySelective(existPO);
            }
        }
        for (AirportWarnDTO airportWarnDTO : param) {
            if (StringUtils.isNotBlank(airportWarnDTO.getDepShowDate())) {
                depStartDate = airportWarnDTO.getDepShowDate().substring(0, 10);
                if (airportWarnDTO.getDepShowDate().length() > 10) {
                    depEndDate = airportWarnDTO.getDepShowDate().substring(13, 23);
                }
            }
            if (null != airportWarnDTO.getAirportWarnId()) {
                //修改
                BeanUtils.copyNotNullProperties(param, airportWarnInfoPO);
                airportWarnInfoPO.setId(airportWarnDTO.getAirportWarnId());
                airportWarnInfoPO.setAirportCode(airportCode);
                airportWarnInfoPO.setDepArrFlat(airportWarnDTO.getDepArrFlag());
                airportWarnInfoPO.setSortOrder(Long.parseLong(StringUtils.isEmpty(airportWarnDTO.getSortOrder()) ? "0" : airportWarnDTO.getSortOrder()));
                airportWarnInfoPO.setWarnTitle(airportWarnDTO.getDepWarnTitle());
                airportWarnInfoPO.setWarnContent(airportWarnDTO.getDepWarnContent());
                airportWarnInfoPO.setStartDate(DateUtil.StringToDate(depStartDate, "yyyy-MM-dd"));
                if (StringUtils.isNotBlank(depEndDate)) {
                    airportWarnInfoPO.setEndDate(DateUtil.StringToDate(depEndDate, "yyyy-MM-dd"));
                }
                airportWarnInfoPO.setUpdatedTime(new Date());
                airportWarnInfoPO.setUpdatedBy(SecurityUtils.getUsername());
                airportWarnMapper.updateByPrimaryKeySelective(airportWarnInfoPO);
            } else {
                //添加
                BeanUtils.copyNotNullProperties(param, airportWarnInfoPO);
                airportWarnInfoPO.setId(UUID.randomUUID().toString());
                airportWarnInfoPO.setAirportCode(airportCode);
                airportWarnInfoPO.setDeleteFlat(YorNEnum.N.getStr());
                airportWarnInfoPO.setDepArrFlat(airportWarnDTO.getDepArrFlag());
                airportWarnInfoPO.setSortOrder(Long.parseLong(StringUtils.isEmpty(airportWarnDTO.getSortOrder()) ? "0" : airportWarnDTO.getSortOrder()));
                airportWarnInfoPO.setWarnTitle(airportWarnDTO.getDepWarnTitle());
                airportWarnInfoPO.setWarnContent(airportWarnDTO.getDepWarnContent());
                airportWarnInfoPO.setStartDate(DateUtil.StringToDate(depStartDate, "yyyy-MM-dd"));
                airportWarnInfoPO.setEndDate(DateUtil.StringToDate(depEndDate, "yyyy-MM-dd"));
                airportWarnInfoPO.setCreatedTime(new Date());
                airportWarnInfoPO.setCreatedBy(SecurityUtils.getUsername());
                airportWarnMapper.insertSelective(airportWarnInfoPO);
            }
        }
        return true;
    }

    @Override
    public List<AirportJoinCityPO> searchAirportJoinCity(AirportJoinCityPO airPortInfo) {
        return airportInfoMapper.searchAirportJoinCity(airPortInfo);
    }

    @Override
    public Map<String, AirportJoinCityPO> airportJoinCity() {
        Map<String, AirportJoinCityPO> airportJoinCityMap = Maps.newHashMap();
        List<AirportJoinCityPO> airportJoinCityList = airportInfoMapper.searchAirportJoinCity(null);
        if (CollectionUtils.isEmpty(airportJoinCityList)) {
            return airportJoinCityMap;
        }
        airportJoinCityList.forEach(airportJoinCity -> airportJoinCityMap.put(airportJoinCity.getAirportCode(), airportJoinCity));
        return airportJoinCityMap;
    }

    @Override
    public List<AirportJoinLabelWarnPO> searchAirportJoinLabelAndWarn(AirportInfoPO airPortInfo) {
        List<AirportInfoPO> airportInfoPOS = airportInfoMapper.selectByAll(airPortInfo);
        if (CollectionUtil.isEmpty(airportInfoPOS)) {
            return null;
        }
        List<String> cityCodes = airportInfoPOS.stream().map(i -> i.cityCode).collect(Collectors.toList());
        Map<String, CityInfoPO> cityMap = cityInfoMapper.selectBatchIds(cityCodes).stream().collect(Collectors.toMap(i -> i.cityCode, i -> i));
        Map<String, List<AirportLabelInfoPO>> labelMap = airportLabelMapper.selectList(null).stream().filter(i ->
                        i != null && StringUtils.isNotEmpty(i.airportCode))
                .collect(Collectors.groupingBy(
                        i -> i.airportCode, Collectors.toList()));
        Map<String, List<AirportWarnInfoPO>> warnMap = airportWarnMapper.selectByAll(null).stream().filter(i ->
                i != null && StringUtils.isNotEmpty(i.airportCode)).collect(Collectors.groupingBy(
                i -> i.airportCode, Collectors.toList()));
        return airportInfoPOS.stream().map(i -> {
            AirportJoinLabelWarnPO apiAirPortInfoDto = new AirportJoinLabelWarnPO();
            BeanUtils.copyNotNullProperties(i, apiAirPortInfoDto);
            apiAirPortInfoDto.setCityInfoPO(cityMap.get(i.cityCode));
            apiAirPortInfoDto.setLabelList(labelMap.get(i.airportCode));
            apiAirPortInfoDto.setWarnList(warnMap.get(i.airportCode));
            apiAirPortInfoDto.setPinyinAbb(HOStringUtil.capitalizeFirstLetter(i.getPinyinAbb()));
            return apiAirPortInfoDto;
        }).collect(Collectors.toList());
    }
}
