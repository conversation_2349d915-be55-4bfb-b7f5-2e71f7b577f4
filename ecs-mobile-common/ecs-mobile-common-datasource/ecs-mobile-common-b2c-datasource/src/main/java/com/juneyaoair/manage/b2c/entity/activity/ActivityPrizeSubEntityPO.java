package com.juneyaoair.manage.b2c.entity.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.juneyaoair.ecs.manage.dto.activity.common.DbBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.persistence.Id;

/**
 * <AUTHOR>
 * @date ：Created in 2024-07-09 16:32
 * @description： 子奖品信息表
 * @modified By：
 * @version: $
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "T_ACTIVITY_PRIZE_SUB_ENTITY")
public class ActivityPrizeSubEntityPO extends DbBase {

    @Id
    @ApiModelProperty(value = "主键ID")
    private String prizeSubEntityId;

    @ApiModelProperty(value = "奖品发放编码")
    private String subPrizeCode;

    @ApiModelProperty(value = "奖品编码")
    private String prizeCode;

    @ApiModelProperty(value = "奖品类型 参照：PRIZE_TYPE_ENUM")
    private String subPrizeType;

    @ApiModelProperty(value = "子奖品名称")
    private String subPrizeName;

    @ApiModelProperty(value = "子奖品名称发放数量")
    private Integer subPrizeAmount;

    @ApiModelProperty(value = "状态 Y:有效 N:无效 D:已删除")
    private String status;

}
