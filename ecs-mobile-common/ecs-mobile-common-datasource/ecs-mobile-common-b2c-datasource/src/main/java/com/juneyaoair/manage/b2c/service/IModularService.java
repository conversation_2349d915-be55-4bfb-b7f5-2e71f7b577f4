package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.ecs.manage.dto.modular.ModularSyncDTO;
import com.juneyaoair.manage.b2c.entity.ModularPO;
import com.juneyaoair.ecs.manage.dto.modular.ModularDTO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
public interface IModularService extends IService<ModularPO> {
    /**
     * 查询服务信息
     * @return
     */
    List<ModularDTO> selectModularList(ModularPO modularPO);

    /**
     * 根据条件查询服务名称
     * @param modularPO
     * @return
     */
    List<ModularPO> queryName(ModularPO modularPO);

    /**
     * 初始化新版本号
     */
    String initNewVersion(String oldVersion, String newVersion);

    /**
     * 插入模块版本信息
     */
    void insertModularVersion(ModularPO modularPO);

    /**
     * 添加模块版本
     */
    void addModularVersion(ModularPO modularPO, String versionType, String[] versions);

    /**
     * 新增模块（包含版本信息）
     */
    boolean saveModularWithVersion(ModularPO modularPO);

    /**
     * 更新模块（包含版本信息）
     */
    boolean updateModularWithVersion(ModularPO modularPO);

    /**
     * 同步版本号（包含版本信息）
     */
    boolean synchronousWithVersion(ModularSyncDTO modularPO);

    /**
     * 删除模块（包含版本信息）
     */
    boolean deleteModularWithVersion(ModularPO modularPO);

    /**
     * 查询单个模块详情
     * @param id 模块ID
     * @return 模块详情
     */
    ModularDTO queryModifyModular(String id);
}
