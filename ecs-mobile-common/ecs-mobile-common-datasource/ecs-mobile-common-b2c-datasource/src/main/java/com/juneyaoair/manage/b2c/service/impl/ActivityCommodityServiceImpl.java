package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.ActivityCommodityPO;
import com.juneyaoair.manage.b2c.mapper.ActivityCommodityMapper;
import com.juneyaoair.manage.b2c.service.IActivityCommodityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_ACTIVITY_COMMODITY】的数据库操作Service实现
* @createDate 2023-12-08 15:20:53
*/
@Service
public class ActivityCommodityServiceImpl extends ServiceImpl<ActivityCommodityMapper, ActivityCommodityPO>
    implements IActivityCommodityService {

    @Autowired
    private ActivityCommodityMapper activityCommodityMapper;
    @Override
    public boolean removeById(ActivityCommodityPO activityCommodityPO) {
        return activityCommodityMapper.delByChildInfoId(activityCommodityPO) > 0 ;
    }

    @Override
    public List<ActivityCommodityPO> toGainAllById(ActivityCommodityPO activityCommodityPO) {
        return activityCommodityMapper.searchAllById(activityCommodityPO);
    }
}




