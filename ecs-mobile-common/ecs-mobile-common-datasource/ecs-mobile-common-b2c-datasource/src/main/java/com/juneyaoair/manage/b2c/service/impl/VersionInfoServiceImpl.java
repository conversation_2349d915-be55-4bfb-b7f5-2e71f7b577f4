package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.version.VersionInfoControlPO;
import com.juneyaoair.manage.b2c.service.IVersionInfoService;
import com.juneyaoair.manage.b2c.mapper.VersionInfoMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【T_VERSION_INFO】的数据库操作Service实现
* @createDate 2024-03-15 09:41:05
*/
@Service
public class VersionInfoServiceImpl extends ServiceImpl<VersionInfoMapper, VersionInfoControlPO>
    implements IVersionInfoService {

}




