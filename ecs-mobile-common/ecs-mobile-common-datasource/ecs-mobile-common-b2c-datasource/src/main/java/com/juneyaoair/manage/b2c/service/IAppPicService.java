package com.juneyaoair.manage.b2c.service;

import com.juneyaoair.ecs.manage.dto.activity.response.AppPicture;
import com.juneyaoair.manage.b2c.entity.AppPicPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* @description 针对表【TP_APP_PIC】的数据库操作Service
*/
public interface IAppPicService extends IService<AppPicPO> {
    List<AppPicture> toGainRecordsByCondition(AppPicture appPicture);

}
