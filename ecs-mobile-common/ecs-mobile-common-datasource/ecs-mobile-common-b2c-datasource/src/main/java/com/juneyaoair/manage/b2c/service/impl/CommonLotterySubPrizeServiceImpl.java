package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.CommonLotterySubPrizePO;
import com.juneyaoair.manage.b2c.entity.activity.CommonLotterySubPrizeUpPO;
import com.juneyaoair.manage.b2c.service.CommonLotterySubPrizeService;
import com.juneyaoair.manage.b2c.mapper.CommonLotterySubPrizeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_PRIZE_SUB_ENTITY】的数据库操作Service实现
* @createDate 2023-05-24 10:11:01
*/
@Service
public class CommonLotterySubPrizeServiceImpl extends ServiceImpl<CommonLotterySubPrizeMapper, CommonLotterySubPrizePO>
    implements CommonLotterySubPrizeService {

    @Autowired
    private CommonLotterySubPrizeMapper commonLotterySubPrizeMapper;

    @Override
    public List<CommonLotterySubPrizePO> toGainAllSubPrizeRecords(CommonLotterySubPrizePO commonLotterySubPrizePO) {
        return commonLotterySubPrizeMapper.toGainAllSubPrizeRecords(commonLotterySubPrizePO);
    }

    @Override
    public int toDeleteSubPrizeRecords(CommonLotterySubPrizePO commonLotterySubPrizePO) {
        return commonLotterySubPrizeMapper.toDeleteSubPrizes(commonLotterySubPrizePO);
    }

    @Override
    public int toUpdateSubPrizeRecords(CommonLotterySubPrizeUpPO commonLotterySubPrizeUpPO) {
        return commonLotterySubPrizeMapper.updateByPrimaryKeySelective(commonLotterySubPrizeUpPO);
    }
}




