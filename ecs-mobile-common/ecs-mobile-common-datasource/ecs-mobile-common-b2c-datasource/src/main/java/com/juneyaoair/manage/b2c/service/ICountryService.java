package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.country.CountryDTO;
import com.juneyaoair.manage.b2c.entity.CountryJoinRegionPO;
import com.juneyaoair.manage.b2c.entity.CountryJsonInfo;
import com.juneyaoair.manage.b2c.entity.CountryPO;
import com.juneyaoair.manage.b2c.model.RegionCountryJoin;
import com.ruoyi.common.core.web.page.PageDomain;

import java.util.List;

public interface ICountryService extends IService<CountryPO> {
    PageResult<CountryDTO> searchByAll(CountryPO countryInfo, PageDomain pageDomain);

    boolean deleteCountry(CountryPO countryPO);

    boolean add(CountryPO countryPO);

    boolean update(CountryPO countryPO);

    List<CountryJoinRegionPO> selectJoinRegoinByAll(CountryPO countryPO);
    List<CountryJoinRegionPO> selectByRegionCodeOrderBySequence();

    /**
     * 查询所有国家信息(按洲分组)
     * @return
     */
    List<RegionCountryJoin> searchRegionCountry();

    /**
     * 查询国家信息
     */
    List<CountryJsonInfo> getCountryJsonInfo();

}
