package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.ActivityCouponsPO;
import com.juneyaoair.manage.b2c.service.IActivityCouponsService;
import com.juneyaoair.manage.b2c.mapper.ActivityCouponsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【T_ACTIVITY_COUPONS(统一优惠券活动表)】的数据库操作Service实现
* @createDate 2023-12-11 10:40:44
*/
@Service
public class ActivityCouponsServiceImpl extends ServiceImpl<ActivityCouponsMapper, ActivityCouponsPO>
    implements IActivityCouponsService {

    @Autowired
    private ActivityCouponsMapper activityCouponsMapper;

    @Override
    public boolean insertAll(ActivityCouponsPO activityCouponsPO) {
        return activityCouponsMapper.insertAll(activityCouponsPO) > 0;
    }
}




