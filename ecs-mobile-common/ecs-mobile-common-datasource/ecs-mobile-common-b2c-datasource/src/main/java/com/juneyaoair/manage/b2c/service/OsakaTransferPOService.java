package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.ecs.manage.dto.activity.request.osaka.OsakaCouponClaimQueryRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.osaka.OsakaCouponClaimResponse;
import com.juneyaoair.manage.b2c.entity.activity.OsakaTransferPO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【T_OSAKA_TRANSFER(大阪接送机流水表)】的数据库操作Service
 * @createDate 2025-06-23 14:27:53
 */
public interface OsakaTransferPOService extends IService<OsakaTransferPO> {

    /**
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.activity.response.osaka.OsakaCouponClaimResponse>
     * <AUTHOR>
     * @Description 大阪接送机券码领取查询
     * @Date 14:41 2025/6/23
     **/
    List<OsakaCouponClaimResponse> doCouponClaimListQuery(OsakaCouponClaimQueryRequest osakaCouponClaimQueryRequest);

}
