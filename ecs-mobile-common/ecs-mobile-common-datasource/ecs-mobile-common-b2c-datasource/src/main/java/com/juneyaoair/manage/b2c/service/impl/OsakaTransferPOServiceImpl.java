package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.ecs.manage.dto.activity.request.osaka.OsakaCouponClaimQueryRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.osaka.OsakaCouponClaimResponse;
import com.juneyaoair.manage.b2c.entity.activity.OsakaTransferPO;
import com.juneyaoair.manage.b2c.mapper.OsakaTransferPOMapper;
import com.juneyaoair.manage.b2c.service.OsakaTransferPOService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【T_OSAKA_TRANSFER(大阪接送机流水表)】的数据库操作Service实现
 * @createDate 2025-06-23 14:27:53
 */
@Service
@SuppressWarnings("all")
public class OsakaTransferPOServiceImpl extends ServiceImpl<OsakaTransferPOMapper, OsakaTransferPO>
        implements OsakaTransferPOService {

    @Resource
    private OsakaTransferPOMapper osakaTransferPOMapper;

    @Override
    public List<OsakaCouponClaimResponse> doCouponClaimListQuery(OsakaCouponClaimQueryRequest osakaCouponClaimQueryRequest) {
        return osakaTransferPOMapper.searchAllByCondition(osakaCouponClaimQueryRequest);
    }
}




