package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.ecs.manage.dto.region.RegionReqDTO;
import com.juneyaoair.manage.b2c.entity.RegionPO;

import java.util.List;

public interface IRegionService extends IService<RegionPO> {
    List<RegionPO> searchList(RegionReqDTO regionPO);

    int add(RegionReqDTO regionPO);

    int update(RegionReqDTO regionPO);

    int delete(String regionCode);
}
