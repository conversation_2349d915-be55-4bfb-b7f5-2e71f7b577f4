package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteActivityPO;
import com.juneyaoair.manage.b2c.service.IDiscountRouteActivityServices;
import com.juneyaoair.manage.b2c.mapper.DiscountsRouteActivityMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DISCOUNTS_ROUTE_ACTIVITY(特惠航线配置)】的数据库操作Service实现
* @createDate 2023-11-17 15:53:17
*/
@Service
public class DiscountsRouteActivityServicesImpl extends ServiceImpl<DiscountsRouteActivityMapper, DiscountsRouteActivityPO>
    implements IDiscountRouteActivityServices {

}




