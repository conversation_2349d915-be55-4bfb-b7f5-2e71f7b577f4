package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.CommonLotteryPrizePO;
import com.juneyaoair.manage.b2c.service.CommonLotteryPrizeService;
import com.juneyaoair.manage.b2c.mapper.CommonLotteryPrizeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_PRIZE_ENTITY】的数据库操作Service实现
* @createDate 2023-05-24 08:59:46
*/
@Service
public class CommonLotteryPrizeServiceImpl extends ServiceImpl<CommonLotteryPrizeMapper, CommonLotteryPrizePO>
    implements CommonLotteryPrizeService {
    @Autowired
    private CommonLotteryPrizeMapper commonLotteryPrizeMapper;
    @Override
    public List<CommonLotteryPrizePO> toGainAllPrizeRecords(CommonLotteryPrizePO commonLotteryPrizePO) {
        return commonLotteryPrizeMapper.toGainAllPrizeRecords(commonLotteryPrizePO);
    }

    @Override
    public int toUpdatePrizeInfo(CommonLotteryPrizePO commonLotteryPrizePO) {
        return commonLotteryPrizeMapper.updateByPrimaryKeySelective(commonLotteryPrizePO);
    }

    @Override
    public int toUpdatePrizeRecord(CommonLotteryPrizePO param) {
        return commonLotteryPrizeMapper.toUpdatePrizeRecord(param);
    }
}




