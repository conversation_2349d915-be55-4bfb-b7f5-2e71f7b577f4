package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.ChannelPO;
import com.juneyaoair.manage.b2c.entity.MessagePO;
import com.juneyaoair.manage.b2c.entity.ServicePO;
import com.juneyaoair.manage.b2c.mapper.MessageMapper;
import com.juneyaoair.manage.b2c.service.IChannelService;
import com.juneyaoair.manage.b2c.service.IMessageService;
import com.juneyaoair.manage.b2c.service.IServiceService;
import com.ruoyi.common.core.exception.ServiceException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Service
public class MessageServiceImpl extends ServiceImpl<MessageMapper, MessagePO> implements IMessageService {
    @Autowired
    private MessageMapper messageMapper;
    @Autowired
    private IChannelService channelService;
    @Autowired
    private IServiceService serviceService;

    /**
     * 查询消息列表
     *
     * @param messagePO
     * @return
     */
    @Override
    public List<MessagePO> selectMessageList(MessagePO messagePO) {
        return messageMapper.selectMessageList(messagePO);
    }


    /**
     * 保存公告信息
     *
     * @param messagePO
     * @return
     */
    @DSTransactional
    @Override
    public boolean saveMessage(MessagePO messagePO) {
        messageMapper.insert(messagePO);
        channelService.saveBatch(messagePO.getChannelPOList());
        if (CollectionUtils.isNotEmpty(messagePO.getServicePOList())) {
            serviceService.saveBatch(messagePO.getServicePOList());
        }
        return true;
    }


    /**
     * 更新公告信息
     *
     * @param messagePO
     * @return
     */
    @DSTransactional
    @Override
    public boolean updateMessageByMessageId(MessagePO messagePO) {
        if (StringUtils.isBlank(messagePO.getMessageid())) {
            throw new ServiceException("缺少Messageid");
        }
        //删除原有的渠道关联信息
        QueryWrapper<ChannelPO> channelPOQueryWrapper = new QueryWrapper<>();
        channelPOQueryWrapper.eq("PRO_ID", messagePO.getMessageid());
        channelService.remove(channelPOQueryWrapper);
        //删除原有的服务关联信息
        QueryWrapper<ServicePO> servicePOQueryWrapper = new QueryWrapper<>();
        servicePOQueryWrapper.eq("PRO_ID", messagePO.getMessageid());
        serviceService.remove(servicePOQueryWrapper);
        //更新公告内容
        UpdateWrapper<MessagePO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(MessagePO::getMessageid, messagePO.getMessageid());
        messageMapper.update(messagePO, updateWrapper);
        //重新保存渠道信息
        if (CollectionUtils.isNotEmpty(messagePO.getChannelPOList())) {
            channelService.saveBatch(messagePO.getChannelPOList());
        }
        //重新保存服务信息
        if (CollectionUtils.isNotEmpty(messagePO.getServicePOList())) {
            serviceService.saveBatch(messagePO.getServicePOList());
        }
        return true;
    }
}
