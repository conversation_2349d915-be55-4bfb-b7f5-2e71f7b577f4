package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.juneyaoair.ecs.manage.dto.airport.AirportInfoDTO;
import com.juneyaoair.ecs.manage.dto.airport.AirportLabelInfoDTO;
import com.juneyaoair.ecs.manage.dto.airport.AirportWarnDTO;
import com.juneyaoair.ecs.manage.dto.airport.ParamAirportPage;
import com.juneyaoair.manage.b2c.entity.AirportJoinCityPO;
import com.juneyaoair.manage.b2c.entity.AirportInfoPO;
import com.juneyaoair.manage.b2c.entity.AirportJoinLabelWarnPO;
import com.ruoyi.common.core.web.page.PageDomain;

import java.util.List;
import java.util.Map;

public interface IAirportService extends IService<AirportInfoPO> {
    PageInfo<AirportInfoDTO> searchPage(PageDomain pageDomain, ParamAirportPage param);

    boolean add(AirportInfoPO param);

    boolean delete(String param);

    boolean update(AirportInfoPO param);

    boolean setStatus(AirportInfoPO param);

    boolean updateAirportLabel(List<AirportLabelInfoDTO> param, String airportCode);

    Map<String, List<AirportInfoPO>> searchAirport();

    boolean updateCityWarn(List<AirportWarnDTO> param, String airportCode);

    List<AirportJoinCityPO> searchAirportJoinCity(AirportJoinCityPO airPortInfo);

    /**
     * 机场信息 key:机场三字码 value:机场信息
     * @return
     */
    Map<String, AirportJoinCityPO> airportJoinCity();

    List<AirportJoinLabelWarnPO> searchAirportJoinLabelAndWarn(AirportInfoPO airPortInfo);
}
