package com.juneyaoair.manage.b2c.service;

import com.juneyaoair.ecs.manage.dto.activity.common.ActivityAirLine;
import com.juneyaoair.manage.b2c.entity.activity.ActivityAirlinePO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_ACTIVITY_AIRLINE】的数据库操作Service
* @createDate 2023-11-17 12:17:44
*/
public interface IActivityAirlineService extends IService<ActivityAirlinePO> {
    List<ActivityAirlinePO> toGainAllById(ActivityAirlinePO activityAirlinePO);

    boolean removeById(ActivityAirLine  activityAirlinePO);

}
