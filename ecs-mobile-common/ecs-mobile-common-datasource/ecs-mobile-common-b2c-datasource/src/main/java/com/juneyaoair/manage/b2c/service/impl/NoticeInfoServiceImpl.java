package com.juneyaoair.manage.b2c.service.impl;

import com.juneyaoair.ecs.manage.dto.notice.MaintainInfo;
import com.juneyaoair.ecs.manage.dto.notice.NoticeInfo;
import com.juneyaoair.ecs.manage.dto.notice.NoticeSort;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.mapper.NoticeInfoMapper;
import com.juneyaoair.manage.b2c.service.INoticeInfoService;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName NoticeInfoServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/12/21 9:17
 * @Version 1.0
 */

@Service
@Slf4j
public class NoticeInfoServiceImpl implements INoticeInfoService {

    @Resource
    private NoticeInfoMapper noticeInfoMapper;

    @Override
    public List<NoticeInfo> selectNoticeInfo(NoticeInfo noticeInfo) {
        List<NoticeInfo> noticeInfos;
        if (noticeInfo.isGetRichtext()) {
            noticeInfos = noticeInfoMapper.queryNoticeInfoRich(noticeInfo);
        } else {
            noticeInfos = noticeInfoMapper.queryNoticeInfo(noticeInfo);
        }
        try {
            if (CollectionUtils.isNotEmpty(noticeInfos) && noticeInfo.isGetRichtext()) {
                for (NoticeInfo info : noticeInfos) {
                    if (null != info.getRichText() && info.getRichText().length > 0) {
                        info.setRichContext(new String(info.getRichText(), "GBK"));
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取协议列表异常,错误信息为:", e);
        }
        return noticeInfos;
    }

    @Override
    public List<NoticeSort> selectNoticeSortById(NoticeSort noticeSort) {
        return noticeInfoMapper.selectNoticeSortById(noticeSort);
    }

    @Override
    public List<NoticeSort> selectFatherModule(NoticeSort noticeSort) {
        return noticeInfoMapper.selectFatherModule(noticeSort);
    }

    @Override
    public int addNoticeInfo(NoticeInfo noticeInfo) {
        noticeInfo.setNtInfoId(UUID.randomUUID().toString().replace("-", ""));
        noticeInfo.setNoticeId(noticeInfo.getNoticeBm());
        noticeInfo.setPerson(SecurityUtils.getUsername());
        int i = 0;
        try {
            noticeInfo.setRichText(noticeInfo.getRichContext().getBytes("GBK"));
            i = noticeInfoMapper.addNoticeInfo(noticeInfo);
        } catch (Exception e) {
            log.error("插入信息失败，错误信息为:" + e);
        }
        return i;
    }

    @Override
    public int addNoticeSort(NoticeSort noticeSort) {
        return noticeInfoMapper.addNoticeSort(noticeSort);
    }

    @Override
    public int updateNoticeSort(NoticeSort noticeSort) {
        return noticeInfoMapper.updateNoticeSort(noticeSort);
    }

    @Override
    public int updateNoticeInfo(NoticeInfo noticeInfo) {
        noticeInfo.setModifyTime(DateUtil.convertDate2Str(new Date(), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        noticeInfo.setPerson(SecurityUtils.getUsername());
        int i = 0;
        noticeInfo.setNoticeId(noticeInfo.getNoticeBm());
        try {
            noticeInfo.setRichText(noticeInfo.getRichContext().getBytes("GBK"));
            i = noticeInfoMapper.updateNoticeInfo(noticeInfo);
        } catch (Exception e) {
            log.error("更新信息失败！！" + e.getMessage());
        }
        return i;

    }

    @Override
    public int updateNoticeInfoV2(NoticeInfo noticeInfo) {
        return noticeInfoMapper.updateNoticeInfoV2(noticeInfo);
    }

    @Override
    public int deleteNoticeInfo(NoticeInfo noticeInfo) {
        return noticeInfoMapper.deleteNoticeInfo(noticeInfo);
    }

    @Override
    public int deleteNoticeSort(NoticeSort noticeSort) {
        return noticeInfoMapper.deleteNoticeSort(noticeSort);
    }

    @Override
    public int addMaintainInfo(MaintainInfo maintainInfo) {

        maintainInfo.setFileNames(validateFileName(maintainInfo.getFileNames()));

        if (StringUtils.isBlank(maintainInfo.getLanguage())) {
            throw new HoServiceException("语言不能为空");
        }
        if (StringUtils.isNotEmpty(maintainInfo.getNtPicUrl())) {
            maintainInfo.setFileNames(maintainInfo.getFileNames());
        }
        maintainInfo.setNtMaintainID(HOStringUtil.newGUID());
        maintainInfo.setPerson(SecurityUtils.getUsername());
        try {
            maintainInfo.setRichTexts(maintainInfo.getRichContext().getBytes("GBK"));
        } catch (UnsupportedEncodingException e) {
            log.error("新增条款维护列表异常:", e);
        }
        return noticeInfoMapper.addMaintainInfo(maintainInfo);
    }

    @Override
    public int deleteMaintainInfo(MaintainInfo maintainInfo) {
        return noticeInfoMapper.deleteMaintainInfo(maintainInfo);
    }

    @Override
    public int updateMaintainInfo(MaintainInfo maintainInfo) {
        maintainInfo.setPerson(SecurityUtils.getUsername());
        try {
            maintainInfo.setRichTexts(maintainInfo.getRichContext().getBytes("GBK"));
        } catch (UnsupportedEncodingException e) {
            log.error("更新条款维护列表异常:", e);
        }
        return noticeInfoMapper.updateMaintainInfo(maintainInfo);
    }

    @Override
    public List<MaintainInfo> fetchMaintainInfo(MaintainInfo maintainInfo) {
        List<MaintainInfo> maintainInfos = noticeInfoMapper.fetchMaintainInfo(maintainInfo);
        if (CollectionUtils.isNotEmpty(maintainInfos)) {
            for (MaintainInfo info : maintainInfos) {
                if (null != info.getRichTexts() && info.getRichTexts().length > 0) {
                    try {
                        info.setRichContext(new String(info.getRichTexts(), "GBK"));
                    } catch (UnsupportedEncodingException e) {
                        log.error("获取条款维护列表异常:", e);
                    }
                }
            }
        }
        return maintainInfos;
    }

    @Override
    public List<NoticeInfo> getNoticeInfoList(NoticeInfo noticeInfo) {
        return noticeInfoMapper.getNoticeInfoList(noticeInfo);
    }

    @Override
    public int addNoticeMessage(NoticeInfo noticeInfo) {
        NoticeInfo insertParamPO = new NoticeInfo();
        insertParamPO.setNtInfoId(HOStringUtil.newGUID());
        insertParamPO.setNoticeId(noticeInfo.getNoticeId());
        insertParamPO.setPerson(SecurityUtils.getUsername());
        insertParamPO.setNtInfoCode(noticeInfo.getNtInfoCode());
        insertParamPO.setCanInfoShow(noticeInfo.getCanInfoShow());
        insertParamPO.setMaintainId(noticeInfo.getMaintainId());
        insertParamPO.setCanInfoShow("Y");
        return noticeInfoMapper.addNoticeInfo(insertParamPO);
    }

    @Override
    public int deleteNoticeMessage(NoticeInfo noticeInfo) {
        NoticeInfo deletePO = new NoticeInfo();
        deletePO.setNtInfoId(noticeInfo.getNtInfoId());
        return noticeInfoMapper.deleteNoticeInfo(deletePO);
    }

    @Override
    public int updateNoticeMessage(NoticeInfo noticeInfo) {
        noticeInfo.setCanInfoShow("Y");
        NoticeInfo noticeInfoInsert = new NoticeInfo();
        BeanUtils.copyNotNullProperties(noticeInfo, noticeInfoInsert);
        noticeInfoInsert.setPerson(SecurityUtils.getUsername());
        if (null != noticeInfo.getRichContext()) {
            try {
                noticeInfoInsert.setRichText(noticeInfo.getRichContext().getBytes("GBK"));
            } catch (UnsupportedEncodingException e) {
                log.error("富文本内容转换出错:", e);
            }
        }
        return noticeInfoMapper.updateNoticeInfoV2(noticeInfoInsert);
    }

    @Override
    public List<NoticeInfo> fetchNoticeMessage(NoticeInfo noticeInfo) {
        List<NoticeInfo> resultList = new ArrayList<>();
        List<NoticeInfo> noticeInfoList = noticeInfoMapper.getNoticeInfoList(noticeInfo);
        //如果maintainid为null，需要从旧表中拿数据
        List<NoticeInfo> elemsFromOldTable = noticeInfoList.stream().filter(elem -> null == elem.getMaintainId()).collect(Collectors.toList());
        try {
            for (NoticeInfo o : elemsFromOldTable) {
                if (null != o.getRichTexts() && o.getRichTexts().length > 0) {
                    o.setRichContext(new String(o.getRichTexts(), "GBK"));
                }
            }
            //如果maintainId不为null，则详情需要从新表中查询，其他数据还是走旧的数据表
            List<NoticeInfo> detailFromNewTable = noticeInfoList.stream().filter(elem -> null != elem.getMaintainId()).collect(Collectors.toList());
            List<NoticeInfo> elemsFromNewTable = new ArrayList<>();
            for (NoticeInfo o : detailFromNewTable) {
                NoticeInfo tempElem = new NoticeInfo();
                BeanUtils.copyNotNullProperties(o, tempElem);
                tempElem.setNtInfoName(o.getNtMaintainName());
                /**
                 * 首选NtPicUrl 其次NtOtherUrl，最后保留原有值
                 */
                if (StringUtils.isNotBlank(o.getNtPicUrl())) {
                    tempElem.setNtInfoUrl(o.getNtPicUrl());
                } else if (StringUtils.isNotBlank(o.getNtOtherUrl())) {
                    tempElem.setNtInfoUrl(o.getNtOtherUrl());
                }
                tempElem.setNtInfoDescription(o.getNtMaintainDesciption());
                if (null != o.getRichTexts() && o.getRichTexts().length > 0) {
                    tempElem.setRichContext(new String(o.getRichTexts(), "GBK"));
                }
                elemsFromNewTable.add(tempElem);
            }
            resultList.addAll(elemsFromOldTable);
            resultList.addAll(elemsFromNewTable);
            //重新排序
            if (CollectionUtils.isNotEmpty(resultList)) {
                resultList.sort(Comparator.comparing(el -> Integer.parseInt(el.getNtInfoCode())));
            }
        } catch (UnsupportedEncodingException e) {
            log.error("获取条款详情失败！！" + e.getMessage());
        }
        return resultList;
    }

    private static String toCatchFileName(String oriUrl) {
        if (com.ruoyi.common.core.utils.StringUtils.isEmpty(oriUrl) || !oriUrl.trim().contains(".")) {
            return null;
        }
        try {
            URL url = new URL(oriUrl);
            String fileName = url.getFile().substring(url.getFile().lastIndexOf('/') + 1);
            String[] fileNameList = fileName.split("\\.");
            return fileNameList[0];
        } catch (MalformedURLException e) {
            return null;
        }
    }

    private String validateFileName(String fileNames) {

        String cleanedName = fileNames.replace(" ", "");

        if (cleanedName.contains("@") || cleanedName.contains("/") || cleanedName.contains("?")) {
            throw new HoServiceException("文件名不允许包含以下字符： @ 、 / 、 ?");
        }

        return cleanedName;
    }
}
