package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.service.IProvinceService;
import com.juneyaoair.manage.b2c.entity.ProvincePO;
import com.juneyaoair.manage.b2c.mapper.TProvinceMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ProvinceServiceImpl extends ServiceImpl<TProvinceMapper, ProvincePO> implements IProvinceService {

    @Resource
    TProvinceMapper provinceMapper;

    @Override
    public List<ProvincePO> searchForPage(ProvincePO province) {
        return provinceMapper.selectJoinCountry(province);
    }

    @Override
    public boolean addProvince(ProvincePO param) {
        return provinceMapper.insert(param) > 0;
    }

    @Override
    public boolean updateProvince(ProvincePO param) {
        return provinceMapper.updateByPrimaryKey(param) > 0;
    }

    @Override
    public boolean deleteProvince(ProvincePO param) {
        return provinceMapper.deleteByPrimaryKey(param.provinceId) > 0;
    }

    @Override
    public List<ProvincePO> search(ProvincePO param) {
        return provinceMapper.selectByAll(param);
    }
}
