package com.juneyaoair.manage.b2c.service;

import com.juneyaoair.manage.b2c.entity.AppVersionInfoPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【APP_VERSION_INFO】的数据库操作Service
* @createDate 2023-08-08 14:48:33
*/
public interface IAppVersionInfoService extends IService<AppVersionInfoPO> {

    List<AppVersionInfoPO> checkAppVersion(AppVersionInfoPO info);

    int addAppVersion(AppVersionInfoPO appVersionManage);

    int deleteAppVersion(AppVersionInfoPO appVersionManage);
}
