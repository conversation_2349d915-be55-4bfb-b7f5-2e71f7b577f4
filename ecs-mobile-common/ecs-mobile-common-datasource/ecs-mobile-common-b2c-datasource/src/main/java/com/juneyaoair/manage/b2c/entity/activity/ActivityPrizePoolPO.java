package com.juneyaoair.manage.b2c.entity.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.juneyaoair.ecs.manage.dto.activity.common.DbBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Id;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2024-07-09 16:32
 * @description： 活动奖池信息表（适用于奖池中奖品随机发放）
 * @modified By：
 * @version: $
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "T_ACTIVITY_PRIZE_POOL")
public class ActivityPrizePoolPO extends DbBase {

    @Id
    @ApiModelProperty(value = "奖池编码")
    private String prizePoolCode;

    @ApiModelProperty(value = "奖池名称")
    private String prizePoolName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "状态 Y:有效 N:无效 D:已删除")
    private String status;

}
