package com.juneyaoair.manage.b2c.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.country.CountryDTO;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.manage.b2c.entity.CountryJoinRegionPO;
import com.juneyaoair.manage.b2c.entity.CountryJsonInfo;
import com.juneyaoair.manage.b2c.entity.CountryPO;
import com.juneyaoair.manage.b2c.entity.RegionPO;
import com.juneyaoair.manage.b2c.mapper.TCountryMapper;
import com.juneyaoair.manage.b2c.mapper.TRegionMapper;
import com.juneyaoair.manage.b2c.model.RegionCountryJoin;
import com.juneyaoair.manage.b2c.service.ICountryService;
import com.ruoyi.common.core.web.page.PageDomain;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CountryServiceImpl extends ServiceImpl<TCountryMapper, CountryPO> implements ICountryService {

    @Resource
    private TCountryMapper countryMapper;
    @Resource
    private TRegionMapper regionMapper;


    @Override
    public PageResult<CountryDTO> searchByAll(CountryPO countryInfo, PageDomain pageDomain) {

        LambdaQueryWrapper<CountryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(countryInfo.countryCode), CountryPO::getCountryCode, countryInfo.countryCode);
        queryWrapper.like(StringUtils.isNotBlank(countryInfo.countryName), CountryPO::getCountryName, countryInfo.countryName);
        queryWrapper.eq(StringUtils.isNotBlank(countryInfo.englishName), CountryPO::getEnglishName, countryInfo.englishName);
        queryWrapper.eq(StringUtils.isNotBlank(countryInfo.currency), CountryPO::getCurrency, countryInfo.currency);
        queryWrapper.eq(countryInfo.createDatetime != null, CountryPO::getCreateDatetime, countryInfo.createDatetime);
        queryWrapper.eq(countryInfo.creatorId != null, CountryPO::getCreatorId, countryInfo.creatorId);
        queryWrapper.eq(StringUtils.isNotBlank(countryInfo.countryTelCode), CountryPO::getCountryTelCode, countryInfo.countryTelCode);
        queryWrapper.eq(StringUtils.isNotBlank(countryInfo.hotCountry), CountryPO::getHotCountry, countryInfo.hotCountry);
        queryWrapper.eq(StringUtils.isNotBlank(countryInfo.regionCode), CountryPO::getRegionCode, countryInfo.regionCode);
        queryWrapper.orderByAsc(CountryPO::getSequence);

        Page<CountryPO> page = new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize());
        Page<CountryPO> countryPOPage = countryMapper.selectPage(page, queryWrapper);
        if (CollectionUtil.isEmpty(countryPOPage.getRecords())) {
            return new PageResult<>(null, 0L, pageDomain.getPageNum(), pageDomain.getPageSize());
        }
        LambdaQueryWrapper<RegionPO> regionQueryWrapper = new LambdaQueryWrapper<>();
        regionQueryWrapper.in(RegionPO::getRegionCode, countryPOPage.getRecords().stream().filter(Objects::nonNull).map(i -> i.regionCode).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        List<RegionPO> regionPOS = regionMapper.selectList(regionQueryWrapper);
        List<CountryDTO> countryDTOS = countryPOPage.getRecords().stream().map(i -> {
            CountryDTO countryDTO = new CountryDTO();
            BeanUtils.copyProperties(i, countryDTO);
            if (StringUtils.isBlank(i.regionCode)) {
                //一般都有regionCode
                return countryDTO;
            }
            countryDTO.regionName = regionPOS.stream().filter(it -> i.regionCode.equalsIgnoreCase(it.regionCode)).findFirst().map(it -> it.regionName).orElse(null);
            countryDTO.regionEName = regionPOS.stream().filter(it -> i.regionCode.equalsIgnoreCase(it.regionCode)).findFirst().map(it -> it.regionEName).orElse(null);
            return countryDTO;
        }).collect(Collectors.toList());
        return new PageResult<>(countryDTOS,page.getTotal(),(int)page.getCurrent(),(int)page.getSize());
    }

    @Override
    public boolean deleteCountry(CountryPO countryPO) {
        return countryMapper.deleteByPrimaryKey(countryPO.getCountryCode()) > 0;

    }

    @Override
    public boolean add(CountryPO countryPO) {
        return countryMapper.insertSelective(countryPO) > 0;
    }

    @Override
    public boolean update(CountryPO countryPO) {
        return countryMapper.updateByPrimaryKeySelective(countryPO) > 0;
    }

    @Override
    public List<CountryJoinRegionPO> selectByRegionCodeOrderBySequence() {
        List<CountryJoinRegionPO> list = new ArrayList<>();
        try {
            list = countryMapper.selectJoinReginByRegionCodeOrderBySequence();
            if (CollectionUtil.isNotEmpty(list)) {
                list.removeIf(i -> "CN".equalsIgnoreCase(i.getCountryCode()));
            }
        } catch (Exception e) {
            log.error("查询国家信息错误！{}", e.getMessage());
            throw e;
        }
        return list;
    }

    @Override
    public List<CountryJoinRegionPO> selectJoinRegoinByAll(CountryPO countryPO) {
        List<CountryJoinRegionPO> list = new ArrayList<>();

        try {
            list = countryMapper.selectJoinRegoinByAll(countryPO);
        } catch (Exception e) {
            log.error("查询国家信息错误！{}", e.getMessage());
            throw e;
        }
        return list;
    }

    @Override
    public List<RegionCountryJoin> searchRegionCountry() {
        return countryMapper.searchRegionCountry();
    }

    @Override
    public List<CountryJsonInfo> getCountryJsonInfo() {
        return countryMapper.selectCountryJsonInfo();
    }
}
