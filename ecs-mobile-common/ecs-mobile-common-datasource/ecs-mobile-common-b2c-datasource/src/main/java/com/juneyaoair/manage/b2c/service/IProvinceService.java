package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.manage.b2c.entity.ProvincePO;

import java.util.List;

public interface IProvinceService extends IService<ProvincePO> {
    List<ProvincePO> searchForPage(ProvincePO province);

    boolean addProvince(ProvincePO param);

    boolean updateProvince(ProvincePO param);

    boolean deleteProvince(ProvincePO param);

    List<ProvincePO> search(ProvincePO param);
}
