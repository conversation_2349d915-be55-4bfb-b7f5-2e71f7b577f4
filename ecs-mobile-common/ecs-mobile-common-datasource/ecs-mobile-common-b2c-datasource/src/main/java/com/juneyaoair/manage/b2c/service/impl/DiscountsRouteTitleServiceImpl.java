package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteTitlePO;
import com.juneyaoair.manage.b2c.service.IDiscountsRouteTitleService;
import com.juneyaoair.manage.b2c.mapper.DiscountsRouteTitleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DISCOUNTS_ROUTE_TITLE(标题)】的数据库操作Service实现
* @createDate 2023-11-17 16:01:25
*/
@Service
public class DiscountsRouteTitleServiceImpl extends ServiceImpl<DiscountsRouteTitleMapper, DiscountsRouteTitlePO>
    implements IDiscountsRouteTitleService {

    @Autowired
    private DiscountsRouteTitleMapper discountsRouteTitleMapper;


    @Override
    public List<DiscountsRouteTitlePO> toGainAllRecords(DiscountsRouteTitlePO discountsRouteTitlePO) {
        return discountsRouteTitleMapper.searchAllRecords(discountsRouteTitlePO);
    }
}




