package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.ecs.manage.dto.activity.request.passport.PassportRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.passport.PassportInformation;
import com.juneyaoair.manage.b2c.entity.activity.ActivityPrizeRecordPO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【T_ACTIVITY_PRIZE_RECORD(积分找回奖品领取表)】的数据库操作Service
 * @createDate 2025-04-02 22:00:10
 */
public interface ActivityPrizeRecordService extends IService<ActivityPrizeRecordPO> {

    /**
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.activity.response.passport.PassportInformation>
     * <AUTHOR>
     * @Description 获取护照补全领取列表
     * @Date 22:20 2025/4/2
     **/
    List<PassportInformation> toCatchActivityPrizeRecords(PassportRequest passportRequest);

}
