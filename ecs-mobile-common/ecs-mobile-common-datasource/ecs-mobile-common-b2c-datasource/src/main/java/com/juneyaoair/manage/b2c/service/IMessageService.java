package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.manage.b2c.entity.MessagePO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
public interface IMessageService extends IService<MessagePO> {
    /**
     * 查询消息列表
     * @param message
     * @return
     */
    List<MessagePO> selectMessageList(MessagePO message);

    /**
     * 保存公告信息
     * @param messagePO
     * @return
     */
    boolean saveMessage(MessagePO messagePO);

    /**
     * 更新公告信息
     * @param messagePO
     * @return
     */
    boolean updateMessageByMessageId(MessagePO messagePO);

}
