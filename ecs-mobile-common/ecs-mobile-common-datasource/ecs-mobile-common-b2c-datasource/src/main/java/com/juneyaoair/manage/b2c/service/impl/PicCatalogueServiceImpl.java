package com.juneyaoair.manage.b2c.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.juneyaoair.ecs.manage.dto.picture.DelPicCatalogueReqDTO;
import com.juneyaoair.ecs.manage.dto.picture.GetCatalogueListReqDTO;
import com.juneyaoair.ecs.manage.dto.picture.IndexInfoDTO;
import com.juneyaoair.ecs.manage.dto.picture.UpsertPicCatalogueReqDTO;
import com.juneyaoair.manage.b2c.entity.PicCataloguePO;
import com.juneyaoair.manage.b2c.mapper.PicCatalogueMapper;
import com.juneyaoair.manage.b2c.service.*;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class PicCatalogueServiceImpl implements IPicCatalogueService {

    @Autowired
    PicCatalogueMapper picCatalogueMapper;

    @Override
    public List<IndexInfoDTO> searchPage(GetCatalogueListReqDTO req) {
        List<PicCataloguePO> list = getTpPicCataloguePOS(req);
        if (list == null) return null;
        List<IndexInfoDTO> ret = list.stream().map(this::tpPicCataloguePO2IndexInfo).collect(Collectors.toList());
        getIndexList(ret);
        return ret;
    }

    private List<PicCataloguePO> getTpPicCataloguePOS(GetCatalogueListReqDTO req) {
        LambdaQueryWrapper<PicCataloguePO> queryWrapper = new LambdaQueryWrapper<>();
        if (req.curIndexId != null) {
            if (req.curIndexId <= 0L) {
                queryWrapper.eq(PicCataloguePO::getParentId, req.curIndexId);
            } else {
                queryWrapper.eq(PicCataloguePO::getId, req.curIndexId);
            }
        }
        if (req.type != null && req.type >= 0) {
            queryWrapper.eq(PicCataloguePO::getType, req.type);
        }
        if (StringUtils.isNotBlank(req.picLocation)) {
            queryWrapper.eq(PicCataloguePO::getPicLocation, req.picLocation);
        }
        if (req.parentId != null) {
            queryWrapper.eq(PicCataloguePO::getParentId, req.parentId);
        }
        if (StringUtils.isNotBlank(req.name)) {
            queryWrapper.eq(PicCataloguePO::getName, req.name);
        }
        //非删除目录
        queryWrapper.eq(PicCataloguePO::getDeletedFlag, "0");
        if (req.createTime != null) {
            queryWrapper.eq(PicCataloguePO::getCreateTime, req.createTime);
        }
        if (StringUtils.isNotBlank(req.createUser)) {
            queryWrapper.eq(PicCataloguePO::getCreateUser, req.createUser);
        }
        if (req.updateTime != null) {
            queryWrapper.eq(PicCataloguePO::getUpdateTime, req.updateTime);
        }
        if (StringUtils.isNotBlank(req.updateUser)) {
            queryWrapper.eq(PicCataloguePO::getUpdateUser, req.updateUser);
        }
        List<PicCataloguePO> list = picCatalogueMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return list;
    }

    @Override
    public boolean add(UpsertPicCatalogueReqDTO request) {
        return picCatalogueMapper.insert(upsertPicCatalogueRequest2TpPicCataloguePO(request)) > 0;
    }

    private static PicCataloguePO upsertPicCatalogueRequest2TpPicCataloguePO(UpsertPicCatalogueReqDTO req) {
        PicCataloguePO po = new PicCataloguePO();
        po.id = req.id;
        po.type = req.type;
        po.picLocation = req.picLocation;
        po.parentId = req.parentId;
        po.name = req.name;
        po.deletedFlag = req.deletedFlag;
        po.createTime = req.createTime;
        po.createUser = req.createUser;
        po.updateTime = req.updateTime;
        po.updateUser = req.updateUser;
        return po;
    }

    @Override
    public boolean update(UpsertPicCatalogueReqDTO request) {
        return picCatalogueMapper.updateById(upsertPicCatalogueRequest2TpPicCataloguePO(request)) > 0;
    }

    @Override
    public boolean delete(DelPicCatalogueReqDTO request) {
        return picCatalogueMapper.deleteById(request.id) > 0;
    }

    @Override
    public PicCataloguePO selectById(GetCatalogueListReqDTO param) {
        return picCatalogueMapper.selectById(param.curIndexId);
    }

    private void getIndexList(List<IndexInfoDTO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        for (IndexInfoDTO po : list) {
            if (po == null) {
                continue;
            }
            List<PicCataloguePO> list1 = getTpPicCataloguePOS(
                    tpPicCataloguePOId2GetCatalogueListRequestParentId(po));
            if (CollectionUtil.isEmpty(list1)) {
                continue;
            }
            po.indexInfoList = list1.stream().map(this::tpPicCataloguePO2IndexInfo).collect(Collectors.toList());
            getIndexList(po.indexInfoList);
        }
    }

    private IndexInfoDTO tpPicCataloguePO2IndexInfo(PicCataloguePO po) {
        IndexInfoDTO ret = new IndexInfoDTO();
        ret.indexId = po.id;
        ret.type = po.type;
        ret.pic_location = po.picLocation;
        ret.parent_id = po.parentId;
        ret.name = po.name;
        ret.deletedFlag = po.deletedFlag;
        ret.createTime = po.createTime;
        ret.createUser = po.createUser;
        ret.updateTime = po.updateTime;
        ret.updateUser = po.updateUser;
        return ret;
    }

    private GetCatalogueListReqDTO tpPicCataloguePOId2GetCatalogueListRequestParentId(IndexInfoDTO po) {
        GetCatalogueListReqDTO ret = new GetCatalogueListReqDTO();
        ret.parentId = po.indexId;//父节点为当前节点
        return ret;
    }
}
