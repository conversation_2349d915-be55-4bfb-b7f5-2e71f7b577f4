package com.juneyaoair.manage.b2c.service;

import com.juneyaoair.ecs.manage.dto.notice.MaintainInfo;
import com.juneyaoair.ecs.manage.dto.notice.NoticeInfo;
import com.juneyaoair.ecs.manage.dto.notice.NoticeSort;

import java.util.List;

/**
 * @ClassName INoticeInfoService
 * @Description
 * <AUTHOR>
 * @Date 2023/12/21 9:17
 * @Version 1.0
 */
public interface INoticeInfoService {

    List<NoticeInfo> selectNoticeInfo(NoticeInfo noticeInfo);

    List<NoticeSort> selectNoticeSortById(NoticeSort noticeSort);

    List<NoticeSort> selectFatherModule(NoticeSort noticeSort);

    int addNoticeInfo(NoticeInfo noticeInfo);

    int addNoticeSort(NoticeSort noticeSort);

    int updateNoticeSort(NoticeSort noticeSort);

    int updateNoticeInfo(NoticeInfo noticeInfo);

    int updateNoticeInfoV2(NoticeInfo noticeInfo);

    int deleteNoticeInfo(NoticeInfo noticeInfo);

    int deleteNoticeSort(NoticeSort noticeSort);

    int addMaintainInfo(MaintainInfo maintainInfo);

    int deleteMaintainInfo(MaintainInfo maintainInfo);

    int updateMaintainInfo(MaintainInfo maintainInfo);

    List<MaintainInfo> fetchMaintainInfo(MaintainInfo maintainInfo);

    List<NoticeInfo> getNoticeInfoList(NoticeInfo noticeInfo);

    int addNoticeMessage(NoticeInfo noticeInfo);

    int deleteNoticeMessage(NoticeInfo noticeInfo);

    int updateNoticeMessage(NoticeInfo noticeInfo);

    List<NoticeInfo> fetchNoticeMessage(NoticeInfo noticeInfo);
}
