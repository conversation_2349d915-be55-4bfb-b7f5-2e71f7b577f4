package com.juneyaoair.manage.b2c.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.juneyaoair.manage.b2c.entity.TravelReminderDisplayPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TravelReminderDisplayPOMapper extends BaseMapper<TravelReminderDisplayPO> {
    /**
     * 查询出行提醒详情列表
     *
     * @param theme 主题
     * @param channel 渠道
     * @param maintainer 维护人
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 出行提醒详情列表
     */
    List<TravelReminderDisplayPO> selectDisplayList(@Param("theme") String theme,
                                                   @Param("channel") String channel,
                                                   @Param("maintainer") String maintainer,
                                                   @Param("startTime") String startTime,
                                                   @Param("endTime") String endTime);
}