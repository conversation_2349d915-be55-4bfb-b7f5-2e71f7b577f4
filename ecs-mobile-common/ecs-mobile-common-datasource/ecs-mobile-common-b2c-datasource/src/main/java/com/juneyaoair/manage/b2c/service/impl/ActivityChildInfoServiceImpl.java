package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.ecs.manage.dto.activity.request.ActivityChildInfo;
import com.juneyaoair.manage.b2c.entity.activity.ActivityChildInfoPO;
import com.juneyaoair.manage.b2c.service.IActivityChildInfoService;
import com.juneyaoair.manage.b2c.mapper.ActivityChildInfoMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_ACTIVITY_CHILD_INFO】的数据库操作Service实现
* @createDate 2023-11-17 10:58:47
*/
@Service
public class ActivityChildInfoServiceImpl extends ServiceImpl<ActivityChildInfoMapper, ActivityChildInfoPO>
    implements IActivityChildInfoService {

    @Autowired
    private ActivityChildInfoMapper activityChildInfoMapper;


    @Override
    public List<ActivityChildInfoPO> toGainAllChildRecords(ActivityChildInfoPO activityChildInfoPO) {
        return activityChildInfoMapper.searchAllRecords(activityChildInfoPO);
    }

    @Override
    public ActivityChildInfoPO toGainChildInfoById(ActivityChildInfoPO activityChildInfoPO) {
        List<ActivityChildInfoPO> activityChildInfoPOS = activityChildInfoMapper.selectAllById(activityChildInfoPO);
        if (CollectionUtils.isEmpty(activityChildInfoPOS)) {
            return null;
        } else {
            return activityChildInfoPOS.get(0);
        }
    }

    @Override
    public List<ActivityChildInfo> toGainAllChildRecords(ActivityChildInfo activityChildInfo) {
        return activityChildInfoMapper.searchAllChildRecords(activityChildInfo);
    }

    @Override
    public List<ActivityChildInfoPO> toGainAllRecordsWithNonIds(ActivityChildInfoPO activityChildInfoPO) {
        return activityChildInfoMapper.searchAllRecordsWithNonIds(activityChildInfoPO);
    }

    @Override
    public int updateActivityChildInfo(ActivityChildInfoPO activityChildInfoPO) {
        return activityChildInfoMapper.updateActivityChildInfo(activityChildInfoPO);
    }
}




