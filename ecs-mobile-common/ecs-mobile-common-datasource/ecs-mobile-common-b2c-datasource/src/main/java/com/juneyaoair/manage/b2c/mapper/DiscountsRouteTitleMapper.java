package com.juneyaoair.manage.b2c.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteTitlePO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DISCOUNTS_ROUTE_TITLE(标题)】的数据库操作Mapper
* @createDate 2023-11-17 16:01:25
* @Entity com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteTitle
*/
public interface DiscountsRouteTitleMapper extends BaseMapper<DiscountsRouteTitlePO> {
    List<DiscountsRouteTitlePO> searchAllRecords(DiscountsRouteTitlePO discountsRouteTitlePO);

}




