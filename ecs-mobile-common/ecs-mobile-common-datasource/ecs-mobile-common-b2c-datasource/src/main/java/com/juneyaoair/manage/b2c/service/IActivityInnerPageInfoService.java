package com.juneyaoair.manage.b2c.service;

import com.juneyaoair.manage.b2c.entity.activity.ActivityInnerPageInfoPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_ACTIVITY_INNER_PAGE_INFO】的数据库操作Service
* @createDate 2023-12-11 09:18:00
*/
public interface IActivityInnerPageInfoService extends IService<ActivityInnerPageInfoPO> {
    boolean removeById(ActivityInnerPageInfoPO activityInnerPageInfoPO);

    List<ActivityInnerPageInfoPO> toGainAllById(ActivityInnerPageInfoPO activityInnerPageInfoPO);

}
