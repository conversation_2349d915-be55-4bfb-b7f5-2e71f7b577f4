package com.juneyaoair.manage.b2c.service;

import com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteTitlePO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DISCOUNTS_ROUTE_TITLE(标题)】的数据库操作Service
* @createDate 2023-11-17 16:01:25
*/
public interface IDiscountsRouteTitleService extends IService<DiscountsRouteTitlePO> {

    List<DiscountsRouteTitlePO> toGainAllRecords(DiscountsRouteTitlePO discountsRouteTitlePO);



}
