package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.ecs.manage.dto.airline.AirLineLabelReqDTO;
import com.juneyaoair.ecs.manage.dto.airline.AirLineRequestDTO;
import com.juneyaoair.ecs.manage.dto.airline.AirlineDTO;
import com.juneyaoair.manage.b2c.entity.AirlineAPO;
import com.juneyaoair.manage.b2c.entity.AirlineLabelPO;

import java.util.List;

public interface IAirlineService extends IService<AirlineAPO> {

    List<AirlineDTO> searchList(AirLineRequestDTO airlineAPO);

    List<AirlineLabelPO> selectAirLineLabel(AirlineLabelPO para);

    boolean add(AirlineAPO airLine, boolean needBack);

    boolean delete(AirlineAPO airLine);

    boolean update(AirlineAPO airLine);

    List<AirlineAPO> search(AirlineAPO airLine);

    boolean addLablel(AirLineLabelReqDTO labelPO);

    boolean updateLablel(AirLineLabelReqDTO labelReqDTO);

    boolean deleteLablel(String labelId);

    AirlineLabelPO selectLabelById(String labelId);


}
