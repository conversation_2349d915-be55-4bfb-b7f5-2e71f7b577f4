package com.juneyaoair.manage.b2c.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 航线标签规则
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "T_ROUTE_LABEL_RULE")
public class RouteLabelRulePO {
    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 标签ID;FK
     */
    @TableField(value = "LABEL_ID")
    private String labelId;

    /**
     * 规则名称
     */
    @TableField(value = "LABEL_RULE_NAME")
    private String labelRuleName;

    /**
     * 航线日期-开始
     */
    @TableField(value = "ROUTE_START_DATE")
    private Date routeStartDate;

    /**
     * 航线日期-结束
     */
    @TableField(value = "ROUTE_END_DATE")
    private Date routeEndDate;

    /**
     * 起飞机场;"/"拼接
     */
    @TableField(value = "DEP_AIRPORT")
    private String depAirport;

    /**
     * 起飞机场航站楼;"/"拼接
     */
    @TableField(value = "DEP_TERMINAL")
    private String depTerminal;

    /**
     * 起飞国家;"/"拼接
     */
    @TableField(value = "DEP_COUNTRY")
    private String depCountry;

    /**
     * 起飞地区;"/"拼接
     */
    @TableField(value = "DEP_REGION")
    private String depRegion;

    /**
     * 到达机场;"/"拼接
     */
    @TableField(value = "ARR_AIRPORT")
    private String arrAirport;

    /**
     * 到达机场航站楼;"/"拼接
     */
    @TableField(value = "ARR_TERMINAL")
    private String arrTerminal;

    /**
     * 到达国家;"/"拼接
     */
    @TableField(value = "ARR_COUNTRY")
    private String arrCountry;

    /**
     * 到达地区;"/"拼接
     */
    @TableField(value = "ARR_REGION")
    private String arrRegion;

    /**
     * 承运航司;两段"-"拼接
     */
    @TableField(value = "CARRIER")
    private String carrier;

    /**
     * 是否中转;是/否,1/0
     */
    @TableField(value = "IS_TRANSIT")
    private String isTransit;

    /**
     * 中转机场;"/"拼接
     */
    @TableField(value = "TRANS_AIRPORT")
    private String transAirport;

    /**
     * 中转出发航站楼;"/"拼接
     */
    @TableField(value = "TRANS_DEP_TERMINAL")
    private String transDepTerminal;

    /**
     * 中转要求同场转机;需同场转机/要求不同机场/无限制，Y/N/A
     */
    @TableField(value = "TRANS_SAME_AIRPORT")
    private String transSameAirport;

    /**
     * 中转时长要求-分钟;x-xxxx
     */
    @TableField(value = "TRANS_TIME")
    private String transTime;

    /**
     * 中转日期限制;隔夜-overnight 当日-sameDay  多个使用英文逗号分割
     */
    @TableField(value = "TRANS_DATE_LIMIT")
    private String transDateLimit;

    /**
     * 中转前序航班日期间隔限制;null表示无限制 0-表示当日内 1-表示允许跨一天
     */
    @TableField(value = "TRANS_PRE_FLIGHT_DATE_LIMIT")
    private Short transPreFlightDateLimit;

    /**
     * 中转后序航班日期间隔限制;null表示无限制 0-表示当日内 1-表示允许跨一天
     */
    @TableField(value = "TRANS_NEXT_FLIGHT_DATE_LIMIT")
    private Short transNextFlightDateLimit;

    /**
     * 标签功能;中转住宿:TransitAccommodation
     */
    @TableField(value = "LABEL_FUNCTION")
    private String labelFunction;

    /**
     * 启用状态;启用/禁用，1/0
     */
    @TableField(value = "ENABLE_STATUS")
    private String enableStatus;

    /**
     * 排序
     */
    @TableField(value = "SORT_NUM")
    private Short sortNum;

    /**
     * 备注
     */
    @TableField(value = "REMARK")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "CREATED_TIME")
    private Date createdTime;

    /**
     * 更新人
     */
    @TableField(value = "UPDATED_BY")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATED_TIME")
    private Date updatedTime;

    /**
     * 适用航班号;多个以英文逗号分隔，支持中转航班组合
     */
    @TableField(value = "APPLY_FLIGHT_NO")
    private String applyFlightNo;

    /**
     * 适用机型;多个以英文逗号分隔，仅直达航线生效
     */
    @TableField(value = "APPLY_AIRCRAFT_TYPE")
    private String applyAircraftType;
}