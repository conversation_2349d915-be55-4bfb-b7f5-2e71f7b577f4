package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.ecs.manage.dto.activity.request.pointretrieval.PointRetrievalRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.pointretrieval.PointRetrievalInformation;
import com.juneyaoair.manage.b2c.entity.activity.RetrievalSignUpPO;
import com.juneyaoair.manage.b2c.mapper.RetrievalSignUpPOMapper;
import com.juneyaoair.manage.b2c.service.RetrievalSignUpPOService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【T_RETRIEVAL_SIGN_UP_RECORD(积分找回报名表)】的数据库操作Service实现
 * @createDate 2025-03-17 11:09:13
 */
@Service
@SuppressWarnings("all")
public class RetrievalSignUpPOServiceImpl extends ServiceImpl<RetrievalSignUpPOMapper, RetrievalSignUpPO>
        implements RetrievalSignUpPOService {


    @Resource
    private RetrievalSignUpPOMapper retrievalSignUpPOMapper;

    @Override
    public List<PointRetrievalInformation> toCatchRetrievalSignUpInformation(PointRetrievalRequest pointRetrievalRequest) {
        return retrievalSignUpPOMapper.toCatchRetrievalSignUpInformation(pointRetrievalRequest);
    }
}




