package com.juneyaoair.manage.b2c.service;

import com.juneyaoair.manage.b2c.entity.activity.CommonLotterySubPrizePO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.manage.b2c.entity.activity.CommonLotterySubPrizeUpPO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_PRIZE_SUB_ENTITY】的数据库操作Service
* @createDate 2023-05-24 10:11:01
*/
public interface CommonLotterySubPrizeService extends IService<CommonLotterySubPrizePO> {
    /**
     * 获取符合条件的子奖品记录
     * @param commonLotterySubPrizePO
     * @return
     */
    List<CommonLotterySubPrizePO> toGainAllSubPrizeRecords(CommonLotterySubPrizePO commonLotterySubPrizePO);

    /**
     * 获取符合条件的子奖品记录
     * @param commonLotterySubPrizePO
     * @return
     */
    int toDeleteSubPrizeRecords(CommonLotterySubPrizePO commonLotterySubPrizePO);

    /**
     * 更新符合条件的子奖品记录
     * @param commonLotterySubPrizeUpPO
     * @return
     */
    int toUpdateSubPrizeRecords(CommonLotterySubPrizeUpPO commonLotterySubPrizeUpPO);

}
