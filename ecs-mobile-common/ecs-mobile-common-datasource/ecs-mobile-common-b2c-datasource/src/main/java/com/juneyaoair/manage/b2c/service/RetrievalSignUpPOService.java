package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.ecs.manage.dto.activity.request.pointretrieval.PointRetrievalRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.pointretrieval.PointRetrievalInformation;
import com.juneyaoair.manage.b2c.entity.activity.RetrievalSignUpPO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【T_RETRIEVAL_SIGN_UP_RECORD(积分找回报名表)】的数据库操作Service
 * @createDate 2025-03-17 11:09:13
 */
public interface RetrievalSignUpPOService extends IService<RetrievalSignUpPO> {

    /**
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.activity.response.pointretrieval.PointRetrievalInformation>
     * <AUTHOR>
     * @Description 获取积分找回流水
     * @Date 10:29 2025/3/18
     **/
    List<PointRetrievalInformation> toCatchRetrievalSignUpInformation(PointRetrievalRequest pointRetrievalRequest);

}
