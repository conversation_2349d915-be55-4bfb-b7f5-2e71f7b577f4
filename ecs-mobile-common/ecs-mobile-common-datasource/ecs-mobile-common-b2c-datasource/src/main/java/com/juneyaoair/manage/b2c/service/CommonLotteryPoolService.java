package com.juneyaoair.manage.b2c.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.manage.b2c.entity.activity.CommonLotteryPoolPO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_PRIZE_POOL】的数据库操作Service
* @createDate 2023-05-22 15:48:54
*/
public interface CommonLotteryPoolService extends IService<CommonLotteryPoolPO> {
    /**
     * 获取符合条件的奖池记录
     * @param commonLotteryPoolPO
     * @return
     */
    List<CommonLotteryPoolPO> toGainAllPoolRecords(CommonLotteryPoolPO commonLotteryPoolPO);

}
