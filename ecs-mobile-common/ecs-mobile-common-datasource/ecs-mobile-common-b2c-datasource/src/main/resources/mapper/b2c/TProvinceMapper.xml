<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TProvinceMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.ProvincePO">
        <id column="PROVINCE_ID" jdbcType="DECIMAL" property="provinceId"/>
        <result column="PROVINCE_NAME" jdbcType="VARCHAR" property="provinceName"/>
        <result column="COUNTRY_CODE" jdbcType="VARCHAR" property="countryCode"/>
        <result column="country_name" property="countryName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        PROVINCE_ID, PROVINCE_NAME, COUNTRY_CODE
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_PROVINCE
        where PROVINCE_ID = #{provinceId,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from T_PROVINCE
        where PROVINCE_ID = #{provinceId,jdbcType=DECIMAL}
    </delete>
    <insert id="insert" parameterType="com.juneyaoair.manage.b2c.entity.ProvincePO">
        insert into T_PROVINCE (PROVINCE_ID, PROVINCE_NAME, COUNTRY_CODE)
        values (#{provinceId,jdbcType=DECIMAL}, #{provinceName,jdbcType=VARCHAR}, #{countryCode,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.juneyaoair.manage.b2c.entity.ProvincePO">
        insert into T_PROVINCE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="provinceId != null">
                PROVINCE_ID,
            </if>
            <if test="provinceName != null">
                PROVINCE_NAME,
            </if>
            <if test="countryCode != null">
                COUNTRY_CODE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="provinceId != null">
                #{provinceId,jdbcType=DECIMAL},
            </if>
            <if test="provinceName != null">
                #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="countryCode != null">
                #{countryCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.ProvincePO">
        update T_PROVINCE
        <set>
            <if test="provinceName != null">
                PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="countryCode != null">
                COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR},
            </if>
        </set>
        where PROVINCE_ID = #{provinceId,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.juneyaoair.manage.b2c.entity.ProvincePO">
        update T_PROVINCE
        set PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR},
            COUNTRY_CODE  = #{countryCode,jdbcType=VARCHAR}
        where PROVINCE_ID = #{provinceId,jdbcType=DECIMAL}
    </update>
    <select id="selectJoinCountry" resultMap="BaseResultMap">
        select a.*, b.country_name
        from T_PROVINCE a
                     left join T_COUNTRY b on a.country_code = b.country_code
        <where>
            <if test="provinceId != null">
                and PROVINCE_ID = #{provinceId,jdbcType=DECIMAL}
            </if>
            <if test="provinceName != null">
                and PROVINCE_NAME like '%' || #{provinceName,jdbcType=VARCHAR} || '%'
            </if>
            <if test="countryCode != null">
                and COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
            </if>
        </where>
        order by PROVINCE_ID
    </select>
    <select id="selectByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_PROVINCE
        <where>
            <if test="provinceId != null">
                and PROVINCE_ID = #{provinceId,jdbcType=DECIMAL}
            </if>
            <if test="provinceName != null">
                and PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR}
            </if>
            <if test="countryCode != null">
                and COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
            </if>
            <if test="countryName != null">
                and country_name = #{countryName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>