<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.AirlineClassLogPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.AirlineClassLogPO">
    <!--@mbg.generated-->
    <!--@Table AIRLINE_CLASS_LOG-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="AIRLINE_CLASS_ID" jdbcType="VARCHAR" property="airlineClassId" />
    <result column="AIRLINE_CODE" jdbcType="VARCHAR" property="airlineCode" />
    <result column="BUSINESS_CLASS" jdbcType="VARCHAR" property="businessClass" />
    <result column="ECONOMY_CLASS" jdbcType="VARCHAR" property="economyClass" />
    <result column="EFFECTIVE_DATE" jdbcType="TIMESTAMP" property="effectiveDate" />
    <result column="EXPIRATION_DATE" jdbcType="TIMESTAMP" property="expirationDate" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, AIRLINE_CLASS_ID, AIRLINE_CODE, BUSINESS_CLASS, ECONOMY_CLASS, EFFECTIVE_DATE, 
    EXPIRATION_DATE, "STATUS", CREATED_BY, CREATED_TIME
  </sql>
</mapper>