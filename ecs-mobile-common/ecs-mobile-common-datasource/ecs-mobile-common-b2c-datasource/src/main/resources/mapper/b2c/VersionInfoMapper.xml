<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.VersionInfoMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.version.VersionInfoControlPO">
            <id property="versionId" column="VERSION_ID" jdbcType="DECIMAL"/>
            <result property="channelCode" column="CHANNEL_CODE" jdbcType="VARCHAR"/>
            <result property="versionType" column="VERSION_TYPE" jdbcType="VARCHAR"/>
            <result property="versionNo" column="VERSION_NO" jdbcType="VARCHAR"/>
            <result property="updateDetails" column="UPDATE_DETAILS" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        VERSION_ID,CHANNEL_CODE,VERSION_TYPE,
        VERSION_NO,UPDATE_DETAILS
    </sql>
</mapper>
