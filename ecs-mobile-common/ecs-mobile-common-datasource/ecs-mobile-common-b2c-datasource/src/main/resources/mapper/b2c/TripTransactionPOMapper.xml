<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TripTransactionPOMapper">

    <sql id="Base_Column_List">
        ID,DEPARTMENT,EMPLOYEE_NUMBER,
        DOMAIN_ACCOUNT,EMPLOYEE_CARD_NO,EMPLOYEE_NAME,
        PASSENGER_CARD_NO,TICKET_NO,FLIGHT_NO,
        FLIGHT_DATE,DEPT_AIRPORT_CODE,ARR_AIRPORT_CODE,
        AIRLINE_TYPE,ASSOCIATED_TIME,CREATED_BY,
        CREATED_TIME,UPDATED_BY,UPDATED_TIME
    </sql>
</mapper>
