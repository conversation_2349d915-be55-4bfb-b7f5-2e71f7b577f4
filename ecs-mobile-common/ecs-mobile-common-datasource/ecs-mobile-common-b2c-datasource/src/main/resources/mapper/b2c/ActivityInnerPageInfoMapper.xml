<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.ActivityInnerPageInfoMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.ActivityInnerPageInfoPO">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="pcIconUrl" column="PC_ICON_URL" jdbcType="VARCHAR"/>
            <result property="appIconUrl" column="APP_ICON_URL" jdbcType="VARCHAR"/>
            <result property="pcUrl" column="PC_URL" jdbcType="VARCHAR"/>
            <result property="appUrl" column="APP_URL" jdbcType="VARCHAR"/>
            <result property="orderNo" column="ORDER_NO" jdbcType="DECIMAL"/>
            <result property="seqNo" column="SEQ_NO" jdbcType="DECIMAL"/>
            <result property="childInfoId" column="CHILD_INFO_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PC_ICON_URL,APP_ICON_URL,
        PC_URL,APP_URL,ORDER_NO,
        SEQ_NO,CHILD_INFO_ID
    </sql>
    <delete id="deleteByChildInfoId" parameterType="com.juneyaoair.manage.b2c.entity.activity.ActivityInnerPageInfoPO">
        delete
        from T_ACTIVITY_INNER_PAGE_INFO
        where CHILD_INFO_ID = #{childInfoId,jdbcType=VARCHAR}
    </delete>
    <select id="searchAllById" parameterType="com.juneyaoair.manage.b2c.entity.activity.ActivityInnerPageInfoPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_ACTIVITY_INNER_PAGE_INFO
        <where>
            <if test="childInfoId != null and childInfoId != ''">
                CHILD_INFO_ID = #{childInfoId}
            </if>
        </where>
        order by order_no,seq_no
    </select>
</mapper>
