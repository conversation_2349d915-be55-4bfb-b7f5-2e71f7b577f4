<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TravelReminderDisplayPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.TravelReminderDisplayPO">
    <!--@mbg.generated-->
    <!--@Table TRAVEL_REMINDER_DISPLAY-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="THEME" jdbcType="VARCHAR" property="theme" />
    <result column="ROUTE_TYPE" jdbcType="VARCHAR" property="routeType" />
    <result column="AVAILABLE_ROUTE" jdbcType="VARCHAR" property="availableRoute" />
    <result column="UNAVAILABLE_ROUTE" jdbcType="VARCHAR" property="unavailableRoute" />
    <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="DISPLAY_TYPE" jdbcType="VARCHAR" property="displayType" />
    <result column="CHANNEL" jdbcType="VARCHAR" property="channel" />
    <result column="THEME_STATUS" jdbcType="VARCHAR" property="themeStatus" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, THEME, ROUTE_TYPE, AVAILABLE_ROUTE, UNAVAILABLE_ROUTE, START_TIME, END_TIME, 
    DISPLAY_TYPE, CHANNEL, THEME_STATUS, CREATED_BY, CREATED_TIME, UPDATED_BY, UPDATED_TIME
  </sql>

  <select id="selectDisplayList" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List"/>
    FROM TRAVEL_REMINDER_DISPLAY d
    WHERE 1=1
    <if test="theme != null and theme != ''">
      AND d.THEME LIKE '%' || #{theme} || '%'
    </if>
    <if test="channel != null and channel != ''">
      AND INSTR(',' || d.CHANNEL || ',', ',' || #{channel} || ',') > 0
    </if>
    <if test="maintainer != null and maintainer != ''">
      AND d.UPDATED_BY = #{maintainer}
    </if>
    <if test="startTime != null">
      AND d.UPDATED_TIME &gt;= TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
    </if>
    <if test="endTime != null">
      AND d.UPDATED_TIME &lt;= TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS')
    </if>
    ORDER BY 
    CASE WHEN d.DISPLAY_TYPE = 'TOP' THEN 0 ELSE 1 END,
    d.UPDATED_TIME DESC
  </select>
</mapper>