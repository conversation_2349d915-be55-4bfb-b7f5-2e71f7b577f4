<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.EventInfoMapper">

    <select id="getEventMemberRecord" parameterType="com.juneyaoair.ecs.manage.dto.activity.request.event.EventMemberQuery" resultType="com.juneyaoair.ecs.manage.dto.activity.response.event.EventMemberRecord">
         select EVENT_TYPE "eventType",
                FFP_CARD_NO "ffpCardNo",
                LICENSES_KEY "licensesKey",
                PRIZE_NAME "prizeName",
                COUPON_CODE "couponCode",
                PRIZE_NUMBER "prizeNumber",
                (CASE PROVIDE_STATUS
                when 'D' THEN '已发放'
                WHEN 'DS' THEN '发放中'
                WHEN 'DF' THEN '发放失败'
                WHEN 'F' THEN '未发放'
                WHEN 'UN' THEN '未获得'
                WHEN 'SF' THEN '无法自动发放且未发放'
                ELSE PROVIDE_STATUS END) "provideStatusName",
                to_char(CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') "createTime"
           from T_EVENT_MEMBER_RECORD
          where EVENT_TYPE = #{eventType}
        <if test="ffpCardNo != null and ffpCardNo != ''">
            and FFP_CARD_NO = #{ffpCardNo}
        </if>
        <if test="startTime != null">
            and CREATE_TIME <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and CREATE_TIME <![CDATA[ <= ]]> #{endTime}
        </if>
       order by EVENT_TYPE, CREATE_TIME desc, RECORD_ID
    </select>

</mapper>
