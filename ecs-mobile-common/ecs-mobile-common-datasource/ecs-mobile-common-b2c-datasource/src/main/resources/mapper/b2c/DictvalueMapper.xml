<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.DictvalueMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.dict.DictvaluePO">
            <id property="dvid" column="DVID" jdbcType="VARCHAR"/>
            <result property="dvcode" column="DVCODE" jdbcType="VARCHAR"/>
            <result property="dvname" column="DVNAME" jdbcType="VARCHAR"/>
            <result property="dvdescription" column="DVDESCRIPTION" jdbcType="VARCHAR"/>
            <result property="enable" column="ENABLE" jdbcType="VARCHAR"/>
            <result property="dtid" column="DTID" jdbcType="VARCHAR"/>
            <result property="num" column="NUM" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- dvid主键，dvcode编号， dvname名称，dvdescription描述，enable是否有效，dtid对应的数据类型 -->
    <resultMap type="com.juneyaoair.ecs.manage.dto.activity.response.DICTValue" id="dICTValue">
        <id column="dvid" property="dvid" />
        <result column="dvcode" property="dvCode" />
        <result column="dvname" property="dvName" />
        <result column="dvdescription" property="dvDescription" />
        <result column="enable" property="enable" />
        <result column="dtid" property="dtid" />
    </resultMap>

    <sql id="Base_Column_List">
        DVID,DVCODE,DVNAME,
        DVDESCRIPTION,ENABLE,DTID,
        NUM
    </sql>
    <select id="findDICTValueList" parameterType="com.juneyaoair.manage.b2c.entity.dict.DictvaluePO" resultType="com.juneyaoair.manage.b2c.entity.dict.DictvaluePO">
        select tv.*,tt.DTCODE from TP_DICTVALUE tv inner join TP_DICTTYPE tt on tt.DTID = tv.DTID
        where tv.DTID = tt.DTID
        and tv.ENABLE = 'Y' and tt.ENABLE = 'Y'
        <if test="dtid != null and dtid != ''">
            and tv.DTID = #{dtid}
        </if>
        <if test="dvid != null ">
            and tv.DVID = #{dvid}
        </if>
        <if test="dvcode != null and dvcode != ''">
            and tv.DVCODE = #{dvcode}
        </if>
        <if test="dvname != null and dvname != ''">
            and tv.DVNAME = #{dvname}
        </if>
        <if test="enable != null">
            and tv.ENABLE = #{enable}
        </if>
        <if test="dtCode != null and dtCode != ''">
            and tt.DTCODE = #{dtCode}
        </if>


    </select>
    <insert id="insertSelective">
        insert into TP_DICTVALUE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dvid != null">DVID,</if>
            <if test="dvcode != null">DVCODE,</if>
            <if test="dvname != null">DVNAME,</if>
            <if test="dvdescription != null">DVDESCRIPTION,</if>
            <if test="enable != null">ENABLE,</if>
            <if test="dtid != null">DTID,</if>
            <if test="dtCode != null">,</if>
            <if test="num != null">NUM,</if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dvid != null">#{dvid,jdbcType=VARCHAR},</if>
            <if test="dvcode != null">#{dvcode,jdbcType=VARCHAR},</if>
            <if test="dvname != null">#{dvname,jdbcType=VARCHAR},</if>
            <if test="dvdescription != null">#{dvdescription,jdbcType=VARCHAR},</if>
            <if test="enable != null">#{enable,jdbcType=VARCHAR},</if>
            <if test="dtid != null">#{dtid,jdbcType=VARCHAR},</if>
            <if test="dtCode != null">#{dtCode,jdbcType=VARCHAR},</if>
            <if test="num != null">#{num,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <select id="selectOneByDtid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TP_DICTVALUE
        <where>
            <if test="dtid != null and dtid != ''">
                DTID = #{dtid,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- dvid主键，dvcode编号， dvname名称，dvdescription描述，enable是否有效，dtid对应的数据类型 -->
    <select id="findDICTValueListV2" parameterType="com.juneyaoair.ecs.manage.dto.activity.response.DICTValue"
            resultMap="dICTValue">
        select tv.dtid, tt.dtcode,tv.dvid, tv.dvcode, tv.dvname, tv.dvdescription, tv.enable
        from tp_dictvalue tv, tp_dicttype tt
        where tv.dtid = tt.dtid
        and tv.enable = 'Y'
        and tt.enable = 'Y'
        <if test="dtid != null and dtid != ''">
            and tv.dtid = #{dtid}
        </if>
        <if test="dvid != null ">
            and tv.dvid = #{dvid}
        </if>
        <if test="dvCode != null and dvCode != ''">
            and tv.dvcode = #{dvCode}
        </if>
        <if test="dvName != null and dvName != ''">
            and tv.dvname = #{dvName}
        </if>
        <if test="enable != null">
            and tv.enable = #{enable}
        </if>
        <if test="dtCode != null and dtCode != ''">
            and tt.dtcode = #{dtCode}
        </if>
    </select>

</mapper>
