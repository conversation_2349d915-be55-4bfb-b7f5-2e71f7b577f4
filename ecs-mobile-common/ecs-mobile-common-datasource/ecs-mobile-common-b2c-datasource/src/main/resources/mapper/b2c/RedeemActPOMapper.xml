<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.RedeemActPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.RedeemActPO">
    <!--@mbg.generated-->
    <!--@Table T_REDEEM_ACT-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ACT_NAME" jdbcType="VARCHAR" property="actName" />
    <result column="ACT_START_TIME" jdbcType="TIMESTAMP" property="actStartTime" />
    <result column="ACT_END_TIME" jdbcType="TIMESTAMP" property="actEndTime" />
    <result column="ACT_TYPE" jdbcType="VARCHAR" property="actType" />
    <result column="ACT_STATE" jdbcType="VARCHAR" property="actState" />
    <result column="AUDITED_BY" jdbcType="VARCHAR" property="auditedBy" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ACT_NAME, ACT_START_TIME, ACT_END_TIME, ACT_TYPE, ACT_STATE, AUDITED_BY, CREATED_BY, 
    UPDATED_BY, CREATED_TIME, UPDATED_TIME
  </sql>

  <select id="selectActByTime" resultMap="BaseResultMap">
    SELECT *
    FROM T_REDEEM_ACT
    WHERE ACT_STATE != '0'
      AND ACT_TYPE = #{actType,jdbcType=VARCHAR}
    <if test="request.actStartTime != null and request.actStartTime != ''">
      AND to_date(#{request.actStartTime,jdbcType=VARCHAR}, 'yyyy-MM-dd HH24:MI:SS') &lt; ACT_START_TIME
    </if>
    <if test="request.actEndTime != null and request.actEndTime != ''">
      AND to_date(#{request.actEndTime,jdbcType=VARCHAR}, 'yyyy-MM-dd HH24:MI:SS') &gt; ACT_END_TIME
    </if>
  </select>

  <select id="selectAllAudit" resultMap="BaseResultMap">
    SELECT *
    FROM T_REDEEM_ACT
    WHERE ACT_STATE = '2'
      AND ACT_TYPE = #{actType,jdbcType=VARCHAR}
  </select>
</mapper>