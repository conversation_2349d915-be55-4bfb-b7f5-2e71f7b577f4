<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.ActivityCommodityMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.ActivityCommodityPO">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="commodityName" column="COMMODITY_NAME" jdbcType="VARCHAR"/>
            <result property="currentPrice" column="CURRENT_PRICE" jdbcType="DECIMAL"/>
            <result property="price" column="PRICE" jdbcType="DECIMAL"/>
            <result property="productId" column="PRODUCT_ID" jdbcType="VARCHAR"/>
            <result property="pcPicUrl" column="PC_PIC_URL" jdbcType="VARCHAR"/>
            <result property="appPicUrl" column="APP_PIC_URL" jdbcType="VARCHAR"/>
            <result property="orderNo" column="ORDER_NO" jdbcType="DECIMAL"/>
            <result property="childInfoId" column="CHILD_INFO_ID" jdbcType="VARCHAR"/>
            <result property="seqNo" column="SEQ_NO" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,COMMODITY_NAME,CURRENT_PRICE,
        PRICE,PRODUCT_ID,PC_PIC_URL,
        APP_PIC_URL,ORDER_NO,CHILD_INFO_ID,
        SEQ_NO
    </sql>



    <delete id="delByChildInfoId" parameterType="com.juneyaoair.manage.b2c.entity.activity.ActivityCommodityPO">
        delete
        from T_ACTIVITY_COMMODITY
        where CHILD_INFO_ID = #{childInfoId,jdbcType=VARCHAR}
    </delete>
    <select id="searchAllById" parameterType="com.juneyaoair.manage.b2c.entity.activity.ActivityCommodityPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_ACTIVITY_COMMODITY
        <where>
            <if test="childInfoId != null and childInfoId != ''">
                CHILD_INFO_ID = #{childInfoId}
            </if>
        </where>
    </select>
</mapper>
