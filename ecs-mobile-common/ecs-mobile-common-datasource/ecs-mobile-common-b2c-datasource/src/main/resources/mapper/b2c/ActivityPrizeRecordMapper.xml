<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.ActivityPrizeRecordMapper">

    <select id="toCatchActivityPrizeRecords"
            parameterType="com.juneyaoair.ecs.manage.dto.activity.request.passport.PassportRequest"
            resultType="com.juneyaoair.ecs.manage.dto.activity.response.passport.PassportInformation">
        SELECT apr.FFP_CARD_NO AS ffpCardNo,
        apr.PRIZE_ID AS prizeId,
        apr.PRIZE_AMOUNT AS prizeAmount,
        TO_CHAR(apr.CREATE_TIME, 'YYYY-MM-DD HH24:MI:SS') AS receiveTime,
        (CASE apr.PRIZE_TYPE
        WHEN '1' THEN '积分'
        WHEN '2' THEN '优惠券'
        WHEN '3' THEN '产品券'
        WHEN '4' THEN '定级航段'
        WHEN '5' THEN '实物奖品'
        WHEN '6' THEN '谢谢参与'
        WHEN '7' THEN '会员等级调整'
        WHEN '8' THEN '其他虚拟奖品'
        WHEN '9' THEN '券包多奖品'
        ELSE '其他奖品'
        END) AS prizeType,
        (CASE apr.RECEIVE_STATUS
        WHEN 'PREPARE' THEN '准备发放,未执行发放动作'
        WHEN 'SUCCESS' THEN '发放成功'
        WHEN 'FAILED' THEN '发放失败'
        ELSE '状态未知'
        END) AS prizeStatus,
        ail.FLIGHT_TIMES AS flightTimes
        FROM T_ACTIVITY_PRIZE_RECORD apr
        LEFT JOIN T_ACTIVITY_ISSUE_LICENSE ail
        ON apr.FFP_CARD_NO = ail.FFP_CARD_NO
        AND apr.ACTIVITY_CODE = ail.ACTIVITY_CODE
        <where>
            1=1
            <if test="activityCode != null and activityCode != ''">
                AND apr.ACTIVITY_CODE = #{activityCode,jdbcType=VARCHAR}
            </if>
            <if test="ffpCardNo != null and ffpCardNo != ''">
                AND apr.FFP_CARD_NO = #{ffpCardNo,jdbcType=VARCHAR}
            </if>
            <if test="prizeId != null and prizeId != ''">
                AND apr.PRIZE_ID = #{prizeId,jdbcType=VARCHAR}
            </if>
            <if test="prizeStatus != null and prizeStatus != ''">
                AND apr.RECEIVE_STATUS = #{prizeStatus,jdbcType=VARCHAR}
            </if>
            <if test="receiveStartTime != null and receiveStartTime != '' and receiveEndTime != null and receiveEndTime != ''">
                AND TRUNC(apr.CREATE_TIME) BETWEEN TO_DATE(#{receiveStartTime}, 'YYYY-MM-DD')
                AND TO_DATE(#{receiveEndTime}, 'YYYY-MM-DD') + INTERVAL '1' DAY - INTERVAL '1' SECOND
            </if>
        </where>
        ORDER BY apr.FFP_CARD_NO, apr.CREATE_TIME DESC;
    </select>
</mapper>
