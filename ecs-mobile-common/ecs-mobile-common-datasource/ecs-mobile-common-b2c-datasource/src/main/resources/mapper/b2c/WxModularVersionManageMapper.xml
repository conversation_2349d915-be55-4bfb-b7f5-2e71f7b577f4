<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.WxModularVersionManageMapper">
    <resultMap id="modularVersionMap" type="com.juneyaoair.ecs.manage.dto.datadict.ModularVersion">
        <id column="ID" property="id"/>
        <result column="MODULAR_LD" property="modularId"/>
        <result column="MODULAR_BM" property="modularBm"/>
        <result column="VERSION_NO" property="versionNo"/>
        <result column="VERSION_TYPE" property="versionType"/>
        <result column="INNER_ID" property="innerId"/>
    </resultMap>

    <select id="queryModularByModularId" parameterType="string" resultMap="modularVersionMap">
        SELECT *
        FROM MODULAR_VERSION T
        WHERE T.MODULAR_ID = #{modularId,jdbcType=VARCHAR}
    </select>

    <insert id="addModular" parameterType="com.juneyaoair.ecs.manage.dto.datadict.ModularVersion">
        INSERT INTO
            MODULAR_VERSION
        VALUES (#{id,jdbcType=VARCHAR},
                #{modularId,jdbcType=VARCHAR},
                #{modularBm,jdbcType=VARCHAR},
                #{versionNo,jdbcType=VARCHAR},
                #{versionType,jdbcType=VARCHAR},
                #{innerId,jdbcType=VARCHAR}
               )
    </insert>

    <delete id="deleteModularVersion" parameterType="com.juneyaoair.ecs.manage.dto.datadict.ModularVersion">
        DELETE  FROM MODULAR_VERSION T
        WHERE T.MODULAR_ID = #{modularId,jdbcType=VARCHAR}
    </delete>
</mapper>
