<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.CommonflycitydetailPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.CommonflycitydetailPO">
    <!--@mbg.generated-->
    <!--@Table T_COMMONFLYCITYDETAIL-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CFC_ID" jdbcType="VARCHAR" property="cfcId" />
    <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
    <result column="CITY_NAME" jdbcType="VARCHAR" property="cityName" />
    <result column="CITY_E_NAME" jdbcType="VARCHAR" property="cityEName" />
    <result column="CITY_PIN_YIN" jdbcType="VARCHAR" property="cityPinYin" />
    <result column="IS_INTERNATIONAL" jdbcType="VARCHAR" property="isInternational" />
    <result column="COUNTRY_CODE" jdbcType="VARCHAR" property="countryCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CFC_ID, CITY_CODE, CITY_NAME, CITY_E_NAME, CITY_PIN_YIN, IS_INTERNATIONAL, COUNTRY_CODE
  </sql>
</mapper>