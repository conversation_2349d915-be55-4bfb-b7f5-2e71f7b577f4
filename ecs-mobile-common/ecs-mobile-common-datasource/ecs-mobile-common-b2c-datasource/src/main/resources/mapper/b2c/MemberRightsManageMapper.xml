<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.MemberRightsManageMapper">

    <sql id="Base_Column_List">
        ID,
        NAME,
        SUBNAME,
        RIGHTS_LEVEL,
        ORDER_NO,
        URL,
        CHANNEL_CODE,
        ENABLE_ICON_URL,
        DISABLE_ICON_URL,
        CREATE_TIME,
        CREATE_USER,
        UPDATE_TIME,
        UPDATE_USER,
        REMARK,
        IS_USED,
        RIGHTS_RECORD_ID,
        RIGHTS_NAME_CN,
        RIGHTS_DESC_CN,
        RIGHTS_NAME_EN,
        RIGHTS_DESC_EN,
        ONLY_DISPLAY,
        RULE_ID,
        RECEIVE_NUMBER,
        BACKCOLOR,
        DESCRIBE,
        ICON_URL
    </sql>

    <insert id="addRights">
        INSERT INTO PP_MEMBER_RIGHTS (
        <include refid="Base_Column_List" />
        ) VALUES (
        #{id, jdbcType=VARCHAR}, #{name, jdbcType=VARCHAR}, #{subname, jdbcType=VARCHAR}, #{rightsLevel, jdbcType=VARCHAR}, #{orderNo, jdbcType=INTEGER},
        #{url, jdbcType=VARCHAR}, #{channelCode, jdbcType=VARCHAR}, #{enableIconUrl, jdbcType=VARCHAR}, #{disableIconUrl, jdbcType=VARCHAR},
        #{createTime, jdbcType=TIMESTAMP}, #{createUser, jdbcType=VARCHAR}, #{updateTime, jdbcType=TIMESTAMP}, #{updateUser, jdbcType=VARCHAR},
        #{remark, jdbcType=VARCHAR}, #{isUsed, jdbcType=VARCHAR}, #{rightsRecordId, jdbcType=INTEGER}, #{rightsNameCn, jdbcType=VARCHAR},
        #{rightsDescCn, jdbcType=VARCHAR}, #{rightsNameEn, jdbcType=VARCHAR}, #{rightsDescEn, jdbcType=VARCHAR},
        #{onlyDisplay, jdbcType=VARCHAR}, #{ruleId, jdbcType=VARCHAR}, #{receiveNumber, jdbcType=INTEGER}, #{backcolor, jdbcType=VARCHAR},
        #{describe, jdbcType=VARCHAR}, #{iconUrl, jdbcType=VARCHAR}
        )
    </insert>

    <insert id="insertSingle">
        insert into
        PP_MEMBER_RIGHTS(ID,NAME,SUBNAME,URL,CREATE_TIME,CREATE_USER,IS_USED,RIGHTS_RECORD_ID,RIGHTS_NAME_CN,RIGHTS_DESC_CN,RIGHTS_NAME_EN,RIGHTS_DESC_EN,ONLY_DISPLAY,RULE_ID,RECEIVE_NUMBER)
        (SELECT
        #{item.id,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR},
        #{item.subname,jdbcType=VARCHAR},
        #{item.url,jdbcType=VARCHAR},
        #{item.createTime,jdbcType=DATE},
        #{item.createUser,jdbcType=VARCHAR},
        #{item.isUsed,jdbcType=VARCHAR},
        #{item.rightsRecordId,jdbcType=VARCHAR},
        #{item.rightsNameCn,jdbcType=VARCHAR},
        #{item.rightsDescCn,jdbcType=VARCHAR},
        #{item.rightsNameEn,jdbcType=VARCHAR},
        #{item.rightsDescEn,jdbcType=VARCHAR},
        #{item.onlyDisplay,jdbcType=VARCHAR},
        #{item.ruleId,jdbcType=VARCHAR},
        #{item.receiveNumber,jdbcType=NUMERIC}
        FROM dual)
    </insert>

    <update id="updateRightsById">
        UPDATE PP_MEMBER_RIGHTS
        <set>
            <if test="name != null">NAME = #{name},</if>
            <if test="subname != null">SUBNAME = #{subname},</if>
            <if test="rightsLevel != null">RIGHTS_LEVEL = #{rightsLevel},</if>
            <if test="url != null">URL = #{url},</if>
            <if test="channelCode != null">CHANNEL_CODE = #{channelCode},</if>
            <if test="enableIconUrl != null">ENABLE_ICON_URL = #{enableIconUrl},</if>
            <if test="disableIconUrl != null">DISABLE_ICON_URL = #{disableIconUrl},</if>
            <if test="createUser != null">CREATE_USER = #{createUser},</if>
            <if test="updateUser != null">UPDATE_USER = #{updateUser},</if>
            <if test="remark != null">REMARK = #{remark},</if>
            <if test="isUsed != null">IS_USED = #{isUsed},</if>
            <if test="onlyDisplay != null">ONLY_DISPLAY = #{onlyDisplay},</if>
            <if test="ruleId != null">RULE_ID = #{ruleId},</if>
            <if test="backcolor != null">BACKCOLOR = #{backcolor},</if>
            <if test="describe != null">DESCRIBE = #{describe},</if>
            <if test="rightsNameCn != null">RIGHTS_NAME_CN = #{rightsNameCn},</if>
            <if test="rightsDescCn != null">RIGHTS_DESC_CN = #{rightsDescCn},</if>
            <if test="rightsNameEn != null">RIGHTS_NAME_EN = #{rightsNameEn},</if>
            <if test="rightsDescEn != null">RIGHTS_DESC_EN = #{rightsDescEn},</if>
            <if test="iconUrl != null">ICON_URL = #{iconUrl},</if>
            <if test="orderNo != null">ORDER_NO = #{orderNo},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="receiveNumber != null">RECEIVE_NUMBER = #{receiveNumber},</if>
            <if test="rightsRecordId != null">RIGHTS_RECORD_ID = #{rightsRecordId},</if>
        </set>
        WHERE ID = #{id}
    </update>

    <update id="updateSingle">
        UPDATE PP_MEMBER_RIGHTS
        <set>
            RIGHTS_NAME_CN = #{item.rightsNameCn,jdbcType=VARCHAR},
            RIGHTS_DESC_CN = #{item.rightsDescCn,jdbcType=VARCHAR},
            RIGHTS_NAME_EN = #{item.rightsNameEn,jdbcType=VARCHAR},
            RIGHTS_DESC_EN = #{item.rightsDescEn,jdbcType=VARCHAR},
            ONLY_DISPLAY = #{item.onlyDisplay,jdbcType=VARCHAR},
            UPDATE_USER = #{item.updateUser,jdbcType=VARCHAR},
            UPDATE_TIME = #{item.updateTime,jdbcType=DATE},
            RECEIVE_NUMBER = #{item.receiveNumber,jdbcType=NUMERIC}
        </set>
        WHERE RULE_ID = #{item.ruleId}
        AND  RIGHTS_RECORD_ID = #{item.rightsRecordId}
    </update>

    <delete id="deleteSingle">
        DELETE from PP_MEMBER_RIGHTS
        WHERE RULE_ID = #{item.ruleId}
        AND  RIGHTS_RECORD_ID = #{item.rightsRecordId}
    </delete>

    <delete id="deleteSingleByRuleId">
        DELETE from PP_MEMBER_RIGHTS
        WHERE RULE_ID = #{item.ruleId}
    </delete>

    <select id="memberRightsListPageQuery" resultType="com.juneyaoair.manage.b2c.entity.MemberRightsPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM PP_MEMBER_RIGHTS
        <where>
            <!--过滤已删除数据 -->
            AND (IS_USED != 'N' OR IS_USED IS NULL)

            <!-- 渠道精确匹配 -->
            <if test="channelCode != null and channelCode != ''">
                AND CHANNEL_CODE = #{channelCode}
            </if>

            <!-- 权益所属等级精确匹配 -->
            <if test="ruleId != null and ruleId != ''">
                AND RULE_ID = #{ruleId}
            </if>

            <!-- 权益标题模糊查询 -->
            <if test="name != null and name != ''">
                AND NAME LIKE '%' || #{name} || '%'
            </if>

            <!-- 地址模糊查询 -->
            <if test="url != null and url != ''">
                AND URL LIKE '%' || #{url} || '%'
            </if>
        </where>
        ORDER BY ORDER_NO ASC
    </select>

    <select id="getRightsById" resultType="com.juneyaoair.manage.b2c.entity.MemberRightsPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM PP_MEMBER_RIGHTS
        WHERE ID = #{id}
    </select>

    <select id="selectByRuleId" resultType="com.juneyaoair.manage.b2c.entity.MemberRightsPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM PP_MEMBER_RIGHTS
        WHERE RULE_ID = #{ruleId}
        AND IS_USED = #{isUsed}
    </select>
</mapper>