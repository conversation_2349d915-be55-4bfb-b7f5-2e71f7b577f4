<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.I18nDictionaryValuePOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.I18nDictionaryValuePO">
    <!--@mbg.generated-->
    <!--@Table I18N_DICTIONARY_VALUE-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="DICTIONARY_TYPE" jdbcType="VARCHAR" property="dictionaryType" />
    <result column="ORIGINAL" jdbcType="VARCHAR" property="original" />
    <result column="TRANSLATION" jdbcType="VARCHAR" property="translation" />
    <result column="LANGUAGE_TAG" jdbcType="VARCHAR" property="languageTag" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, DICTIONARY_TYPE, ORIGINAL, "TRANSLATION", LANGUAGE_TAG, REMARK, CREATED_TIME
  </sql>

    <insert id="insertByUnicode">
        INSERT INTO I18N_DICTIONARY_VALUE(ID, DICTIONARY_TYPE, ORIGINAL, "TRANSLATION", LANGUAGE_TAG, REMARK,
                                          CREATED_TIME)
        VALUES (#{id},
                #{dictionaryType},
                #{original,jdbcType=NVARCHAR},
                #{translation,jdbcType=NVARCHAR},
                #{languageTag},
                #{remark,jdbcType=VARCHAR},
                #{createdTime})
    </insert>

    <select id="selectByDictionaryType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from I18N_DICTIONARY_VALUE
        where DICTIONARY_TYPE = #{dictionaryType}
    </select>

    <select id="selectByOriginalAndLanguageTag" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from I18N_DICTIONARY_VALUE
        where ORIGINAL = #{key} and LANGUAGE_TAG = #{languageEnum}
    </select>

    <select id="queryByDictionaryTypeAndOriginal" resultMap="BaseResultMap">
        SELECT
            ID,
            DICTIONARY_TYPE,
            ORIGINAL,
            TRANSLATION,
            LANGUAGE_TAG,
            REMARK,
            CREATED_TIME
        FROM I18N_DICTIONARY_VALUE
        WHERE DICTIONARY_TYPE = #{dictionaryType}
        <if test="original != null and original != ''">
            <choose>
                <when test="fuzzyOriginal != null and fuzzyOriginal == true">
                    AND ORIGINAL LIKE '%' || #{original} || '%'
                </when>
                <otherwise>
                    AND ORIGINAL = #{original}
                </otherwise>
            </choose>
        </if>
        <if test="languageTag != null and languageTag != ''">
            AND LANGUAGE_TAG = #{languageTag}
        </if>
        <if test="translation != null and translation != ''">
            AND "TRANSLATION" LIKE '%' || #{translation} || '%'
        </if>
        ORDER BY CREATED_TIME DESC
    </select>

    <update id="updateByUnicode">
        UPDATE I18N_DICTIONARY_VALUE
        SET DICTIONARY_TYPE = #{dictionaryType},
            ORIGINAL = #{original,jdbcType=NVARCHAR},
            "TRANSLATION" = #{translation,jdbcType=NVARCHAR},
            LANGUAGE_TAG = #{languageTag},
            REMARK = #{remark,jdbcType=VARCHAR}
        WHERE ID = #{id}
    </update>
</mapper>