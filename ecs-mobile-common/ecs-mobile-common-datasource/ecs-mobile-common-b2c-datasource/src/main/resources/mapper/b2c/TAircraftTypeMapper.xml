<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TAircraftTypeMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.AircraftTypePO">
        <id column="AIRCRAFT_TYPE_ID" jdbcType="VARCHAR" property="aircraftTypeId"/>
        <result column="AIRCRAFT_TYPE_CODE" jdbcType="VARCHAR" property="aircraftTypeCode"/>
        <result column="AIRCRAFT_TYPE_NAME" jdbcType="VARCHAR" property="aircraftTypeName"/>
        <result column="AIRCRAFT_ICON" jdbcType="VARCHAR" property="aircraftIcon"/>
        <result column="BUSINESS_CLASS_NUM" jdbcType="DECIMAL" property="businessClassNum"/>
        <result column="ECONOMY_CLASS_NUM" jdbcType="DECIMAL" property="economyClassNum"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="CREATETIME" jdbcType="TIMESTAMP" property="createtime"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="UPDATETIME" jdbcType="TIMESTAMP" property="updatetime"/>
    </resultMap>
    <select id="selectByAll" resultMap="BaseResultMap">
         select tat.AIRCRAFT_TYPE_ID,
                tat.AIRCRAFT_TYPE_CODE,
                tat.AIRCRAFT_TYPE_NAME,
                tat.AIRCRAFT_ICON,
                tat.BUSINESS_CLASS_NUM,
                tat.ECONOMY_CLASS_NUM,
                tat.STATUS,
                tat.CREATE_USER,
                tat.CREATETIME,
                tat.UPDATE_USER,
                tat.UPDATETIME
           from T_AIRCRAFT_TYPE tat
        <where>
            <if test="aircraftTypeId != null and aircraftTypeId != ''">
                and tat.AIRCRAFT_TYPE_ID = #{aircraftTypeId}
            </if>
            <if test="aircraftTypeCode != null and aircraftTypeCode != ''">
                and exists (select am.AIRCRAFT_TYPE_ID from T_AIRCRAFT_MAPPING am
                          where am.AIRCRAFT_TYPE_ID = tat.AIRCRAFT_TYPE_ID
                            and am.AIRCRAFT_CODE = #{aircraftTypeCode}
                    <if test="status != null and status != ''">
                        and am.STATUS = #{status}
                    </if>
                    )
            </if>
            <if test="status != null and status != ''">
                and tat.STATUS = #{status}
            </if>
        </where>
        ORDER BY tat.STATUS DESC, tat.UPDATETIME DESC
    </select>

    <update id="updateTypeStatus" parameterType="java.util.Map">
        UPDATE T_AIRCRAFT_TYPE
           SET STATUS = #{status},
               UPDATE_USER = #{username},
               UPDATETIME = sysdate
         where AIRCRAFT_TYPE_ID = #{aircraftTypeId}
    </update>

    <update id="updateMappingStatus" parameterType="java.util.Map">
        UPDATE T_AIRCRAFT_MAPPING
           SET STATUS = #{status},
               UPDATE_USER = #{username},
               UPDATETIME = sysdate
         where AIRCRAFT_TYPE_ID = #{aircraftTypeId}
    </update>

</mapper>