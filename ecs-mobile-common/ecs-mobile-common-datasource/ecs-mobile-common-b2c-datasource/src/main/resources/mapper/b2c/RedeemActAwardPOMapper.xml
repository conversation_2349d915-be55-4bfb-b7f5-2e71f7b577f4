<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.RedeemActAwardPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.RedeemActAwardPO">
    <!--@mbg.generated-->
    <!--@Table T_REDEEM_ACT_AWARD-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ACT_ID" jdbcType="VARCHAR" property="actId" />
    <result column="AWARD_ID" jdbcType="VARCHAR" property="awardId" />
    <result column="AWARD_NAME" jdbcType="VARCHAR" property="awardName" />
    <result column="AWARD_DESC" jdbcType="VARCHAR" property="awardDesc" />
    <result column="MONTHLY_LIMIT" jdbcType="VARCHAR" property="monthlyLimit" />
    <result column="CONSUMED_POINT" jdbcType="VARCHAR" property="consumedPoint" />
    <result column="CONSUMED_POINT_TYPE" jdbcType="VARCHAR" property="consumedPointType" />
    <result column="AWARD_ENUM_TYPE" jdbcType="VARCHAR" property="awardEnumType" />
    <result column="AWARD_ENUM_TYPE_ATTR" jdbcType="VARCHAR" property="awardEnumTypeAttr" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ACT_ID, AWARD_ID, AWARD_NAME, AWARD_DESC, MONTHLY_LIMIT, CONSUMED_POINT, CONSUMED_POINT_TYPE, 
    AWARD_ENUM_TYPE, AWARD_ENUM_TYPE_ATTR, CREATED_BY, UPDATED_BY, CREATED_TIME, UPDATED_TIME
  </sql>

  <select id="selectByActId" resultMap="BaseResultMap">
    SELECT *
    FROM T_REDEEM_ACT_AWARD
    WHERE ACT_ID = #{id,jdbcType=VARCHAR}
  </select>
</mapper>