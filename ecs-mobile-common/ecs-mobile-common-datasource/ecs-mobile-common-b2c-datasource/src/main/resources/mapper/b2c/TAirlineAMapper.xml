<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TAirlineAMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.AirlineAPO">
        <id column="AIRLINE_ID" jdbcType="VARCHAR" property="airlineId"/>
        <result column="DEP_AIRPORT" jdbcType="VARCHAR" property="depAirport"/>
        <result column="DEP_CITY" jdbcType="VARCHAR" property="depCity"/>
        <result column="ARR_AIRPORT" jdbcType="VARCHAR" property="arrAirport"/>
        <result column="ARR_CITY" jdbcType="VARCHAR" property="arrCity"/>
        <result column="CARRIER_COMPANY" jdbcType="VARCHAR" property="carrierCompany"/>
        <result column="IS_INTERNATIONAL_AIRLINE" jdbcType="VARCHAR" property="isInternationalAirline"/>
        <result column="IS_TRANSIT" jdbcType="VARCHAR" property="isTransit"/>
        <result column="TRANSIT_CITY" jdbcType="VARCHAR" property="transitCity"/>
        <result column="TRANSIT_AIRPORT" jdbcType="VARCHAR" property="transitAirport"/>
        <result column="IS_HO_LINE" jdbcType="VARCHAR" property="isHoLine"/>
        <result column="AIRLINE_FRONT_REMARK" jdbcType="VARCHAR" property="airlineFrontRemark"/>
        <result column="CREATE_MAN" jdbcType="VARCHAR" property="createMan"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="DELFLAG" jdbcType="CHAR" property="delflag"/>
        <result column="ADDON_REMARK" jdbcType="CHAR" property="addonRemark"/>
        <result column="AIRLINE_BEGINDATE" jdbcType="TIMESTAMP" property="airlineBegindate"/>
        <result column="AIRLINE_ENDDATE" jdbcType="TIMESTAMP" property="airlineEnddate"/>
        <result column="IS_BAGGAGE_DIRECT" jdbcType="VARCHAR" property="isBaggageDirect"/>
    </resultMap>
    <sql id="Base_Column_List">
        AIRLINE_ID,
        DEP_AIRPORT,
        DEP_CITY,
        ARR_AIRPORT,
        ARR_CITY,
        CARRIER_COMPANY,
        IS_INTERNATIONAL_AIRLINE,
        IS_TRANSIT,
        TRANSIT_CITY,
        TRANSIT_AIRPORT,
        IS_HO_LINE,
        AIRLINE_FRONT_REMARK,
        CREATE_MAN,
        CREATE_TIME,
        DELFLAG,
        ADDON_REMARK,
        AIRLINE_BEGINDATE,
        AIRLINE_ENDDATE,
        IS_BAGGAGE_DIRECT
    </sql>
    <select id="selectByLabelName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from (
        select a.* from T_AIRLINE_A a
        inner join T_AIRLINE_LABEL l on
        a.AIRLINE_ID = l.AIRLINE_ID
        where l.LABEL_NAME = #{labelName}
        ) s order by s.CREATE_TIME
    </select>

    <select id="selectByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AIRLINE_A
        <where>
            <if test="airlineId != null">
                and AIRLINE_ID = #{airlineId,jdbcType=VARCHAR}
            </if>
            <if test="depAirport != null">
                and DEP_AIRPORT = #{depAirport,jdbcType=VARCHAR}
            </if>
            <if test="depCity != null">
                and DEP_CITY = #{depCity,jdbcType=VARCHAR}
            </if>
            <if test="arrAirport != null">
                and ARR_AIRPORT = #{arrAirport,jdbcType=VARCHAR}
            </if>
            <if test="arrCity != null">
                and ARR_CITY = #{arrCity,jdbcType=VARCHAR}
            </if>
            <if test="carrierCompany != null">
                and CARRIER_COMPANY = #{carrierCompany,jdbcType=VARCHAR}
            </if>
            <if test="isInternationalAirline != null">
                and IS_INTERNATIONAL_AIRLINE = #{isInternationalAirline,jdbcType=VARCHAR}
            </if>
            <if test="isTransit != null">
                and IS_TRANSIT = #{isTransit,jdbcType=VARCHAR}
            </if>
            <if test="transitCity != null">
                and TRANSIT_CITY = #{transitCity,jdbcType=VARCHAR}
            </if>
            <if test="transitAirport != null">
                and TRANSIT_AIRPORT = #{transitAirport,jdbcType=VARCHAR}
            </if>
            <if test="isHoLine != null">
                and IS_HO_LINE = #{isHoLine,jdbcType=VARCHAR}
            </if>
            <if test="airlineFrontRemark != null">
                and AIRLINE_FRONT_REMARK = #{airlineFrontRemark,jdbcType=VARCHAR}
            </if>
            <if test="createMan != null">
                and CREATE_MAN = #{createMan,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="delflag != null">
                and DELFLAG = #{delflag,jdbcType=CHAR}
            </if>
            <if test="addonRemark != null">
                and ADDON_REMARK = #{addonRemark,jdbcType=CHAR}
            </if>
            <if test="airlineBegindate != null">
                and AIRLINE_BEGINDATE = #{airlineBegindate,jdbcType=TIMESTAMP}
            </if>
            <if test="airlineEnddate != null">
                and AIRLINE_ENDDATE = #{airlineEnddate,jdbcType=TIMESTAMP}
            </if>
            <if test="isBaggageDirect != null">
                and IS_BAGGAGE_DIRECT = #{isBaggageDirect,jdbcType=VARCHAR}
            </if>
        </where>
        order by CREATE_TIME DESC
    </select>
    <delete id="deleteByAirlineId">
        delete from  T_AIRLINE_A
        where AIRLINE_ID=#{airlineId,jdbcType=VARCHAR}
    </delete>
    <select id="selectByDelflagAndIsTransitAndAddonRemark" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from T_AIRLINE_A
        where DELFLAG=#{delflag,jdbcType=CHAR} and IS_TRANSIT=#{isTransit,jdbcType=VARCHAR} and ADDON_REMARK=#{addonRemark,jdbcType=CHAR}
    </select>
</mapper>