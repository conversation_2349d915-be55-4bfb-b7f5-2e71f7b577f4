<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TEpidemicInfoMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.EpidemicInfoPO">

    <!--@Table T_EPIDEMIC_INFO-->
    <id column="CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
    <result column="JUMP_TYPE" jdbcType="VARCHAR" property="jumpType" />
    <result column="JUMP_URL" jdbcType="VARCHAR" property="jumpUrl" />
    <result column="PIC_TIP" jdbcType="VARCHAR" property="picTip" />
  </resultMap>
  <sql id="Base_Column_List">

    C<PERSON>Y_CODE, JUMP_TYPE, JUMP_URL, PIC_TIP
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">

    select 
    <include refid="Base_Column_List" />
    from T_EPIDEMIC_INFO
    where CITY_CODE = #{cityCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from T_EPIDEMIC_INFO
    where CITY_CODE = #{cityCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.juneyaoair.manage.b2c.entity.EpidemicInfoPO">

    insert into T_EPIDEMIC_INFO (CITY_CODE, JUMP_TYPE, JUMP_URL, 
      PIC_TIP)
    values (#{cityCode,jdbcType=VARCHAR}, #{jumpType,jdbcType=VARCHAR}, #{jumpUrl,jdbcType=VARCHAR}, 
      #{picTip,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.juneyaoair.manage.b2c.entity.EpidemicInfoPO">

    insert into T_EPIDEMIC_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cityCode != null">
        CITY_CODE,
      </if>
      <if test="jumpType != null">
        JUMP_TYPE,
      </if>
      <if test="jumpUrl != null">
        JUMP_URL,
      </if>
      <if test="picTip != null">
        PIC_TIP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="jumpType != null">
        #{jumpType,jdbcType=VARCHAR},
      </if>
      <if test="jumpUrl != null">
        #{jumpUrl,jdbcType=VARCHAR},
      </if>
      <if test="picTip != null">
        #{picTip,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.EpidemicInfoPO">

    update T_EPIDEMIC_INFO
    <set>
      <if test="jumpType != null">
        JUMP_TYPE = #{jumpType,jdbcType=VARCHAR},
      </if>
      <if test="jumpUrl != null">
        JUMP_URL = #{jumpUrl,jdbcType=VARCHAR},
      </if>
      <if test="picTip != null">
        PIC_TIP = #{picTip,jdbcType=VARCHAR},
      </if>
    </set>
    where CITY_CODE = #{cityCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.juneyaoair.manage.b2c.entity.EpidemicInfoPO">
    <!--@mbg.generated-->
    update T_EPIDEMIC_INFO
    set JUMP_TYPE = #{jumpType,jdbcType=VARCHAR},
      JUMP_URL = #{jumpUrl,jdbcType=VARCHAR},
      PIC_TIP = #{picTip,jdbcType=VARCHAR}
    where CITY_CODE = #{cityCode,jdbcType=VARCHAR}
  </update>
</mapper>