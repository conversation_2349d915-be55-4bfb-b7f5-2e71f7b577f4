<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.DictTypesMapper">
    <!-- 数据库表tfb_dicttype与 DICTType实体类的映射关系 -->
    <!-- dtid主键，dtcode编号， dtname名称，dtdescription描述，enable是否有效 -->
    <resultMap type="com.juneyaoair.ecs.manage.dto.datadict.DICTType" id="dICTType">
        <id column="dtid" property="dtid"/>
        <result column="dtcode" property="dtCode"/>
        <result column="dtname" property="dtName"/>
        <result column="dtdescription" property="dtDescription"/>
        <result column="enable" property="enable"/>
    </resultMap>

    <!-- 查询数据字典类型DICTType -->
    <!-- dtid主键，dtcode编号， dtname名称，dtdescription描述，enable是否有效 -->
    <select id="toCatchDictTypeList" parameterType="com.juneyaoair.ecs.manage.dto.datadict.DICTType" resultMap="dICTType">
        select dtid, dtcode, dtname, dtdescription, enable from
        tp_dicttype
        <where>
            <if test="dtid != null and dtid != ''">
                and dtid = #{dtid}
            </if>
            <if test="dtCode != null and dtCode != ''">
                and dtcode = #{dtCode}
            </if>
            <if test="dtName != null and dtName != ''">
                and dtname like '%${dtName}%'
            </if>
            <if test="dtDescription != null and dtDescription != ''">
                and dtdescription = #{dtDescription}
            </if>
            <if test="enable != null and enable != ''">
                and enable = #{enable}
            </if>
        </where>
        order by dtcode
    </select>

    <!-- 更新数据字典类型 -->
    <!-- dtid主键，dtcode编号， dtname名称，dtdescription描述，enable是否有效 -->
    <update id="toUpdateType" parameterType="com.juneyaoair.ecs.manage.dto.datadict.DICTType">
        update tp_dicttype
        <set>
            <if test="dtid != null and dtid != ''">
                dtid = #{dtid},
            </if>
            <if test="dtCode != null and dtCode != ''">
                dtcode = #{dtCode},
            </if>
            <if test="dtName != null and dtName != ''">
                dtname = #{dtName},
            </if>
            <if test="dtDescription != null and dtDescription != ''">
                dtdescription =#{dtDescription},
            </if>
            <if test="enable != null and enable != ''">
                enable = #{enable}
            </if>
        </set>
        <where>
            <if test="dtid != null and dtid != ''">
                and dtid = #{dtid}
            </if>
        </where>
    </update>

    <!-- 新增数据字典类型 DICTType -->
    <!-- dtid主键，dtcode编号， dtname名称，dtdescription描述，enable是否有效 -->
    <insert id="toAddType" parameterType="com.juneyaoair.ecs.manage.dto.datadict.DICTType">
        insert into
            tp_dicttype
            (dtid, dtcode, dtname, dtdescription, enable)
        values
            (#{dtid,jdbcType=VARCHAR},
             #{dtCode,jdbcType=VARCHAR},
             #{dtName,jdbcType=VARCHAR}, #{dtDescription,jdbcType=VARCHAR},
             #{enable,jdbcType=VARCHAR})
    </insert>

</mapper>
