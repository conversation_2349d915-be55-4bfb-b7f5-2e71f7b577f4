<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TSaleOfficeMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.saleoffice.SaleOfficePO">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="cityCode" column="CITY_CODE" jdbcType="VARCHAR"/>
            <result property="address" column="ADDRESS" jdbcType="VARCHAR"/>
            <result property="officeTel" column="OFFICE_TEL" jdbcType="VARCHAR"/>
            <result property="fax" column="FAX" jdbcType="VARCHAR"/>
            <result property="officeTimeDesc" column="OFFICE_TIME_DESC" jdbcType="VARCHAR"/>
            <result property="counterTel" column="COUNTER_TEL" jdbcType="VARCHAR"/>
            <result property="dutyTel" column="DUTY_TEL" jdbcType="VARCHAR"/>
            <result property="officeEmail" column="OFFICE_EMAIL" jdbcType="VARCHAR"/>
            <result property="deleteFlag" column="DELETE_FLAG" jdbcType="CHAR"/>
            <result property="region" column="REGION" jdbcType="VARCHAR"/>
            <result property="createDatetime" column="CREATE_DATETIME" jdbcType="TIMESTAMP"/>
            <result property="createMan" column="CREATE_MAN" jdbcType="VARCHAR"/>
            <result property="updateDatetime" column="UPDATE_DATETIME" jdbcType="TIMESTAMP"/>
            <result property="updateMan" column="UPDATE_MAN" jdbcType="VARCHAR"/>
            <result property="seq" column="SEQ" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,NAME,CITY_CODE,ADDRESS,
        OFFICE_TEL,FAX,OFFICE_TIME_DESC,
        COUNTER_TEL,DUTY_TEL,OFFICE_EMAIL,DELETE_FLAG,
        REGION,CREATE_DATETIME,CREATE_MAN,
        UPDATE_DATETIME,UPDATE_MAN,SEQ
    </sql>
</mapper>
