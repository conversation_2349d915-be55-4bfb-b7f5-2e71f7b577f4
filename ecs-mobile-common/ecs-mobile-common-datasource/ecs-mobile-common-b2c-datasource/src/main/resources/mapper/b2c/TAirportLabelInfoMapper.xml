<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TAirportLabelInfoMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.AirportLabelInfoPO">
        <!--@Table T_AIRPORT_LABEL_INFO-->
        <id column="AIRPORT_LABEL_ID" jdbcType="DECIMAL" property="airportLabelId"/>
        <result column="AIRPORT_CODE" jdbcType="VARCHAR" property="airportCode"/>
        <result column="AIRPORT_LABEL_NAME" jdbcType="VARCHAR" property="airportLabelName"/>
        <result column="AIRPORT_LABEL_URL" jdbcType="VARCHAR" property="airportLabelUrl"/>
        <result column="LABEL_INTRODUCE" jdbcType="VARCHAR" property="labelIntroduce"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="CREATETIME" jdbcType="TIMESTAMP" property="createtime"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="UPDATETIME" jdbcType="TIMESTAMP" property="updatetime"/>
    </resultMap>
    <sql id="Base_Column_List">
        AIRPORT_LABEL_ID,
        AIRPORT_CODE,
        AIRPORT_LABEL_NAME,
        AIRPORT_LABEL_URL,
        LABEL_INTRODUCE,
        CREATE_USER,
        CREATETIME,
        UPDATE_USER,
        UPDATETIME
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.math.BigDecimal" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AIRPORT_LABEL_INFO
        where AIRPORT_LABEL_ID = #{airportLabelId,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal">
        delete
        from T_AIRPORT_LABEL_INFO
        where AIRPORT_LABEL_ID = #{airportLabelId,jdbcType=DECIMAL}
    </delete>
    <insert id="insert" parameterType="com.juneyaoair.manage.b2c.entity.AirportLabelInfoPO">
        insert into T_AIRPORT_LABEL_INFO (AIRPORT_LABEL_ID, AIRPORT_CODE, AIRPORT_LABEL_NAME,
                                          AIRPORT_LABEL_URL, LABEL_INTRODUCE, CREATE_USER,
                                          CREATETIME, UPDATE_USER, UPDATETIME)
        values (SEQ_AIRPORT_LABEL_INFO.nextval, #{airportCode,jdbcType=VARCHAR},
                #{airportLabelName,jdbcType=VARCHAR},
                #{airportLabelUrl,jdbcType=VARCHAR}, #{labelIntroduce,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR},
                #{createtime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updatetime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.juneyaoair.manage.b2c.entity.AirportLabelInfoPO">
        insert into T_AIRPORT_LABEL_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            AIRPORT_LABEL_ID,
            <if test="airportCode != null">
                AIRPORT_CODE,
            </if>
            <if test="airportLabelName != null">
                AIRPORT_LABEL_NAME,
            </if>
            <if test="airportLabelUrl != null">
                AIRPORT_LABEL_URL,
            </if>
            <if test="labelIntroduce != null">
                LABEL_INTRODUCE,
            </if>
            <if test="createUser != null">
                CREATE_USER,
            </if>
            <if test="createtime != null">
                CREATETIME,
            </if>
            <if test="updateUser != null">
                UPDATE_USER,
            </if>
            <if test="updatetime != null">
                UPDATETIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{airportLabelId,jdbcType=DECIMAL},
            <if test="airportCode != null">
                #{airportCode,jdbcType=VARCHAR},
            </if>
            <if test="airportLabelName != null">
                #{airportLabelName,jdbcType=VARCHAR},
            </if>
            <if test="airportLabelUrl != null">
                #{airportLabelUrl,jdbcType=VARCHAR},
            </if>
            <if test="labelIntroduce != null">
                #{labelIntroduce,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createtime != null">
                #{createtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updatetime != null">
                #{updatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.AirportLabelInfoPO">
        update T_AIRPORT_LABEL_INFO
        <set>
            <if test="airportCode != null">
                AIRPORT_CODE = #{airportCode,jdbcType=VARCHAR},
            </if>
            <if test="airportLabelName != null">
                AIRPORT_LABEL_NAME = #{airportLabelName,jdbcType=VARCHAR},
            </if>
            <if test="airportLabelUrl != null">
                AIRPORT_LABEL_URL = #{airportLabelUrl,jdbcType=VARCHAR},
            </if>
            <if test="labelIntroduce != null">
                LABEL_INTRODUCE = #{labelIntroduce,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createtime != null">
                CREATETIME = #{createtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updatetime != null">
                UPDATETIME = #{updatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where AIRPORT_LABEL_ID = #{airportLabelId,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.juneyaoair.manage.b2c.entity.AirportLabelInfoPO">
        update T_AIRPORT_LABEL_INFO
        set AIRPORT_CODE       = #{airportCode,jdbcType=VARCHAR},
            AIRPORT_LABEL_NAME = #{airportLabelName,jdbcType=VARCHAR},
            AIRPORT_LABEL_URL  = #{airportLabelUrl,jdbcType=VARCHAR},
            LABEL_INTRODUCE    = #{labelIntroduce,jdbcType=VARCHAR},
            CREATE_USER        = #{createUser,jdbcType=VARCHAR},
            CREATETIME         = #{createtime,jdbcType=TIMESTAMP},
            UPDATE_USER        = #{updateUser,jdbcType=VARCHAR},
            UPDATETIME         = #{updatetime,jdbcType=TIMESTAMP}
        where AIRPORT_LABEL_ID = #{airportLabelId,jdbcType=DECIMAL}
    </update>
    <select id="selectByAirportCodeIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AIRPORT_LABEL_INFO
                where AIRPORT_CODE in
        <foreach item="item" index="index" collection="airportCodeCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>