<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.ModularMapper">

    <resultMap type="com.juneyaoair.ecs.manage.dto.modular.ModularDTO" id="moduleListResult">
        <id column="ID" property="id"></id>
        <result column="URL" property="url"></result>
        <result column="NAME" property="name"></result>
        <result column="MODULAR_BM" property="modularBm"></result>
        <result column="ICONURL" property="iconurl"></result>
        <result column="ORDER_NO" property="orderNo"></result>
        <result column="REMARK" property="remark"></result>
        <result column="IOS" property="ios"></result>
        <result column="ANDROID" property="android"></result>
        <result column="HARMONY" property="harmony"></result>
        <result column="SECONDARY" property="secondary"></result>
        <result column="DISPLAY_NEW" property="displayNew"></result>
        <result column="CHANNEL_CODE" property="channelCode" jdbcType="VARCHAR"/>
        <result column="LOGIN_FLAG" property="loginFlag" jdbcType="VARCHAR"/>
        <result column="CORNER_ICON_URL" property="cornerIconUrl" jdbcType="VARCHAR"/>
        <result column="DVNAME" property="modularName"></result>
        <result column="EVENTMODULE" property="eventModule"></result>
        <result column="EVENTTYPE" property="eventType"></result>
        <result column="WINNAME" property="winName"></result>
        <result column="CREATE_TIME" property="createTime"></result>
        <result column="UPDATE_TIME" property="updateTime"></result>
        <result column="IS_USED" property="isUsed"></result>
    </resultMap>

    <sql id="selectModuleSql">
        SELECT ID,
        NAME,
        URL,
        ICONURL,
        IS_USED,
        MODULAR_BM,
        ORDER_NO,
        REMARK,
        IOS,
        ANDROID,
        HARMONY,
        EVENTMODULE,
        EVENTTYPE,
        WINNAME,
        SECONDARY,
        DISPLAY_NEW,
        CHANNEL_CODE,
        LOGIN_FLAG,
        CORNER_ICON_URL
        FROM PP_MODULAR T
    </sql>

    <select id="selectModularList" parameterType="com.juneyaoair.manage.b2c.entity.ModularPO"
            resultMap="moduleListResult">
        SELECT T.*,a.DVNAME
        FROM PP_MODULAR T
        INNER JOIN TP_DICTVALUE a ON T.MODULAR_BM = a.DVCODE
        <where>
            T.IS_USED = 'Y'
            <if test="name != null and name != ''">
                AND T.name like concat(concat('%',#{name,jdbcType=VARCHAR}),'%')
            </if>
            <if test="url != null and url != ''">
                AND T.url like concat(concat('%',#{url,jdbcType=VARCHAR}),'%')
            </if>
            <if test="modularBm != null and modularBm != ''">
                AND T.MODULAR_BM = #{modularBm,jdbcType=VARCHAR}
            </if>
            <if test="channelCode!= null and channelCode != ''">
                AND T.CHANNEL_CODE like concat(concat('%',#{channelCode,jdbcType=VARCHAR}),'%')
            </if>
        </where>
        ORDER BY T.ORDER_NO ASC, NVL2(T.UPDATE_TIME, T.UPDATE_TIME, T.CREATE_TIME) DESC
    </select>

    <select id="queryName" parameterType="com.juneyaoair.manage.b2c.entity.ModularPO" resultType="com.juneyaoair.manage.b2c.entity.ModularPO">
        SELECT ID,NAME,IOS,ANDROID,HARMONY,CHANNEL_CODE,MODULAR_BM
        FROM PP_MODULAR t
        WHERE t.IS_USED = 'Y'
        AND t.CHANNEL_CODE like '%MOBILE%'
        AND (
            t.IOS like '%${ios}%'
            OR t.ANDROID like '%${android}%'
            OR t.HARMONY like '%${harmony}%'
        )
    </select>

    <select id="queryModifyModular" parameterType="string" resultMap="moduleListResult">
        SELECT T.*,a.DVNAME
        FROM PP_MODULAR T
        LEFT JOIN TP_DICTVALUE a ON T.MODULAR_BM = a.DVCODE
        WHERE T.IS_USED = 'Y'
        AND T.ID = #{id,jdbcType=VARCHAR}
    </select>
</mapper>
