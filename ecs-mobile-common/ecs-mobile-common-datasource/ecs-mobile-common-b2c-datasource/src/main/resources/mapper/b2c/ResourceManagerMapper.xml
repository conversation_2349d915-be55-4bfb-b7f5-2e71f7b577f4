<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.ResourceManagerMapper">

    <sql id="Base_Column_List">
        RECORDID,
        URL,
        MIN_VER,
        MAX_VER,
        PLATFORM,
        ZIP_MD5,
        CREATER,
        CREATE_DATE,
        UPDATEER,
        UPDATE_DATE,
        FILE_IDS,
        DELETE_FLAG,
        MODULE_NAME
    </sql>
    <insert id="addResource">
        INSERT INTO T_RESOURCE_MANA (
        <include refid="Base_Column_List"/>
        ) VALUES (
        #{recordID},
        #{URL},
        #{minVer},
        #{maxVer},
        #{platform},
        #{zipMD5},
        #{creater},
        #{createDate},
        #{updateer},
        #{updateDate},
        #{fileIDS},
        #{deleteFlag},
        #{moduleName}
        )
    </insert>

    <update id="updateResourceById" >
        UPDATE T_RESOURCE_MANA
        <set>
            <if test="URL != null and URL != ''">
                URL = #{URL},
            </if>
            <if test="minVer != null and minVer != ''">
                MIN_VER = #{minVer},
            </if>
            <if test="maxVer != null and maxVer != ''">
                MAX_VER = #{maxVer},
            </if>
            <if test="platform != null and platform != ''">
                PLATFORM = #{platform},
            </if>
            <if test="zipMD5 != null and zipMD5 != ''">
                ZIP_MD5 = #{zipMD5},
            </if>
            <if test="creater != null and creater != ''">
                CREATER = #{creater},
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{createDate},
            </if>
            <if test="updateer != null and updateer != ''">
                UPDATEER = #{updateer},
            </if>
            <if test="updateDate != null">
                UPDATE_DATE = #{updateDate},
            </if>
            <if test="fileIDS != null and fileIDS != ''">
                FILE_IDS = #{fileIDS},
            </if>
            <if test="deleteFlag != null and deleteFlag != ''">
                DELETE_FLAG = #{deleteFlag},
            </if>
            <if test="moduleName != null and moduleName != ''">
                MODULE_NAME = #{moduleName},
            </if>
        </set>
        WHERE RECORDID = #{recordID}
    </update>

    <select id="resourceListPageQuery" resultType="com.juneyaoair.manage.b2c.entity.ResourcePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_RESOURCE_MANA
        <where>

            <!--过滤已删除数据 -->
            AND (DELETE_FLAG != 'D' OR DELETE_FLAG IS NULL)

            <!-- 平台模糊查询 -->
            <if test="platform != null and platform != ''">
                AND PLATFORM LIKE '%' || #{platform} || '%'
            </if>

            <!-- 模块名称模糊查询 -->
            <if test="moduleName != null and moduleName != ''">
                AND MODULE_NAME LIKE '%' || #{moduleName} || '%'
            </if>

            <!-- 版本状态精确匹配 -->
            <if test="deleteFlag != null and deleteFlag != ''">
                AND DELETE_FLAG = #{deleteFlag}
            </if>

            <!-- 最小版本模糊查询 -->
            <if test="minVer != null and minVer != ''">
                AND MIN_VER LIKE '%' || #{minVer} || '%'
            </if>

            <!-- 最大版本模糊查询 -->
            <if test="maxVer != null and maxVer != ''">
                AND MAX_VER LIKE '%' || #{maxVer} || '%'
            </if>
        </where>
        ORDER BY CREATE_DATE DESC
    </select>

    <select id="getResourceById" resultType="com.juneyaoair.manage.b2c.entity.ResourcePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_RESOURCE_MANA
        WHERE RECORDID = #{id}
    </select>
</mapper>