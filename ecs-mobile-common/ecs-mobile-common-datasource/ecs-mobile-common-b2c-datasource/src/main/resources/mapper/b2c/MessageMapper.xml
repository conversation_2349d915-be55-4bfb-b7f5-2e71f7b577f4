<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.MessageMapper">

    <resultMap type="com.juneyaoair.manage.b2c.entity.MessagePO" id="MessageResult">
        <id column="MESSAGEID" property="messageid" jdbcType="VARCHAR"></id>
        <result column="MESSAGE_TITLE" property="messageTitle" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_AUTH" property="messageAuth" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_CONTENT" property="messageContent" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_CONTENT2" property="messageContent2" jdbcType="BLOB"></result>
        <result column="MESSAGE_PUBLISHMAN" property="messagePublishman" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_TYPE" property="messageType" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_PUBLISHTIME" property="messagePublishtime" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_PIC_URL" property="messagePicUrl" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_STATUS" property="messageStatus" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_SENDORNOT" property="messageSendornot" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_CREATETIME" property="messageCreatetime" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_STARTTIME" property="messageStarttime" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_ENDTIME" property="messageEndtime" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_UPDATETIME" property="messageUpdatetime" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_UPDATEMAN" property="messageUpdateman" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_URL" property="messageUrl" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_INFO_TYPE" property="messageInfoType" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_INFO_TYPE_CODE" property="messageInfoTypeCode" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_TYPE_CODE" property="messageTypeCode" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_STATUS_CODE" property="messageStatusCode" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_ISTOP" property="messageIstop" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_ISANNOUNCEMENT" property="messageIsannouncement" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_PLAINTXT" property="messagePlaintxt" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_PUSHTITLE" property="messagePushtitle" jdbcType="VARCHAR"></result>
        <result column="MOBILE_CONTENT" property="mobileContent" jdbcType="BLOB"></result>
        <result column="MESSAGE_TAG_ID" property="messageTagId" jdbcType="VARCHAR"></result>
        <result column="MESSAGE_TYPE_NAME" property="messageTypeName" jdbcType="VARCHAR"></result>
        <result column="LANGUAGE" property="language" jdbcType="VARCHAR"></result>
    </resultMap>

    <sql id="selectMessageSql">
        SELECT  MESSAGEID,
        MESSAGE_TITLE,
        MESSAGE_AUTH,
        MESSAGE_CONTENT,
        MESSAGE_PUBLISHMAN,
        MESSAGE_TYPE,
        MESSAGE_PUBLISHTIME,
        MESSAGE_PIC_URL,
        MESSAGE_STARTTIME,
        MESSAGE_ENDTIME,
        MESSAGE_ISTOP,
        MESSAGE_PLAINTXT,
        MESSAGE_URL,
        LANGUAGE
        FROM TP_MESSAGE
    </sql>
    <select id="selectMessageList" parameterType="com.juneyaoair.manage.b2c.entity.MessagePO" resultMap="MessageResult">
        <include refid="selectMessageSql"/>
        <where>
            <if test="messageTitle != null and messageTitle != ''" >
                and MESSAGE_TITLE like '%${messageTitle}%'
            </if>
            <if test="language != null and language != ''" >
                and language = #{language}
            </if>
        </where>
        ORDER BY MESSAGE_CREATETIME DESC
    </select>
</mapper>
