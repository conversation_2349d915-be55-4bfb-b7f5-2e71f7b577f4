<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.ThirdPrizeRecordMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.ThirdPrizeRecordPO">
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="activityCode" column="ACTIVITY_CODE" jdbcType="VARCHAR"/>
        <result property="merchantCode" column="MERCHANT_CODE" jdbcType="VARCHAR"/>
        <result property="prizeCode" column="PRIZE_CODE" jdbcType="VARCHAR"/>
        <result property="prizeName" column="PRIZE_NAME" jdbcType="VARCHAR"/>
        <result property="prizeType" column="PRIZE_TYPE" jdbcType="VARCHAR"/>
        <result property="prizeAmount" column="PRIZE_AMOUNT" jdbcType="DECIMAL"/>
        <result property="prizeStatus" column="PRIZE_STATUS" jdbcType="VARCHAR"/>
        <result property="ffpCardNo" column="FFP_CARD_NO" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="CREATE_USER" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="UPDATE_USER" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,ACTIVITY_CODE,MERCHANT_CODE,
        PRIZE_CODE,PRIZE_NAME,PRIZE_TYPE,PRIZE_AMOUNT,
        PRIZE_STATUS,FFP_CARD_NO,CREATE_TIME,
        CREATE_USER,UPDATE_TIME,UPDATE_USER
    </sql>

    <select id="searchAllByCondition"
            parameterType="com.juneyaoair.ecs.manage.dto.activity.request.coupon.ThirdPartPrizeRequest"
            resultType="com.juneyaoair.ecs.manage.dto.activity.response.commonprizesend.ThirdPartPrizeResponse">
        select atpr.ACTIVITY_CODE as activityCode,
        atpr.MERCHANT_CODE as merchantCode,
        atpr.PRIZE_CODE as prizeCode,
        atpr.PRIZE_NAME as prizeName,
        (case atpr.PRIZE_STATUS
        when 'INIT' then '未发放'
        when 'PREPARE' then '准备发放,未执行发放动作'
        when 'SUCCESS' then '发放成功'
        when 'FAILED' then '发放失败' end) as prizeStatus,
        atpr.FFP_CARD_NO as ffpCardNo,
        atpr.UPDATE_TIME as receiveTime
        from T_ACTIVITY_THIRD_PRIZE_RECORD atpr
        <where>
            1 = 1
            <if test="activityCode != null and activityCode != ''">
                and ACTIVITY_CODE = #{activityCode,jdbcType=VARCHAR}
            </if>
            <if test="merchantCode != null and merchantCode != ''">
                and MERCHANT_CODE = #{merchantCode,jdbcType=VARCHAR}
            </if>
            <if test="prizeCode != null and prizeCode != ''">
                and PRIZE_CODE = #{prizeCode,jdbcType=VARCHAR}
            </if>
            <if test="prizeName != null and prizeName != ''">
                and PRIZE_NAME like '%${prizeName}%'
            </if>
            <if test="prizeType != null and prizeType != ''">
                and PRIZE_TYPE = #{prizeType,jdbcType=VARCHAR}
            </if>
            <if test="prizeStatus != null and prizeStatus != ''">
                and PRIZE_STATUS = #{prizeStatus,jdbcType=VARCHAR}
            </if>
            <if test="ffpCardNo != null and ffpCardNo != ''">
                and FFP_CARD_NO = #{ffpCardNo,jdbcType=VARCHAR}
            </if>
        </where>
        order by atpr.ACTIVITY_CODE, atpr.MERCHANT_CODE,
        DECODE(atpr.PRIZE_STATUS,
        'SUCCESS', 1,
        'FAILED', 2,
        'PREPARE', 3,
        'INIT', 4,
        5)
    </select>

    <insert id="insertBatch">
        insert into T_ACTIVITY_THIRD_PRIZE_RECORD(ID,ACTIVITY_CODE,MERCHANT_CODE,
        PRIZE_CODE,PRIZE_NAME,PRIZE_TYPE,PRIZE_AMOUNT,
        PRIZE_STATUS,FFP_CARD_NO,CREATE_TIME,
        CREATE_USER)
        values
        <foreach collection="thirdPrizeRecordPOCollection" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR},#{item.activityCode,jdbcType=VARCHAR},#{item.merchantCode,jdbcType=VARCHAR},
            #{item.prizeCode,jdbcType=VARCHAR},#{item.prizeName,jdbcType=VARCHAR},#{item.prizeType,jdbcType=VARCHAR},#{item.prizeAmount,jdbcType=NUMERIC},
            #{item.prizeStatus,jdbcType=VARCHAR},#{item.ffpCardNo,jdbcType=VARCHAR},sysdate,
            #{item.createUser,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>
