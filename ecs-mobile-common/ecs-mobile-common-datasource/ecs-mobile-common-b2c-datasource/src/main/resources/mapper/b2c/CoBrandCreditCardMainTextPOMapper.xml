<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.CoBrandCreditCardMainTextPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.CoBrandCreditCardMainTextPO">
    <!--@mbg.generated-->
    <!--@Table CO_BRAND_CREDIT_CARD_MAIN_TEXT-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CONTENT" jdbcType="CLOB" property="content" />
    <result column="SUB_TITLE" jdbcType="VARCHAR" property="subTitle" />
    <result column="SORT_NUM" jdbcType="DECIMAL" property="sortNum" />
    <result column="CONTENT_ID" jdbcType="VARCHAR" property="contentId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONTENT, SUB_TITLE, SORT_NUM, CONTENT_ID
  </sql>
</mapper>