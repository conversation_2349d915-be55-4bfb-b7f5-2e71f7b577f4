<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.ActivityIssueLicenseMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.ActivityIssueLicensePO">
        <result property="ffpCardNo" column="FFP_CARD_NO" jdbcType="VARCHAR"/>
        <result property="activityCode" column="ACTIVITY_CODE" jdbcType="VARCHAR"/>
        <result property="auxiliayField" column="AUXILIARY_FIELD" jdbcType="VARCHAR"/>
        <result property="flightTimes" column="FLIGHT_TIMES" jdbcType="DECIMAL"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="CREATE_USER" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="UPDATE_USER" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        FFP_CARD_NO
        ,ACTIVITY_CODE,AUXILIARY_FIELD,
        FLIGHT_TIMES,CREATE_TIME,CREATE_USER,
        UPDATE_TIME,UPDATE_USER
    </sql>
</mapper>
