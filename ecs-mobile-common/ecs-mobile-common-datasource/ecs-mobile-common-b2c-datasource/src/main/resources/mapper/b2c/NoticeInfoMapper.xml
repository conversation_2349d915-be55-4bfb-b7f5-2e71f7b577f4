<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.NoticeInfoMapper">
    <resultMap id="NoticeMap" type="com.juneyaoair.ecs.manage.dto.notice.NoticeInfo">
        <id column="NT_INFO_ID" property="ntInfoId"></id>
        <result column="NOTICE_ID" property="noticeId"></result>
        <result column="NT_INFO_NAME" property="ntInfoName"></result>
        <result column="NT_INFO_URL" property="ntInfoUrl"></result>
        <result column="CREATE_TIME" property="createTime"></result>
        <result column="MODIFY_TIME" property="modifyTime"></result>
        <result column="PERSON" property="person"></result>
        <result column="NT_INFO_CODE" property="ntInfoCode"></result>
        <result column="NT_INFO_DESCRIPTION" property="ntInfoDescription"></result>
        <result column="CAN_INFO_SHOW" property="canInfoShow"></result>
        <result column="DVNAME" property="dtName"></result>
        <result column="DVID" property="modularBm"></result>
        <result column="NT_ID" property="noticeBm"></result>
        <result column="NT_NAME" property="ntName"></result>
        <result column="RICH_TEXT" property="richText"></result>
        <result column="NT_OTHER_URL" property="ntOtherUrl"></result>
        <result column="NT_PIC_URL" property="ntPicUrl"></result>
        <result column="MAINTAIN_ID" property="maintainId"></result>
        <result column="NT_MAINTAIN_NAME" property="ntMaintainName"></result>
        <result column="NT_MAINTAIN_DESCRIPTION" property="ntMaintainDesciption"></result>
        <result column="RICH_TEXTS" property="richTexts"></result>
        <result column="LANGUAGE" property="language"></result>
    </resultMap>

    <resultMap id="NoticeFatherMap" type="com.juneyaoair.ecs.manage.dto.notice.NoticeSort">
        <id column="NT_ID" property="ntId"></id>
        <result column="DICT_ID" property="dictId"></result>
        <result column="NT_NAME" property="ntName"></result>
        <result column="NT_CODE" property="ntCode"></result>
        <result column="NT_DESCRIPTION" property="ntDescription"></result>
        <result column="CAN_SHOW" property="canShow"></result>
        <result column="DVID" property="diId"></result>
        <result column="DVNAME" property="diName"></result>
        <result column="LANGUAGE" property="language"></result>
    </resultMap>

    <resultMap id="MaintainInfoMap" type="com.juneyaoair.ecs.manage.dto.notice.MaintainInfo">
        <id column="NT_MAINTAIN_ID" property="ntMaintainID"></id>
        <result column="NT_MAINTAIN_NAME" property="ntMaintainName"></result>
        <result column="NT_OTHER_URL" property="ntOtherUrl"></result>
        <result column="CREATE_TIME" property="createTime"></result>
        <result column="MODIFY_TIME" property="modifyTime"></result>
        <result column="PERSON" property="person"></result>
        <result column="NT_MAINTAIN_CODE" property="ntMaintainCode"></result>
        <result column="NT_MAINTAIN_DESCRIPTION" property="ntMaintainDesciption"></result>
        <result column="RICH_TEXTS" property="richTexts"></result>
        <result column="NT_PIC_URL" property="ntPicUrl"></result>
        <result column="FILE_NAMES" property="fileNames"></result>
    </resultMap>

    <select id="queryNoticeInfo" parameterType="com.juneyaoair.ecs.manage.dto.notice.NoticeInfo" resultMap="NoticeMap">
        SELECT
        t2.nt_info_id,
        t2.notice_id,
        t2.nt_info_url,
        TO_CHAR(t2.create_time,'yyyy-MM-dd hh24:mi:ss') create_time,
        TO_CHAR(t2.modify_time,'yyyy-MM-dd hh24:mi:ss') modify_time,
        t2.person,
        t2.nt_info_code,
        t2.nt_info_description,
        t2.CAN_INFO_SHOW,
        t2.nt_info_name,
        t3.dvname,
        t3.dvid,
        t1.nt_name,
        t1.language,
        t1.DICT_ID
        FROM
        t_notice t1,
        t_notice_info t2,
        tp_dictvalue t3
        WHERE
        t1.dict_id = t3.dvid
        AND t1.CAN_SHOW = 'Y'
        AND t2.CAN_INFO_SHOW = 'Y'
        AND t3.ENABLE = 'Y'
        AND t1.nt_id = t2.notice_id
        <if test="null != ntInfoName and ntInfoName != ''">
            and t2.nt_info_name like '%${ntInfoName}%'
        </if>
        <if test=" null!= ntInfoUrl and ntInfoUrl != ''">
            and t2.nt_info_url = #{ntInfoUrl,jdbcType=VARCHAR}
        </if>
        <if test=" null!= modularBm and modularBm != ''">
            and t3.dvid = #{modularBm,jdbcType=VARCHAR}
        </if>
        <if test=" null!= noticeBm and noticeBm != ''">
            and t2.notice_id = #{noticeBm,jdbcType=VARCHAR}
        </if>
        <if test=" null!= ntInfoId and ntInfoId != ''">
            and t2.nt_info_id = #{ntInfoId,jdbcType=VARCHAR}
        </if>
        ORDER BY
        t2.nt_info_code asc ,
        t2.modify_time asc
    </select>

    <select id="getNoticeInfoList" parameterType="com.juneyaoair.ecs.manage.dto.notice.NoticeInfo" resultMap="NoticeMap">
        SELECT  r.*,
                t4.NT_MAINTAIN_ID,
                t4.NT_MAINTAIN_NAME,
                t4.NT_OTHER_URL,
                t4.NT_MAINTAIN_DESCRIPTION,
                t4.RICH_TEXTS,
                t4.NT_PIC_URL
        FROM
        (SELECT t1.nt_name,
                t1.NT_ID,
                t1.LANGUAGE,
                t2.nt_info_id,
                t2.notice_id,
                t2.nt_info_url,
                TO_CHAR(t2.create_time,'yyyy-MM-dd hh24:mi:ss') create_time,
                TO_CHAR(t2.modify_time,'yyyy-MM-dd hh24:mi:ss') modify_time,
                t2.person,
                t2.nt_info_code,
                t2.nt_info_description,
                t2.CAN_INFO_SHOW,
                t2.nt_info_name,
                t2.maintain_id,
                t3.dvname,
                t3.dvid
           FROM
                t_notice t1,
                t_notice_info t2,
                tp_dictvalue t3
          WHERE t1.dict_id = t3.dvid
            AND t1.CAN_SHOW = 'Y'
            AND t2.CAN_INFO_SHOW = 'Y'
            AND t3.ENABLE = 'Y'
            AND t1.nt_id = t2.notice_id
        ) r left join t_maintain_info t4 on r.MAINTAIN_ID = t4.NT_MAINTAIN_ID where 1=1
        <if test="null != language and language != ''">
            and r.LANGUAGE = #{language,jdbcType=VARCHAR}
        </if>
        <if test="null != ntInfoName and ntInfoName != ''">
            and (r.nt_info_name like '%${ntInfoName}%' or t4.NT_MAINTAIN_NAME like '%${ntInfoName}%')
        </if>
        <if test=" null!= ntInfoUrl and ntInfoUrl != ''">
            and (r.nt_info_url like '%${ntInfoUrl}%' or t4.NT_OTHER_URL like '%${ntInfoUrl}%' or t4.NT_PIC_URL like '%${ntInfoUrl}%')
        </if>
        <if test=" null!= modularBm and modularBm != ''">
            and r.dvid = #{modularBm,jdbcType=VARCHAR}
        </if>
        <if test=" null!= noticeBm and noticeBm != ''">
            and r.notice_id = #{noticeBm,jdbcType=VARCHAR}
        </if>
        <if test=" null!= ntInfoId and ntInfoId != ''">
            and r.nt_info_id = #{ntInfoId,jdbcType=VARCHAR}
        </if>
        ORDER BY TO_NUMBER(r.NT_INFO_CODE),r.NT_INFO_ID ASC
    </select>

    <select id="queryNoticeInfoRich" parameterType="com.juneyaoair.ecs.manage.dto.notice.NoticeInfo" resultMap="NoticeMap">
        SELECT
        t2.nt_info_id,
        t2.notice_id,
        t2.nt_info_url,
        TO_CHAR(t2.create_time,'yyyy-MM-dd hh24:mi:ss') create_time,
        TO_CHAR(t2.create_time,'yyyy-MM-dd hh24:mi:ss') modify_time,
        t2.person,
        t2.nt_info_code,
        t2.nt_info_description,
        t2.CAN_INFO_SHOW,
        t2.nt_info_name,
        t3.dvname,
        t3.dvid,
        t1.nt_name,
        t1.language,
        t2.rich_text
        FROM
        t_notice t1,
        t_notice_info t2,
        tp_dictvalue t3
        WHERE
        t1.dict_id = t3.dvid
        AND t1.CAN_SHOW = 'Y'
        AND t2.CAN_INFO_SHOW = 'Y'
        AND t3.ENABLE = 'Y'
        AND t1.nt_id = t2.notice_id
        <if test="null != ntInfoName and ntInfoName != ''">
            and t2.nt_info_name like '%${ntInfoName}%'
        </if>
        <if test=" null!= ntInfoUrl and ntInfoUrl != ''">
            and t2.nt_info_url = #{ntInfoUrl,jdbcType=VARCHAR}
        </if>
        <if test=" null!= modularBm and modularBm != ''">
            and t3.dvid = #{modularBm,jdbcType=VARCHAR}
        </if>
        <if test=" null!= noticeBm and noticeBm != ''">
            and t2.notice_id = #{noticeBm,jdbcType=VARCHAR}
        </if>
        <if test=" null!= ntInfoId and ntInfoId != ''">
            and t2.nt_info_id = #{ntInfoId,jdbcType=VARCHAR}
        </if>
        ORDER BY
        t2.nt_info_code asc ,
        t2.modify_time asc
    </select>


    <select id="selectFatherModule" parameterType="com.juneyaoair.ecs.manage.dto.notice.NoticeSort" resultMap="NoticeFatherMap">
        SELECT
        t1.NT_ID,
        t1.DICT_ID,
        t1.NT_NAME,
        t1.LANGUAGE,
        t3.DVID,
        t3.DVNAME
        FROM T_NOTICE t1,TP_DICTVALUE t3
        WHERE t1.DICT_ID = t3.DVID
        AND t1.CAN_SHOW = 'Y'
        AND t3.ENABLE = 'Y'
        <if test="ntId !=  null and ntId != ''">
            and t1.NT_ID = #{ntId,jdbcType=VARCHAR}
        </if>
        <if test="dictId !=  null and dictId != ''">
            and t3.DVID = #{dictId,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectNoticeSortById" parameterType="com.juneyaoair.ecs.manage.dto.notice.NoticeSort"
            resultMap="NoticeFatherMap">
         SELECT t.*,
                t1.DVID,
                t1.DVNAME
           FROM T_NOTICE t
           join TP_DICTVALUE t1 on t.DICT_ID = t1.DVID
          WHERE t1.ENABLE = 'Y'
        <if test="canShow!=null and canShow != ''">
            and t.CAN_SHOW = #{canShow,jdbcType=VARCHAR}
        </if>
        <if test="ntId!=null and ntId != ''">
            and t.NT_ID = #{ntId,jdbcType=VARCHAR}
        </if>
        <if test="ntName!=null and ntName != ''">
            and t.NT_NAME like '%${ntName}%'
        </if>
        <if test="dictId !=null and dictId != ''">
            and t.DICT_ID = #{dictId,jdbcType=VARCHAR}
        </if>
        <if test="language !=null and language != ''">
            and t.LANGUAGE = #{language,jdbcType=VARCHAR}
        </if>
        order by TO_NUMBER(t.NT_CODE),t.NT_ID asc
    </select>

    <insert id="addNoticeInfo" parameterType="com.juneyaoair.ecs.manage.dto.notice.NoticeInfo">
        insert into T_NOTICE_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ntInfoId != null">
                NT_INFO_ID,
            </if>
            <if test="noticeId != null">
                NOTICE_ID,
            </if>
            <if test="ntInfoName != null">
                NT_INFO_NAME,
            </if>
            <if test="ntInfoUrl != null">
                NT_INFO_URL,
            </if>
            <if test="person != null">
                PERSON,
            </if>
            <if test="language != null">
                LANGUAGE,
            </if>
            CREATE_TIME,
            MODIFY_TIME,
            <if test="ntInfoCode != null">
                NT_INFO_CODE,
            </if>
            <if test="ntInfoDescription != null">
                NT_INFO_DESCRIPTION,
            </if>
            <if test="canInfoShow != null">
                CAN_INFO_SHOW,
            </if>
            <if test="richText != null">
                RICH_TEXT,
            </if>
            <if test="maintainId != null">
                MAINTAIN_ID
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ntInfoId != null">
                #{ntInfoId,jdbcType=VARCHAR},
            </if>
            <if test="noticeId != null">
                #{noticeId,jdbcType=VARCHAR},
            </if>
            <if test="ntInfoName != null">
                #{ntInfoName,jdbcType=VARCHAR},
            </if>
            <if test="ntInfoUrl != null">
                #{ntInfoUrl,jdbcType=VARCHAR},
            </if>
            <if test="person != null">
                #{person,jdbcType=VARCHAR},
            </if>
            <if test="language != null">
                #{language,jdbcType=VARCHAR},
            </if>
            sysdate,
            sysdate,
            <if test="ntInfoCode != null">
                #{ntInfoCode,jdbcType=VARCHAR},
            </if>
            <if test="ntInfoDescription != null">
                #{ntInfoDescription,jdbcType=VARCHAR},
            </if>
            <if test="canInfoShow != null">
                #{canInfoShow,jdbcType=VARCHAR},
            </if>
            <if test="richText != null">
                #{richText,jdbcType=BLOB}
            </if>
            <if test="maintainId != null">
                #{maintainId,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>


    <update id="updateNoticeInfo" parameterType="com.juneyaoair.ecs.manage.dto.notice.NoticeInfo">
        UPDATE T_NOTICE_INFO T
        SET T.NT_INFO_NAME =  #{ntInfoName,jdbcType=VARCHAR},
            T.NOTICE_ID =  #{noticeId,jdbcType=VARCHAR},
            T.PERSON = #{person,jdbcType=VARCHAR},
            T.NT_INFO_URL =  #{ntInfoUrl,jdbcType=VARCHAR},
            T.MODIFY_TIME = sysdate,
            T.NT_INFO_CODE = #{ntInfoCode,jdbcType=VARCHAR},
            T.NT_INFO_DESCRIPTION = #{ntInfoDescription,jdbcType=VARCHAR},
            T.CAN_INFO_SHOW = #{canInfoShow,jdbcType=VARCHAR},
            T.RICH_TEXT = #{richText,jdbcType=BLOB}
        WHERE T.NT_INFO_ID = #{ntInfoId,jdbcType=VARCHAR}
    </update>

    <update id="updateNoticeInfoV2" parameterType="com.juneyaoair.ecs.manage.dto.notice.NoticeInfo">
        UPDATE T_NOTICE_INFO T
        SET T.NT_INFO_NAME =  #{ntInfoName,jdbcType=VARCHAR},
            T.NOTICE_ID =  #{noticeId,jdbcType=VARCHAR},
            T.NT_INFO_URL =  #{ntInfoUrl,jdbcType=VARCHAR},
            T.MODIFY_TIME = sysdate,
            T.NT_INFO_CODE = #{ntInfoCode,jdbcType=VARCHAR},
            T.NT_INFO_DESCRIPTION = #{ntInfoDescription,jdbcType=VARCHAR},
            T.CAN_INFO_SHOW = #{canInfoShow,jdbcType=VARCHAR},
            T.PERSON = #{person,jdbcType=VARCHAR},
            T.RICH_TEXT = #{richText,jdbcType=BLOB},
            T.MAINTAIN_ID = #{maintainId,jdbcType=VARCHAR}
        WHERE T.NT_INFO_ID = #{ntInfoId,jdbcType=VARCHAR}
    </update>

    <insert id="addNoticeSort" parameterType="com.juneyaoair.ecs.manage.dto.notice.NoticeSort">
        INSERT INTO T_NOTICE (NT_ID,DICT_ID,NT_NAME,NT_CODE,NT_DESCRIPTION,CAN_SHOW,LANGUAGE)
        VALUES (#{ntId,jdbcType=VARCHAR},
                #{dictId,jdbcType=VARCHAR},
                #{ntName,jdbcType=VARCHAR},
                #{ntCode,jdbcType=VARCHAR},
                #{ntDescription,jdbcType=VARCHAR},
                #{canShow,jdbcType=VARCHAR},
                #{language,jdbcType=VARCHAR}
               )
    </insert>

    <update id="updateNoticeSort" parameterType="com.juneyaoair.ecs.manage.dto.notice.NoticeSort">
        UPDATE T_NOTICE T
        SET T.NT_NAME =  #{ntName,jdbcType=VARCHAR},
            T.DICT_ID =  #{dictId,jdbcType=VARCHAR},
            T.NT_CODE =  #{ntCode,jdbcType=VARCHAR},
            T.NT_DESCRIPTION =  #{ntDescription,jdbcType=VARCHAR},
        <if test="null != language and language != ''">
            T.LANGUAGE =  #{language,jdbcType=VARCHAR},
        </if>
            T.CAN_SHOW = #{canShow,jdbcType=VARCHAR}
        WHERE T.NT_ID = #{ntId,jdbcType=VARCHAR}
    </update>

    <delete id="deleteNoticeInfo" parameterType="com.juneyaoair.ecs.manage.dto.notice.NoticeInfo">
        DELETE  FROM T_NOTICE_INFO T
        WHERE T.NT_INFO_ID = #{ntInfoId,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteNoticeSort" parameterType="com.juneyaoair.ecs.manage.dto.notice.NoticeSort">
        DELETE  FROM T_NOTICE T
        WHERE T.NT_ID = #{ntId,jdbcType=VARCHAR}
    </delete>

    <insert id="addMaintainInfo" parameterType="com.juneyaoair.ecs.manage.dto.notice.MaintainInfo">
        insert into T_MAINTAIN_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ntMaintainID != null">
                NT_MAINTAIN_ID,
            </if>
            <if test="ntMaintainName != null">
                NT_MAINTAIN_NAME,
            </if>
            <if test="ntOtherUrl != null">
                NT_OTHER_URL,
            </if>
            CREATE_TIME,
            MODIFY_TIME,
            <if test="person != null">
                PERSON,
            </if>
            <if test="ntMaintainCode != null">
                NT_MAINTAIN_CODE,
            </if>
            <if test="ntMaintainDesciption != null">
                NT_MAINTAIN_DESCRIPTION,
            </if>
            <if test="richTexts != null">
                RICH_TEXTS,
            </if>
            <if test="ntPicUrl != null">
                NT_PIC_URL,
            </if>
            <if test="language != null">
                LANGUAGE,
            </if>
            <if test="fileNames != null">
                FILE_NAMES
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ntMaintainID != null">
                #{ntMaintainID,jdbcType=VARCHAR},
            </if>
            <if test="ntMaintainName != null">
                #{ntMaintainName,jdbcType=VARCHAR},
            </if>
            <if test="ntOtherUrl != null">
                #{ntOtherUrl,jdbcType=VARCHAR},
            </if>
            sysdate,
            sysdate,
            <if test="person != null">
                #{person,jdbcType=VARCHAR},
            </if>
            <if test="ntMaintainCode != null">
                #{ntMaintainCode,jdbcType=VARCHAR},
            </if>
            <if test="ntMaintainDesciption != null">
                #{ntMaintainDesciption,jdbcType=VARCHAR},
            </if>
            <if test="richTexts != null">
                #{richTexts,jdbcType=BLOB},
            </if>
            <if test="ntPicUrl != null">
                #{ntPicUrl,jdbcType=VARCHAR},
            </if>
            <if test="language != null">
                #{language,jdbcType=VARCHAR},
            </if>
            <if test="fileNames != null">
                #{fileNames,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>


    <delete id="deleteMaintainInfo" parameterType="com.juneyaoair.ecs.manage.dto.notice.MaintainInfo">
        DELETE  FROM T_MAINTAIN_INFO T
        WHERE T.NT_MAINTAIN_ID = #{ntMaintainID,jdbcType=VARCHAR}
    </delete>

    <update id="updateMaintainInfo" parameterType="com.juneyaoair.ecs.manage.dto.notice.MaintainInfo">
        UPDATE T_MAINTAIN_INFO T
        SET T.NT_MAINTAIN_ID =  #{ntMaintainID,jdbcType=VARCHAR},
            T.NT_MAINTAIN_NAME =  #{ntMaintainName,jdbcType=VARCHAR},
            T.NT_OTHER_URL =  #{ntOtherUrl,jdbcType=VARCHAR},
            T.MODIFY_TIME = sysdate,
            T.PERSON = #{person,jdbcType=VARCHAR},
            T.NT_MAINTAIN_CODE = #{ntMaintainCode,jdbcType=VARCHAR},
            T.NT_MAINTAIN_DESCRIPTION = #{ntMaintainDesciption,jdbcType=VARCHAR},
            T.RICH_TEXTS = #{richTexts,jdbcType=BLOB},
        <if test="null != language and language != ''">
            T.LANGUAGE =  #{language,jdbcType=VARCHAR},
        </if>
            T.NT_PIC_URL = #{ntPicUrl,jdbcType=VARCHAR}
        WHERE T.NT_MAINTAIN_ID = #{ntMaintainID,jdbcType=VARCHAR}
    </update>

    <select id="fetchMaintainInfo" parameterType="com.juneyaoair.ecs.manage.dto.notice.MaintainInfo"
            resultMap="MaintainInfoMap">
        SELECT
        T.NT_MAINTAIN_ID,
        T.NT_MAINTAIN_NAME,
        T.NT_OTHER_URL,
        T.PERSON,
        T.NT_MAINTAIN_CODE,
        T.NT_MAINTAIN_DESCRIPTION,
        T.RICH_TEXTS,
        T.NT_PIC_URL,
        T.LANGUAGE,
        T.FILE_NAMES,
        T.CREATE_TIME,
        T.MODIFY_TIME
        FROM T_MAINTAIN_INFO T
        WHERE
        1 = 1
        <if test="ntMaintainID!=null and ntMaintainID != ''">
            and T.NT_MAINTAIN_ID = #{ntMaintainID,jdbcType=VARCHAR}
        </if>
        <if test="ntMaintainName!=null and ntMaintainName != ''">
            and t.NT_MAINTAIN_NAME like '%${ntMaintainName}%'
        </if>
        <if test="language!=null and language != ''">
            and t.LANGUAGE = #{language}
        </if>
        ORDER BY CASE WHEN T.MODIFY_TIME IS NOT NULL THEN T.MODIFY_TIME ELSE T.CREATE_TIME END DESC
    </select>


</mapper>
