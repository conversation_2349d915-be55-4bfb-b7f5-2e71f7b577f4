<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.ActivityCouponDetailMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.ActivityCouponDetailPO">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="couponGiftId" column="COUPON_GIFT_ID" jdbcType="VARCHAR"/>
            <result property="couponId" column="COUPON_ID" jdbcType="VARCHAR"/>
            <result property="couponPrice" column="COUPON_PRICE" jdbcType="DECIMAL"/>
            <result property="couponName" column="COUPON_NAME" jdbcType="VARCHAR"/>
            <result property="couponActivityName" column="COUPON_ACTIVITY_NAME" jdbcType="VARCHAR"/>
            <result property="couponUseRule" column="COUPON_USE_RULE" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="createUser" column="CREATE_USER" jdbcType="VARCHAR"/>
            <result property="createDate" column="CREATE_DATE" jdbcType="VARCHAR"/>
            <result property="lastUpdateUser" column="LAST_UPDATE_USER" jdbcType="VARCHAR"/>
            <result property="lastUpdateDate" column="LAST_UPDATE_DATE" jdbcType="VARCHAR"/>
            <result property="couponUseStartDate" column="COUPON_USE_START_DATE" jdbcType="VARCHAR"/>
            <result property="couponUseEndDate" column="COUPON_USE_END_DATE" jdbcType="VARCHAR"/>
            <result property="couponType" column="COUPON_TYPE" jdbcType="VARCHAR"/>
            <result property="callInner" column="CALL_INNER" jdbcType="VARCHAR"/>
            <result property="restNum" column="REST_NUM" jdbcType="VARCHAR"/>
            <result property="couponEnough" column="COUPON_ENOUGH" jdbcType="VARCHAR"/>
            <result property="couponGiftSalePrice" column="COUPON_GIFT_SALE_PRICE" jdbcType="VARCHAR"/>
            <result property="couponGiftBasePrice" column="COUPON_GIFT_BASE_PRICE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,COUPON_GIFT_ID,COUPON_ID,
        COUPON_PRICE,COUPON_NAME,COUPON_ACTIVITY_NAME,
        COUPON_USE_RULE,REMARK,CREATE_USER,
        CREATE_DATE,LAST_UPDATE_USER,LAST_UPDATE_DATE,
        COUPON_USE_START_DATE,COUPON_USE_END_DATE,COUPON_TYPE,
        CALL_INNER,REST_NUM,COUPON_ENOUGH,
        COUPON_GIFT_SALE_PRICE,COUPON_GIFT_BASE_PRICE,DETAIL_EXCHANGE_CODE
    </sql>
</mapper>
