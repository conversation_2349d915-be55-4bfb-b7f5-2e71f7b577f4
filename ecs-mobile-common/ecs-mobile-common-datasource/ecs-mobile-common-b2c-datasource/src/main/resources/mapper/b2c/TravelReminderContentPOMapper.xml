<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TravelReminderContentPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.TravelReminderContentPO">
    <!--@mbg.generated-->
    <!--@Table TRAVEL_REMINDER_CONTENT-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="THEME_ID" jdbcType="VARCHAR" property="themeId" />
    <result column="LANGUAGE_TAG" jdbcType="VARCHAR" property="languageTag" />
    <result column="TITLE" jdbcType="VARCHAR" property="title" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, THEME_ID, LANGUAGE_TAG, TITLE, CONTENT, CREATED_BY, CREATED_TIME, UPDATED_BY, 
    UPDATED_TIME
  </sql>
</mapper>