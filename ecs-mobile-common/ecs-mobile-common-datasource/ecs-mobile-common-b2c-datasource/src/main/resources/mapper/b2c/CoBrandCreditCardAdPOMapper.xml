<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.CoBrandCreditCardAdPOMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.CoBrandCreditCardAdPO">
        <!--@mbg.generated-->
        <!--@Table CO_BRAND_CREDIT_CARD_AD-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="THEME" jdbcType="VARCHAR" property="theme"/>
        <result column="SUB_TITLE" jdbcType="VARCHAR" property="subTitle"/>
        <result column="TAG" jdbcType="VARCHAR" property="tag"/>
        <result column="IMG_URL" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="SORT_NUM" jdbcType="DECIMAL" property="sortNum"/>
        <result column="THEME_ENABLE" jdbcType="VARCHAR" property="themeEnable"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        THEME,
        SUB_TITLE,
        TAG,
        IMG_URL,
        START_TIME,
        END_TIME,
        SORT_NUM,
        THEME_ENABLE,
        CREATED_BY,
        CREATED_TIME,
        UPDATED_BY,
        UPDATED_TIME
    </sql>


    <resultMap id="CoBrandCreditCardAdBO"
               type="com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.bo.CoBrandCreditCardAdBO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="THEME" jdbcType="VARCHAR" property="theme"/>
        <result column="SUB_TITLE" jdbcType="VARCHAR" property="subTitle"/>
        <result column="TAG" jdbcType="VARCHAR" property="tag"/>
        <result column="IMG_URL" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="SORT_NUM" jdbcType="VARCHAR" property="sort"/>
        <result column="THEME_ENABLE" jdbcType="VARCHAR" property="themeEnable"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime"/>
        <!-- detail by CoBrandCreditCardAdDetail -->
        <collection ofType="com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.bo.CoBrandCreditCardAdDetail"
                    column="ID" select="selectAdDetailBoDetail" property="detail">
        </collection>
    </resultMap>
    <resultMap id="CoBrandCreditCardAdBoDetail"
               type="com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.bo.CoBrandCreditCardAdDetail">
        <id column="D_ID" jdbcType="VARCHAR" property="id"/>
        <result column="FRONT_END_TYPE" jdbcType="VARCHAR" property="frontEndType"/>
        <result column="SUPPORT_JUMP" jdbcType="VARCHAR" property="enable"/>
        <result column="JUMP_URL" jdbcType="VARCHAR" property="jumpUrl"/>
        <result column="JUMP_TEXT" jdbcType="VARCHAR" property="jumpText"/>
        <result column="DETAIL_TITLE" jdbcType="VARCHAR" property="detailTitle"/>
        <result column="D_SORT" jdbcType="VARCHAR" property="sort"/>
        <result column="D_CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="D_CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="D_UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="D_UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime"/>
        <!-- contests by CoBrandCreditCardAdContent -->
        <collection ofType="com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.bo.CoBrandCreditCardAdContent"
                    column="D_ID" select="selectAdDetailBoContent" property="contents">
        </collection>
    </resultMap>
    <resultMap id="CoBrandCreditCardAdBoContent"
               type="com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.bo.CoBrandCreditCardAdContent">
        <id column="C_ID" jdbcType="VARCHAR" property="id"/>
        <result column="IMG_URL_LIST" jdbcType="VARCHAR" property="imgUrlList"/>
        <result column="TITLE" jdbcType="VARCHAR" property="title"/>
        <result column="C_SORT" jdbcType="VARCHAR" property="sort"/>
        <result column="C_CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="C_CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="C_UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="C_UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime"/>
        <collection ofType="com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.bo.CoBrandCreditCardAdMainText"
                    column="C_ID" select="selectAdDetailBoMainText" property="mainText">
        </collection>
    </resultMap>
    <resultMap id="CoBrandCreditCardAdBoMainText"
               type="com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.bo.CoBrandCreditCardAdMainText">
        <id column="M_ID" jdbcType="VARCHAR" property="id"/>
        <result column="CONTENT" jdbcType="CLOB" property="content"/>
        <result column="CONTENT_SUB_TITLE" jdbcType="VARCHAR" property="contentSubTitle"/>
    </resultMap>

    <select id="selectAdDetailBoDetail"  resultMap="CoBrandCreditCardAdBoDetail">
        select de.ID           as D_ID,
               de.FRONT_END_TYPE,
               de.SUPPORT_JUMP,
               de.JUMP_URL,
               de.JUMP_TEXT,
               de.DETAIL_TITLE,
               de.CREATED_BY   as D_CREATED_BY,
               de.CREATED_TIME as D_CREATED_TIME,
               de.UPDATED_BY   as D_UPDATED_BY,
               de.UPDATED_TIME as D_UPDATED_TIME,
               de.SORT_NUM     as D_SORT
        from CO_BRAND_CREDIT_CARD_DETAIL de where de.THEME_ID = #{ID}
    </select>

    <select id="selectAdDetailBoContent" resultMap="CoBrandCreditCardAdBoContent">
        select co.ID           as C_ID,
        co.IMG_URL_LIST,
        co.TITLE,
        co.SORT_NUM     as C_SORT,
        co.CREATED_BY   as C_CREATED_BY,
        co.CREATED_TIME as C_CREATED_TIME,
        co.UPDATED_BY   as C_UPDATED_BY,
        co.UPDATED_TIME as C_UPDATED_TIME
        from CO_BRAND_CREDIT_CARD_CONTENT co where co.DETAIL_ID = #{D_ID}
    </select>

    <select id="selectAdDetailBoMainText" resultMap="CoBrandCreditCardAdBoMainText">
        select mt.ID        as M_ID,
               mt.CONTENT,
               mt.SUB_TITLE as CONTENT_SUB_TITLE
        from CO_BRAND_CREDIT_CARD_MAIN_TEXT mt
        where mt.CONTENT_ID = #{C_ID}
    </select>


    <select id="selectBO" resultMap="CoBrandCreditCardAdBO">
        select ad.ID,
               ad.THEME,
               ad.SUB_TITLE,
               ad.TAG,
               ad.IMG_URL,
               ad.START_TIME,
               ad.END_TIME,
               ad.SORT_NUM,
               ad.THEME_ENABLE,
               ad.CREATED_BY,
               ad.CREATED_TIME,
               ad.UPDATED_BY,
               ad.UPDATED_TIME
        from CO_BRAND_CREDIT_CARD_AD ad
        <where>
            1 = 1
            <if test="themeId != null and themeId != ''">
                AND ID = #{themeId,jdbcType=VARCHAR}
            </if>
            <if test="themeName != null and themeName != ''">
                AND THEME = #{themeName,jdbcType=VARCHAR}
            </if>
            <if test="themeStatus != null and themeStatus != ''">
                AND THEME_ENABLE = #{themeStatus,jdbcType=VARCHAR}
            </if>
        </where>
    </select>


    <select id="selectBoById" resultMap="CoBrandCreditCardAdBO">
        select ad.ID,
               ad.THEME,
               ad.SUB_TITLE,
               ad.TAG,
               ad.IMG_URL,
               ad.START_TIME,
               ad.END_TIME,
               ad.SORT_NUM,
               ad.THEME_ENABLE,
               ad.CREATED_BY,
               ad.CREATED_TIME,
               ad.UPDATED_BY,
               ad.UPDATED_TIME
        from CO_BRAND_CREDIT_CARD_AD ad
        where ad.ID = #{id,jdbcType=VARCHAR}
    </select>
</mapper>