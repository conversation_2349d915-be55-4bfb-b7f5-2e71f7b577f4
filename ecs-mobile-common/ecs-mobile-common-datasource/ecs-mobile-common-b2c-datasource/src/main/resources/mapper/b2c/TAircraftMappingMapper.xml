<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TAircraftMappingMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.ecs.manage.dto.aircraft.AircraftMappingDTO">
        <id column="AIRCRAFT_MAPPING_ID" jdbcType="VARCHAR" property="aircraftMappingId"/>
        <result column="AIRCRAFT_TYPE_ID" jdbcType="VARCHAR" property="aircraftTypeId"/>
        <result column="AIRCRAFT_CODE" jdbcType="VARCHAR" property="aircraftCode"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="CREATETIME" jdbcType="TIMESTAMP" property="createtime"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="UPDATETIME" jdbcType="TIMESTAMP" property="updatetime"/>
    </resultMap>
    <sql id="Base_Column_List">
        AIRCRAFT_MAPPING_ID,
        AIRCRAFT_TYPE_ID,
        AIRCRAFT_CODE,
        STATUS,
        CREATE_USER,
        CREATETIME,
        UPDATE_USER,
        UPDATETIME
    </sql>

    <select id="queryAircraftTypeById" parameterType="java.util.Map" resultType="com.juneyaoair.ecs.manage.dto.aircraft.AircraftMappingDTO">
        select
        <include refid="Base_Column_List"/>
        from T_AIRCRAFT_MAPPING
        where AIRCRAFT_TYPE_ID = #{aircraftTypeId}
    </select>

    <delete id="deleteByAircraftTypeId">
        delete
        from T_AIRCRAFT_MAPPING
        where AIRCRAFT_TYPE_ID = #{aircraftTypeId}
    </delete>


</mapper>