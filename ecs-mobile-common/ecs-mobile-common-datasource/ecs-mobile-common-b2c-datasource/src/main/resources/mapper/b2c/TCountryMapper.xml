<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TCountryMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.CountryPO">
        <id column="COUNTRY_CODE" jdbcType="VARCHAR" property="countryCode"/>
        <result column="COUNTRY_NAME" jdbcType="VARCHAR" property="countryName"/>
        <result column="ENGLISH_NAME" jdbcType="VARCHAR" property="englishName"/>
        <result column="CURRENCY" jdbcType="VARCHAR" property="currency"/>
        <result column="CREATE_DATETIME" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="CREATOR_ID" jdbcType="DECIMAL" property="creatorId"/>
        <result column="COUNTRY_TEL_CODE" jdbcType="VARCHAR" property="countryTelCode"/>
        <result column="HOT_COUNTRY" jdbcType="VARCHAR" property="hotCountry"/>
        <result column="SEQUENCE" jdbcType="DECIMAL" property="sequence"/>
        <result column="REGION_CODE" jdbcType="VARCHAR" property="regionCode"/>
    </resultMap>
    <resultMap id="CountryJoinRegionResultMap" type="com.juneyaoair.manage.b2c.entity.CountryJoinRegionPO">
        <id column="COUNTRY_CODE" jdbcType="VARCHAR" property="countryCode"/>
        <result column="COUNTRY_NAME" jdbcType="VARCHAR" property="countryName"/>
        <result column="ENGLISH_NAME" jdbcType="VARCHAR" property="englishName"/>
        <result column="CURRENCY" jdbcType="VARCHAR" property="currency"/>
        <result column="CREATE_DATETIME" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="CREATOR_ID" jdbcType="DECIMAL" property="creatorId"/>
        <result column="COUNTRY_TEL_CODE" jdbcType="VARCHAR" property="countryTelCode"/>
        <result column="HOT_COUNTRY" jdbcType="VARCHAR" property="hotCountry"/>
        <result column="SEQUENCE" jdbcType="DECIMAL" property="sequence"/>
        <result column="REGION_CODE" jdbcType="VARCHAR" property="regionCode"/>

        <result column="REGION_NAME" jdbcType="VARCHAR" property="regionName"/>
        <result column="REGION_E_NAME" jdbcType="VARCHAR" property="regionEName"/>
    </resultMap>
    <sql id="Base_Column_List">
        COUNTRY_CODE,
        COUNTRY_NAME,
        ENGLISH_NAME,
        CURRENCY,
        CREATE_DATETIME,
        CREATOR_ID,
        COUNTRY_TEL_CODE,
        HOT_COUNTRY,
        "SEQUENCE",
        REGION_CODE
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_COUNTRY
        where COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from T_COUNTRY
        where COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.juneyaoair.manage.b2c.entity.CountryPO">
        insert into T_COUNTRY (COUNTRY_CODE, COUNTRY_NAME, ENGLISH_NAME,
                               CURRENCY, CREATE_DATETIME, CREATOR_ID,
                               COUNTRY_TEL_CODE, HOT_COUNTRY, "SEQUENCE",
                               REGION_CODE)
        values (#{countryCode,jdbcType=VARCHAR}, #{countryName,jdbcType=VARCHAR}, #{englishName,jdbcType=VARCHAR},
                #{currency,jdbcType=VARCHAR}, #{createDatetime,jdbcType=TIMESTAMP}, #{creatorId,jdbcType=DECIMAL},
                #{countryTelCode,jdbcType=VARCHAR}, #{hotCountry,jdbcType=VARCHAR}, #{sequence,jdbcType=DECIMAL},
                #{regionCode,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.juneyaoair.manage.b2c.entity.CountryPO">
        insert into T_COUNTRY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="countryCode != null">
                COUNTRY_CODE,
            </if>
            <if test="countryName != null">
                COUNTRY_NAME,
            </if>
            <if test="englishName != null">
                ENGLISH_NAME,
            </if>
            <if test="currency != null">
                CURRENCY,
            </if>
            <if test="createDatetime != null">
                CREATE_DATETIME,
            </if>
            <if test="creatorId != null">
                CREATOR_ID,
            </if>
            <if test="countryTelCode != null">
                COUNTRY_TEL_CODE,
            </if>
            <if test="hotCountry != null">
                HOT_COUNTRY,
            </if>
            <if test="sequence != null">
                "SEQUENCE",
            </if>
            <if test="regionCode != null">
                REGION_CODE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="countryCode != null">
                #{countryCode,jdbcType=VARCHAR},
            </if>
            <if test="countryName != null">
                #{countryName,jdbcType=VARCHAR},
            </if>
            <if test="englishName != null">
                #{englishName,jdbcType=VARCHAR},
            </if>
            <if test="currency != null">
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="creatorId != null">
                #{creatorId,jdbcType=DECIMAL},
            </if>
            <if test="countryTelCode != null">
                #{countryTelCode,jdbcType=VARCHAR},
            </if>
            <if test="hotCountry != null">
                #{hotCountry,jdbcType=VARCHAR},
            </if>
            <if test="sequence != null">
                #{sequence,jdbcType=DECIMAL},
            </if>
            <if test="regionCode != null">
                #{regionCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.CountryPO">
        update T_COUNTRY
        <set>
            <if test="countryName != null">
                COUNTRY_NAME = #{countryName,jdbcType=VARCHAR},
            </if>
            <if test="englishName != null">
                ENGLISH_NAME = #{englishName,jdbcType=VARCHAR},
            </if>
            <if test="currency != null">
                CURRENCY = #{currency,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                CREATE_DATETIME = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="creatorId != null">
                CREATOR_ID = #{creatorId,jdbcType=DECIMAL},
            </if>
            <if test="countryTelCode != null">
                COUNTRY_TEL_CODE = #{countryTelCode,jdbcType=VARCHAR},
            </if>
            <if test="hotCountry != null">
                HOT_COUNTRY = #{hotCountry,jdbcType=VARCHAR},
            </if>
            <if test="sequence != null">
                "SEQUENCE" = #{sequence,jdbcType=DECIMAL},
            </if>
            <if test="regionCode != null">
                REGION_CODE = #{regionCode,jdbcType=VARCHAR},
            </if>
        </set>
        where COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.juneyaoair.manage.b2c.entity.CountryPO">
        update T_COUNTRY
        set COUNTRY_NAME     = #{countryName,jdbcType=VARCHAR},
            ENGLISH_NAME     = #{englishName,jdbcType=VARCHAR},
            CURRENCY         = #{currency,jdbcType=VARCHAR},
            CREATE_DATETIME  = #{createDatetime,jdbcType=TIMESTAMP},
            CREATOR_ID       = #{creatorId,jdbcType=DECIMAL},
            COUNTRY_TEL_CODE = #{countryTelCode,jdbcType=VARCHAR},
            HOT_COUNTRY      = #{hotCountry,jdbcType=VARCHAR},
            "SEQUENCE"       = #{sequence,jdbcType=DECIMAL},
            REGION_CODE      = #{regionCode,jdbcType=VARCHAR}
        where COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
    </update>
    <select id="selectJoinReginByRegionCodeOrderBySequence" resultMap="CountryJoinRegionResultMap">
        select a.*,
               b.REGION_NAME,
               b.REGION_E_NAME
        from T_COUNTRY a
                     left join T_REGION b on a.REGION_CODE = b.REGION_CODE
        order by SEQUENCE asc
    </select>

    <select id="selectJoinRegoinByAll" resultMap="CountryJoinRegionResultMap">
        select a.*, b.REGION_NAME, b.REGION_E_NAME
        from T_COUNTRY a
                     left join T_REGION b on a.REGION_CODE = b.REGION_CODE
        <where>
            <if test="countryCode != null">
                and COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
            </if>
            <if test="countryName != null">
                and COUNTRY_NAME = #{countryName,jdbcType=VARCHAR}
            </if>
            <if test="englishName != null">
                and ENGLISH_NAME = #{englishName,jdbcType=VARCHAR}
            </if>
            <if test="currency != null">
                and CURRENCY = #{currency,jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                and CREATE_DATETIME = #{createDatetime,jdbcType=TIMESTAMP}
            </if>
            <if test="creatorId != null">
                and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
            </if>
            <if test="countryTelCode != null">
                and COUNTRY_TEL_CODE = #{countryTelCode,jdbcType=VARCHAR}
            </if>
            <if test="hotCountry != null">
                and HOT_COUNTRY = #{hotCountry,jdbcType=VARCHAR}
            </if>
            <if test="sequence != null">
                and `SEQUENCE` = #{sequence,jdbcType=DECIMAL}
            </if>
            <if test="regionCode != null">
                and a.REGION_CODE = #{regionCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <resultMap id="searchRegionCountryMap" type="com.juneyaoair.manage.b2c.model.RegionCountryJoin">
        <result column="REGION_CODE" jdbcType="VARCHAR" property="regionCode"/>
        <result column="REGION_NAME" jdbcType="VARCHAR" property="regionName"/>
        <result column="REGION_E_NAME" jdbcType="VARCHAR" property="regionEName"/>
        <collection property="countryList" ofType="com.juneyaoair.manage.b2c.entity.CountryPO">
            <result column="COUNTRY_CODE" jdbcType="VARCHAR" property="countryCode"/>
            <result column="COUNTRY_NAME" jdbcType="VARCHAR" property="countryName"/>
            <result column="ENGLISH_NAME" jdbcType="VARCHAR" property="englishName"/>
            <result column="CURRENCY" jdbcType="VARCHAR" property="currency"/>
            <result column="CREATE_DATETIME" jdbcType="TIMESTAMP" property="createDatetime"/>
            <result column="CREATOR_ID" jdbcType="DECIMAL" property="creatorId"/>
            <result column="COUNTRY_TEL_CODE" jdbcType="VARCHAR" property="countryTelCode"/>
            <result column="HOT_COUNTRY" jdbcType="VARCHAR" property="hotCountry"/>
            <result column="SEQUENCE" jdbcType="DECIMAL" property="sequence"/>
            <result column="REGION_CODE" jdbcType="VARCHAR" property="regionCode"/>
        </collection>
    </resultMap>
    <select id="searchRegionCountry" resultMap="searchRegionCountryMap">
         select a.COUNTRY_CODE,
                a.COUNTRY_NAME,
                a.ENGLISH_NAME,
                a.CURRENCY,
                a.CREATE_DATETIME,
                a.CREATOR_ID,
                a.COUNTRY_TEL_CODE,
                a.HOT_COUNTRY,
                a.SEQUENCE,
                NVL(a.REGION_CODE, 'UNKNOWN') REGION_CODE,
                NVL(b.REGION_NAME, '其他') REGION_NAME,
                NVL(b.REGION_E_NAME, 'OTHER') REGION_E_NAME
           from T_COUNTRY a
      left join T_REGION b on a.REGION_CODE = b.REGION_CODE
       order by REGION_CODE, a.SEQUENCE
    </select>

    <select id="selectCountryJsonInfo" resultType="com.juneyaoair.manage.b2c.entity.CountryJsonInfo">
         select a.COUNTRY_CODE,
                a.CURRENCY,
                a.COUNTRY_TEL_CODE,
                a.SEQUENCE,
                NVL(a.REGION_CODE, 'UNKNOWN') REGION_CODE
           from T_COUNTRY a
       order by a.SEQUENCE
    </select>

</mapper>