<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.TpDicttypeMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.dict.TpDictTypePO">
            <id property="dtid" column="DTID" jdbcType="VARCHAR"/>
            <result property="dtcode" column="DTCODE" jdbcType="VARCHAR"/>
            <result property="dtname" column="DTNAME" jdbcType="VARCHAR"/>
            <result property="dtdescription" column="DTDESCRIPTION" jdbcType="VARCHAR"/>
            <result property="enable" column="ENABLE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        DTID,DTCODE,DTNAME,
        DTDESCRIPTION,ENABLE
    </sql>
</mapper>
