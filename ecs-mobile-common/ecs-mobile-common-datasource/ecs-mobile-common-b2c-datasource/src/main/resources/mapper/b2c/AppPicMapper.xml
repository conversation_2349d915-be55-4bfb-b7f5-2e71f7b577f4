<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.AppPicMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.AppPicPO">
        <id property="picId" column="PIC_ID" jdbcType="VARCHAR"/>
        <result property="picStarttime" column="PIC_STARTTIME" jdbcType="VARCHAR"/>
        <result property="picEndtime" column="PIC_ENDTIME" jdbcType="VARCHAR"/>
        <result property="title" column="TITLE" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="VARCHAR"/>
        <result property="updateMan" column="UPDATE_MAN" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="VARCHAR"/>
        <result property="createMan" column="CREATE_MAN" jdbcType="VARCHAR"/>
        <result property="fildIds" column="FILD_IDS" jdbcType="VARCHAR"/>
        <result property="url" column="URL" jdbcType="VARCHAR"/>
        <result property="statusCode" column="STATUS_CODE" jdbcType="VARCHAR"/>
        <result property="descriptionPlaintxt" column="DESCRIPTION_PLAINTXT" jdbcType="VARCHAR"/>
        <result property="isLogin" column="IS_LOGIN" jdbcType="VARCHAR"/>
        <result property="modeType" column="MODE_TYPE" jdbcType="VARCHAR"/>
        <result property="isShared" column="IS_SHARED" jdbcType="VARCHAR"/>
        <result property="shareIconUrl" column="SHARE_ICON_URL" jdbcType="VARCHAR"/>
        <result property="shareDesc" column="SHARE_DESC" jdbcType="VARCHAR"/>
        <result property="isGiftpoints" column="IS_GIFTPOINTS" jdbcType="VARCHAR"/>
        <result property="isGiftcoupons" column="IS_GIFTCOUPONS" jdbcType="VARCHAR"/>
        <result property="shareScore" column="SHARE_SCORE" jdbcType="DECIMAL"/>
        <result property="shareCoupons" column="SHARE_COUPONS" jdbcType="VARCHAR"/>
        <result property="winName" column="WIN_NAME" jdbcType="VARCHAR"/>
        <result property="mobileModel" column="MOBILE_MODEL" jdbcType="VARCHAR"/>
        <result property="displayTime" column="DISPLAY_TIME" jdbcType="DECIMAL"/>
        <result property="maxVersion" column="MAX_VERSION" jdbcType="VARCHAR"/>
        <result property="minVersion" column="MIN_VERSION" jdbcType="VARCHAR"/>
        <result property="titleStyle" column="TITLE_STYLE" jdbcType="VARCHAR"/>
        <result property="orderNum" column="ORDER_NUM" jdbcType="DECIMAL"/>
        <result property="shareStyle" column="SHARE_STYLE" jdbcType="VARCHAR"/>
        <result property="homePageDis" column="HOME_PAGE_DIS" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="appPicture" type="com.juneyaoair.ecs.manage.dto.activity.response.AppPicture">
        <id property="picId" column="PIC_ID" jdbcType="VARCHAR"></id>
        <result property="startTime" column="PIC_STARTTIME" jdbcType="VARCHAR"></result>
        <result property="endTime" column="PIC_ENDTIME" jdbcType="VARCHAR"></result>
        <result property="picUrl" column="PIC_URL" jdbcType="VARCHAR"></result>
        <result property="title" column="TITLE" jdbcType="VARCHAR"></result>
        <result property="description2" column="DESCRIPTION2" jdbcType="BLOB"></result>
        <result property="status" column="STATUS" jdbcType="VARCHAR"></result>
        <result property="updateMan" column="UPDATE_MAN" jdbcType="VARCHAR"></result>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="VARCHAR"></result>
        <result property="createTime" column="CREATE_TIME" jdbcType="VARCHAR"></result>
        <result property="createMan" column="CREATE_MAN" jdbcType="VARCHAR"></result>
        <result property="fileIds" column="FILD_IDS" jdbcType="VARCHAR"></result>
        <result property="url" column="URL" jdbcType="VARCHAR"></result>
        <result property="statusCode" column="STATUS_CODE" jdbcType="VARCHAR"></result>
        <result property="descriptionPlainTxt" column="DESCRIPTION_PLAINTXT" jdbcType="VARCHAR"></result>
        <result property="isLogin" column="IS_LOGIN" jdbcType="VARCHAR"></result>
        <result property="modeType" column="MODE_TYPE" jdbcType="VARCHAR"></result>
        <result property="isShared" column="IS_SHARED" jdbcType="VARCHAR"></result>
        <result property="shareIconUrl" column="SHARE_ICON_URL" jdbcType="VARCHAR"></result>
        <result property="shareDesc" column="SHARE_DESC" jdbcType="VARCHAR"></result>
        <result property="isGiftPoints" column="IS_GIFTPOINTS" jdbcType="VARCHAR"></result>
        <result property="isGiftCoupons" column="IS_GIFTCOUPONS" jdbcType="VARCHAR"></result>
        <result property="shareScore" column="SHARE_SCORE" jdbcType="NUMERIC" ></result>
        <result property="shareCouponsStr" column="SHARE_COUPONS" jdbcType="VARCHAR"></result>
        <result property="winName" column="WIN_NAME" jdbcType="VARCHAR"></result>
        <result property="mobileModel" column="MOBILE_MODEL" jdbcType="VARCHAR"></result>
        <result property="displayTime" column="DISPLAY_TIME" jdbcType="NUMERIC"></result>
        <result property="fileType" column="file_type" jdbcType="VARCHAR"></result>
        <result property="maxVersion" column="MAX_VERSION" jdbcType="VARCHAR"></result>
        <result property="minVersion" column="MIN_VERSION" jdbcType="VARCHAR"></result>
        <result property="titleStyle" column="TITLE_STYLE" jdbcType="VARCHAR"></result>
        <result property="orderNum" column="ORDER_NUM" jdbcType="INTEGER"></result>
    </resultMap>

    <sql id="Base_Column_List">
        PIC_ID,
        PIC_STARTTIME,
        PIC_ENDTIME,
        TITLE,
        STATUS,
        UPDATE_MAN,
        UPDATE_TIME,
        CREATE_TIME,
        CREATE_MAN,
        FILD_IDS,
        URL,
        STATUS_CODE,
        DESCRIPTION_PLAINTXT,
        IS_LOGIN,
        MODE_TYPE,
        IS_SHARED,
        SHARE_ICON_URL,
        SHARE_DESC,
        IS_GIFTPOINTS,
        IS_GIFTCOUPONS,
        SHARE_SCORE,
        SHARE_COUPONS,
        WIN_NAME,
        MOBILE_MODEL,
        DISPLAY_TIME,
        MAX_VERSION,
        MIN_VERSION,
        TITLE_STYLE,
        ORDER_NUM,
        SHARE_STYLE,
        DESCRIPTION2
    </sql>
    <update id="updatePicture" parameterType="com.juneyaoair.manage.b2c.entity.AppPicPO">
        update tp_app_pic
        SET
        STATUS_CODE=#{statusCode,jdbcType=VARCHAR},
        UPDATE_MAN=#{updateMan,jdbcType=VARCHAR},
        UPDATE_TIME=#{updateTime,jdbcType=VARCHAR},
        STATUS=#{status,jdbcType=VARCHAR}

        <if test="title != null and title != ''">
            , PIC_STARTTIME =#{picStarttime,jdbcType=VARCHAR} ,
            PIC_ENDTIME=#{picEndtime,jdbcType=VARCHAR},
            TITLE=#{title,jdbcType=VARCHAR},
            URL=#{url,jdbcType=VARCHAR},
            WIN_NAME=#{winName,jdbcType=VARCHAR},
            FILD_IDS=#{fildIds,jdbcType=VARCHAR},
            DESCRIPTION_PLAINTXT=#{descriptionPlaintxt,jdbcType=VARCHAR},
            IS_LOGIN=#{isLogin,jdbcType=VARCHAR},
            IS_SHARED=#{isShared,jdbcType=VARCHAR},
            IS_GIFTPOINTS=#{isGiftpoints,jdbcType=VARCHAR},
            IS_GIFTCOUPONS=#{isGiftcoupons,jdbcType=VARCHAR},
            SHARE_ICON_URL=#{shareIconUrl,jdbcType=VARCHAR},
            SHARE_DESC=#{shareDesc,jdbcType=VARCHAR},
            SHARE_SCORE=#{shareScore,jdbcType=NUMERIC},
            SHARE_COUPONS=#{shareCoupons,jdbcType=VARCHAR},
            MODE_TYPE=#{modeType,jdbcType=VARCHAR},
            DESCRIPTION2=#{description2,jdbcType=BLOB},
            MAX_VERSION=#{maxVersion,jdbcType=VARCHAR},
            MIN_VERSION=#{minVersion,jdbcType=VARCHAR},
            DISPLAY_TIME=#{displayTime,jdbcType=NUMERIC},
            ORDER_NUM = #{orderNum,jdbcType = INTEGER},
            TITLE_STYLE = #{titleStyle,jdbcType=VARCHAR}
        </if>
        where PIC_ID=#{picId,jdbcType=VARCHAR}
    </update>
    <select id="searchAllInCondition" parameterType="com.juneyaoair.ecs.manage.dto.activity.response.AppPicture" resultMap="appPicture">
        select
        <include refid="Base_Column_List"/>
        from TP_APP_PIC
        <where>
            <if test="picId != null and picId != ''">
                PIC_ID = #{picId}
            </if>
            <if test="title != null and title != ''">
                AND TITLE LIKE CONCAT(CONCAT('%',#{title}),'%')
            </if>
            <if test="status != null and status != ''">
                AND STATUS = #{status,jdbcType=VARCHAR}
            </if>
            <if test="statusCode != null and statusCode != ''">
                AND STATUS_CODE = #{statusCode,jdbcType=VARCHAR}
            </if>
            <if test="endTime !=null and endTime !=''">
                AND PIC_ENDTIME &gt;= #{picEndtime,jdbcType=VARCHAR}
            </if>
        </where>
        order by STATUS DESC NULLS LAST,ORDER_NUM

    </select>
    <select id="searchOneByPicId" parameterType="com.juneyaoair.ecs.manage.dto.activity.response.AppPicture" resultMap="appPicture">
        select
        <include refid="Base_Column_List"/>
        from TP_APP_PIC
        <where>
            <if test="picId != null and picId != ''">
                PIC_ID = #{picId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <update id="updatePictureV2" parameterType="com.juneyaoair.ecs.manage.dto.activity.response.AppPicture">
        update tp_app_pic
        SET
        STATUS_CODE=#{statusCode,jdbcType=VARCHAR},
        UPDATE_MAN=#{updateMan,jdbcType=VARCHAR},
        UPDATE_TIME=#{updateTime,jdbcType=VARCHAR},
        STATUS=#{status,jdbcType=VARCHAR}
        <if test="title != null and title != ''">
            , PIC_STARTTIME =#{startTime,jdbcType=VARCHAR} ,
            PIC_ENDTIME=#{endTime,jdbcType=VARCHAR},
            TITLE=#{title,jdbcType=VARCHAR},
            URL=#{url,jdbcType=VARCHAR},
            WIN_NAME=#{winName,jdbcType=VARCHAR},
            FILD_IDS=#{fileIds,jdbcType=VARCHAR},
            DESCRIPTION_PLAINTXT=#{descriptionPlainTxt,jdbcType=VARCHAR},
            IS_LOGIN=#{isLogin,jdbcType=VARCHAR},
            IS_SHARED=#{isShared,jdbcType=VARCHAR},
            IS_GIFTPOINTS=#{isGiftPoints,jdbcType=VARCHAR},
            IS_GIFTCOUPONS=#{isGiftCoupons,jdbcType=VARCHAR},
            SHARE_ICON_URL=#{shareIconUrl,jdbcType=VARCHAR},
            SHARE_DESC=#{shareDesc,jdbcType=VARCHAR},
            SHARE_SCORE=#{shareScore,jdbcType=NUMERIC},
            SHARE_COUPONS=#{shareCouponsStr,jdbcType=VARCHAR},
            MODE_TYPE=#{modeType,jdbcType=VARCHAR},
            DESCRIPTION2=#{description2,jdbcType=BLOB},
            MAX_VERSION=#{maxVersion,jdbcType=VARCHAR},
            MIN_VERSION=#{minVersion,jdbcType=VARCHAR},
            DISPLAY_TIME=#{displayTime,jdbcType=NUMERIC},
            ORDER_NUM = #{orderNum,jdbcType = INTEGER},
            TITLE_STYLE = #{titleStyle,jdbcType=VARCHAR}
        </if>
        where PIC_ID=#{picId,jdbcType=VARCHAR}
    </update>

    <insert id="save" parameterType="com.juneyaoair.ecs.manage.dto.activity.response.AppPicture">
        insert into tp_app_pic(
            PIC_ID,
            PIC_STARTTIME,
            PIC_ENDTIME,
            TITLE,
            STATUS,
            UPDATE_TIME,
            CREATE_TIME,
            CREATE_MAN,
            URL,
            WIN_NAME,
            FILD_IDS,
            STATUS_CODE,
            DESCRIPTION_PLAINTXT,
            IS_LOGIN,
            MODE_TYPE,
            DESCRIPTION2,
            IS_SHARED,
            SHARE_ICON_URL,
            SHARE_DESC,
            IS_GIFTPOINTS,
            IS_GIFTCOUPONS,
            SHARE_SCORE,
            SHARE_COUPONS,
            MAX_VERSION,
            MIN_VERSION,
            DISPLAY_TIME,
            ORDER_NUM,
            TITLE_STYLE
        )
        values(
                  #{picId,jdbcType=VARCHAR},
                  #{startTime,jdbcType=VARCHAR},
                  #{endTime,jdbcType=VARCHAR},
                  #{title,jdbcType=VARCHAR},
                  #{status,jdbcType=VARCHAR},
                  #{updateTime,jdbcType=VARCHAR},
                  #{createTime,jdbcType=VARCHAR},
                  #{createMan,jdbcType=VARCHAR},
                  #{url,jdbcType=VARCHAR},
                  #{winName,jdbcType=VARCHAR},
                  #{fileIds,jdbcType=VARCHAR},
                  #{statusCode,jdbcType=VARCHAR},
                  #{descriptionPlainTxt,jdbcType=VARCHAR},
                  #{isLogin,jdbcType=VARCHAR},
                  #{modeType,jdbcType=VARCHAR},
                  #{description2,jdbcType=BLOB},
                  #{isShared,jdbcType=VARCHAR},
                  #{shareIconUrl,jdbcType=VARCHAR},
                  #{shareDesc,jdbcType=VARCHAR},
                  #{isGiftPoints,jdbcType=VARCHAR},
                  #{isGiftCoupons,jdbcType=VARCHAR},
                  #{shareScore,jdbcType=NUMERIC},
                  #{shareCouponsStr,jdbcType=VARCHAR},
                  #{maxVersion,jdbcType=VARCHAR},
                  #{minVersion,jdbcType=VARCHAR},
                  #{displayTime,jdbcType=NUMERIC},
                  #{orderNum,jdbcType = INTEGER},
                  #{titleStyle,jdbcType=VARCHAR})
    </insert>


</mapper>
