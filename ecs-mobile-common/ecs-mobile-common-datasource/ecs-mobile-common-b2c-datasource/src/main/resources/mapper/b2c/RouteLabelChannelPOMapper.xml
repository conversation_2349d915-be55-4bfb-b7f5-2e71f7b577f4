<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.RouteLabelChannelPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.RouteLabelChannelPO">
    <!--@mbg.generated-->
    <!--@Table T_ROUTE_LABEL_CHANNEL-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="LABEL_ID" jdbcType="VARCHAR" property="labelId" />
    <result column="CHANNEL" jdbcType="VARCHAR" property="channel" />
    <result column="CHANNEL_URL" jdbcType="VARCHAR" property="channelUrl" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, LABEL_ID, CHANNEL, CHANNEL_URL, CREATED_BY, CREATED_TIME, UPDATED_BY, UPDATED_TIME
  </sql>

  <select id="selectByLabelId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    FROM T_ROUTE_LABEL_CHANNEL
    WHERE LABEL_ID = #{id,jdbcType=VARCHAR}
  </select>
</mapper>