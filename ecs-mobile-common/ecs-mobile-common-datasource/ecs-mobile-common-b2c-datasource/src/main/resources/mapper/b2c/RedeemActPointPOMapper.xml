<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.RedeemActPointPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.RedeemActPointPO">
    <!--@mbg.generated-->
    <!--@Table T_REDEEM_ACT_POINT-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="FFP_ID" jdbcType="VARCHAR" property="ffpId" />
    <result column="FFP_NO" jdbcType="VARCHAR" property="ffpNo" />
    <result column="POINT_ENUM_TYPE" jdbcType="VARCHAR" property="pointEnumType" />
    <result column="POINT" jdbcType="DECIMAL" property="point" />
    <result column="SIGN_MONTH" jdbcType="VARCHAR" property="signMonth" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, FFP_ID, FFP_NO, POINT_ENUM_TYPE, POINT, SIGN_MONTH, CREATED_TIME, UPDATED_TIME
  </sql>

  <select id="selectActUserList" resultType="com.juneyaoair.ecs.manage.dto.activity.redeem.segact.SegActUser">
    SELECT FFP_NO                                                              AS ffpNo,
           SUM(CASE WHEN POINT_ENUM_TYPE = 'SEG_PRO' THEN POINT ELSE 0 END)    AS pointCount,
           SUM(CASE WHEN POINT_ENUM_TYPE = 'SEG_NORMAL' THEN POINT ELSE 0 END) AS proPointCount
    FROM T_REDEEM_ACT_POINT
    WHERE ROWNUM &lt; 3000
    <if test="ffpNo != null and ffpNo != ''">
      AND FFP_NO = #{ffpNo,jdbcType=VARCHAR}
    </if>
    GROUP BY FFP_NO
  </select>

  <select id="selectByFfpNo" resultMap="BaseResultMap">
    SELECT *
    FROM T_REDEEM_ACT_POINT
    WHERE FFP_NO = #{requestDto.ffpNo,jdbcType=VARCHAR}
  </select>
</mapper>