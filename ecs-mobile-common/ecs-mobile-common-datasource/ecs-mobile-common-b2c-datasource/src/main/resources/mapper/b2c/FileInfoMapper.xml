<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.FileInfoMapper">
    <!-- @方法：updateFileInfo 修改文件 @参数：FileInfoPO 用户修改的文件信息 @返回值：无 -->
    <sql id="Base_Column_List">
        fileid,
        linkid,
        fileName,
        fileUrl,
        fileSize,
        createTime,
        creatorId,
        modifyTime,
        realname,
        imgserviceurl,
        hostname,
        filePath,
        fileType,
        serverPath
    </sql>
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.FileInfoPO">
        <result column="fileid" property="fileid"/>
        <result column="linkid" property="linkid"/>
        <result column="fileName" property="fileName"/>
        <result column="fileUrl" property="fileUrl"/>
        <result column="fileSize" property="fileSize"/>
        <result column="createTime" property="createTime"/>
        <result column="creatorId" property="creatorId"/>
        <result column="modifyTime" property="modifyTime"/>
        <result column="realname" property="realname"/>
        <result column="imgserviceurl" property="imgserviceurl"/>
        <result column="hostname" property="hostname"/>
        <result column="filePath" property="filePath"/>
        <result column="fileType" property="fileType"/>
        <result column="serverPath" property="serverPath"/>
    </resultMap>

    <resultMap type="com.juneyaoair.ecs.manage.dto.activity.response.FileInfo" id="FileInfo">
        <id property="fileId" column="FILEID" javaType="string" jdbcType="VARCHAR" />
        <result property="linkId" column="LINKID" javaType="string"
                jdbcType="VARCHAR" />
        <result property="fileName" column="FILE_NAME" javaType="string"
                jdbcType="VARCHAR" />
        <result property="fileURL" column="FILE_URL" javaType="string"
                jdbcType="VARCHAR" />
        <result property="fileSize" column="FILE_SIZE" javaType="string"
                jdbcType="VARCHAR" />
        <result property="createTime" column="CREATE_TIME" javaType="java.sql.Date"
                jdbcType="DATE" />
        <result property="creatorId" column="CREATOR_ID" javaType="string"
                jdbcType="VARCHAR" />
        <result property="realName" column="REALNAME" javaType="string"
                jdbcType="VARCHAR" />
        <result property="imgServiceUrl" column="IMGSERVICEURL" javaType="string"
                jdbcType="VARCHAR" />
        <result property="modifyTime" column="MODIFY_TIME" javaType="java.sql.Date"
                jdbcType="DATE" />
        <result property="fileType" column="FILE_TYPE" javaType="string"
                jdbcType="VARCHAR" />
    </resultMap>

    <update id="updateFileInfo" parameterType="com.juneyaoair.manage.b2c.entity.FileInfoPO">
        update TP_FILE_INFO
        set LINKID=#{linkid,jdbcType=VARCHAR},
            FILE_NAME=#{fileName,jdbcType=VARCHAR},
            FILE_URL=#{fileUrl,jdbcType=VARCHAR},
            FILE_SIZE=#{fileSize,jdbcType=VARCHAR},
            REALNAME=#{realname,jdbcType=VARCHAR},
            IMGSERVICEURL=#{imgserviceurl,jdbcType=VARCHAR},
            CREATOR_ID=#{creatorId,jdbcType=VARCHAR},
            HOSTNAME=#{hostname,jdbcType=VARCHAR},
            FILE_PATH=#{filePath,jdbcType=VARCHAR},
            MODIFY_TIME=sysdate
        where FILEID = #{fileid,jdbcType=VARCHAR}
    </update>

    <!-- @方法：insertFileInfo 添加新的文件 @参数：FileInfoPO 用户选择的文件信息 @返回值：无 -->
    <insert id="insertFileInfo" parameterType="com.juneyaoair.manage.b2c.entity.FileInfoPO">
        insert into TP_FILE_INFO
                (FILEID, LINKID, FILE_NAME, FILE_URL, FILE_SIZE, CREATE_TIME, CREATOR_ID, REALNAME, IMGSERVICEURL,
                 HOSTNAME, FILE_PATH, FILE_TYPE)
        values (#{fileid,jdbcType=VARCHAR},
                #{linkid,jdbcType=VARCHAR},
                #{fileName,jdbcType=VARCHAR},
                #{fileUrl,jdbcType=VARCHAR},
                #{fileSize,jdbcType=VARCHAR},
                #{createTime,jdbcType=DATE},
                #{creatorId,jdbcType=VARCHAR},
                #{realname,jdbcType=VARCHAR},
                #{imgserviceurl,jdbcType=VARCHAR},
                #{hostname,jdbcType=VARCHAR},
                #{filePath,jdbcType=VARCHAR},
                #{fileType,jdbcType=VARCHAR})
    </insert>

    <select id="selectByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TP_FILE_INFO
        <where>
            <if test="fileid != null">
                and fileid = #{fileid}
            </if>
            <if test="linkid != null">
                and linkid = #{linkid}
            </if>
            <if test="fileName != null">
                and fileName = #{fileName}
            </if>
            <if test="fileUrl != null">
                and fileUrl = #{fileUrl}
            </if>
            <if test="fileSize != null">
                and fileSize = #{fileSize}
            </if>
            <if test="createTime != null">
                and createTime = #{createTime}
            </if>
            <if test="creatorId != null">
                and creatorId = #{creatorId}
            </if>
            <if test="modifyTime != null">
                and modifyTime = #{modifyTime}
            </if>
            <if test="realname != null">
                and realname = #{realname}
            </if>
            <if test="imgserviceurl != null">
                and imgserviceurl = #{imgserviceurl}
            </if>
            <if test="hostname != null">
                and hostname = #{hostname}
            </if>
            <if test="filePath != null">
                and filePath = #{filePath}
            </if>
            <if test="fileType != null">
                and fileType = #{fileType}
            </if>
            <if test="serverPath != null">
                and serverPath = #{serverPath}
            </if>
        </where>
    </select>
    <select id="searchAllByCondition" parameterType="com.juneyaoair.ecs.manage.dto.activity.response.FileInfo" resultMap="FileInfo">
        select * from TP_FILE_INFO
        <where>
            <if test="fileId != null and fileId != ''">
                fileid > #{fileid,jdbcType=VARCHAR}
            </if>
            <if test="linkId != null and linkId != ''">
                AND linkid = #{linkid,jdbcType=VARCHAR}
            </if>
            <if test="fileType != null and fileType != ''">
                AND fileType = #{fileType,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- @方法：updateFileInfo 修改文件 @参数：FileInfo 用户修改的文件信息 @返回值：无 -->
    <update id="updateFileInfoV2" parameterType="com.juneyaoair.ecs.manage.dto.activity.response.FileInfo">
        update TP_FILE_INFO
        set
            LINKID=#{linkId,jdbcType=VARCHAR},
            FILE_NAME=#{fileName,jdbcType=VARCHAR},
            FILE_URL=#{fileURL,jdbcType=VARCHAR},
            FILE_SIZE=#{fileSize,jdbcType=VARCHAR},
            REALNAME=#{realName,jdbcType=VARCHAR},
            IMGSERVICEURL=#{imgServiceUrl,jdbcType=VARCHAR},
            CREATOR_ID=#{creatorId,jdbcType=VARCHAR},
            HOSTNAME=#{hostName,jdbcType=VARCHAR},
            FILE_PATH=#{filePath,jdbcType=VARCHAR},
            MODIFY_TIME=sysdate
        where
            FILEID=#{fileId,jdbcType=VARCHAR}
    </update>

    <!-- @方法：insertFileInfo 添加新的文件 @参数：FileInfo 用户选择的文件信息 @返回值：无 -->
    <insert id="insertFileInfoV2" parameterType="com.juneyaoair.ecs.manage.dto.activity.response.FileInfo">
        insert into
            TP_FILE_INFO
        (FILEID,LINKID,FILE_NAME,FILE_URL,FILE_SIZE,CREATE_TIME,CREATOR_ID,REALNAME,IMGSERVICEURL,HOSTNAME,FILE_PATH,FILE_TYPE)
        values
            (#{fileId,jdbcType=VARCHAR},
             #{linkId,jdbcType=VARCHAR},
             #{fileName,jdbcType=VARCHAR},
             #{fileURL,jdbcType=VARCHAR},
             #{fileSize,jdbcType=VARCHAR},
             #{createTime,jdbcType=DATE},
             #{creatorId,jdbcType=VARCHAR},
             #{realName,jdbcType=VARCHAR},
             #{imgServiceUrl,jdbcType=VARCHAR},
             #{hostName,jdbcType=VARCHAR},
             #{filePath,jdbcType=VARCHAR},
             #{fileType,jdbcType=VARCHAR})
    </insert>
</mapper>
