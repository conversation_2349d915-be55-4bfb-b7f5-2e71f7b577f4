<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.I18nDictionaryPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.I18nDictionaryPO">
    <!--@mbg.generated-->
    <!--@Table I18N_DICTIONARY-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="DICTIONARY_NAME" jdbcType="VARCHAR" property="dictionaryName" />
    <result column="DICTIONARY_TYPE" jdbcType="VARCHAR" property="dictionaryType" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, DICTIONARY_NAME, DICTIONARY_TYPE, REMARK, CREATED_TIME
  </sql>
</mapper>