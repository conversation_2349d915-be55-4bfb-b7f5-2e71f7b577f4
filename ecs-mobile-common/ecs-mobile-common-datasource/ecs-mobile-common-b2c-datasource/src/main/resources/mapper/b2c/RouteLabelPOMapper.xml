<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.RouteLabelPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.RouteLabelPO">
    <!--@mbg.generated-->
    <!--@Table T_ROUTE_LABEL-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="LABEL_NAME" jdbcType="VARCHAR" property="labelName" />
    <result column="LABEL_IMG" jdbcType="VARCHAR" property="labelImg" />
    <result column="DISPLAY_AREA_TITLE" jdbcType="VARCHAR" property="displayAreaTitle" />
    <result column="DISPLAY_VICE_TEXT" jdbcType="VARCHAR" property="displayViceText" />
    <result column="DISPLAY_MAIN_TEXT" jdbcType="VARCHAR" property="displayMainText" />
    <result column="START_DATE" jdbcType="TIMESTAMP" property="startDate" />
    <result column="END_DATE" jdbcType="TIMESTAMP" property="endDate" />
    <result column="LABEL_FUNCTION" jdbcType="VARCHAR" property="labelFunction" />
    <result column="ENABLE_STATUS" jdbcType="VARCHAR" property="enableStatus" />
    <result column="SORT_NUM" jdbcType="DECIMAL" property="sortNum" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="LABEL_DISPLAY_TYPE" jdbcType="VARCHAR" property="labelDisplayType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, LABEL_NAME, START_DATE, END_DATE, LABEL_FUNCTION, ENABLE_STATUS, SORT_NUM,
    REMARK, CREATED_BY, CREATED_TIME, UPDATED_BY, UPDATED_TIME, LABEL_DISPLAY_TYPE,
      DISPLAY_AREA_TITLE,DISPLAY_VICE_TEXT,DISPLAY_MAIN_TEXT
  </sql>

  <resultMap id="RouteLabelBO" type="com.juneyaoair.ecs.manage.dto.airline.routelabel.bo.RouteLabelBO">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="LABEL_NAME" jdbcType="VARCHAR" property="labelName" />
    <result column="LABEL_IMG" jdbcType="VARCHAR" property="labelImg" />
    <result column="DISPLAY_AREA_TITLE" jdbcType="VARCHAR" property="displayAreaTitle" />
    <result column="DISPLAY_VICE_TEXT" jdbcType="VARCHAR" property="displayViceText" />
    <result column="DISPLAY_MAIN_TEXT" jdbcType="VARCHAR" property="displayMainText" />
    <result column="START_DATE" jdbcType="TIMESTAMP" property="startDate" />
    <result column="END_DATE" jdbcType="TIMESTAMP" property="endDate" />
    <result column="LABEL_FUNCTION" jdbcType="VARCHAR" property="labelFunction" />
    <result column="ENABLE_STATUS" jdbcType="VARCHAR" property="enableStatus" />
    <result column="SORT_NUM" jdbcType="DECIMAL" property="sortNum" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="LABEL_DISPLAY_TYPE" jdbcType="VARCHAR" property="labelDisplayType" />
    <collection column="ID" ofType="com.juneyaoair.ecs.manage.dto.airline.routelabel.bo.RouteLabelBO$RouteLabelRuleBO" property="ruleBOList">
        <id column="RULE_ID" jdbcType="VARCHAR" property="ruleId" />
        <result column="LABEL_RULE_NAME" jdbcType="VARCHAR" property="labelRuleName" />
        <result column="ROUTE_START_DATE" jdbcType="TIMESTAMP" property="routeStartDate" />
        <result column="ROUTE_END_DATE" jdbcType="TIMESTAMP" property="routeEndDate" />
        <result column="DEP_AIRPORT" jdbcType="VARCHAR" property="depAirport" />
        <result column="DEP_TERMINAL" jdbcType="VARCHAR" property="depTerminal" />
        <result column="DEP_COUNTRY" jdbcType="VARCHAR" property="depCountry" />
        <result column="DEP_REGION" jdbcType="VARCHAR" property="depRegion" />
        <result column="ARR_AIRPORT" jdbcType="VARCHAR" property="arrAirport" />
        <result column="ARR_TERMINAL" jdbcType="VARCHAR" property="arrTerminal" />
        <result column="ARR_COUNTRY" jdbcType="VARCHAR" property="arrCountry" />
        <result column="ARR_REGION" jdbcType="VARCHAR" property="arrRegion" />
        <result column="CARRIER" jdbcType="VARCHAR" property="carrier" />
        <result column="IS_TRANSIT" jdbcType="VARCHAR" property="transit" />
        <result column="TRANS_AIRPORT" jdbcType="VARCHAR" property="transAirport" />
        <result column="TRANS_DEP_TERMINAL" jdbcType="VARCHAR" property="transDepTerminal" />
        <result column="TRANS_SAME_AIRPORT" jdbcType="VARCHAR" property="transSameAirport" />
        <result column="TRANS_TIME" jdbcType="VARCHAR" property="transTime" />
        <result column="TRANS_DATE_LIMIT" jdbcType="VARCHAR" property="transDateLimit" />
        <result column="TRANS_PRE_FLIGHT_DATE_LIMIT" jdbcType="DECIMAL" property="transPreFlightDateLimit" />
        <result column="TRANS_NEXT_FLIGHT_DATE_LIMIT" jdbcType="DECIMAL" property="transNextFlightDateLimit" />
        <result column="RULE_LABEL_FUNCTION" jdbcType="VARCHAR" property="labelFunction" />
        <result column="RULE_ENABLE_STATUS" jdbcType="VARCHAR" property="enableStatus" />
        <result column="RULE_SORT_NUM" jdbcType="DECIMAL" property="sortNum" />
        <result column="RULE_REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="RULE_CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
        <result column="RULE_CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="RULE_UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
        <result column="RULE_UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
        <result column="APPLY_FLIGHT_NO" jdbcType="VARCHAR" property="applyFlightNo" />
        <result column="APPLY_AIRCRAFT_TYPE" jdbcType="VARCHAR" property="applyAircraftType" />
    </collection>
      <collection column="ID" ofType="com.juneyaoair.ecs.manage.dto.airline.routelabel.bo.RouteLabelBO$RouteLabelChannelDTO" property="channelList">
          <id column="CHANNEL_ID" jdbcType="VARCHAR" property="channelId"/>
          <result column="CHANNEL" jdbcType="VARCHAR" property="channel" />
          <result column="CHANNEL_URL" jdbcType="VARCHAR" property="channelUrl" />
      </collection>
  </resultMap>

  <select id="selectListBO" resultMap="RouteLabelBO">
    SELECT rl.ID,
           rl.LABEL_NAME,
           rl.LABEL_IMG,
           rl.DISPLAY_AREA_TITLE,
           rl.DISPLAY_VICE_TEXT,
           rl.DISPLAY_MAIN_TEXT,
           rl.START_DATE,
           rl.END_DATE,
           rl.LABEL_FUNCTION,
           rl.ENABLE_STATUS,
           rl.SORT_NUM,
           rl.REMARK,
           rl.CREATED_BY,
           rl.CREATED_TIME,
           rl.UPDATED_BY,
           rl.UPDATED_TIME,
           rl.LABEL_DISPLAY_TYPE,
              rlr.ID             AS RULE_ID,
              rlr.LABEL_RULE_NAME,
              rlr.DEP_AIRPORT,
              rlr.DEP_TERMINAL,
              rlr.DEP_COUNTRY,
              rlr.DEP_REGION,
              rlr.ARR_AIRPORT,
              rlr.ARR_TERMINAL,
              rlr.ARR_COUNTRY,
              rlr.ARR_REGION,
              rlr.CARRIER,
              rlr.IS_TRANSIT,
              rlr.TRANS_AIRPORT,
              rlr.TRANS_DEP_TERMINAL,
              rlr.TRANS_SAME_AIRPORT,
              rlr.TRANS_TIME,
              rlr.TRANS_DATE_LIMIT,
              rlr.TRANS_PRE_FLIGHT_DATE_LIMIT,
              rlr.TRANS_NEXT_FLIGHT_DATE_LIMIT,
              rlr.ROUTE_START_DATE,
              rlr.ROUTE_END_DATE,
              rlr.LABEL_FUNCTION AS RULE_LABEL_FUNCTION,
              rlr.ENABLE_STATUS  AS RULE_ENABLE_STATUS,
              rlr.SORT_NUM       AS RULE_SORT_NUM,
              rlr.REMARK         AS RULE_REMARK,
              rlr.CREATED_BY     AS RULE_CREATED_BY,
              rlr.CREATED_TIME   AS RULE_CREATED_TIME,
              rlr.UPDATED_BY     AS RULE_UPDATED_BY,
              rlr.UPDATED_TIME   AS RULE_UPDATED_TIME,
              rlr.APPLY_FLIGHT_NO,
              rlr.APPLY_AIRCRAFT_TYPE,
                  rlc.ID AS CHANNEL_ID,
                  rlc.CHANNEL,
                  rlc.CHANNEL_URL
    FROM T_ROUTE_LABEL rl
             left join T_ROUTE_LABEL_RULE rlr ON rl.ID = rlr.LABEL_ID
             left join T_ROUTE_LABEL_CHANNEL rlc on rl.ID = rlc.LABEL_ID
    <where>
        <if test="labelName != null and labelName != ''">
            AND LABEL_NAME LIKE '%' || #{labelName,jdbcType=VARCHAR} || '%'
        </if>
        <if test="depAirport != null and depAirport != ''">
            AND rlr.DEP_AIRPORT LIKE '%' || #{depAirport,jdbcType=VARCHAR} || '%'
        </if>
        <if test="arrAirport != null and arrAirport != ''">
            AND rlr.ARR_AIRPORT LIKE '%' || #{arrAirport,jdbcType=VARCHAR} || '%'
        </if>
        <if test="routeDate != null and routeDate != ''">
            AND to_date(#{routeDate,jdbcType=VARCHAR}, 'yyyy-MM-dd hh24:mi:ss') between rlr.ROUTE_START_DATE and rlr.ROUTE_END_DATE
        </if>
        <if test="enableStatus !=null ">
            <if test="enableStatus">
                AND rl.ENABLE_STATUS = '1'
            </if>
            <if test="!enableStatus">
                AND rl.ENABLE_STATUS = '0'
            </if>
        </if>
    </where>
  </select>
</mapper>