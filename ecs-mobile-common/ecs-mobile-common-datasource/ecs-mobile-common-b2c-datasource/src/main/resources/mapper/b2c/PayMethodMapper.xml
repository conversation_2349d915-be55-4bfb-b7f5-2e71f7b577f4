<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.PayMethodMapper">

    <sql id="Base_Column_List">
        ID,
        PAY_METHOD_NAME,
        PAY_PRODUCT_TYPE,
        CHANNEL,
        STATUS,
        ORDER_NO,
        PAY_LOGO_STREAM,
        UNIONPAY_LOGO_STREAM,
        CREATER,
        CREATE_TIME,
        UPDATER,
        UPDATE_TIME,
        PAY_METHOD_CODE,
        IS_DEFAULT,
        IS_HIDE,
        MERCHANT_PAYMENT,
        POSITION
    </sql>

    <insert id="addPayMethod">
        INSERT INTO T_PAY_METHOD (
        <include refid="Base_Column_List"/>
        ) VALUES (
        #{id,jdbcType=VARCHAR},
        #{payMethodName,jdbcType=VARCHAR},
        #{payProductType,jdbcType=VARCHAR},
        #{channel,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR},
        #{orderNo,jdbcType=INTEGER},
        #{payLogoStream,jdbcType=VARCHAR},
        #{unionpayLogoStream,jdbcType=VARCHAR},
        #{creater,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{updater,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP},
        #{payMethodCode,jdbcType=VARCHAR},
        #{isDefault,jdbcType=VARCHAR},
        #{isHide,jdbcType=VARCHAR},
        #{merchantPayment,jdbcType=VARCHAR},
        #{position,jdbcType=VARCHAR}
        )

    </insert>

    <update id="updatePayMethodById">
        UPDATE T_PAY_METHOD
        <set>
            <if test="payMethodName != null and payMethodName != ''">
                PAY_METHOD_NAME = #{payMethodName,jdbcType=VARCHAR},
            </if>
            <if test="payProductType != null and payProductType != ''">
                PAY_PRODUCT_TYPE = #{payProductType,jdbcType=VARCHAR},
            </if>
            <if test="channel != null and channel != ''">
                CHANNEL = #{channel,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                STATUS = #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null and orderNo != ''">
                ORDER_NO = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="payLogoStream != null and payLogoStream != ''">
                PAY_LOGO_STREAM = #{payLogoStream,jdbcType=VARCHAR},
            </if>
            <if test="unionpayLogoStream != null and unionpayLogoStream != ''">
                UNIONPAY_LOGO_STREAM = #{unionpayLogoStream,jdbcType=VARCHAR},
            </if>
            <if test="creater != null and creater != ''">
                CREATER = #{creater,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null and updater != ''">
                UPDATER = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payMethodCode != null and payMethodCode != ''">
                PAY_METHOD_CODE = #{payMethodCode,jdbcType=VARCHAR},
            </if>
            <if test="isDefault != null and isDefault != ''">
                IS_DEFAULT = #{isDefault,jdbcType=VARCHAR},
            </if>
            <if test="isHide != null and isHide != ''">
                IS_HIDE = #{isHide,jdbcType=VARCHAR},
            </if>
            <if test="merchantPayment != null and merchantPayment != ''">
                MERCHANT_PAYMENT = #{merchantPayment,jdbcType=VARCHAR},
            </if>
            <if test="position != null and position != ''">
                POSITION = #{position,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE ID = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateByConditionSelective">
        UPDATE T_PAY_METHOD
        <set>
            <if test="isDefault != null and isDefault != ''">
                IS_DEFAULT = #{isDefault,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null and updater != ''">
                UPDATER = #{updater,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE CHANNEL = #{channel,jdbcType=VARCHAR}
        AND PAY_PRODUCT_TYPE = #{payProductType,jdbcType=VARCHAR}
        AND IS_DEFAULT = 'Y'
        AND ID != #{id,jdbcType=VARCHAR}
    </update>

    <select id="payMethodListPageQuery" resultType="com.juneyaoair.manage.b2c.entity.PayMethodSearchPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_PAY_METHOD
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="getpayMethodById" resultType="com.juneyaoair.manage.b2c.entity.PayMethodPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_PAY_METHOD
        WHERE ID = #{id,jdbcType=VARCHAR}
    </select>

    <select id="selectByCondition" resultType="com.juneyaoair.manage.b2c.entity.PayMethodPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_PAY_METHOD
        WHERE CHANNEL = #{channel,jdbcType=VARCHAR}
        AND PAY_PRODUCT_TYPE = #{payProductType,jdbcType=VARCHAR}
        AND IS_DEFAULT = #{isDefault,jdbcType=VARCHAR}
        <if test="id != null and id != ''">
            AND ID != #{id,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>