<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TAirlineLabelMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.AirlineLabelPO">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="AIRLINE_ID" jdbcType="VARCHAR" property="airlineId" />
        <result column="SEQUENCE" jdbcType="VARCHAR" property="sequence" />
        <result column="LABEL_TYPE" jdbcType="VARCHAR" property="labelType" />
        <result column="LABEL_IMG" jdbcType="VARCHAR" property="labelImg" />
        <result column="LABEL_NAME" jdbcType="VARCHAR" property="labelName" />
        <result column="URL" jdbcType="VARCHAR" property="url" />
        <result column="FLIGHT_NOS" jdbcType="VARCHAR" property="flightNos" />
        <result column="START_TIME" jdbcType="VARCHAR" property="startTime" />
        <result column="END_TIME" jdbcType="VARCHAR" property="endTime" />
        <result column="FIT_FLIGHT_DATE_STR" jdbcType="VARCHAR" property="fitFlightDateStr" />
        <result column="APPLICABL_MODELS" jdbcType="VARCHAR" property="applicablModels" />
        <result column="FLOATING_DATE" jdbcType="VARCHAR" property="floatingDate" />
    </resultMap>
    <sql id="Base_Column_List">
        ID, AIRLINE_ID, "SEQUENCE", LABEL_TYPE, LABEL_NAME, URL, FLIGHT_NOS, START_TIME,
        END_TIME, FIT_FLIGHT_DATE_STR, APPLICABL_MODELS, FLOATING_DATE,LABEL_IMG
    </sql>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from T_AIRLINE_LABEL
        where ID = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.juneyaoair.manage.b2c.entity.AirlineLabelPO">
        insert into T_AIRLINE_LABEL (ID, AIRLINE_ID, "SEQUENCE",
                                     LABEL_TYPE, LABEL_IMG,LABEL_NAME, URL,
                                     FLIGHT_NOS, START_TIME, END_TIME,
                                     FIT_FLIGHT_DATE_STR, APPLICABL_MODELS, FLOATING_DATE)
        values (#{id,jdbcType=VARCHAR}, #{airlineId,jdbcType=VARCHAR}, #{sequence,jdbcType=VARCHAR},
                #{labelType,jdbcType=VARCHAR},#{labelImg,jdbcType=VARCHAR}, #{labelName,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR},
                #{flightNos,jdbcType=VARCHAR}, #{startTime,jdbcType=VARCHAR}, #{endTime,jdbcType=VARCHAR},
                #{fitFlightDateStr,jdbcType=VARCHAR}, #{applicablModels,jdbcType=VARCHAR},#{floatingDate,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.juneyaoair.manage.b2c.entity.AirlineLabelPO">
        insert into T_AIRLINE_LABEL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="airlineId != null">
                AIRLINE_ID,
            </if>
            <if test="sequence != null">
                "SEQUENCE",
            </if>
            <if test="labelType != null">
                LABEL_TYPE,
            </if>
            <if test="labelImg != null">
                LABEL_IMG,
            </if>
            <if test="labelName != null">
                LABEL_NAME,
            </if>
            <if test="url != null">
                URL,
            </if>
            <if test="flightNos != null">
                FLIGHT_NOS,
            </if>
            <if test="startTime != null">
                START_TIME,
            </if>
            <if test="endTime != null">
                END_TIME,
            </if>
            <if test="fitFlightDateStr != null">
                FIT_FLIGHT_DATE_STR,
            </if>
            <if test="applicablModels != null">
                APPLICABL_MODELS,
            </if>
            <if test="floatingDate != null">
                FLOATING_DATE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="airlineId != null">
                #{airlineId,jdbcType=VARCHAR},
            </if>
            <if test="sequence != null">
                #{sequence,jdbcType=VARCHAR},
            </if>
            <if test="labelType != null">
                #{labelType,jdbcType=VARCHAR},
            </if>
            <if test="labelImg != null">
                #{labelImg,jdbcType=VARCHAR},
            </if>
            <if test="labelName != null">
                #{labelName,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="flightNos != null">
                #{flightNos,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=VARCHAR},
            </if>
            <if test="fitFlightDateStr != null">
                #{fitFlightDateStr,jdbcType=VARCHAR},
            </if>
            <if test="applicablModels != null">
                #{applicablModels,jdbcType=VARCHAR},
            </if>
            <if test="floatingDate != null">
                #{floatingDate,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.AirlineLabelPO">
        update T_AIRLINE_LABEL
        <set>
            <if test="airlineId != null">
                AIRLINE_ID = #{airlineId,jdbcType=VARCHAR},
            </if>
            <if test="sequence != null">
                "SEQUENCE" = #{sequence,jdbcType=VARCHAR},
            </if>
            <if test="labelType != null">
                LABEL_TYPE = #{labelType,jdbcType=VARCHAR},
            </if>
            <if test="labelImg != null">
                LABEL_IMG = #{labelImg,jdbcType=VARCHAR},
            </if>
            <if test="labelName != null">
                LABEL_NAME = #{labelName,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                URL = #{url,jdbcType=VARCHAR},
            </if>
            <if test="flightNos != null">
                FLIGHT_NOS = #{flightNos,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                START_TIME = #{startTime,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null">
                END_TIME = #{endTime,jdbcType=VARCHAR},
            </if>
            <if test="fitFlightDateStr != null">
                FIT_FLIGHT_DATE_STR = #{fitFlightDateStr,jdbcType=VARCHAR},
            </if>
            <if test="applicablModels != null">
                APPLICABL_MODELS = #{applicablModels,jdbcType=VARCHAR},
            </if>
            <if test="floatingDate != null">
                FLOATING_DATE = #{floatingDate,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.juneyaoair.manage.b2c.entity.AirlineLabelPO">
        update T_AIRLINE_LABEL
        set AIRLINE_ID          = #{airlineId,jdbcType=VARCHAR},
            "SEQUENCE"          = #{sequence,jdbcType=VARCHAR},
            LABEL_TYPE          = #{labelType,jdbcType=VARCHAR},
            LABEL_NAME          = #{labelName,jdbcType=VARCHAR},
            URL                 = #{url,jdbcType=VARCHAR},
            FLIGHT_NOS          = #{flightNos,jdbcType=VARCHAR},
            START_TIME          = #{startTime,jdbcType=VARCHAR},
            END_TIME            = #{endTime,jdbcType=VARCHAR},
            FIT_FLIGHT_DATE_STR = #{fitFlightDateStr,jdbcType=VARCHAR},
            APPLICABL_MODELS    = #{applicablModels,jdbcType=VARCHAR},
            FLOATING_DATE       = #{floatingDate,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <select id="selectByAirlineIdin" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_AIRLINE_LABEL
                where AIRLINE_ID in
        <foreach close=")" collection="airlineIdCollection" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_AIRLINE_LABEL
        <where>
            <if test="id != null">
                and ID = #{id,jdbcType=VARCHAR}
            </if>
            <if test="airlineId != null">
                and AIRLINE_ID = #{airlineId,jdbcType=VARCHAR}
            </if>
            <if test="sequence != null">
                and `SEQUENCE` = #{sequence,jdbcType=VARCHAR}
            </if>
            <if test="labelType != null">
                and LABEL_TYPE = #{labelType,jdbcType=VARCHAR}
            </if>
            <if test="labelName != null">
                and LABEL_NAME = #{labelName,jdbcType=VARCHAR}
            </if>
            <if test="url != null">
                and URL = #{url,jdbcType=VARCHAR}
            </if>
            <if test="flightNos != null">
                and FLIGHT_NOS = #{flightNos,jdbcType=VARCHAR}
            </if>
            <if test="startTime != null">
                and START_TIME = #{startTime,jdbcType=VARCHAR}
            </if>
            <if test="endTime != null">
                and END_TIME = #{endTime,jdbcType=VARCHAR}
            </if>
            <if test="fitFlightDateStr != null">
                and FIT_FLIGHT_DATE_STR = #{fitFlightDateStr,jdbcType=VARCHAR}
            </if>
            <if test="applicablModels != null">
                and APPLICABL_MODELS = #{applicablModels,jdbcType=VARCHAR}
            </if>
            <if test="floatingDate != null">
                and FLOATING_DATE = #{floatingDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectByPkId" resultMap="BaseResultMap">
        select
        *
        from T_AIRLINE_LABEL
        where
        ID = #{id,jdbcType=VARCHAR}
    </select>
</mapper>