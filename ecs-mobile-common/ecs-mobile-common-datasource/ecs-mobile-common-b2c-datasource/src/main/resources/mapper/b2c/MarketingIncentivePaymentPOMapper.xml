<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.MarketingIncentivePaymentPOMapper">

    <sql id="Base_Column_List">
        ID
        ,DEPARTMENT,EMPLOYEE_NUMBER,
        DOMAIN_ACCOUNT,EMPLOYEE_CARD_NO,EMPLOYEE_NAME,
        REFERRAL_REWARD_STATUS,FIRST_TRIP_INCENTIVE_STATUS,DEPARTMENT_TIME,
        CREATED_BY,CREATED_TIME,UPDATED_BY,
        UPDATED_TIME
    </sql>

    <resultMap id="MarketingIncentiveResultMap"
               type="com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingTotalQueryResponse">
        <id column="employeeNumber" property="employeeNumber"/>
        <result column="employeeName" property="employeeName"/>
        <result column="department" property="department"/>
        <result column="domainAccount" property="domainAccount"/>
        <result column="employeeCardNo" property="employeeCardNo"/>
        <result column="newCustomerStatus" property="newCustomerStatus"/>
        <result column="firstTripStatus" property="firstTripStatus"/>
        <result column="totalTripTimes" property="totalTripTimes"/>
        <result column="internationalTripTimes" property="internationalTripTimes"/>
        <result column="domesticTripTimes" property="domesticTripTimes"/>
        <result column="totalRewards" property="totalRewards"/>
        <result column="developedCustomer" property="developedCustomer"/>
    </resultMap>

    <select id="queryMarketingData"
            parameterType="map"
            resultMap="MarketingIncentiveResultMap">
        SELECT
        MAX(A.EMPLOYEE_NUMBER) AS employeeNumber,
        MAX(A.EMPLOYEE_NAME) AS employeeName,
        A.DEPARTMENT AS department,
        A.DOMAIN_ACCOUNT AS domainAccount,
        MAX(A.EMPLOYEE_CARD_NO) AS employeeCardNo,

        MAX(CASE A.REFERRAL_REWARD_STATUS
        WHEN 'INIT' THEN '未发放'
        WHEN 'PREPARE' THEN '准备发放'
        WHEN 'SUCCESS' THEN '发放成功'
        WHEN 'FAILED' THEN '发放失败'
        ELSE '未知状态'
        END) AS newCustomerStatus,

        MAX(CASE A.FIRST_TRIP_INCENTIVE_STATUS
        WHEN 'INIT' THEN '未发放'
        WHEN 'PREPARE' THEN '准备发放'
        WHEN 'SUCCESS' THEN '发放成功'
        WHEN 'FAILED' THEN '发放失败'
        ELSE '未知状态'
        END) AS firstTripStatus,

        COALESCE(B.totalTripTimes, 0) AS totalTripTimes,
        COALESCE(B.internationalTripTimes, 0) AS internationalTripTimes,
        COALESCE(B.domesticTripTimes, 0) AS domesticTripTimes,
        COALESCE(B.totalRewards, 0) AS totalRewards,

        COALESCE(C.developedCustomer, 0) AS developedCustomer

        FROM T_MARKETING_INCENTIVE_PAYMENT A

        LEFT JOIN (
        SELECT
        DOMAIN_ACCOUNT,
        DEPARTMENT,
        COUNT(ID) AS totalTripTimes,
        SUM(CASE WHEN AIRLINE_TYPE = 'I' THEN 1 ELSE 0 END) AS internationalTripTimes,
        SUM(CASE WHEN AIRLINE_TYPE = 'D' THEN 1 ELSE 0 END) AS domesticTripTimes,
        SUM(CASE
        WHEN AIRLINE_TYPE = 'I' THEN 40
        WHEN AIRLINE_TYPE = 'D' THEN 20
        ELSE 0
        END) AS totalRewards
        FROM T_TRIP_TRANSACTION
        WHERE TO_DATE(FLIGHT_DATE, 'YYYY-MM-DD') BETWEEN TO_DATE(#{query.tripStartTime}, 'YYYY-MM-DD')
        AND TO_DATE(#{query.tripEndTime}, 'YYYY-MM-DD')
        AND TO_DATE(ASSOCIATED_TIME, 'YYYY-MM-DD HH24:MI:SS')
        BETWEEN TO_DATE(#{query.associatedStartTime}, 'YYYY-MM-DD HH24:MI:SS')
        AND TO_DATE(#{query.associatedEndTime}, 'YYYY-MM-DD HH24:MI:SS')
        GROUP BY DOMAIN_ACCOUNT, DEPARTMENT
        ) B ON A.DOMAIN_ACCOUNT = B.DOMAIN_ACCOUNT AND A.DEPARTMENT = B.DEPARTMENT

        LEFT JOIN (
        SELECT
        NAME AS DOMAIN_ACCOUNT,
        SCENE_SOURCE AS DEPARTMENT,
        COUNT(DISTINCT FFP_CARDNO) AS developedCustomer
        FROM MP_SCENE_QRCODE_RECORD
        WHERE TO_DATE(ASSOCIATED_TIME, 'YYYY-MM-DD HH24:MI:SS')
        BETWEEN TO_DATE(#{query.associatedStartTime}, 'YYYY-MM-DD HH24:MI:SS')
        AND TO_DATE(#{query.associatedEndTime}, 'YYYY-MM-DD HH24:MI:SS')
        GROUP BY NAME, SCENE_SOURCE
        ) C ON A.DOMAIN_ACCOUNT = C.DOMAIN_ACCOUNT AND A.DEPARTMENT = C.DEPARTMENT

        <where>
            <if test="query.domainAccount != null and query.domainAccount != ''">
                AND A.DOMAIN_ACCOUNT = #{query.domainAccount}
            </if>
            <if test="query.department != null and query.department != ''">
                AND A.DEPARTMENT = #{query.department}
            </if>
            <if test="query.employeeCardNo != null and query.employeeCardNo != ''">
                AND A.EMPLOYEE_CARD_NO = #{query.employeeCardNo}
            </if>
        </where>

        GROUP BY A.DEPARTMENT, A.DOMAIN_ACCOUNT,
        B.totalTripTimes, B.internationalTripTimes, B.domesticTripTimes, B.totalRewards,
        C.developedCustomer
    </select>

    <select id="queryMarketingDataCount" resultType="long" parameterType="map">
        SELECT COUNT(*) FROM (
        SELECT 1
        FROM T_MARKETING_INCENTIVE_PAYMENT A

        LEFT JOIN (
        SELECT DOMAIN_ACCOUNT, DEPARTMENT
        FROM T_TRIP_TRANSACTION
        WHERE TO_DATE(FLIGHT_DATE, 'YYYY-MM-DD') BETWEEN TO_DATE(#{query.tripStartTime}, 'YYYY-MM-DD')
        AND TO_DATE(#{query.tripEndTime}, 'YYYY-MM-DD')
        AND TO_DATE(ASSOCIATED_TIME, 'YYYY-MM-DD HH24:MI:SS')
        BETWEEN TO_DATE(#{query.associatedStartTime}, 'YYYY-MM-DD HH24:MI:SS')
        AND TO_DATE(#{query.associatedEndTime}, 'YYYY-MM-DD HH24:MI:SS')
        GROUP BY DOMAIN_ACCOUNT, DEPARTMENT
        ) B ON A.DOMAIN_ACCOUNT = B.DOMAIN_ACCOUNT AND A.DEPARTMENT = B.DEPARTMENT

        LEFT JOIN (
        SELECT NAME AS DOMAIN_ACCOUNT, SCENE_SOURCE AS DEPARTMENT
        FROM MP_SCENE_QRCODE_RECORD
        WHERE TO_DATE(ASSOCIATED_TIME, 'YYYY-MM-DD HH24:MI:SS')
        BETWEEN TO_DATE(#{query.associatedStartTime}, 'YYYY-MM-DD HH24:MI:SS')
        AND TO_DATE(#{query.associatedEndTime}, 'YYYY-MM-DD HH24:MI:SS')
        GROUP BY NAME, SCENE_SOURCE
        ) C ON A.DOMAIN_ACCOUNT = C.DOMAIN_ACCOUNT AND A.DEPARTMENT = C.DEPARTMENT

        <where>
            <if test="query.domainAccount != null and query.domainAccount != ''">
                AND A.DOMAIN_ACCOUNT = #{query.domainAccount}
            </if>
            <if test="query.department != null and query.department != ''">
                AND A.DEPARTMENT = #{query.department}
            </if>
            <if test="query.employeeCardNo != null and query.employeeCardNo != ''">
                AND A.EMPLOYEE_CARD_NO = #{query.employeeCardNo}
            </if>
        </where>

        GROUP BY A.DEPARTMENT, A.DOMAIN_ACCOUNT
        ) tmp
    </select>

    <select id="queryMarketingDataForExport"
            parameterType="map"
            resultType="com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingTotalQueryResponse">
        SELECT
        MAX(A.EMPLOYEE_NUMBER) AS employeeNumber,
        MAX(A.EMPLOYEE_NAME) AS employeeName,
        A.DEPARTMENT AS department,
        A.DOMAIN_ACCOUNT AS domainAccount,
        MAX(A.EMPLOYEE_CARD_NO) AS employeeCardNo,

        MAX(CASE A.REFERRAL_REWARD_STATUS
        WHEN 'INIT' THEN '未发放'
        WHEN 'PREPARE' THEN '准备发放'
        WHEN 'SUCCESS' THEN '发放成功'
        WHEN 'FAILED' THEN '发放失败'
        ELSE '未知状态'
        END) AS newCustomerStatus,

        MAX(CASE A.FIRST_TRIP_INCENTIVE_STATUS
        WHEN 'INIT' THEN '未发放'
        WHEN 'PREPARE' THEN '准备发放'
        WHEN 'SUCCESS' THEN '发放成功'
        WHEN 'FAILED' THEN '发放失败'
        ELSE '未知状态'
        END) AS firstTripStatus,

        MAX(COALESCE(B.totalTripTimes, 0)) AS totalTripTimes,
        MAX(COALESCE(B.internationalTripTimes, 0)) AS internationalTripTimes,
        MAX(COALESCE(B.domesticTripTimes, 0)) AS domesticTripTimes,
        MAX(COALESCE(B.totalRewards, 0)) AS totalRewards,

        MAX(COALESCE(C.developedCustomer, 0)) AS developedCustomer

        FROM T_MARKETING_INCENTIVE_PAYMENT A

        LEFT JOIN (
        SELECT
        DOMAIN_ACCOUNT,
        DEPARTMENT,
        COUNT(ID) AS totalTripTimes,
        SUM(CASE WHEN AIRLINE_TYPE = 'I' THEN 1 ELSE 0 END) AS internationalTripTimes,
        SUM(CASE WHEN AIRLINE_TYPE = 'D' THEN 1 ELSE 0 END) AS domesticTripTimes,
        SUM(CASE
        WHEN AIRLINE_TYPE = 'I' THEN 40
        WHEN AIRLINE_TYPE = 'D' THEN 20
        ELSE 0
        END) AS totalRewards
        FROM T_TRIP_TRANSACTION
        WHERE TO_DATE(FLIGHT_DATE, 'YYYY-MM-DD') BETWEEN TO_DATE(#{query.tripStartTime}, 'YYYY-MM-DD')
        AND TO_DATE(#{query.tripEndTime}, 'YYYY-MM-DD')
        AND TO_DATE(ASSOCIATED_TIME, 'YYYY-MM-DD HH24:MI:SS')
        BETWEEN TO_DATE(#{query.associatedStartTime}, 'YYYY-MM-DD HH24:MI:SS')
        AND TO_DATE(#{query.associatedEndTime}, 'YYYY-MM-DD HH24:MI:SS')
        GROUP BY DOMAIN_ACCOUNT, DEPARTMENT
        ) B ON A.DOMAIN_ACCOUNT = B.DOMAIN_ACCOUNT AND A.DEPARTMENT = B.DEPARTMENT

        LEFT JOIN (
        SELECT
        NAME AS DOMAIN_ACCOUNT,
        SCENE_SOURCE AS DEPARTMENT,
        COUNT(DISTINCT FFP_CARDNO) AS developedCustomer
        FROM MP_SCENE_QRCODE_RECORD
        WHERE TO_DATE(ASSOCIATED_TIME, 'YYYY-MM-DD HH24:MI:SS')
        BETWEEN TO_DATE(#{query.associatedStartTime}, 'YYYY-MM-DD HH24:MI:SS')
        AND TO_DATE(#{query.associatedEndTime}, 'YYYY-MM-DD HH24:MI:SS')
        GROUP BY NAME, SCENE_SOURCE
        ) C ON A.DOMAIN_ACCOUNT = C.DOMAIN_ACCOUNT AND A.DEPARTMENT = C.DEPARTMENT

        <where>
            <if test="query.domainAccount != null and query.domainAccount != ''">
                AND A.DOMAIN_ACCOUNT = #{query.domainAccount}
            </if>
            <if test="query.department != null and query.department != ''">
                AND A.DEPARTMENT = #{query.department}
            </if>
            <if test="query.employeeCardNo != null and query.employeeCardNo != ''">
                AND A.EMPLOYEE_CARD_NO = #{query.employeeCardNo}
            </if>
        </where>

        GROUP BY A.DEPARTMENT, A.DOMAIN_ACCOUNT
    </select>

    <resultMap id="TripIncentiveResultMap"
               type="com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingDetailQueryResponse">
        <id column="DOMAIN_ACCOUNT" property="domainAccount"/>
        <id column="PASSENGER_CARD_NO" property="passengerCardNo"/>
        <result column="DEPARTMENT" property="department"/>
        <result column="ASSOCIATED_TIME" property="associatedTime"/>
        <result column="INTERNATIONAL_TRIP_TIMES" property="internationalTripTimes"/>
        <result column="DOMESTIC_TRIP_TIMES" property="domesticTripTimes"/>
        <result column="TOTAL_TRIP_TIMES" property="totalTripTimes"/>
        <result column="TOTAL_REWARDS" property="totalRewards"/>
    </resultMap>

    <select id="getTripIncentiveStatistics"
            parameterType="com.juneyaoair.ecs.manage.dto.activity.request.integratingmarketingController.IntegratingMarketingDetailQueryRequest"
            resultMap="TripIncentiveResultMap">
        SELECT
        a.DOMAIN_ACCOUNT,
        b.FFP_CARDNO AS PASSENGER_CARD_NO,
        a.DEPARTMENT,
        MAX(b.ASSOCIATED_TIME) AS ASSOCIATED_TIME,
        SUM(CASE WHEN c.AIRLINE_TYPE = 'I' THEN 1 ELSE 0 END) AS INTERNATIONAL_TRIP_TIMES,
        SUM(CASE WHEN c.AIRLINE_TYPE = 'D' THEN 1 ELSE 0 END) AS DOMESTIC_TRIP_TIMES,
        COUNT(c.ID) AS TOTAL_TRIP_TIMES,
        (SUM(CASE WHEN c.AIRLINE_TYPE = 'I' THEN 1 ELSE 0 END) * 40 +
        SUM(CASE WHEN c.AIRLINE_TYPE = 'D' THEN 1 ELSE 0 END) * 20) AS TOTAL_REWARDS
        FROM
        T_MARKETING_INCENTIVE_PAYMENT a
        LEFT JOIN MP_SCENE_QRCODE_RECORD b
        ON a.DOMAIN_ACCOUNT = b.NAME
        AND a.DEPARTMENT = b.SCENE_SOURCE
        LEFT JOIN T_TRIP_TRANSACTION c
        ON b.FFP_CARDNO = c.PASSENGER_CARD_NO
        AND TO_DATE(c.FLIGHT_DATE, 'YYYY-MM-DD')
        BETWEEN TO_DATE(#{tripStartTime}, 'YYYY-MM-DD')
        AND TO_DATE(#{tripEndTime}, 'YYYY-MM-DD')
        WHERE
        1=1

        <!-- associated_time 时间范围 -->
        AND TO_DATE(b.ASSOCIATED_TIME, 'YYYY-MM-DD HH24:MI:SS')
        BETWEEN TO_DATE(#{associatedStartTime}, 'YYYY-MM-DD HH24:MI:SS')
        AND TO_DATE(#{associatedEndTime}, 'YYYY-MM-DD HH24:MI:SS')

        <!-- 可选：单独处理 domainAccount/passengerCardNo/department -->
        <if test="department != null and department != ''">
            AND a.DEPARTMENT = #{department}
        </if>
        <if test="domainAccount != null and domainAccount != ''">
            AND a.DOMAIN_ACCOUNT = #{domainAccount}
        </if>
        <if test="passengerCardNo != null and passengerCardNo != ''">
            AND b.FFP_CARDNO = #{passengerCardNo}
        </if>
        GROUP BY
        a.DOMAIN_ACCOUNT,
        b.FFP_CARDNO,
        a.DEPARTMENT
        ORDER BY
        a.DEPARTMENT, a.DOMAIN_ACCOUNT, b.FFP_CARDNO
    </select>

    <resultMap id="MonthlyIncentiveResultMap"
               type="com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingRewardResponse">
        <result column="employeeNumber" property="employeeNumber"/>
        <result column="department" property="department"/>
        <result column="employeeName" property="employeeName"/>
        <result column="monthBelong" property="monthBelong"/>
        <result column="totalRewards" property="totalRewards"/>
    </resultMap>

    <select id="getMonthlyIncentiveStatistics"
            parameterType="map"
            resultMap="MonthlyIncentiveResultMap">
        SELECT MAX(A.EMPLOYEE_NUMBER)                                     AS employeeNumber,
               A.DEPARTMENT                                               AS department,
               MAX(A.EMPLOYEE_NAME)                                       AS employeeName,
               TO_CHAR(#{firstDayOfMonth}, 'YYYY-MM')                     AS monthBelong,
               SUM(CASE WHEN B.AIRLINE_TYPE = 'D' THEN 1 ELSE 0 END) * 20 +
               SUM(CASE WHEN B.AIRLINE_TYPE = 'I' THEN 1 ELSE 0 END) * 40 AS totalRewards
        FROM T_MARKETING_INCENTIVE_PAYMENT A
                 LEFT JOIN T_TRIP_TRANSACTION B
                           ON A.DOMAIN_ACCOUNT = B.DOMAIN_ACCOUNT
                               AND A.DEPARTMENT = B.DEPARTMENT
                               AND B.CREATED_TIME >= #{firstDayOfMonth}
                               AND B.CREATED_TIME
                                  &lt;
                                   #{firstDayOfNextMonth}
        GROUP BY A.DEPARTMENT, A.DOMAIN_ACCOUNT
    </select>

</mapper>
