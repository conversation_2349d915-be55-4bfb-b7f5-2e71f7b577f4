<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.DiscountsRouteActivityMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteActivityPO">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="activityName" column="ACTIVITY_NAME" jdbcType="VARCHAR"/>
            <result property="mainTitle" column="MAIN_TITLE" jdbcType="VARCHAR"/>
            <result property="viceTitle" column="VICE_TITLE" jdbcType="VARCHAR"/>
            <result property="activityParam" column="ACTIVITY_PARAM" jdbcType="VARCHAR"/>
            <result property="headPicture" column="HEAD_PICTURE" jdbcType="VARCHAR"/>
            <result property="startDate" column="START_DATE" jdbcType="VARCHAR"/>
            <result property="endDate" column="END_DATE" jdbcType="VARCHAR"/>
            <result property="shareUrl" column="SHARE_URL" jdbcType="VARCHAR"/>
            <result property="createUser" column="CREATE_USER" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="VARCHAR"/>
            <result property="updateUser" column="UPDATE_USER" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="VARCHAR"/>
            <result property="activityUrl" column="ACTIVITY_URL" jdbcType="VARCHAR"/>
            <result property="isValid" column="IS_VALID" jdbcType="VARCHAR"/>
            <result property="status" column="STATUS" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ACTIVITY_NAME,MAIN_TITLE,
        VICE_TITLE,ACTIVITY_PARAM,HEAD_PICTURE,
        START_DATE,END_DATE,SHARE_URL,
        CREATE_USER,CREATE_TIME,UPDATE_USER,
        UPDATE_TIME,ACTIVITY_URL,IS_VALID,
        STATUS
    </sql>
</mapper>
