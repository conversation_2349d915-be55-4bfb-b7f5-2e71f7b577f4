<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.CommonLotteryPoolMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.CommonLotteryPoolPO">
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="prizePoolCode" column="PRIZE_POOL_CODE" jdbcType="VARCHAR"/>
            <result property="prizePoolName" column="PRIZE_POOL_NAME" jdbcType="VARCHAR"/>
            <result property="deleted" column="DELETED" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="CREATE_USER" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="UPDATE_USER" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PRIZE_POOL_CODE,PRIZE_POOL_NAME,
        DELETED,CREATE_TIME,CREATE_USER,
        UPDATE_TIME,UPDATE_USER
    </sql>

    <select id="toGainAllPoolRecords" parameterType="com.juneyaoair.manage.b2c.entity.activity.CommonLotteryPoolPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_PRIZE_POOL
        <where>
            <if test="id != null and id != ''">
                ID = #{id,jdbcType=VARCHAR}
            </if>
            <if test="deleted != null and deleted != ''">
                AND DELETED = #{deleted,jdbcType=VARCHAR}
            </if>
            <if test="prizePoolCode != null and prizePoolCode != ''">
                AND PRIZE_POOL_CODE = #{prizePoolCode,jdbcType=VARCHAR}
            </if>
            <if test="prizePoolName != null and prizePoolName != ''">
                AND PRIZE_POOL_NAME = #{prizePoolName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>
