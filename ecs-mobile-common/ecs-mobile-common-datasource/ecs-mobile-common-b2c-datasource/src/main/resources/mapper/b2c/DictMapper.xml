<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.DictMapper">

    <!-- 数据库表tfb_dictvalue与 DICTValue实体的映射 -->
    <!-- dvid主键，dvcode编号， dvname名称，dvdescription描述，enable是否有效，dtid对应的数据类型 -->
    <resultMap type="com.juneyaoair.ecs.manage.dto.notice.DICTValue" id="dICTValue">
        <id column="dvid" property="dvid" />
        <result column="dvcode" property="dvCode" />
        <result column="dvname" property="dvName" />
        <result column="dvdescription" property="dvDescription" />
        <result column="enable" property="enable" />
        <result column="dtid" property="dtid" />
    </resultMap>

    <!-- 新增数据项 -->
    <!-- dvid主键，dvcode编号， dvname名称，dvdescription描述，enable是否有效，dtid对应的数据类型 -->
    <insert id="addDICTValue" parameterType="com.juneyaoair.ecs.manage.dto.notice.DICTValue">
        insert into
            tp_dictvalue
            (dvid, dvcode, dvname, dvdescription, enable, dtid)
        values
            (#{dvid,jdbcType=VARCHAR}, #{dvCode,jdbcType=VARCHAR},
             #{dvName,jdbcType=VARCHAR}, #{dvDescription,jdbcType=VARCHAR},
             #{enable,jdbcType=VARCHAR},
             #{dtid,jdbcType=VARCHAR})
    </insert>

    <!-- 根据主键删除数据项 -->
    <!-- dvid主键，dvcode编号， dvname名称，dvdescription描述，enable是否有效，dtid对应的数据类型 -->
    <delete id="deleteDICTValueByID" parameterType="String">
        delete from
            tp_dictvalue
        where dvid=#{dvid}
    </delete>

    <!-- 更新数据项 -->
    <!-- dvid主键，dvcode编号， dvname名称，dvdescription描述，enable是否有效，dtid对应的数据类型 -->
    <update id="upateDICTValue" parameterType="com.juneyaoair.ecs.manage.dto.notice.DICTValue">
        update tp_dictvalue
        <set>
            <if test="dvid != null and dvid != ''">
                dvid = #{dvid},
            </if>
            <if test="dvCode != null and dvCode != ''">
                dvcode = #{dvCode},
            </if>
            <if test="dvName != null and dvName != ''">
                dvname = #{dvName},
            </if>
            <if test="dvDescription != null and dvDescription != ''">
                dvdescription = #{dvDescription},
            </if>
            <if test="enable != null and enable != ''">
                enable = #{enable},
            </if>
            <if test="dtid != null and dtid != ''">
                dtid = #{dtid}
            </if>
        </set>
        <where>
            <if test="dvid != null and dvid != ''">
                and dvid = #{dvid}
            </if>
            <if test="dtid != null and dtid != ''">
                and dtid = #{dtid}
            </if>
        </where>
    </update>
    <!-- 查询数据项 -->
    <!-- dvid主键，dvcode编号， dvname名称，dvdescription描述，enable是否有效，dtid对应的数据类型 -->
    <select id="findDICTValueList" parameterType="com.juneyaoair.ecs.manage.dto.notice.DICTValue"
            resultMap="dICTValue">
        select tv.dtid, tt.dtcode,tv.dvid, tv.dvcode, tv.dvname, tv.dvdescription, tv.enable
        from tp_dictvalue tv, tp_dicttype tt
        where tv.dtid = tt.dtid
        and tv.enable = 'Y'
        and tt.enable = 'Y'
        <if test="dtid != null and dtid != ''">
            and tv.dtid = #{dtid}
        </if>
        <if test="dvid != null ">
            and tv.dvid = #{dvid}
        </if>
        <if test="dvCode != null and dvCode != ''">
            and tv.dvcode = #{dvCode}
        </if>
        <if test="dvName != null and dvName != ''">
            and tv.dvname = #{dvName}
        </if>
        <if test="enable != null">
            and tv.enable = #{enable}
        </if>
        <if test="dtCode != null and dtCode != ''">
            and tt.dtcode = #{dtCode}
        </if>
    </select>

    <!-- 查询数据项 -->
    <!-- dvid主键，dvcode编号， dvname名称，dvdescription描述，enable是否有效，dtid对应的数据类型 -->
    <select id="findDICTValue" parameterType="com.juneyaoair.ecs.manage.dto.notice.DICTValue" resultMap="dICTValue">
        select tv.dvid, tv.dvcode, tv.dvname, tv.dvdescription,tv.enable,
        tv.dtid
        from tp_dictvalue tv
        <where>
            <if test="dtid != null and dtid != ''">
                and tv.dtid = #{dtid}
            </if>
            <if test="dvid != null and dvid != '' ">
                and tv.dvid = #{dvid}
            </if>
            <if test="dvCode != null and dvCode != ''">
                and tv.dvcode = #{dvCode}
            </if>
            <if test="dvName != null and dvName != ''">
                and tv.dvname like '%${dvName}%'
            </if>
            <if test="dvDescription != null and dvDescription != ''">
                and tv.dvdescription = #{dvDescription}
            </if>
            <if test="enable != null">
                and tv.enable = #{enable}
            </if>
        </where>
    </select>

    <!-- @对应数据库表：planetype @备注：机型信息 -->
    <resultMap type="com.juneyaoair.ecs.manage.dto.notice.DICTValue" id="planeType">
        <id property="dvid" column="AC_TYPE_SHORT" javaType="string"
            jdbcType="VARCHAR" />
        <result property="dvName" column="CHINESE_NAME" javaType="string"
                jdbcType="VARCHAR" />
    </resultMap>

    <!-- @方法：findPlaneType 查询机型信息 @参数：数据字典实体 @返回值：数据字典实体 -->
    <select id="findPlaneType" resultMap="planeType">
        select
            P.AC_TYPE_SHORT,P.CHINESE_NAME from PLANETYPE P
    </select>
    <select id="getChannels" resultMap="dICTValue">
        select * FROM TP_DICTVALUE where dtid=#{dtid,jdbcType=VARCHAR}
    </select>

    <!-- 更新数据项 -->
    <!-- dvid主键，dvcode编号， dvname名称，dvdescription描述，enable是否有效，dtid对应的数据类型 -->
    <update id="updateDictValue" parameterType="com.juneyaoair.ecs.manage.dto.notice.DICTValue">
        update tp_dictvalue
        <set>
            <if test="dvid != null and dvid != ''">
                dvid = #{dvid},
            </if>
            <if test="dvCode != null and dvCode != ''">
                dvcode = #{dvCode},
            </if>
            <if test="dvName != null and dvName != ''">
                dvname = #{dvName},
            </if>
            <if test="dvDescription != null and dvDescription != ''">
                dvdescription = #{dvDescription},
            </if>
            <if test="enable != null and enable != ''">
                enable = #{enable},
            </if>
            <if test="dtid != null and dtid != ''">
                dtid = #{dtid}
            </if>
        </set>
        <where>
            <if test="dvid != null and dvid != ''">
                and dvid = #{dvid}
            </if>
            <if test="dtid != null and dtid != ''">
                and dtid = #{dtid}
            </if>
        </where>
    </update>

    <!-- 新增数据项 -->
    <!-- dvid主键，dvcode编号， dvname名称，dvdescription描述，enable是否有效，dtid对应的数据类型 -->
    <insert id="addDictValue" parameterType="com.juneyaoair.ecs.manage.dto.notice.DICTValue"><![CDATA[
        insert into
            tp_dictvalue
            (dvid, dvcode, dvname, dvdescription, enable, dtid)
        values
            (#{dvid,jdbcType=VARCHAR}, #{dvCode,jdbcType=VARCHAR},
             #{dvName,jdbcType=VARCHAR}, #{dvDescription,jdbcType=VARCHAR},
             #{enable,jdbcType=VARCHAR},
             #{dtid,jdbcType=VARCHAR})
    ]]></insert>


</mapper>
