<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.DiscountsRouteTitleMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteTitlePO">
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="routeActivityId" column="ROUTE_ACTIVITY_ID" jdbcType="VARCHAR"/>
            <result property="parentTitleId" column="PARENT_TITLE_ID" jdbcType="VARCHAR"/>
            <result property="title" column="TITLE" jdbcType="VARCHAR"/>
            <result property="childName" column="CHILD_NAME" jdbcType="VARCHAR"/>
            <result property="titleLevel" column="TITLE_LEVEL" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ROUTE_ACTIVITY_ID,PARENT_TITLE_ID,
        TITLE,CHILD_NAME,TITLE_LEVEL
    </sql>
    <select id="searchAllRecords" parameterType="com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteTitlePO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from DISCOUNTS_ROUTE_TITLE
        <where>
            <if test="routeActivityId != null and routeActivityId != ''">
                ROUTE_ACTIVITY_ID = #{routeActivityId,jdbcType=VARCHAR}
            </if>
            <if test="titleLevel != null and titleLevel != ''">
                AND TITLE_LEVEL = #{titleLevel,jdbcType=VARCHAR}
            </if>
            <if test="parentTitleId != null and parentTitleId != ''">
                AND PARENT_TITLE_ID = #{parentTitleId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>
