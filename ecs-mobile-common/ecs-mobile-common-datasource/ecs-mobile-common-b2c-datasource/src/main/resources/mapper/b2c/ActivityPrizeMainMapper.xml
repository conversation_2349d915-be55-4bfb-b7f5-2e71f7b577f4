<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.ActivityPrizeMapper">

    <select id="queryPrizeRecord" parameterType="com.juneyaoair.ecs.seat.param.ActivityPrizeQuery"
            resultType="com.juneyaoair.ecs.seat.result.ActivityPrizeMainInfo">
        select
        ID id,
        ACTIVITY_TYPE "activityType",
        GROUP_TYPE "groupType",
        FFP_CARD_NO "ffpCardNo",
        POOL_CODE "poolCode",
        PRIZE_CODE "prizeCode",
        PRIZE_NAME "prizeName",
        (CASE PROVIDE_STATUS
        when 'D' THEN '已发放'
        WHEN 'DS' THEN '发放中'
        WHEN 'DF' THEN '发放失败'
        WHEN 'F' THEN '未发放'
        WHEN 'UN' THEN '未获得'
        WHEN 'SF' THEN '无法自动发放且未发放'
        ELSE PROVIDE_STATUS END) "provideStatusName",
        to_char(CREATE_TIME,'yyyy-mm-dd hh24:mi:ss') "createTime"
        from T_ACTIVITY_PRIZE_MAIN
        where 1 = 1
        <if test="activityType != null and activityType != ''">
            and ACTIVITY_TYPE = #{activityType}
        </if>
        <if test="groupType != null and groupType != ''">
            and GROUP_TYPE = #{groupType}
        </if>
        <if test="poolCode != null and poolCode != ''">
            and POOL_CODE = #{poolCode}
        </if>
        <if test="prizeCode != null and prizeCode != ''">
            and PRIZE_CODE = #{prizeCode}
        </if>
        <if test="ffpCardNo != null and ffpCardNo != ''">
            and FFP_CARD_NO = #{ffpCardNo}
        </if>
        order by ACTIVITY_TYPE, CREATE_TIME desc, ID
    </select>

    <select id="querySubPrizeRecord" parameterType="com.juneyaoair.ecs.seat.param.ActivitySubPrizeQuery"
            resultType="com.juneyaoair.ecs.seat.result.ActivityPrizeSubInfo">
        select PRIZE_NAME                                    "prizeName",
               COUPON_CODE                                   "couponCode",
               PRIZE_NUMBER                                  "prizeNumber",
               (CASE PROVIDE_STATUS
                    when 'D' THEN '已发放'
                    WHEN 'DS' THEN '发放中'
                    WHEN 'DF' THEN '发放失败'
                    WHEN 'F' THEN '未发放'
                    WHEN 'UN' THEN '未获得'
                    WHEN 'SF' THEN '无法自动发放且未发放'
                    ELSE PROVIDE_STATUS END)                 "provideStatusName",
               to_char(CREATE_TIME, 'yyyy-mm-dd hh24:mi:ss') "createTime"
        from T_ACTIVITY_PRIZE_SUB
        where MAIN_ID = #{mainId}
        order by MAIN_ID, CREATE_TIME desc, ID
    </select>

</mapper>
