<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.VersionManageMapper">
    <resultMap id="versionManage" type="com.juneyaoair.ecs.manage.dto.datadict.VersionManage">
        <id column="VERSION_ID" property="versionID" jdbcType="INTEGER"></id>
        <result column="CHANNEL_CODE" property="channelCode" jdbcType="VARCHAR"></result>
        <result column="VERSION_TYPE" property="versionType" jdbcType="VARCHAR"></result>
        <result column="VERSION_NO" property="versionNo" jdbcType="VARCHAR"></result>
        <result column="UPDATE_DETAILS" property="updateDetails" jdbcType="VARCHAR"></result>
    </resultMap>

    <select id="getList" resultMap="versionManage">
        SELECT * from t_version_info
        <where>
            1=1
            <if test="versionID !=null and versionID != ''">
                and VERSION_ID = #{versionID}
            </if>
            <if test="channelCode !=null and channelCode != ''">
                and CHANNEL_CODE = #{channelCode}
            </if>
            <if test="versionNo != null and versionNo != ''">
                and VERSION_NO = #{versionNo}
            </if>
            <if test="versionType != null and versionType != ''">
                and VERSION_Type LIKE '%${versionType}%'
            </if>
        </where>
        ORDER BY CHANNEL_CODE NULLS LAST
    </select>

    <select id="checkVersion" resultMap="versionManage">
        SELECT * from t_version_info
        <where>
            1=1
            <if test="versionID !=null and versionID != ''">
                and VERSION_ID != #{versionID}
            </if>
            <if test="channelCode !=null and channelCode != ''">
                and CHANNEL_CODE = #{channelCode}
            </if>
            <if test="versionNo != null and versionNo != ''">
                and VERSION_NO = #{versionNo}
            </if>
            <if test="versionType != null and versionType != ''">
                and VERSION_Type = #{versionType}
            </if>
        </where>
    </select>

    <update id="updateVersion" parameterType="com.juneyaoair.ecs.manage.dto.datadict.VersionManage">
        update t_version_info set CHANNEL_CODE = #{channelCode},
        <if test="versionType != null and versionType !=''">VERSION_TYPE = #{versionType},</if>
        VERSION_NO=#{versionNo},
        UPDATE_DETAILS = #{updateDetails,jdbcType=VARCHAR}
        where VERSION_TYPE = #{versionType}
        <if test="versionID != null and versionID !=0 "> AND VERSION_ID = #{versionID}</if>
    </update>

    <insert id="toAddVersion" parameterType="com.juneyaoair.ecs.manage.dto.datadict.VersionManage">
        <selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="versionID">
            SELECT seq_win_type.nextval as versionID from DUAL
        </selectKey>

        insert into t_version_info(VERSION_ID,CHANNEL_CODE,VERSION_TYPE,VERSION_NO,UPDATE_DETAILS)
        values(seq_win_type.currval,#{channelCode,jdbcType=VARCHAR},#{versionType,jdbcType=VARCHAR},
        #{versionNo,jdbcType=VARCHAR},#{updateDetails,jdbcType=VARCHAR})
    </insert>

    <delete id="deleteVersion" parameterType="com.juneyaoair.ecs.manage.dto.datadict.VersionManage">
        delete from t_version_info where VERSION_ID = #{versionID}
    </delete>

</mapper>
