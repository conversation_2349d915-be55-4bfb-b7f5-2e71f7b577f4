<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TAirportInfoMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.AirportInfoPO">
        <id column="AIRPORT_CODE" jdbcType="VARCHAR" property="airportCode"/>
        <result column="AIRPORT_NAME" jdbcType="VARCHAR" property="airportName"/>
        <result column="AIRPORT_E_NAME" jdbcType="VARCHAR" property="airportEName"/>
        <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode"/>
        <result column="NAME_ABB" jdbcType="VARCHAR" property="nameAbb"/>
        <result column="ENGLISH_NAME_ABB" jdbcType="VARCHAR" property="englishNameAbb"/>
        <result column="PINYIN_ABB" jdbcType="VARCHAR" property="pinyinAbb"/>
        <result column="CREATE_DATETIME" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="CREATE_ID" jdbcType="DECIMAL" property="createId"/>
        <result column="BAIDU_MAP_POINT" jdbcType="VARCHAR" property="baiduMapPoint"/>
        <result column="AIRPORT_PINYIN" jdbcType="VARCHAR" property="airportPinyin"/>
        <result column="WEBSITE" jdbcType="VARCHAR" property="website"/>
        <result column="TERMINALPOSITION" jdbcType="VARCHAR" property="terminalposition"/>
        <result column="CHECKINCOUNTER" jdbcType="VARCHAR" property="checkincounter"/>
        <result column="FIRSTCLASSCHECKINCOUNTER" jdbcType="VARCHAR" property="firstclasscheckincounter"/>
        <result column="TICKETCOUNTER" jdbcType="VARCHAR" property="ticketcounter"/>
        <result column="CHECKINBEGINTIME" jdbcType="VARCHAR" property="checkinbegintime"/>
        <result column="CHECKINENDTIME" jdbcType="VARCHAR" property="checkinendtime"/>
        <result column="VIPROOM" jdbcType="VARCHAR" property="viproom"/>
        <result column="I_TERMINALPOSITION" jdbcType="VARCHAR" property="iTerminalposition"/>
        <result column="I_CHECKINCOUNTER" jdbcType="VARCHAR" property="iCheckincounter"/>
        <result column="I_FIRSTCLASSCHECKINCOUNTER" jdbcType="VARCHAR" property="iFirstclasscheckincounter"/>
        <result column="I_VIPROOM" jdbcType="VARCHAR" property="iViproom"/>
        <result column="I_TICKETCOUNTER" jdbcType="VARCHAR" property="iTicketcounter"/>
        <result column="AIRPORT_KO_NAME" jdbcType="VARCHAR" property="airportKoName"/>
        <result column="AIRPORT_JP_NAME" jdbcType="VARCHAR" property="airportJpName"/>
        <result column="AIRPORT_TH_NAME" jdbcType="VARCHAR" property="airportThName"/>
        <result column="AIRPORT_TC_NAME" jdbcType="VARCHAR" property="airportTcName"/>
        <result column="ZONES_LEVEL" jdbcType="VARCHAR" property="zonesLevel"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="GATE_CLOSE_DATE" jdbcType="VARCHAR" property="gateCloseDate"/>
        <result column="LONGITUDE" jdbcType="VARCHAR" property="longitude"/>
        <result column="LATITUDE" jdbcType="VARCHAR" property="latitude"/>
    </resultMap>
    <sql id="Base_Column_List">
        AIRPORT_CODE,
        AIRPORT_NAME,
        AIRPORT_E_NAME,
        CITY_CODE,
        NAME_ABB,
        ENGLISH_NAME_ABB,
        PINYIN_ABB,
        CREATE_DATETIME,
        CREATE_ID,
        BAIDU_MAP_POINT,
        AIRPORT_PINYIN,
        WEBSITE,
        TERMINALPOSITION,
        CHECKINCOUNTER,
        FIRSTCLASSCHECKINCOUNTER,
        TICKETCOUNTER,
        CHECKINBEGINTIME,
        CHECKINENDTIME,
        VIPROOM,
        I_TERMINALPOSITION,
        I_CHECKINCOUNTER,
        I_FIRSTCLASSCHECKINCOUNTER,
        I_VIPROOM,
        I_TICKETCOUNTER,
        AIRPORT_KO_NAME,
        AIRPORT_JP_NAME,
        AIRPORT_TH_NAME,
        AIRPORT_TC_NAME,
        ZONES_LEVEL,
        "STATUS",
        GATE_CLOSE_DATE,
        LONGITUDE,
        LATITUDE
    </sql>

    <resultMap id="AirportJoinCityBaseResultMap" type="com.juneyaoair.manage.b2c.entity.AirportJoinCityPO">
        <id column="AIRPORT_CODE" property="airportCode" jdbcType="VARCHAR"/>
        <result column="AIRPORT_NAME" property="airportName" jdbcType="VARCHAR"/>
        <result column="AIRPORT_E_NAME" property="airportEName" jdbcType="VARCHAR"/>
        <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR"/>
        <result column="COUNTRY_CODE" property="countryCode" jdbcType="VARCHAR"/>
        <result column="NAME_ABB" property="nameAbb" jdbcType="VARCHAR"/>
        <result column="ENGLISH_NAME_ABB" property="englishNameAbb" jdbcType="VARCHAR"/>
        <result column="PINYIN_ABB" property="pinyinAbb" jdbcType="VARCHAR"/>
        <result column="CREATE_DATETIME" property="createDatetime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_ID" property="createId" jdbcType="DECIMAL"/>
        <result column="BAIDU_MAP_POINT" property="baiduMapPoint" jdbcType="VARCHAR"/>
        <result column="AIRPORT_PINYIN" property="airportPinyin" jdbcType="VARCHAR"/>
        <result column="WEBSITE" property="website" jdbcType="VARCHAR"/>
        <result column="TERMINALPOSITION" property="terminalposition" jdbcType="VARCHAR"/>
        <result column="CHECKINCOUNTER" property="checkincounter" jdbcType="VARCHAR"/>
        <result column="FIRSTCLASSCHECKINCOUNTER" property="firstclasscheckincounter" jdbcType="VARCHAR"/>
        <result column="TICKETCOUNTER" property="ticketcounter" jdbcType="VARCHAR"/>
        <result column="CHECKINBEGINTIME" property="checkinbegintime" jdbcType="VARCHAR"/>
        <result column="CHECKINENDTIME" property="checkinendtime" jdbcType="VARCHAR"/>
        <result column="VIPROOM" property="viproom" jdbcType="VARCHAR"/>
        <result column="I_TERMINALPOSITION" property="iTerminalposition" jdbcType="VARCHAR"/>
        <result column="I_CHECKINCOUNTER" property="iCheckincounter" jdbcType="VARCHAR"/>
        <result column="I_FIRSTCLASSCHECKINCOUNTER" property="iFirstclasscheckincounter" jdbcType="VARCHAR"/>
        <result column="I_VIPROOM" property="iViproom" jdbcType="VARCHAR"/>
        <result column="I_TICKETCOUNTER" property="iTicketcounter" jdbcType="VARCHAR"/>
        <result column="AIRPORT_KO_NAME" property="airportKoName" jdbcType="VARCHAR"/>
        <result column="AIRPORT_JP_NAME" property="airportJpName" jdbcType="VARCHAR"/>
        <result column="AIRPORT_TH_NAME" property="airportThName" jdbcType="VARCHAR"/>
        <result column="AIRPORT_TC_NAME" property="airportTcName" jdbcType="VARCHAR"/>
        <result column="city_name" property="cityName" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AIRPORT_INFO
        where AIRPORT_CODE = #{airportCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from T_AIRPORT_INFO
        where AIRPORT_CODE = #{airportCode,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.juneyaoair.manage.b2c.entity.AirportInfoPO">
        insert into T_AIRPORT_INFO (AIRPORT_CODE, AIRPORT_NAME, AIRPORT_E_NAME,
                                    CITY_CODE, NAME_ABB, ENGLISH_NAME_ABB,
                                    PINYIN_ABB, CREATE_DATETIME, CREATE_ID,
                                    BAIDU_MAP_POINT, AIRPORT_PINYIN, WEBSITE,
                                    TERMINALPOSITION, CHECKINCOUNTER, FIRSTCLASSCHECKINCOUNTER,
                                    TICKETCOUNTER, CHECKINBEGINTIME, CHECKINENDTIME,
                                    VIPROOM, I_TERMINALPOSITION, I_CHECKINCOUNTER,
                                    I_FIRSTCLASSCHECKINCOUNTER, I_VIPROOM, I_TICKETCOUNTER,
                                    AIRPORT_KO_NAME, AIRPORT_JP_NAME, AIRPORT_TH_NAME,
                                    AIRPORT_TC_NAME, ZONES_LEVEL, "STATUS",
                                    GATE_CLOSE_DATE, LONGITUDE, LATITUDE)
        values (#{airportCode,jdbcType=VARCHAR}, #{airportName,jdbcType=VARCHAR}, #{airportEName,jdbcType=VARCHAR},
                #{cityCode,jdbcType=VARCHAR}, #{nameAbb,jdbcType=VARCHAR}, #{englishNameAbb,jdbcType=VARCHAR},
                #{pinyinAbb,jdbcType=VARCHAR}, #{createDatetime,jdbcType=TIMESTAMP}, #{createId,jdbcType=DECIMAL},
                #{baiduMapPoint,jdbcType=VARCHAR}, #{airportPinyin,jdbcType=VARCHAR}, #{website,jdbcType=VARCHAR},
                #{terminalposition,jdbcType=VARCHAR}, #{checkincounter,jdbcType=VARCHAR},
                #{firstclasscheckincounter,jdbcType=VARCHAR},
                #{ticketcounter,jdbcType=VARCHAR}, #{checkinbegintime,jdbcType=VARCHAR},
                #{checkinendtime,jdbcType=VARCHAR},
                #{viproom,jdbcType=VARCHAR}, #{iTerminalposition,jdbcType=VARCHAR}, #{iCheckincounter,jdbcType=VARCHAR},
                #{iFirstclasscheckincounter,jdbcType=VARCHAR}, #{iViproom,jdbcType=VARCHAR},
                #{iTicketcounter,jdbcType=VARCHAR},
                #{airportKoName,jdbcType=VARCHAR}, #{airportJpName,jdbcType=VARCHAR}, #{airportThName,jdbcType=VARCHAR},
                #{airportTcName,jdbcType=VARCHAR}, #{zonesLevel,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
                #{gateCloseDate,jdbcType=VARCHAR}, #{longitude,jdbcType=VARCHAR}, #{latitude,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.juneyaoair.manage.b2c.entity.AirportInfoPO">
        insert into T_AIRPORT_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="airportCode != null">
                AIRPORT_CODE,
            </if>
            <if test="airportName != null">
                AIRPORT_NAME,
            </if>
            <if test="airportEName != null">
                AIRPORT_E_NAME,
            </if>
            <if test="cityCode != null">
                CITY_CODE,
            </if>
            <if test="nameAbb != null">
                NAME_ABB,
            </if>
            <if test="englishNameAbb != null">
                ENGLISH_NAME_ABB,
            </if>
            <if test="pinyinAbb != null">
                PINYIN_ABB,
            </if>
            <if test="createDatetime != null">
                CREATE_DATETIME,
            </if>
            <if test="createId != null">
                CREATE_ID,
            </if>
            <if test="baiduMapPoint != null">
                BAIDU_MAP_POINT,
            </if>
            <if test="airportPinyin != null">
                AIRPORT_PINYIN,
            </if>
            <if test="website != null">
                WEBSITE,
            </if>
            <if test="terminalposition != null">
                TERMINALPOSITION,
            </if>
            <if test="checkincounter != null">
                CHECKINCOUNTER,
            </if>
            <if test="firstclasscheckincounter != null">
                FIRSTCLASSCHECKINCOUNTER,
            </if>
            <if test="ticketcounter != null">
                TICKETCOUNTER,
            </if>
            <if test="checkinbegintime != null">
                CHECKINBEGINTIME,
            </if>
            <if test="checkinendtime != null">
                CHECKINENDTIME,
            </if>
            <if test="viproom != null">
                VIPROOM,
            </if>
            <if test="iTerminalposition != null">
                I_TERMINALPOSITION,
            </if>
            <if test="iCheckincounter != null">
                I_CHECKINCOUNTER,
            </if>
            <if test="iFirstclasscheckincounter != null">
                I_FIRSTCLASSCHECKINCOUNTER,
            </if>
            <if test="iViproom != null">
                I_VIPROOM,
            </if>
            <if test="iTicketcounter != null">
                I_TICKETCOUNTER,
            </if>
            <if test="airportKoName != null">
                AIRPORT_KO_NAME,
            </if>
            <if test="airportJpName != null">
                AIRPORT_JP_NAME,
            </if>
            <if test="airportThName != null">
                AIRPORT_TH_NAME,
            </if>
            <if test="airportTcName != null">
                AIRPORT_TC_NAME,
            </if>
            <if test="zonesLevel != null">
                ZONES_LEVEL,
            </if>
            <if test="status != null">
                "STATUS",
            </if>
            <if test="gateCloseDate != null">
                GATE_CLOSE_DATE,
            </if>
            <if test="longitude != null">
                LONGITUDE,
            </if>
            <if test="latitude != null">
                LATITUDE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="airportCode != null">
                #{airportCode,jdbcType=VARCHAR},
            </if>
            <if test="airportName != null">
                #{airportName,jdbcType=VARCHAR},
            </if>
            <if test="airportEName != null">
                #{airportEName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="nameAbb != null">
                #{nameAbb,jdbcType=VARCHAR},
            </if>
            <if test="englishNameAbb != null">
                #{englishNameAbb,jdbcType=VARCHAR},
            </if>
            <if test="pinyinAbb != null">
                #{pinyinAbb,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createId != null">
                #{createId,jdbcType=DECIMAL},
            </if>
            <if test="baiduMapPoint != null">
                #{baiduMapPoint,jdbcType=VARCHAR},
            </if>
            <if test="airportPinyin != null">
                #{airportPinyin,jdbcType=VARCHAR},
            </if>
            <if test="website != null">
                #{website,jdbcType=VARCHAR},
            </if>
            <if test="terminalposition != null">
                #{terminalposition,jdbcType=VARCHAR},
            </if>
            <if test="checkincounter != null">
                #{checkincounter,jdbcType=VARCHAR},
            </if>
            <if test="firstclasscheckincounter != null">
                #{firstclasscheckincounter,jdbcType=VARCHAR},
            </if>
            <if test="ticketcounter != null">
                #{ticketcounter,jdbcType=VARCHAR},
            </if>
            <if test="checkinbegintime != null">
                #{checkinbegintime,jdbcType=VARCHAR},
            </if>
            <if test="checkinendtime != null">
                #{checkinendtime,jdbcType=VARCHAR},
            </if>
            <if test="viproom != null">
                #{viproom,jdbcType=VARCHAR},
            </if>
            <if test="iTerminalposition != null">
                #{iTerminalposition,jdbcType=VARCHAR},
            </if>
            <if test="iCheckincounter != null">
                #{iCheckincounter,jdbcType=VARCHAR},
            </if>
            <if test="iFirstclasscheckincounter != null">
                #{iFirstclasscheckincounter,jdbcType=VARCHAR},
            </if>
            <if test="iViproom != null">
                #{iViproom,jdbcType=VARCHAR},
            </if>
            <if test="iTicketcounter != null">
                #{iTicketcounter,jdbcType=VARCHAR},
            </if>
            <if test="airportKoName != null">
                #{airportKoName,jdbcType=VARCHAR},
            </if>
            <if test="airportJpName != null">
                #{airportJpName,jdbcType=VARCHAR},
            </if>
            <if test="airportThName != null">
                #{airportThName,jdbcType=VARCHAR},
            </if>
            <if test="airportTcName != null">
                #{airportTcName,jdbcType=VARCHAR},
            </if>
            <if test="zonesLevel != null">
                #{zonesLevel,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="gateCloseDate != null">
                #{gateCloseDate,jdbcType=VARCHAR},
            </if>
            <if test="longitude != null">
                #{longitude,jdbcType=VARCHAR},
            </if>
            <if test="latitude != null">
                #{latitude,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.AirportInfoPO">
        update T_AIRPORT_INFO
        <set>
            <if test="airportName != null">
                AIRPORT_NAME = #{airportName,jdbcType=VARCHAR},
            </if>
            <if test="airportEName != null">
                AIRPORT_E_NAME = #{airportEName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                CITY_CODE = #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="nameAbb != null">
                NAME_ABB = #{nameAbb,jdbcType=VARCHAR},
            </if>
            <if test="englishNameAbb != null">
                ENGLISH_NAME_ABB = #{englishNameAbb,jdbcType=VARCHAR},
            </if>
            <if test="pinyinAbb != null">
                PINYIN_ABB = #{pinyinAbb,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                CREATE_DATETIME = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createId != null">
                CREATE_ID = #{createId,jdbcType=DECIMAL},
            </if>
            <if test="baiduMapPoint != null">
                BAIDU_MAP_POINT = #{baiduMapPoint,jdbcType=VARCHAR},
            </if>
            <if test="airportPinyin != null">
                AIRPORT_PINYIN = #{airportPinyin,jdbcType=VARCHAR},
            </if>
            <if test="website != null">
                WEBSITE = #{website,jdbcType=VARCHAR},
            </if>
            <if test="terminalposition != null">
                TERMINALPOSITION = #{terminalposition,jdbcType=VARCHAR},
            </if>
            <if test="checkincounter != null">
                CHECKINCOUNTER = #{checkincounter,jdbcType=VARCHAR},
            </if>
            <if test="firstclasscheckincounter != null">
                FIRSTCLASSCHECKINCOUNTER = #{firstclasscheckincounter,jdbcType=VARCHAR},
            </if>
            <if test="ticketcounter != null">
                TICKETCOUNTER = #{ticketcounter,jdbcType=VARCHAR},
            </if>
            <if test="checkinbegintime != null">
                CHECKINBEGINTIME = #{checkinbegintime,jdbcType=VARCHAR},
            </if>
            <if test="checkinendtime != null">
                CHECKINENDTIME = #{checkinendtime,jdbcType=VARCHAR},
            </if>
            <if test="viproom != null">
                VIPROOM = #{viproom,jdbcType=VARCHAR},
            </if>
            <if test="iTerminalposition != null">
                I_TERMINALPOSITION = #{iTerminalposition,jdbcType=VARCHAR},
            </if>
            <if test="iCheckincounter != null">
                I_CHECKINCOUNTER = #{iCheckincounter,jdbcType=VARCHAR},
            </if>
            <if test="iFirstclasscheckincounter != null">
                I_FIRSTCLASSCHECKINCOUNTER = #{iFirstclasscheckincounter,jdbcType=VARCHAR},
            </if>
            <if test="iViproom != null">
                I_VIPROOM = #{iViproom,jdbcType=VARCHAR},
            </if>
            <if test="iTicketcounter != null">
                I_TICKETCOUNTER = #{iTicketcounter,jdbcType=VARCHAR},
            </if>
            <if test="airportKoName != null">
                AIRPORT_KO_NAME = #{airportKoName,jdbcType=VARCHAR},
            </if>
            <if test="airportJpName != null">
                AIRPORT_JP_NAME = #{airportJpName,jdbcType=VARCHAR},
            </if>
            <if test="airportThName != null">
                AIRPORT_TH_NAME = #{airportThName,jdbcType=VARCHAR},
            </if>
            <if test="airportTcName != null">
                AIRPORT_TC_NAME = #{airportTcName,jdbcType=VARCHAR},
            </if>
            <if test="zonesLevel != null">
                ZONES_LEVEL = #{zonesLevel,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                "STATUS" = #{status,jdbcType=VARCHAR},
            </if>
            <if test="gateCloseDate != null">
                GATE_CLOSE_DATE = #{gateCloseDate,jdbcType=VARCHAR},
            </if>
            <if test="longitude != null">
                LONGITUDE = #{longitude,jdbcType=VARCHAR},
            </if>
            <if test="latitude != null">
                LATITUDE = #{latitude,jdbcType=VARCHAR},
            </if>
        </set>
        where AIRPORT_CODE = #{airportCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.juneyaoair.manage.b2c.entity.AirportInfoPO">
        update T_AIRPORT_INFO
        set AIRPORT_NAME               = #{airportName,jdbcType=VARCHAR},
            AIRPORT_E_NAME             = #{airportEName,jdbcType=VARCHAR},
            CITY_CODE                  = #{cityCode,jdbcType=VARCHAR},
            NAME_ABB                   = #{nameAbb,jdbcType=VARCHAR},
            ENGLISH_NAME_ABB           = #{englishNameAbb,jdbcType=VARCHAR},
            PINYIN_ABB                 = #{pinyinAbb,jdbcType=VARCHAR},
            CREATE_DATETIME            = #{createDatetime,jdbcType=TIMESTAMP},
            CREATE_ID                  = #{createId,jdbcType=DECIMAL},
            BAIDU_MAP_POINT            = #{baiduMapPoint,jdbcType=VARCHAR},
            AIRPORT_PINYIN             = #{airportPinyin,jdbcType=VARCHAR},
            WEBSITE                    = #{website,jdbcType=VARCHAR},
            TERMINALPOSITION           = #{terminalposition,jdbcType=VARCHAR},
            CHECKINCOUNTER             = #{checkincounter,jdbcType=VARCHAR},
            FIRSTCLASSCHECKINCOUNTER   = #{firstclasscheckincounter,jdbcType=VARCHAR},
            TICKETCOUNTER              = #{ticketcounter,jdbcType=VARCHAR},
            CHECKINBEGINTIME           = #{checkinbegintime,jdbcType=VARCHAR},
            CHECKINENDTIME             = #{checkinendtime,jdbcType=VARCHAR},
            VIPROOM                    = #{viproom,jdbcType=VARCHAR},
            I_TERMINALPOSITION         = #{iTerminalposition,jdbcType=VARCHAR},
            I_CHECKINCOUNTER           = #{iCheckincounter,jdbcType=VARCHAR},
            I_FIRSTCLASSCHECKINCOUNTER = #{iFirstclasscheckincounter,jdbcType=VARCHAR},
            I_VIPROOM                  = #{iViproom,jdbcType=VARCHAR},
            I_TICKETCOUNTER            = #{iTicketcounter,jdbcType=VARCHAR},
            AIRPORT_KO_NAME            = #{airportKoName,jdbcType=VARCHAR},
            AIRPORT_JP_NAME            = #{airportJpName,jdbcType=VARCHAR},
            AIRPORT_TH_NAME            = #{airportThName,jdbcType=VARCHAR},
            AIRPORT_TC_NAME            = #{airportTcName,jdbcType=VARCHAR},
            ZONES_LEVEL                = #{zonesLevel,jdbcType=VARCHAR},
            "STATUS"                   = #{status,jdbcType=VARCHAR},
            GATE_CLOSE_DATE            = #{gateCloseDate,jdbcType=VARCHAR},
            LONGITUDE                  = #{longitude,jdbcType=VARCHAR},
            LATITUDE                   = #{latitude,jdbcType=VARCHAR}
        where AIRPORT_CODE = #{airportCode,jdbcType=VARCHAR}
    </update>

    <select id="selectByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AIRPORT_INFO
        <where>
            <if test="airportCode != null">
                and AIRPORT_CODE = #{airportCode,jdbcType=VARCHAR}
            </if>
            <if test="airportName != null">
                and AIRPORT_NAME = #{airportName,jdbcType=VARCHAR}
            </if>
            <if test="airportEName != null">
                and AIRPORT_E_NAME = #{airportEName,jdbcType=VARCHAR}
            </if>
            <if test="cityCode != null">
                and CITY_CODE = #{cityCode,jdbcType=VARCHAR}
            </if>
            <if test="nameAbb != null">
                and NAME_ABB = #{nameAbb,jdbcType=VARCHAR}
            </if>
            <if test="englishNameAbb != null">
                and ENGLISH_NAME_ABB = #{englishNameAbb,jdbcType=VARCHAR}
            </if>
            <if test="pinyinAbb != null">
                and PINYIN_ABB = #{pinyinAbb,jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                and CREATE_DATETIME = #{createDatetime,jdbcType=TIMESTAMP}
            </if>
            <if test="createId != null">
                and CREATE_ID = #{createId,jdbcType=DECIMAL}
            </if>
            <if test="baiduMapPoint != null">
                and BAIDU_MAP_POINT = #{baiduMapPoint,jdbcType=VARCHAR}
            </if>
            <if test="airportPinyin != null">
                and AIRPORT_PINYIN = #{airportPinyin,jdbcType=VARCHAR}
            </if>
            <if test="website != null">
                and WEBSITE = #{website,jdbcType=VARCHAR}
            </if>
            <if test="terminalposition != null">
                and TERMINALPOSITION = #{terminalposition,jdbcType=VARCHAR}
            </if>
            <if test="checkincounter != null">
                and CHECKINCOUNTER = #{checkincounter,jdbcType=VARCHAR}
            </if>
            <if test="firstclasscheckincounter != null">
                and FIRSTCLASSCHECKINCOUNTER = #{firstclasscheckincounter,jdbcType=VARCHAR}
            </if>
            <if test="ticketcounter != null">
                and TICKETCOUNTER = #{ticketcounter,jdbcType=VARCHAR}
            </if>
            <if test="checkinbegintime != null">
                and CHECKINBEGINTIME = #{checkinbegintime,jdbcType=VARCHAR}
            </if>
            <if test="checkinendtime != null">
                and CHECKINENDTIME = #{checkinendtime,jdbcType=VARCHAR}
            </if>
            <if test="viproom != null">
                and VIPROOM = #{viproom,jdbcType=VARCHAR}
            </if>
            <if test="iTerminalposition != null">
                and I_TERMINALPOSITION = #{iTerminalposition,jdbcType=VARCHAR}
            </if>
            <if test="iCheckincounter != null">
                and I_CHECKINCOUNTER = #{iCheckincounter,jdbcType=VARCHAR}
            </if>
            <if test="iFirstclasscheckincounter != null">
                and I_FIRSTCLASSCHECKINCOUNTER = #{iFirstclasscheckincounter,jdbcType=VARCHAR}
            </if>
            <if test="iViproom != null">
                and I_VIPROOM = #{iViproom,jdbcType=VARCHAR}
            </if>
            <if test="iTicketcounter != null">
                and I_TICKETCOUNTER = #{iTicketcounter,jdbcType=VARCHAR}
            </if>
            <if test="airportKoName != null">
                and AIRPORT_KO_NAME = #{airportKoName,jdbcType=VARCHAR}
            </if>
            <if test="airportJpName != null">
                and AIRPORT_JP_NAME = #{airportJpName,jdbcType=VARCHAR}
            </if>
            <if test="airportThName != null">
                and AIRPORT_TH_NAME = #{airportThName,jdbcType=VARCHAR}
            </if>
            <if test="airportTcName != null">
                and AIRPORT_TC_NAME = #{airportTcName,jdbcType=VARCHAR}
            </if>
            <if test="zonesLevel != null">
                and ZONES_LEVEL = #{zonesLevel,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and STATUS = #{status,jdbcType=VARCHAR}
            </if>
            <if test="gateCloseDate != null">
                and GATE_CLOSE_DATE = #{gateCloseDate,jdbcType=VARCHAR}
            </if>
            <if test="longitude != null">
                and LONGITUDE = #{longitude,jdbcType=VARCHAR}
            </if>
            <if test="latitude != null">
                and LATITUDE = #{latitude,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="searchAirportJoinCity" resultMap="AirportJoinCityBaseResultMap">
        select *
        from T_AIRPORT_INFO
   left join T_CITY_INFO on T_CITY_INFO.City_code = T_AIRPORT_INFO.City_code
        <where>
            <if test="airportCode != null and airportCode != ''">
                and AIRPORT_Code = upper(#{airportCode,jdbcType=VARCHAR})
            </if>
            <if test="airportName != null and airportName != ''">
                and  AIRPORT_NAME like '%' || #{airportName,jdbcType=VARCHAR} || '%'
            </if>
            <if test="airportEName != null and airportEName != ''">
                and  AIRPORT_E_NAME like '%' || #{airportEName,jdbcType=VARCHAR} || '%'
            </if>
            <if test="cityCode != null and cityCode != ''">
                and  CITY_CODE = #{cityCode,jdbcType=VARCHAR}
            </if>
            <if test="nameAbb != null and nameAbb != ''">
                and  NAME_ABB = #{nameAbb,jdbcType=VARCHAR}
            </if>
            <if test="englishNameAbb != null and englishNameAbb != ''">
                and ENGLISH_NAME_ABB = #{englishNameAbb,jdbcType=VARCHAR}
            </if>
            <if test="pinyinAbb != null and pinyinAbb != ''">
                and PINYIN_ABB = #{pinyinAbb,jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null and createDatetime != ''">
                and CREATE_DATETIME = #{createDatetime,jdbcType=TIMESTAMP}
            </if>
            <if test="createId != null and createId != ''">
                and  CREATE_ID = #{createId,jdbcType=DECIMAL}
            </if>
            <if test="baiduMapPoint != null and baiduMapPoint != ''">
                and  BAIDU_MAP_POINT = #{baiduMapPoint,jdbcType=VARCHAR}
            </if>
            <if test="airportPinyin != null and airportPinyin != ''">
                and   AIRPORT_PINYIN = #{airportPinyin,jdbcType=VARCHAR}
            </if>
            <if test="website != null and website != ''">
                and    WEBSITE = #{website,jdbcType=VARCHAR}
            </if>
            <if test="terminalposition != null and terminalposition != ''">
                and    TERMINALPOSITION = #{terminalposition,jdbcType=VARCHAR}
            </if>
            <if test="checkincounter != null and checkincounter != ''">
                and  CHECKINCOUNTER = #{checkincounter,jdbcType=VARCHAR}
            </if>
            <if test="firstclasscheckincounter != null and firstclasscheckincounter != ''">
                and  FIRSTCLASSCHECKINCOUNTER = #{firstclasscheckincounter,jdbcType=VARCHAR}
            </if>
            <if test="ticketcounter != null and ticketcounter != ''">
                and   TICKETCOUNTER = #{ticketcounter,jdbcType=VARCHAR}
            </if>
            <if test="checkinbegintime != null and checkinbegintime != ''">
                and  CHECKINBEGINTIME = #{checkinbegintime,jdbcType=VARCHAR}
            </if>
            <if test="checkinendtime != null and checkinendtime != ''">
                and  CHECKINENDTIME = #{checkinendtime,jdbcType=VARCHAR}
            </if>
            <if test="viproom != null and viproom != ''">
                and  VIPROOM = #{viproom,jdbcType=VARCHAR}
            </if>
            <if test="iTerminalposition != null and iTerminalposition != ''">
                and   I_TERMINALPOSITION = #{iTerminalposition,jdbcType=VARCHAR}
            </if>
            <if test="iCheckincounter != null and iCheckincounter != ''">
                and   I_CHECKINCOUNTER = #{iCheckincounter,jdbcType=VARCHAR}
            </if>
            <if test="iFirstclasscheckincounter != null and iFirstclasscheckincounter != ''">
                and I_FIRSTCLASSCHECKINCOUNTER = #{iFirstclasscheckincounter,jdbcType=VARCHAR}
            </if>
            <if test="iViproom != null and iViproom != ''">
                and I_VIPROOM = #{iViproom,jdbcType=VARCHAR}
            </if>
            <if test="iTicketcounter != null and iTicketcounter != ''">
                and I_TICKETCOUNTER = #{iTicketcounter,jdbcType=VARCHAR}
            </if>
            <if test="airportKoName != null and airportKoName != ''">
                and  AIRPORT_KO_NAME = #{airportKoName,jdbcType=VARCHAR}
            </if>
            <if test="airportJpName != null and airportJpName != ''">
                and  AIRPORT_JP_NAME = #{airportJpName,jdbcType=VARCHAR}
            </if>
            <if test="airportThName != null and airportThName != ''">
                and  AIRPORT_TH_NAME = #{airportThName,jdbcType=VARCHAR}
            </if>
            <if test="airportTcName != null and airportTcName != ''">
                and  AIRPORT_TC_NAME = #{airportTcName,jdbcType=VARCHAR}
            </if>
            <if test="isInternational != null and isInternational != ''">
                and  IS_INTERNATIONAL = #{isInternational,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectAirportJoinLabelAndWarn"
            resultType="com.juneyaoair.manage.b2c.entity.AirportJoinLabelWarnPO">
            select


    </select>
</mapper>