<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.PayMethodRouteMapper">

    <sql id="Base_Column_List">
        ID,
        ROUTE_TYPE,
        PAY_ACTIVITY_ID,
        DEP_AIRPORT,
        ARR_AIRPORT,
        CREATE_USER,
        CREATE_TIME,
        UPDATE_USER,
        UPDATE_TIME
    </sql>

    <insert id="add">
        INSERT INTO T_PAY_METHOD_ROUTE (
        <include refid="Base_Column_List"/>
        )
        VALUES (
        #{id, jdbcType=VARCHAR},
        #{routeType, jdbcType=VARCHAR},
        #{payActivityId, jdbcType=VARCHAR},
        #{depAirport, jdbcType=VARCHAR},
        #{arrAirport, jdbcType=VARCHAR},
        #{createUser, jdbcType=VARCHAR},
        #{createTime, jdbcType=TIMESTAMP},
        #{updateUser, jdbcType=VARCHAR},
        #{updateTime, jdbcType=TIMESTAMP}
        )
    </insert>

    <delete id="deleteByPayActivityId">
        DELETE FROM T_PAY_METHOD_ROUTE
        WHERE PAY_ACTIVITY_ID = #{id}
    </delete>

    <select id="selectByRouteType" resultType="com.juneyaoair.manage.b2c.entity.PayMethodRoutePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_PAY_METHOD_ROUTE
        WHERE
        PAY_ACTIVITY_ID = #{id}
        AND ROUTE_TYPE = #{routeType}
    </select>
</mapper>