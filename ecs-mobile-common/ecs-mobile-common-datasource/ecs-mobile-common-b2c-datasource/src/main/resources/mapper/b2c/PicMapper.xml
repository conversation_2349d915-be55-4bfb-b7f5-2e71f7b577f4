<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.PicMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.PicturePO">
        <id property="picId" column="PIC_ID" jdbcType="VARCHAR"/>
        <result property="picStarttime" column="PIC_STARTTIME" jdbcType="VARCHAR"/>
        <result property="picEndtime" column="PIC_ENDTIME" jdbcType="VARCHAR"/>
        <result property="picLocation" column="PIC_LOCATION" jdbcType="VARCHAR"/>
        <result property="title" column="TITLE" jdbcType="VARCHAR"/>
        <result property="description" column="DESCRIPTION" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="VARCHAR"/>
        <result property="updateMan" column="UPDATE_MAN" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="VARCHAR"/>
        <result property="createMan" column="CREATE_MAN" jdbcType="VARCHAR"/>
        <result property="fildIds" column="FILD_IDS" jdbcType="VARCHAR"/>
        <result property="url" column="URL" jdbcType="VARCHAR"/>
        <result property="channels" column="CHANNELS" jdbcType="VARCHAR"/>
        <result property="statusCode" column="STATUS_CODE" jdbcType="VARCHAR"/>
        <result property="descriptionPlaintxt" column="DESCRIPTION_PLAINTXT" jdbcType="VARCHAR"/>
        <result property="isLogin" column="IS_LOGIN" jdbcType="VARCHAR"/>
        <result property="modeType" column="MODE_TYPE" jdbcType="VARCHAR"/>
        <result property="isShared" column="IS_SHARED" jdbcType="VARCHAR"/>
        <result property="shareIconUrl" column="SHARE_ICON_URL" jdbcType="VARCHAR"/>
        <result property="shareDesc" column="SHARE_DESC" jdbcType="VARCHAR"/>
        <result property="isGiftpoints" column="IS_GIFTPOINTS" jdbcType="VARCHAR"/>
        <result property="isGiftcoupons" column="IS_GIFTCOUPONS" jdbcType="VARCHAR"/>
        <result property="shareScore" column="SHARE_SCORE" jdbcType="DECIMAL"/>
        <result property="shareCoupons" column="SHARE_COUPONS" jdbcType="VARCHAR"/>
        <result property="winName" column="WIN_NAME" jdbcType="VARCHAR"/>
        <result property="picUrl" column="PIC_URL" jdbcType="VARCHAR"/>
        <result property="statisticalEvents" column="STATISTICAL_EVENTS" jdbcType="VARCHAR"/>
        <result property="orderNum" column="ORDER_NUM" jdbcType="DECIMAL"/>
        <result property="titleStyle" column="TITLE_STYLE" jdbcType="VARCHAR"/>
        <result property="minVer" column="MIN_VER" jdbcType="VARCHAR"/>
        <result property="maxVer" column="MAX_VER" jdbcType="VARCHAR"/>
        <result property="platforminfo" column="PLATFORMINFO" jdbcType="VARCHAR"/>
        <result property="isGuanggao" column="IS_GUANGGAO" jdbcType="VARCHAR"/>
        <result property="shareTitle" column="SHARE_TITLE" jdbcType="VARCHAR"/>
        <result property="shareUrl" column="SHARE_URL" jdbcType="VARCHAR"/>
        <result property="type" column="TYPE" jdbcType="VARCHAR"/>
        <result property="littleTitle" column="LITTLE_TITLE" jdbcType="VARCHAR"/>
        <result property="enabledMode" column="ENABLED_MODE" jdbcType="VARCHAR"/>
        <result property="showLabelInfo" column="SHOW_LABEL_INFO" jdbcType="VARCHAR"/>
        <result property="cabins" column="CABINS" jdbcType="VARCHAR"/>
        <result property="picShowTimes" column="PIC_SHOW_TIMES" jdbcType="VARCHAR"/>
        <result property="videoOrimgShowTimes" column="VIDEO_ORIMG_SHOW_TIMES" jdbcType="VARCHAR"/>
        <result property="advShow" column="ADV_SHOW" jdbcType="VARCHAR"/>
        <result property="airlines" column="AIRLINES" jdbcType="VARCHAR"/>
        <result property="eventType" column="EVENT_TYPE" jdbcType="VARCHAR"/>
        <result property="picType" column="PIC_TYPE" jdbcType="VARCHAR"/>
        <result property="homePageDis" column="HOME_PAGE_DIS" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="pictureParam">
        PIC_ID,
        PIC_STARTTIME,
        PIC_ENDTIME,
        PIC_LOCATION,
        PIC_URL,
        TITLE,
        DESCRIPTION,
        STATUS,
        UPDATE_MAN,
        UPDATE_TIME,
        CREATE_TIME,
        CREATE_MAN,
        FILD_IDS,
        URL,
        CHANNELS,
        STATUS_CODE,
        DESCRIPTION_PLAINTXT,
        IS_LOGIN,
        MODE_TYPE,
        IS_SHARED,
        SHARE_ICON_URL,
        SHARE_DESC,
        IS_GIFTPOINTS,
        IS_GIFTCOUPONS,
        SHARE_SCORE,
        SHARE_COUPONS,
        WIN_NAME,
        STATISTICAL_EVENTS,
        ORDER_NUM,
        TITLE_STYLE,
        MIN_VER,
        MAX_VER,
        PLATFORMINFO,
        IS_GUANGGAO,
        SHARE_TITLE,
        SHARE_URL,
        TYPE,
        SHOW_LABEL_INFO,
        AIRLINES,
        CABINS,
        PIC_SHOW_TIMES,
        VIDEO_ORIMG_SHOW_TIMES,
        ADV_SHOW,
        HOME_PAGE_DIS
    </sql>
    <update id="updatePicture" parameterType="com.juneyaoair.manage.b2c.entity.PicturePO">
        update TP_PIC
        SET
        STATUS_CODE=#{statusCode,jdbcType=VARCHAR},
        UPDATE_MAN=#{updateMan,jdbcType=VARCHAR},
        UPDATE_TIME=#{updateTime,jdbcType=VARCHAR},
        <if test="enabledMode !=null">
            ENABLED_MODE=#{enabledMode,jdbcType=VARCHAR},
        </if>
        <if test="language !=null and language != ''">
            LANGUAGE=#{language,jdbcType=VARCHAR},
        </if>
        STATUS=#{status,jdbcType=VARCHAR}
        <if test="title != null and title != ''">,
            PIC_STARTTIME =#{picStarttime,jdbcType=VARCHAR} ,
            PIC_ENDTIME=#{picEndtime,jdbcType=VARCHAR},
            PIC_LOCATION=#{picLocation,jdbcType=VARCHAR},
            TITLE=#{title,jdbcType=VARCHAR},
            LITTLE_TITLE=#{littleTitle,jdbcType=VARCHAR},
            DESCRIPTION=#{description,jdbcType=VARCHAR},
            URL=#{url,jdbcType=VARCHAR},
            WIN_NAME=#{winName,jdbcType=VARCHAR},
            STATISTICAL_EVENTS=#{statisticalEvents,jdbcType=VARCHAR},
            EVENT_TYPE=#{eventType,jdbcType=VARCHAR},
            ORDER_NUM=#{orderNum,jdbcType=INTEGER},
            FILD_IDS=#{fildIds,jdbcType=VARCHAR},
            DESCRIPTION_PLAINTXT=#{descriptionPlaintxt,jdbcType=VARCHAR},
            IS_LOGIN=#{isLogin,jdbcType=VARCHAR},
            IS_SHARED=#{isShared,jdbcType=VARCHAR},
            IS_GIFTPOINTS=#{isGiftpoints,jdbcType=VARCHAR},
            IS_GIFTCOUPONS=#{isGiftcoupons,jdbcType=VARCHAR},
            SHARE_ICON_URL=#{shareIconUrl,jdbcType=VARCHAR},
            SHARE_DESC=#{shareDesc,jdbcType=VARCHAR},
            SHARE_SCORE=#{shareScore,jdbcType=NUMERIC},
            SHARE_COUPONS=#{shareCoupons,jdbcType=VARCHAR},
            MODE_TYPE=#{modeType,jdbcType=VARCHAR},
            TITLE_STYLE=#{titleStyle,jdbcType=VARCHAR},
            PLATFORMINFO=#{platforminfo,jdbcType=VARCHAR},
            MIN_VER=#{minVer,jdbcType=VARCHAR},
            MAX_VER=#{maxVer,jdbcType=VARCHAR},
            DESCRIPTION2=#{description2,jdbcType=BLOB},
            MOBILE_CONTENT=#{mobileContent,jdbcType=BLOB},
            IS_GUANGGAO=#{isGuanggao,jdbcType=VARCHAR},
            SHARE_TITLE=#{shareTitle,jdbcType=VARCHAR},
            SHARE_URL=#{shareUrl,jdbcType=VARCHAR},
            TYPE =#{type ,jdbcType=VARCHAR},
            SHOW_LABEL_INFO=#{showLabelInfo,jdbcType=VARCHAR},
            AIRLINES=#{airlines,jdbcType=VARCHAR},
            CABINS=#{cabins,jdbcType=VARCHAR},
            PIC_SHOW_TIMES=#{picShowTimes,jdbcType=VARCHAR},
            VIDEO_ORIMG_SHOW_TIMES=#{videoOrimgShowTimes,jdbcType=VARCHAR},
            HOME_PAGE_DIS=#{homePageDis,jdbcType=VARCHAR},
            ADV_SHOW=#{advShow,jdbcType=VARCHAR},
            PIC_TYPE=#{picType,jdbcType=VARCHAR}
        </if>
        where PIC_ID=#{picId,jdbcType=VARCHAR}


    </update>

    <select id="getList" parameterType="com.juneyaoair.manage.b2c.entity.PicturePO" resultMap="BaseResultMap">
        SELECT
        distinct tp_pic.PIC_ID,
        tp_pic.PIC_STARTTIME,
        tp_pic.PIC_ENDTIME,
        tp_pic.PIC_LOCATION,
        tp_pic.PIC_URL,
        tp_pic.TITLE,
        tp_pic.DESCRIPTION,
        tp_pic.STATUS,
        tp_pic.UPDATE_MAN,
        tp_pic.UPDATE_TIME,
        tp_pic.CREATE_TIME,
        tp_pic.CREATE_MAN,
        tp_pic.FILD_IDS,
        tp_pic.URL,
        tp_pic.CHANNELS,
        tp_pic.STATUS_CODE,
        tp_pic.DESCRIPTION_PLAINTXT,
        tp_pic.IS_LOGIN,
        tp_pic.MODE_TYPE,
        tp_pic.IS_SHARED,
        tp_pic.SHARE_ICON_URL,
        tp_pic.SHARE_DESC,
        tp_pic.IS_GIFTPOINTS,
        tp_pic.IS_GIFTCOUPONS,
        tp_pic.SHARE_SCORE,
        tp_pic.SHARE_COUPONS,
        tp_pic.WIN_NAME,
        tp_pic.STATISTICAL_EVENTS,
        tp_pic.ORDER_NUM,
        tp_pic.TITLE_STYLE,
        tp_pic.MIN_VER,
        tp_pic.MAX_VER,
        tp_pic.PLATFORMINFO,
        tp_pic.IS_GUANGGAO,
        tp_pic.SHARE_TITLE,
        tp_pic.SHARE_URL,
        tp_pic.TYPE,
        tp_pic.SHOW_LABEL_INFO,
        tp_pic.AIRLINES,
        tp_pic.CABINS,
        tp_pic.PIC_SHOW_TIMES,
        tp_pic.VIDEO_ORIMG_SHOW_TIMES,
        tp_pic.ADV_SHOW,
        tp_pic.LANGUAGE,
        tp_pic.HOME_PAGE_DIS,
        TPC.NAME picLocationDesc
        from tp_pic
   left join TP_PIC_CATALOGUE TPC on TP_PIC.PIC_LOCATION = TPC.PIC_LOCATION
   left join tp_channel channel on tp_pic.PIC_ID = channel.PRO_ID
        <where>
            1=1
            <if test="picId != null and picId != ''">
                and PIC_ID = #{picId}
            </if>
            <if test="title != null and title != ''">
                and title like  CONCAT('%',CONCAT(#{title},'%'))
            </if>
            <if test="status != null and status != ''">
                and STATUS = #{status}
            </if>
            <if test="statusCode != null and statusCode != ''">
                and STATUS_CODE = #{statusCode}
            </if>
            <if test="picEndtime !=null and picEndtime !=''">
                and PIC_ENDTIME &gt;= #{picEndtime}
            </if>
            <if test="picLocation !=null and picLocation !=''">
                and tp_pic.PIC_LOCATION = #{picLocation}
            </if>
            <if test="picLocationList !=null and picLocationList.size!=0">
                and tp_pic.PIC_LOCATION in
                <foreach collection="picLocationList" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="channelCode != null and channelCode!=''">
                and channel.channel_Type = #{channelCode,jdbcType=VARCHAR}
            </if>
            <if test="language != null and language!=''">
                and tp_pic.LANGUAGE = #{language,jdbcType=VARCHAR}
            </if>
            <if test="platforminfo != null">
                <choose>
                    <when test="platforminfo == 'All' || platforminfo == 'all'">
                        AND (PLATFORMINFO = 'All' or PLATFORMINFO = 'all')
                    </when>
                    <when test="platforminfo == 'android' || platforminfo == 'ios' || platforminfo == 'harmony'">
                        AND (PLATFORMINFO = 'All' or PLATFORMINFO = 'all'
                        or PLATFORMINFO = #{platforminfo})
                    </when>
                </choose>
            </if>
            <if test="verInt !=null and verInt !=''"><![CDATA[
                AND (
                CASE
                WHEN MIN_VER is not null THEN to_number(MIN_VER)
                ELSE 0 END) <= #{verInt}
                AND (
                CASE
                WHEN MAX_VER is not null THEN to_number(MAX_VER)
                ELSE 99999999 END) >= #{verInt}
            ]]></if>
        </where>
        ORDER by STATUS DESC,ORDER_NUM ASC,tp_pic.UPDATE_TIME DESC
    </select>


</mapper>
