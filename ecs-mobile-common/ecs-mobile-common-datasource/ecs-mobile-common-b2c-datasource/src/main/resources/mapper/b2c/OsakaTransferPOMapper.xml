<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.OsakaTransferPOMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.OsakaTransferPO">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="merchantCode" column="MERCHANT_CODE" jdbcType="VARCHAR"/>
        <result property="ffpCardNo" column="FFP_CARD_NO" jdbcType="VARCHAR"/>
        <result property="ticketNo" column="TICKET_NO" jdbcType="VARCHAR"/>
        <result property="flightNo" column="FLIGHT_NO" jdbcType="VARCHAR"/>
        <result property="flightDate" column="FLIGHT_DATE" jdbcType="VARCHAR"/>
        <result property="flightTime" column="FLIGHT_TIME" jdbcType="VARCHAR"/>
        <result property="dayOfWeek" column="DAY_OF_WEEK" jdbcType="VARCHAR"/>
        <result property="deptAirportCode" column="DEPT_AIRPORT_CODE" jdbcType="VARCHAR"/>
        <result property="arrAirportCode" column="ARR_AIRPORT_CODE" jdbcType="VARCHAR"/>
        <result property="deptAirportName" column="DEPT_AIRPORT_NAME" jdbcType="VARCHAR"/>
        <result property="arrAirportName" column="ARR_AIRPORT_NAME" jdbcType="VARCHAR"/>
        <result property="deptTerminal" column="DEPT_TERMINAL" jdbcType="VARCHAR"/>
        <result property="arrTerminal" column="ARR_TERMINAL" jdbcType="VARCHAR"/>
        <result property="cabin" column="CABIN" jdbcType="VARCHAR"/>
        <result property="countryCode" column="COUNTRY_CODE" jdbcType="VARCHAR"/>
        <result property="phoneNumber" column="PHONE_NUMBER" jdbcType="VARCHAR"/>
        <result property="couponCode" column="COUPON_CODE" jdbcType="VARCHAR"/>
        <result property="couponName" column="COUPON_NAME" jdbcType="VARCHAR"/>
        <result property="couponType" column="COUPON_TYPE" jdbcType="VARCHAR"/>
        <result property="couponValidStartTime" column="COUPON_VALID_START_TIME" jdbcType="TIMESTAMP"/>
        <result property="couponValidEndTime" column="COUPON_VALID_END_TIME" jdbcType="TIMESTAMP"/>
        <result property="couponStatus" column="COUPON_STATUS" jdbcType="VARCHAR"/>
        <result property="createdBy" column="CREATED_BY" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="recordSource" column="RECORD_SOURCE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,MERCHANT_CODE,FFP_CARD_NO,
        TICKET_NO,FLIGHT_NO,FLIGHT_DATE,
        FLIGHT_TIME,DAY_OF_WEEK,DEPT_AIRPORT_CODE,
        ARR_AIRPORT_CODE,DEPT_AIRPORT_NAME,ARR_AIRPORT_NAME,
        DEPT_TERMINAL,ARR_TERMINAL,CABIN,
        COUNTRY_CODE,PHONE_NUMBER,COUPON_CODE,
        COUPON_NAME,COUPON_TYPE,COUPON_VALID_START_TIME,
        COUPON_VALID_END_TIME,COUPON_STATUS,CREATED_BY,
        CREATED_TIME,UPDATE_BY,UPDATE_TIME,RECORD_SOURCE
    </sql>
    <select id="searchAllByCondition"
            parameterType="com.juneyaoair.ecs.manage.dto.activity.request.osaka.OsakaCouponClaimQueryRequest"
            resultType="com.juneyaoair.ecs.manage.dto.activity.response.osaka.OsakaCouponClaimResponse">
        select
        ot.MERCHANT_CODE as merchantCode,
        ot.FFP_CARD_NO as ffpCardNo,
        ot.TICKET_NO as ticketNo,
        ot.FLIGHT_NO as flightNo,
        ot.FLIGHT_DATE as flightDate,
        ot.DEPT_AIRPORT_CODE as deptAirPortCode,
        ot.DEPT_AIRPORT_NAME as deptAirPortName,
        ot.ARR_AIRPORT_CODE as arrAirPortCode,
        ot.ARR_AIRPORT_NAME as arrAirPortName,
        ot.CABIN as cabinClass,
        ot.PHONE_NUMBER as phoneNumber,
        ot.COUPON_TYPE as couponType,
        ot.COUPON_CODE as couponCode,
        ot.COUPON_NAME as couponName,
        TO_CHAR(ot.COUPON_VALID_START_TIME, 'YYYY-MM-DD HH24:MI:SS') as couponValidStartTime,
        TO_CHAR(ot.COUPON_VALID_END_TIME, 'YYYY-MM-DD HH24:MI:SS') as couponValidEndTime,
        (CASE
        WHEN ot.COUPON_STATUS IS NULL
        OR TRIM(ot.COUPON_STATUS) = ''
        OR ot.COUPON_STATUS IN ('INIT', 'PREPARE')
        THEN '未发放'

        WHEN ot.COUPON_STATUS = 'SUCCESS'
        THEN '发放成功'

        WHEN ot.COUPON_STATUS = 'FAILED'
        THEN '发放失败'

        WHEN ot.COUPON_STATUS = 'SCAN'
        THEN '乘客扫码完成'

        WHEN ot.COUPON_STATUS = 'SCAN_CANCEL'
        THEN '乘客撤销扫码'

        WHEN ot.COUPON_STATUS = 'ROLLBACK_SUCCESS'
        THEN '券作废成功'

        WHEN ot.COUPON_STATUS = 'ROLLBACK_FAILED'
        THEN '券作废失败'

        -- 将 WRITE_OFF、CREATE_ORDER、CANCEL_ORDER 都归为 '已核销'
        WHEN ot.COUPON_STATUS IN ('WRITE_OFF', 'CREATE_ORDER', 'CANCEL_ORDER')
        THEN '已核销'

        END) AS couponStatus,
        (case
        when ot.RECORD_SOURCE = 'INNNPVG' then '浦东机场'
        when ot.RECORD_SOURCE = 'INNNNKG' then '南京分公司'
        when ot.RECORD_SOURCE = 'INNNWUX' then '无锡营业部'
        when ot.RECORD_SOURCE = 'INNNBJS' then '北京营业部'
        when ot.RECORD_SOURCE = 'INNNCSX' then '长沙营业部'
        else '其他'
        end) as recordSource,
        TO_CHAR(ot.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') as claimTime,
        ot.ORDER_NO as orderNo,
        ot.CONTACTS_PHONE as contactsPhone,
        ot.CONTACTS as contacts,
        ot.PERSON_NUM as personNum,
        ot.LUGGAGE_NUM as luggageNum,
        (case
        when ot.TYPE = 1 then '接机'
        when ot.TYPE = 2 then '送机'
        else '其他'
        end) as transferType,
        ot.ORDER_CREATE_TIME as orderCreateTime,
        ot.STATISTICS_TIME as statisticsTime,
        ot.DEPARTURE as departure,
        ot.DEPARTURE_ADDRESS as departureAddress,
        ot.DEST as dest,
        ot.DEST_ADDRESS as destAddress,
        ot.AGENT_NAME as agentName,
        ot.TOTAL_AMOUNT_CNY as totalAmountCny,
        (case
        when ot.TRAVEL_FLAG = 'OW' then '单程'
        when ot.TRAVEL_FLAG = 'RT' then '往返'
        when ot.TRAVEL_FLAG = 'CF' then '中转'
        else '其他'
        end) as travelFlag,
        ot.TICKET_ISSUE_TIME as ticketIssueTime
        from T_OSAKA_TRANSFER ot
        <where>
            1 = 1
            <if test="merchantCode != null and merchantCode != ''">
                and MERCHANT_CODE = #{merchantCode,jdbcType=VARCHAR}
            </if>
            <if test="ffpCardNo != null and ffpCardNo != ''">
                and FFP_CARD_NO = #{ffpCardNo,jdbcType=VARCHAR}
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                and PHONE_NUMBER = #{phoneNumber,jdbcType=VARCHAR}
            </if>
            <if test="couponCode != null and couponCode != ''">
                and COUPON_CODE = #{couponCode,jdbcType=VARCHAR}
            </if>
            <if test="ticketNo != null and ticketNo != ''">
                and TICKET_NO = #{ticketNo,jdbcType=VARCHAR}
            </if>
            <if test="flightNo != null and flightNo != ''">
                and FLIGHT_NO = #{flightNo,jdbcType=VARCHAR}
            </if>
            <if test="couponStatus != null and couponStatus != ''">
                and COUPON_STATUS = #{couponStatus,jdbcType=VARCHAR}
            </if>
            <if test="claimStartTime != null and claimStartTime != '' and claimEndTime != null and claimEndTime != ''">
                and CREATED_TIME between
                TO_TIMESTAMP(#{claimStartTime}, 'YYYY-MM-DD') and
                TO_TIMESTAMP(CONCAT(#{claimEndTime}, ' 23:59:59'), 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="recordSource != null and recordSource != ''">
                and RECORD_SOURCE = #{recordSource,jdbcType=VARCHAR}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and ORDER_NO = #{orderNo,jdbcType=VARCHAR}
            </if>
            <if test="contactsPhone != null and contactsPhone != ''">
                and CONTACTS_PHONE = #{contactsPhone,jdbcType=VARCHAR}
            </if>
            <if test="contacts != null and contacts != ''">
                and CONTACTS = #{contacts,jdbcType=VARCHAR}
            </if>
            <if test="transferType != null">
                and TYPE = #{transferType,jdbcType=NUMERIC}
            </if>
            <if test="agentName != null and agentName != ''">
                and AGENT_NAME = #{agentName,jdbcType=VARCHAR}
            </if>
            <if test="travelFlag != null and travelFlag != ''">
                and TRAVEL_FLAG = #{travelFlag,jdbcType=VARCHAR}
            </if>
        </where>
        order by
        CREATED_TIME desc,
        FFP_CARD_NO asc
    </select>
</mapper>
