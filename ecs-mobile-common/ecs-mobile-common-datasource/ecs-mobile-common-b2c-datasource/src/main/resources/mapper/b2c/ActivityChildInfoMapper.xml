<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.ActivityChildInfoMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.ActivityChildInfoPO">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="dataType" column="DATA_TYPE" jdbcType="VARCHAR"/>
            <result property="dataName" column="DATA_NAME" jdbcType="VARCHAR"/>
            <result property="linkName" column="LINK_NAME" jdbcType="VARCHAR"/>
            <result property="dataDesc" column="DATA_DESC" jdbcType="VARCHAR"/>
            <result property="startDate" column="START_DATE" jdbcType="VARCHAR"/>
            <result property="endDate" column="END_DATE" jdbcType="VARCHAR"/>
            <result property="baseinfoId" column="BASEINFO_ID" jdbcType="VARCHAR"/>
            <result property="createMan" column="CREATE_MAN" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="VARCHAR"/>
            <result property="updateMan" column="UPDATE_MAN" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="VARCHAR"/>
            <result property="isValid" column="IS_VALID" jdbcType="VARCHAR"/>
            <result property="seckillStartTime" column="SECKILL_START_TIME" jdbcType="VARCHAR"/>
            <result property="seckillEndTime" column="SECKILL_END_TIME" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="BaseResultMapV2" type="com.juneyaoair.ecs.manage.dto.activity.request.ActivityChildInfo">
        <id property="id" column="ID"></id>
        <result property="dataType" column="DATA_TYPE" jdbcType="VARCHAR"></result>
        <result property="dataName" column="DATA_NAME" jdbcType="VARCHAR"></result>
        <result property="linkName" column="LINK_NAME"></result>
        <result property="dataDesc" column="DATA_DESC" jdbcType="VARCHAR"></result>
        <result property="startDate" column="START_DATE" jdbcType="VARCHAR"></result>
        <result property="endDate" column="END_DATE" jdbcType="VARCHAR"></result>
        <result property="baseinfoId" column="BASEINFO_ID" jdbcType="VARCHAR"></result>
        <result property="createMan" column="CREATE_MAN" jdbcType="VARCHAR"></result>
        <result property="createTime" column="CREATE_TIME" jdbcType="VARCHAR"></result>
        <result property="updateMan" column="UPDATE_MAN" jdbcType="VARCHAR"></result>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="VARCHAR"></result>
        <result property="isValid" column="IS_VALID" jdbcType="VARCHAR"></result>
        <result property="activityName" column="ACTIVITY_NAME" jdbcType="VARCHAR"></result>
        <result property="activityStartDate" column="ACTIVITY_START_DATE" jdbcType="VARCHAR"></result>
        <result property="activityEndDate" column="ACTIVITY_END_DATE" jdbcType="VARCHAR"></result>
        <result property="seckillStartTime" column="SECKILL_START_TIME" jdbcType="VARCHAR"></result>
        <result property="seckillEndTime" column="SECKILL_END_TIME" jdbcType="VARCHAR"></result>
    </resultMap>

    <sql id="Base_Column_List">
        ID,DATA_TYPE,DATA_NAME,
        LINK_NAME,DATA_DESC,START_DATE,
        END_DATE,BASEINFO_ID,CREATE_MAN,
        CREATE_TIME,UPDATE_MAN,UPDATE_TIME,
        IS_VALID,SECKILL_START_TIME,SECKILL_END_TIME
    </sql>
    <select id="searchAllRecords" parameterType="com.juneyaoair.manage.b2c.entity.activity.ActivityChildInfoPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_ACTIVITY_CHILD_INFO
        <where>
            IS_VALID = 'Y'
            <if test="dataType != null and dataType != ''">
                AND DATA_TYPE = #{dataType,jdbcType=VARCHAR}
            </if>
            <if test="baseinfoId != null and baseinfoId != ''">
                AND BASEINFO_ID = #{baseinfoId,jdbcType=VARCHAR}
            </if>
            <if test="linkName != null and linkName != ''">
                AND LINK_NAME = #{linkName,jdbcType=VARCHAR}
            </if>
            <if test="dataName != null and dataName != ''">
                AND DATA_NAME = #{dataName,jdbcType=VARCHAR}
            </if>
            <if test="dataDesc != null and dataDesc != ''">
                AND DATA_DESC = #{dataDesc,jdbcType=VARCHAR}
            </if>
            <if test="id != null and id != ''">
                AND ID != #{id,jdbcType=VARCHAR}
            </if>
        </where>
        order by link_name
    </select>

    <select id="searchAllChildRecords" parameterType="com.juneyaoair.ecs.manage.dto.activity.request.ActivityChildInfo" resultMap="BaseResultMapV2">
        select * from (SELECT child.id id,
        child.data_type data_type,
        child.data_name data_name,
        child.link_name link_name,
        child.data_desc data_desc,
        child.start_date start_date,
        child.end_date end_date,
        child.baseinfo_id baseinfo_id,
        child.create_man create_man,
        child.create_time create_time,
        child.update_man update_man,
        child.update_time update_time,
        COALESCE(child.is_valid,'N') is_valid,
        rout.activity_name activity_name,
        rout.start_date activity_start_date,
        rout.end_date activity_end_date
        FROM T_ACTIVITY_CHILD_INFO child,
        DISCOUNTS_ROUTE_ACTIVITY rout
        WHERE 1=1
        and child.baseinfo_id = rout.id
        <if test="dataType != null and dataType != ''">
            AND DATA_TYPE = #{dataType}
        </if>
        <if test="baseinfoId != null and baseinfoId != ''">
            AND BASEINFO_ID = #{baseinfoId}
        </if>
        <if test="dataName != null and dataName != ''">
            AND DATA_NAME like '%${dataName}%'
        </if>
        <if test="linkName != null and linkName != ''">
            AND LINK_NAME like '%${linkName}%'
        </if>
        union all
        SELECT childInfo.id id,
        childInfo.data_type data_type,
        childInfo.data_name data_name,
        childInfo.link_name link_name,
        childInfo.data_desc data_desc,
        childInfo.start_date start_date,
        childInfo.end_date end_date,
        childInfo.baseinfo_id baseinfo_id,
        childInfo.create_man create_man,
        childInfo.create_time create_time,
        childInfo.update_man update_man,
        childInfo.update_time update_time,
        COALESCE(childInfo.is_valid,'N') is_valid,
        baseInfo.area activity_name,
        baseInfo.job_start_date activity_start_date,
        baseInfo.job_end_date activity_end_date
        FROM T_ACTIVITY_CHILD_INFO childInfo,
        T_ACTIVITY_BASEINFO baseInfo
        WHERE 1=1
        and childInfo.baseinfo_id = baseInfo.id
        <if test="dataType != null and dataType != ''">
            AND DATA_TYPE = #{dataType}
        </if>
        <if test="baseinfoId != null and baseinfoId != ''">
            AND BASEINFO_ID = #{baseinfoId}
        </if>
        <if test="dataName != null and dataName != ''">
            AND DATA_NAME like '%${dataName}%'
        </if>
        <if test="linkName != null and linkName != ''">
            AND LINK_NAME like '%${linkName}%'
        </if>
        ) A ORDER BY UPDATE_TIME DESC

    </select>
    <select id="searchAllRecordsWithNonIds"
            resultType="com.juneyaoair.manage.b2c.entity.activity.ActivityChildInfoPO">
        select
        <include refid="Base_Column_List"/>
        from T_ACTIVITY_CHILD_INFO
        <where>
            IS_VALID = 'Y'
            <if test="dataType != null and dataType != ''">
                AND DATA_TYPE = #{dataType,jdbcType=VARCHAR}
            </if>
            <if test="baseinfoId != null and baseinfoId != ''">
                AND BASEINFO_ID = #{baseinfoId,jdbcType=VARCHAR}
            </if>
            <if test="linkName != null and linkName != ''">
                AND LINK_NAME = #{linkName,jdbcType=VARCHAR}
            </if>
            <if test="dataName != null and dataName != ''">
                AND DATA_NAME = #{dataName,jdbcType=VARCHAR}
            </if>
            <if test="dataDesc != null and dataDesc != ''">
                AND DATA_DESC = #{dataDesc,jdbcType=VARCHAR}
            </if>
            <if test="id != null and id != ''">
                AND ID != #{id,jdbcType=VARCHAR}
            </if>
        </where>
        order by link_name
    </select>

    <update id="updateActivityChildInfo" parameterType="com.juneyaoair.manage.b2c.entity.activity.ActivityChildInfoPO">
        update T_ACTIVITY_CHILD_INFO
        SET
            DATA_NAME=#{dataName,jdbcType=VARCHAR},
            LINK_NAME=#{linkName,jdbcType=VARCHAR},
            DATA_DESC = #{dataDesc,jdbcType=VARCHAR},
            START_DATE = #{startDate,jdbcType=VARCHAR},
            END_DATE = #{endDate,jdbcType=VARCHAR},
            BASEINFO_ID = #{baseinfoId,jdbcType=VARCHAR},
            UPDATE_MAN = #{updateMan,jdbcType=VARCHAR},
            UPDATE_TIME = #{updateTime,jdbcType=VARCHAR},
            SECKILL_START_TIME = #{updateTime,jdbcType=VARCHAR},
            SECKILL_END_TIME = #{seckillEndTime,jdbcType=VARCHAR}
        where ID=#{id,jdbcType=VARCHAR}
    </update>
    <select id="selectAllById" parameterType="com.juneyaoair.manage.b2c.entity.activity.ActivityChildInfoPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_ACTIVITY_CHILD_INFO
        <where>
            <if test="id != null and id != ''">
                ID = #{id,jdbcType=VARCHAR}
            </if>
        </where>
    </select>


</mapper>
