<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.RedeemActSegPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.RedeemActSegPO">
    <!--@mbg.generated-->
    <!--@Table T_REDEEM_ACT_SEG-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="RECORD_ID" jdbcType="VARCHAR" property="recordId" />
    <result column="FFP_ID" jdbcType="VARCHAR" property="ffpId" />
    <result column="FFP_NO" jdbcType="VARCHAR" property="ffpNo" />
    <result column="PAID_CLASS" jdbcType="VARCHAR" property="paidClass" />
    <result column="BOOKING_CLASS" jdbcType="VARCHAR" property="bookingClass" />
    <result column="TICKET_NUMBER" jdbcType="VARCHAR" property="ticketNumber" />
    <result column="FLIGHT_DATE" jdbcType="VARCHAR" property="flightDate" />
    <result column="AIRLINE_CODE" jdbcType="VARCHAR" property="airlineCode" />
    <result column="FLIGHT_NUMBER" jdbcType="VARCHAR" property="flightNumber" />
    <result column="VERIFY_STATE" jdbcType="VARCHAR" property="verifyState" />
    <result column="ACT_STATE" jdbcType="VARCHAR" property="actState" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, RECORD_ID, FFP_ID, FFP_NO, PAID_CLASS, BOOKING_CLASS, TICKET_NUMBER, FLIGHT_DATE, 
    AIRLINE_CODE, FLIGHT_NUMBER, VERIFY_STATE, ACT_STATE, CREATED_TIME, UPDATED_TIME
  </sql>

  <select id="selectByFfpNoComputed" resultMap="BaseResultMap">
    SELECT *
    FROM T_REDEEM_ACT_SEG
    WHERE FFP_NO = #{ffpNo,jdbcType=VARCHAR}
      AND ACT_STATE = 'COMPUTED'
  </select>

  <select id="selectByFfpNo" resultMap="BaseResultMap">
    SELECT *
    FROM T_REDEEM_ACT_SEG
    WHERE FFP_NO = #{ffpNo,jdbcType=VARCHAR}
  </select>
</mapper>