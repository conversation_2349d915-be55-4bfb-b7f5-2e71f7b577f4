<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.PayMethodActivityMapper">

    <sql id="Base_Column_List">
        ID,
        PAY_METHOD_ID,
        PROMOTION_DESCRIPTION,
        START_TIME,
        END_TIME,
        STATUS,
        CREATER,
        CREATE_TIME,
        UPDATER,
        UPDATE_TIME,
        ACTIVITY_DESCRIPTION,
        URL,
        PROMO_PARAM,
        DISTINGUISH_ROUTE
    </sql>

    <insert id="addPayMethodActivity">
        INSERT INTO T_PAY_METHOD_ACTIVITY (
        <include refid="Base_Column_List"/>
        ) VALUES (
        #{id, jdbcType=VARCHAR},
        #{payMethodId, jdbcType=VARCHAR},
        #{promotionDescription, jdbcType=VARCHAR},
        #{startTime, jdbcType=TIMESTAMP},
        #{endTime, jdbcType=TIMESTAMP},
        #{status, jdbcType=CHAR},
        #{creater, jdbcType=VARCHAR},
        #{createTime, jdbcType=TIMESTAMP},
        #{updater, jdbcType=VARCHAR},
        #{updateTime, jdbcType=TIMESTAMP},
        #{activityDescription, jdbcType=VARCHAR},
        #{url, jdbcType=VARCHAR},
        #{promoParam, jdbcType=VARCHAR},
        #{distinguishRoute, jdbcType=CHAR}
        )
    </insert>

    <update id="updatePayMethodActivityById">
        UPDATE T_PAY_METHOD_ACTIVITY
        <set>
            <if test="payMethodId != null and payMethodId != ''">
                PAY_METHOD_ID = #{payMethodId},
            </if>
            <if test="promotionDescription != null and promotionDescription != ''">
                PROMOTION_DESCRIPTION = #{promotionDescription},
            </if>
            <if test="startTime != null">
                START_TIME = #{startTime},
            </if>
            <if test="endTime != null">
                END_TIME = #{endTime},
            </if>
            <if test="status != null and status != ''">
                STATUS = #{status},
            </if>
            <if test="activityDescription != null and activityDescription != ''">
                ACTIVITY_DESCRIPTION = #{activityDescription},
            </if>
            <if test="url != null and url != ''">
                URL = #{url},
            </if>
            <if test="promoParam != null and promoParam != ''">
                PROMO_PARAM = #{promoParam},
            </if>
            <if test="distinguishRoute != null and distinguishRoute != ''">
                DISTINGUISH_ROUTE = #{distinguishRoute},
            </if>
            <if test="creater != null and creater != ''">
                CREATER = #{creater},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime},
            </if>
            <if test="updater != null and updater != ''">
                UPDATER = #{updater},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime},
            </if>
        </set>
        WHERE ID = #{id}
    </update>

    <delete id="deletePayMethodActivityById">
        DELETE FROM
        T_PAY_METHOD_ACTIVITY
        WHERE
        ID = #{id}
    </delete>

    <select id="payMethodActivityQuery"
            resultType="com.juneyaoair.manage.b2c.entity.activity.PayMethodActivityPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_PAY_METHOD_ACTIVITY
    </select>

    <select id="getpayMethodActivityById"
            resultType="com.juneyaoair.manage.b2c.entity.activity.PayMethodActivityPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_PAY_METHOD_ACTIVITY
        WHERE ID = #{id}
    </select>

    <select id="getpayMethodActivityByPayMethodId"
            resultType="com.juneyaoair.manage.b2c.entity.activity.PayMethodActivityPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_PAY_METHOD_ACTIVITY
        WHERE PAY_METHOD_ID = #{payMethodId}
    </select>
</mapper>