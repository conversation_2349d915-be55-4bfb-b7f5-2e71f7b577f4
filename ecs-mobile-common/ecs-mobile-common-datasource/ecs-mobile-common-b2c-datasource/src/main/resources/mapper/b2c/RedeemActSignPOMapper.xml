<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.RedeemActSignPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.RedeemActSignPO">
    <!--@mbg.generated-->
    <!--@Table T_REDEEM_ACT_SIGN-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="FFP_ID" jdbcType="VARCHAR" property="ffpId" />
    <result column="FFP_NO" jdbcType="VARCHAR" property="ffpNo" />
    <result column="SIGN_DATE" jdbcType="VARCHAR" property="signDate" />
    <result column="MONTH_STR" jdbcType="VARCHAR" property="monthStr" />
    <result column="PUNCH_IN_TIME" jdbcType="TIMESTAMP" property="punchInTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, FFP_ID, FFP_NO, SIGN_DATE, MONTH_STR, PUNCH_IN_TIME
  </sql>

  <select id="selectSignUserList" resultType="com.juneyaoair.ecs.manage.dto.activity.redeem.signact.SignActUser">
    SELECT FFP_NO        AS ffpNo,
           COUNT(FFP_NO) AS signCount
    FROM T_REDEEM_ACT_SIGN
    WHERE 1 = 1
    <if test="month != null and month != ''">
      AND MONTH_STR = #{month,jdbcType=VARCHAR}
    </if>
    <if test="ffpNo != null and ffpNo != ''">
      AND FFP_NO = #{ffpNo,jdbcType=VARCHAR}
    </if>
    GROUP BY FFP_NO
  </select>

  <select id="selectExportExcel" resultMap="BaseResultMap">
    SELECT *
    FROM T_REDEEM_ACT_SIGN
    WHERE FFP_NO = #{ffpNo,jdbcType=VARCHAR}
    <if test="month != null and month != ''">
      AND MONTH_STR = #{month,jdbcType=VARCHAR}
    </if>
  </select>
</mapper>