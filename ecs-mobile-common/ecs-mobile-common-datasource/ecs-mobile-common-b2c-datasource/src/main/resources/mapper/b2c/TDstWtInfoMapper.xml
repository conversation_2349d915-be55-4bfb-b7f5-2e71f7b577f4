<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TDstWtInfoMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.DstWtInfoPO">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
    <result column="LAST_UPDATE_TIME" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="LAST_UPDATE_USER" jdbcType="VARCHAR" property="lastUpdateUser" />
    <result column="DST_MSG" jdbcType="VARCHAR" property="dstMsg" />
    <result column="DST_OFFSET" jdbcType="VARCHAR" property="dstOffset" />
    <result column="DST_START" jdbcType="VARCHAR" property="dstStart" />
    <result column="DST_END" jdbcType="VARCHAR" property="dstEnd" />
    <result column="DST_OPPSET" jdbcType="VARCHAR" property="dstOppset" />
    <result column="WT_START" jdbcType="VARCHAR" property="wtStart" />
    <result column="WT_END" jdbcType="VARCHAR" property="wtEnd" />
    <result column="WT_OPPSET" jdbcType="VARCHAR" property="wtOppset" />
  </resultMap>
  <sql id="Base_Column_List">

    ID, CREATE_TIME, CREATE_USER, LAST_UPDATE_TIME, LAST_UPDATE_USER, DST_MSG, DST_OFFSET, 
    DST_START, DST_END, DST_OPPSET, WT_START, WT_END, WT_OPPSET
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">

    select 
    <include refid="Base_Column_List" />
    from T_DST_WT_INFO
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from T_DST_WT_INFO
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.juneyaoair.manage.b2c.entity.DstWtInfoPO">
    insert into T_DST_WT_INFO (ID, CREATE_TIME, CREATE_USER, 
      LAST_UPDATE_TIME, LAST_UPDATE_USER, DST_MSG, 
      DST_OFFSET, DST_START, DST_END, 
      DST_OPPSET, WT_START, WT_END, 
      WT_OPPSET)
    values (#{id,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUser,jdbcType=VARCHAR}, #{dstMsg,jdbcType=VARCHAR}, 
      #{dstOffset,jdbcType=VARCHAR}, #{dstStart,jdbcType=VARCHAR}, #{dstEnd,jdbcType=VARCHAR}, 
      #{dstOppset,jdbcType=VARCHAR}, #{wtStart,jdbcType=VARCHAR}, #{wtEnd,jdbcType=VARCHAR}, 
      #{wtOppset,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.juneyaoair.manage.b2c.entity.DstWtInfoPO">
    insert into T_DST_WT_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createUser != null">
        CREATE_USER,
      </if>
      <if test="lastUpdateTime != null">
        LAST_UPDATE_TIME,
      </if>
      <if test="lastUpdateUser != null">
        LAST_UPDATE_USER,
      </if>
      <if test="dstMsg != null">
        DST_MSG,
      </if>
      <if test="dstOffset != null">
        DST_OFFSET,
      </if>
      <if test="dstStart != null">
        DST_START,
      </if>
      <if test="dstEnd != null">
        DST_END,
      </if>
      <if test="dstOppset != null">
        DST_OPPSET,
      </if>
      <if test="wtStart != null">
        WT_START,
      </if>
      <if test="wtEnd != null">
        WT_END,
      </if>
      <if test="wtOppset != null">
        WT_OPPSET,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUser != null">
        #{lastUpdateUser,jdbcType=VARCHAR},
      </if>
      <if test="dstMsg != null">
        #{dstMsg,jdbcType=VARCHAR},
      </if>
      <if test="dstOffset != null">
        #{dstOffset,jdbcType=VARCHAR},
      </if>
      <if test="dstStart != null">
        #{dstStart,jdbcType=VARCHAR},
      </if>
      <if test="dstEnd != null">
        #{dstEnd,jdbcType=VARCHAR},
      </if>
      <if test="dstOppset != null">
        #{dstOppset,jdbcType=VARCHAR},
      </if>
      <if test="wtStart != null">
        #{wtStart,jdbcType=VARCHAR},
      </if>
      <if test="wtEnd != null">
        #{wtEnd,jdbcType=VARCHAR},
      </if>
      <if test="wtOppset != null">
        #{wtOppset,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.DstWtInfoPO">

    update T_DST_WT_INFO
    <set>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        CREATE_USER = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUser != null">
        LAST_UPDATE_USER = #{lastUpdateUser,jdbcType=VARCHAR},
      </if>
      <if test="dstMsg != null">
        DST_MSG = #{dstMsg,jdbcType=VARCHAR},
      </if>
      <if test="dstOffset != null">
        DST_OFFSET = #{dstOffset,jdbcType=VARCHAR},
      </if>
      <if test="dstStart != null">
        DST_START = #{dstStart,jdbcType=VARCHAR},
      </if>
      <if test="dstEnd != null">
        DST_END = #{dstEnd,jdbcType=VARCHAR},
      </if>
      <if test="dstOppset != null">
        DST_OPPSET = #{dstOppset,jdbcType=VARCHAR},
      </if>
      <if test="wtStart != null">
        WT_START = #{wtStart,jdbcType=VARCHAR},
      </if>
      <if test="wtEnd != null">
        WT_END = #{wtEnd,jdbcType=VARCHAR},
      </if>
      <if test="wtOppset != null">
        WT_OPPSET = #{wtOppset,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.juneyaoair.manage.b2c.entity.DstWtInfoPO">

    update T_DST_WT_INFO
    set CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_USER = #{createUser,jdbcType=VARCHAR},
      LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
      LAST_UPDATE_USER = #{lastUpdateUser,jdbcType=VARCHAR},
      DST_MSG = #{dstMsg,jdbcType=VARCHAR},
      DST_OFFSET = #{dstOffset,jdbcType=VARCHAR},
      DST_START = #{dstStart,jdbcType=VARCHAR},
      DST_END = #{dstEnd,jdbcType=VARCHAR},
      DST_OPPSET = #{dstOppset,jdbcType=VARCHAR},
      WT_START = #{wtStart,jdbcType=VARCHAR},
      WT_END = #{wtEnd,jdbcType=VARCHAR},
      WT_OPPSET = #{wtOppset,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectbyidIn" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from T_DST_WT_INFO
    where ID in
    <foreach item="item" index="index" collection="idCollection"
          open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_DST_WT_INFO
  </select>
</mapper>