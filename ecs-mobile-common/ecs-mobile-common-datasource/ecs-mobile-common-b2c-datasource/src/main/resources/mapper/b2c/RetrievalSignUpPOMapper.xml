<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.RetrievalSignUpPOMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.RetrievalSignUpPO">
        <id property="ffpCardNo" column="FFP_CARD_NO" jdbcType="VARCHAR"/>
        <result property="signUpTime" column="SIGN_UP_TIME" jdbcType="VARCHAR"/>
        <result property="invalidScores" column="INVALID_SCORES" jdbcType="DECIMAL"/>
        <result property="flightTimes" column="FLIGHT_TIMES" jdbcType="DECIMAL"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="CREATE_USER" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="UPDATE_USER" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        FFP_CARD_NO
        ,SIGN_UP_TIME,INVALID_SCORES,
        FLIGHT_TIMES,CREATE_TIME,CREATE_USER,
        UPDATE_TIME,UPDATE_USER
    </sql>

    <select id="toCatchRetrievalSignUpInformation"
            parameterType="com.juneyaoair.ecs.manage.dto.activity.request.pointretrieval.PointRetrievalRequest"
            resultType="com.juneyaoair.ecs.manage.dto.activity.response.pointretrieval.PointRetrievalInformation">
        select rsur.FFP_CARD_NO as ffpCardNo,
        rsur.SIGN_UP_TIME as signUpTime,
        rsur.FLIGHT_TIMES as flightTimes,
        rsur.INVALID_SCORES as invalidScores,
        pprr.PRIZE_AMOUNT as receiveScores,
        pprr.RECEIVE_TIME as receiveTime,
        (case pprr.RECEIVE_STATUS
        when 'PREPARE' then '准备发放,未执行发放动作'
        when 'SUCCESS' then '发放成功'
        when 'FAILED' then '发放失败' else '还未成行,无领取记录' end) as receiveStatus
        from T_RETRIEVAL_SIGN_UP_RECORD rsur
        left join T_POINT_PRIZE_RECEIVE_RECORD pprr on rsur.FFP_CARD_NO = pprr.FFP_CARD_NO
        <where>
            1 = 1
            <if test="ffpCardNo != null and ffpCardNo != ''">
                and rsur.FFP_CARD_NO = #{ffpCardNo,jdbcType=VARCHAR}
            </if>
            <if test="signUpStartTime != null and signUpStartTime != '' and signUpEndTime != null and signUpEndTime != ''">
                and TO_DATE(rsur.SIGN_UP_TIME, 'YYYY-MM-DD') BETWEEN TO_DATE(#{signUpStartTime}, 'YYYY-MM-DD') and
                TO_DATE(#{signUpEndTime}, 'YYYY-MM-DD')
            </if>
            <if test="receiveStartTime!=null and  receiveStartTime!='' and receiveEndTime!=null and  receiveEndTime!=''">
                and TO_DATE(pprr.RECEIVE_TIME, 'YYYY-MM-DD') BETWEEN TO_DATE(#{receiveStartTime}, 'YYYY-MM-DD') and
                TO_DATE(#{receiveEndTime}, 'YYYY-MM-DD')
            </if>
        </where>
        order by rsur.SIGN_UP_TIME desc, pprr.FFP_CARD_NO, pprr.RECEIVE_TIME desc
    </select>


</mapper>
