<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.ActivityCouponsMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.ActivityCouponsPO">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="couponGiftName" column="COUPON_GIFT_NAME" jdbcType="VARCHAR"/>
            <result property="couponGiftImageUrl" column="COUPON_GIFT_IMAGE_URL" jdbcType="VARCHAR"/>
            <result property="activityStartTime" column="ACTIVITY_START_TIME" jdbcType="VARCHAR"/>
            <result property="activityEndTime" column="ACTIVITY_END_TIME" jdbcType="VARCHAR"/>
            <result property="activityRules" column="ACTIVITY_RULES" jdbcType="VARCHAR"/>
            <result property="activityUrl" column="ACTIVITY_URL" jdbcType="VARCHAR"/>
            <result property="redirectUrl" column="REDIRECT_URL" jdbcType="VARCHAR"/>
            <result property="couponGiftType" column="COUPON_GIFT_TYPE" jdbcType="VARCHAR"/>
            <result property="createUser" column="CREATE_USER" jdbcType="VARCHAR"/>
            <result property="createDate" column="CREATE_DATE" jdbcType="VARCHAR"/>
            <result property="lastUpdateUser" column="LAST_UPDATE_USER" jdbcType="VARCHAR"/>
            <result property="lastUpdateDate" column="LAST_UPDATE_DATE" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="isValid" column="IS_VALID" jdbcType="VARCHAR"/>
            <result property="bgColor" column="BG_COLOR" jdbcType="VARCHAR"/>
            <result property="modelUrl" column="MODEL_URL" jdbcType="VARCHAR"/>
            <result property="packageName" column="PACKAGE_NAME" jdbcType="VARCHAR"/>
            <result property="getStyle" column="GET_STYLE" jdbcType="VARCHAR"/>
            <result property="getLimit" column="GET_LIMIT" jdbcType="VARCHAR"/>
            <result property="getLevel" column="GET_LEVEL" jdbcType="VARCHAR"/>
            <result property="memberYears" column="MEMBER_YEARS" jdbcType="VARCHAR"/>
            <result property="memberCardOrPhoneNum" column="MEMBER_CARD_OR_PHONE_NUM" jdbcType="VARCHAR"/>
            <result property="memberCardOrPhoneNumDesc" column="MEMBER_CARD_OR_PHONE_NUM_DESC" jdbcType="VARCHAR"/>
            <result property="canShare" column="CAN_SHARE" jdbcType="VARCHAR"/>
            <result property="shareTitle" column="SHARE_TITLE" jdbcType="VARCHAR"/>
            <result property="shareDesc" column="SHARE_DESC" jdbcType="VARCHAR"/>
            <result property="shareLink" column="SHARE_LINK" jdbcType="VARCHAR"/>
            <result property="shareImgUrl" column="SHARE_IMG_URL" jdbcType="VARCHAR"/>
            <result property="packageNum" column="PACKAGE_NUM" jdbcType="VARCHAR"/>
            <result property="packagePrice" column="PACKAGE_PRICE" jdbcType="VARCHAR"/>
            <result property="upperLimit" column="UPPER_LIMIT" jdbcType="VARCHAR"/>
            <result property="packageNameColor" column="PACKAGE_NAME_COLOR" jdbcType="VARCHAR"/>
            <result property="vCodeCheck" column="V_CODE_CHECK" jdbcType="VARCHAR"/>
            <result property="showType" column="SHOW_TYPE" jdbcType="VARCHAR"/>
            <result property="stock" column="STOCK" jdbcType="DECIMAL"/>
            <result property="checkRealName" column="CHECK_REAL_NAME" jdbcType="VARCHAR"/>
            <result property="nameAuthentication" column="NAME_AUTHENTICATION" jdbcType="VARCHAR"/>
            <result property="merchantPayment" column="MERCHANT_PAYMENT" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,COUPON_GIFT_NAME,COUPON_GIFT_IMAGE_URL,
        ACTIVITY_START_TIME,ACTIVITY_END_TIME,ACTIVITY_RULES,
        ACTIVITY_URL,REDIRECT_URL,COUPON_GIFT_TYPE,
        CREATE_USER,CREATE_DATE,LAST_UPDATE_USER,
        LAST_UPDATE_DATE,REMARK,IS_VALID,
        BG_COLOR,MODEL_URL,PACKAGE_NAME,
        GET_STYLE,GET_LIMIT,GET_LEVEL,
        MEMBER_YEARS,MEMBER_CARD_OR_PHONE_NUM,MEMBER_CARD_OR_PHONE_NUM_DESC,
        CAN_SHARE,SHARE_TITLE,SHARE_DESC,
        SHARE_LINK,SHARE_IMG_URL,PACKAGE_NUM,
        PACKAGE_PRICE,UPPER_LIMIT,PACKAGE_NAME_COLOR,
        V_CODE_CHECK,SHOW_TYPE,STOCK,
        CHECK_REAL_NAME,NAME_AUTHENTICATION,MERCHANT_PAYMENT,
        V_CODE
    </sql>
    <insert id="insertAll">
        insert into T_ACTIVITY_COUPONS
        (ID, COUPON_GIFT_NAME, COUPON_GIFT_IMAGE_URL,
         ACTIVITY_START_TIME, ACTIVITY_END_TIME, ACTIVITY_RULES,
         ACTIVITY_URL, REDIRECT_URL, COUPON_GIFT_TYPE,
         CREATE_USER, CREATE_DATE, LAST_UPDATE_USER,
         LAST_UPDATE_DATE, REMARK, IS_VALID,
         BG_COLOR, MODEL_URL, PACKAGE_NAME,
         GET_STYLE, GET_LIMIT, GET_LEVEL,
         MEMBER_YEARS, MEMBER_CARD_OR_PHONE_NUM, MEMBER_CARD_OR_PHONE_NUM_DESC,
         CAN_SHARE, SHARE_TITLE, SHARE_DESC,
         SHARE_LINK, SHARE_IMG_URL, PACKAGE_NUM,
         PACKAGE_PRICE, UPPER_LIMIT, PACKAGE_NAME_COLOR,
         V_CODE_CHECK, SHOW_TYPE, STOCK,
         CHECK_REAL_NAME, NAME_AUTHENTICATION,
         <if test="merchantPayment != null and merchantPayment != ''">
         MERCHANT_PAYMENT,
         </if>,
         V_CODE)
        values (#{id,jdbcType=VARCHAR}, #{couponGiftName,jdbcType=VARCHAR}, #{couponGiftImageUrl,jdbcType=VARCHAR},
                #{activityStartTime,jdbcType=VARCHAR}, #{activityEndTime,jdbcType=VARCHAR},
                #{activityRules,jdbcType=VARCHAR},
                #{activityUrl,jdbcType=VARCHAR}, #{redirectUrl,jdbcType=VARCHAR}, #{couponGiftType,jdbcType=VARCHAR},
                #{createUser,jdbcType=VARCHAR}, #{createDate,jdbcType=VARCHAR}, #{lastUpdateUser,jdbcType=VARCHAR},
                #{lastUpdateDate,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 'N',
                #{bgColor,jdbcType=VARCHAR}, #{modelUrl,jdbcType=VARCHAR}, #{packageName,jdbcType=VARCHAR},
                #{getStyle,jdbcType=VARCHAR}, #{getLimit,jdbcType=VARCHAR}, #{getLevel,jdbcType=VARCHAR},
                #{memberYears,jdbcType=VARCHAR}, #{memberCardOrPhoneNum,jdbcType=VARCHAR},
                #{memberCardOrPhoneNumDesc,jdbcType=VARCHAR},
                #{canShare,jdbcType=VARCHAR}, #{shareTitle,jdbcType=VARCHAR}, #{shareDesc,jdbcType=VARCHAR},
                #{shareLink,jdbcType=VARCHAR}, #{shareImgUrl,jdbcType=VARCHAR}, #{packageNum,jdbcType=VARCHAR},
                #{packagePrice,jdbcType=VARCHAR}, #{upperLimit,jdbcType=VARCHAR}, #{packageNameColor,jdbcType=VARCHAR},
                #{vCodeCheck,jdbcType=VARCHAR}, #{showType,jdbcType=VARCHAR}, #{stock,jdbcType=NUMERIC},
                #{checkRealName,jdbcType=VARCHAR}, #{nameAuthentication,jdbcType=VARCHAR},
                <if test="merchantPayment != null and merchantPayment != ''" >
                #{merchantPayment,jdbcType=VARCHAR},
                </if>
                #{vCode,jdbcType=VARCHAR})
    </insert>
</mapper>
