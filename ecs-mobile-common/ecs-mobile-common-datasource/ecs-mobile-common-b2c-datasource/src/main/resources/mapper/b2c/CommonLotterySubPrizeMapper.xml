<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.CommonLotterySubPrizeMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.CommonLotterySubPrizePO">
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="prizeCode" column="PRIZE_CODE" jdbcType="VARCHAR"/>
            <result property="subPrizeCategory" column="SUB_PRIZE_CATEGORY" jdbcType="VARCHAR"/>
            <result property="subPrizeCode" column="SUB_PRIZE_CODE" jdbcType="VARCHAR"/>
            <result property="subPrizeName" column="SUB_PRIZE_NAME" jdbcType="VARCHAR"/>
            <result property="subPrizeAmount" column="SUB_PRIZE_AMOUNT" jdbcType="DECIMAL"/>
            <result property="deleted" column="DELETED" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="CREATE_USER" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="UPDATE_USER" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PRIZE_CODE,SUB_PRIZE_CATEGORY,
        SUB_PRIZE_CODE,SUB_PRIZE_NAME,SUB_PRIZE_AMOUNT,
        DELETED,CREATE_TIME,CREATE_USER,
        UPDATE_TIME,UPDATE_USER
    </sql>
    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.activity.CommonLotterySubPrizeUpPO">
        update T_PRIZE_SUB_ENTITY
        SET DELETED = #{deleted,jdbcType=TIMESTAMP},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        UPDATE_USER = #{updateUser,jdbcType=VARCHAR}
        <where>
            <if test=" prizeCodes != null and prizeCodes != ''">
                AND PRIZE_CODE in
                <foreach collection="prizeCodes" item="prizeCode" index="index" open="(" close=")" separator=",">
                    #{prizeCode}
                </foreach>
            </if>
        </where>
    </update>
    <select id="toGainAllSubPrizeRecords" parameterType="com.juneyaoair.manage.b2c.entity.activity.CommonLotterySubPrizePO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_PRIZE_SUB_ENTITY
        <where>
            <if test="prizeCode != null and prizeCode != ''">
                PRIZE_CODE = #{prizeCode,jdbcType=VARCHAR}
            </if>
            <if test="deleted != null and deleted != ''">
                AND DELETED = #{deleted,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <delete id="toDeleteSubPrizes" parameterType="com.juneyaoair.manage.b2c.entity.activity.CommonLotterySubPrizePO">
        delete from T_PRIZE_SUB_ENTITY
        <where>
            <if test="id != null and id != ''">
                ID = #{id,jdbcType=VARCHAR}
            </if>
            <if test="prizeCode != null and prizeCode != ''">
                AND PRIZE_CODE = #{prizeCode,jdbcType=VARCHAR}
            </if>
        </where>
    </delete>
</mapper>
