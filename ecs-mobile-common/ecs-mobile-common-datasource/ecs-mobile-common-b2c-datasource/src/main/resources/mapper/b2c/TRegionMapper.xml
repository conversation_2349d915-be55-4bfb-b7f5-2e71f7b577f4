<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TRegionMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.RegionPO">
        <!--@mbg.generated-->
        <!--@Table T_REGION-->
        <id column="REGION_CODE" jdbcType="VARCHAR" property="regionCode"/>
        <result column="REGION_NAME" jdbcType="VARCHAR" property="regionName"/>
        <result column="REGION_E_NAME" jdbcType="VARCHAR" property="regionEName"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="LAST_UPDATE_TIME" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LAST_UPDATE_USER" jdbcType="VARCHAR" property="lastUpdateUser"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        REGION_CODE, REGION_NAME, REGION_E_NAME, CREATE_TIME, CREATE_USER, LAST_UPDATE_TIME,
        LAST_UPDATE_USER
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_REGION
        where REGION_CODE = #{regionCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete
        from T_REGION
        where REGION_CODE = #{regionCode,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.juneyaoair.manage.b2c.entity.RegionPO">
        <!--@mbg.generated-->
        insert into T_REGION (REGION_CODE, REGION_NAME, REGION_E_NAME,
                              CREATE_TIME, CREATE_USER, LAST_UPDATE_TIME,
                              LAST_UPDATE_USER)
        values (#{regionCode,jdbcType=VARCHAR}, #{regionName,jdbcType=VARCHAR}, #{regionEName,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP},
                #{lastUpdateUser,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.juneyaoair.manage.b2c.entity.RegionPO">
        insert into T_REGION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="regionCode != null">
                REGION_CODE,
            </if>
            <if test="regionName != null">
                REGION_NAME,
            </if>
            <if test="regionEName != null">
                REGION_E_NAME,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="createUser != null">
                CREATE_USER,
            </if>
            <if test="lastUpdateTime != null">
                LAST_UPDATE_TIME,
            </if>
            <if test="lastUpdateUser != null">
                LAST_UPDATE_USER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="regionCode != null">
                #{regionCode,jdbcType=VARCHAR},
            </if>
            <if test="regionName != null">
                #{regionName,jdbcType=VARCHAR},
            </if>
            <if test="regionEName != null">
                #{regionEName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.RegionPO">
        <!--@mbg.generated-->
        update T_REGION
        <set>
            <if test="regionName != null">
                REGION_NAME = #{regionName,jdbcType=VARCHAR},
            </if>
            <if test="regionEName != null">
                REGION_E_NAME = #{regionEName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LAST_UPDATE_USER = #{lastUpdateUser,jdbcType=VARCHAR},
            </if>
        </set>
        where REGION_CODE = #{regionCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.juneyaoair.manage.b2c.entity.RegionPO">
        <!--@mbg.generated-->
        update T_REGION
        set REGION_NAME      = #{regionName,jdbcType=VARCHAR},
            REGION_E_NAME    = #{regionEName,jdbcType=VARCHAR},
            CREATE_TIME      = #{createTime,jdbcType=TIMESTAMP},
            CREATE_USER      = #{createUser,jdbcType=VARCHAR},
            LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
            LAST_UPDATE_USER = #{lastUpdateUser,jdbcType=VARCHAR}
        where REGION_CODE = #{regionCode,jdbcType=VARCHAR}
    </update>
    <select id="selectByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_REGION
        <where>
            <if test="regionCode != null">
                and REGION_CODE = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test="regionName != null">
                and REGION_NAME = #{regionName,jdbcType=VARCHAR}
            </if>
            <if test="regionEName != null">
                and REGION_E_NAME = #{regionEName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createUser != null">
                and CREATE_USER = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="lastUpdateTime != null">
                and LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="lastUpdateUser != null">
                and LAST_UPDATE_USER = #{lastUpdateUser,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>