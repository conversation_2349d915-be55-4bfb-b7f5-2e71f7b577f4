<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.ActivityAirlineMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.ActivityAirlinePO">
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="orderNo" column="ORDER_NO" jdbcType="DECIMAL"/>
            <result property="arrCode" column="ARR_CODE" jdbcType="VARCHAR"/>
            <result property="sendCode" column="SEND_CODE" jdbcType="VARCHAR"/>
            <result property="arrCityName" column="ARR_CITY_NAME" jdbcType="VARCHAR"/>
            <result property="depCityName" column="DEP_CITY_NAME" jdbcType="VARCHAR"/>
            <result property="seckillCabin" column="SECKILL_CABIN" jdbcType="VARCHAR"/>
            <result property="seckillPrice" column="SECKILL_PRICE" jdbcType="DECIMAL"/>
            <result property="childInfoId" column="CHILD_INFO_ID" jdbcType="VARCHAR"/>
            <result property="seqNo" column="SEQ_NO" jdbcType="DECIMAL"/>
            <result property="filghtNumber" column="FILGHT_NUMBER" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ORDER_NO,ARR_CODE,
        SEND_CODE,ARR_CITY_NAME,DEP_CITY_NAME,
        SECKILL_CABIN,SECKILL_PRICE,CHILD_INFO_ID,
        SEQ_NO,FILGHT_NUMBER
    </sql>
    <select id="searchAllById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_ACTIVITY_AIRLINE
        <where>
            <if test="id != null and id != ''">
                ID = #{id,jdbcType=VARCHAR}
            </if>
            <if test="childInfoId != null and childInfoId != ''">
                CHILD_INFO_ID = #{childInfoId}
            </if>
        </where>
        order by order_no,seq_no
    </select>

    <delete id="delByChildInfoId" parameterType="com.juneyaoair.ecs.manage.dto.activity.common.ActivityAirLine">
        delete
        from T_ACTIVITY_AIRLINE
        where CHILD_INFO_ID = #{childInfoId,jdbcType=VARCHAR}
    </delete>
</mapper>
