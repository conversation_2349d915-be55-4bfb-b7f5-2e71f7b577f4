<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.CoBrandCreditCardContentPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.CoBrandCreditCardContentPO">
    <!--@mbg.generated-->
    <!--@Table CO_BRAND_CREDIT_CARD_CONTENT-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="IMG_URL_LIST" jdbcType="VARCHAR" property="imgUrlList" />
    <result column="TITLE" jdbcType="VARCHAR" property="title" />
    <result column="SORT_NUM" jdbcType="DECIMAL" property="sortNum" />
    <result column="DETAIL_ID" jdbcType="VARCHAR" property="detailId" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, IMG_URL_LIST, TITLE, SORT_NUM, DETAIL_ID, CREATED_BY, CREATED_TIME, UPDATED_BY, 
    UPDATED_TIME
  </sql>
</mapper>