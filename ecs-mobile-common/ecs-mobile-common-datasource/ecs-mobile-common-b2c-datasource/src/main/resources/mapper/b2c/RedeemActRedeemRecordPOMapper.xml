<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.RedeemActRedeemRecordPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.RedeemActRedeemRecordPO">
    <!--@mbg.generated-->
    <!--@Table T_REDEEM_ACT_REDEEM_RECORD-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="FFP_ID" jdbcType="VARCHAR" property="ffpId" />
    <result column="FFP_NO" jdbcType="VARCHAR" property="ffpNo" />
    <result column="ACTIVE_ID" jdbcType="VARCHAR" property="activeId" />
    <result column="AWARD_ID" jdbcType="VARCHAR" property="awardId" />
    <result column="AWARD_NAME" jdbcType="VARCHAR" property="awardName" />
    <result column="AWARD_DESC" jdbcType="VARCHAR" property="awardDesc" />
    <result column="POINT_ENUM_TYPE" jdbcType="VARCHAR" property="pointEnumType" />
    <result column="POINT" jdbcType="VARCHAR" property="point" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CRM_RECORD_ID" jdbcType="VARCHAR" property="crmRecordId" />
    <result column="TICKET_NUMBER" jdbcType="VARCHAR" property="ticketNumber" />
    <result column="ISSUANCE_STATE" jdbcType="VARCHAR" property="issuanceState" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, FFP_ID, FFP_NO, ACTIVE_ID, AWARD_ID, AWARD_NAME, AWARD_DESC, POINT_ENUM_TYPE, 
    POINT, CREATE_TIME, CRM_RECORD_ID, TICKET_NUMBER, ISSUANCE_STATE
  </sql>

  <select id="selectSignRedeemRecord" resultMap="BaseResultMap">
    SELECT *
    FROM T_REDEEM_ACT_REDEEM_RECORD
    WHERE POINT_ENUM_TYPE = 'SIGN'
      AND ROWNUM &lt; 1000
    <if test="actStartTime != null and actStartTime != ''">
      AND to_date(#{actStartTime,jdbcType=VARCHAR}, 'yyyy-MM-dd HH24:MI:SS') &lt; CREATE_TIME
    </if>
    <if test="actEndTime != null and actEndTime != '' != ''">
      AND to_date(#{actEndTime,jdbcType=VARCHAR}, 'yyyy-MM-dd HH24:MI:SS') &gt; CREATE_TIME
    </if>
    <if test="ffpNo != null and ffpNo != ''">
      AND FFP_NO = #{ffpNo,jdbcType=VARCHAR}
    </if>
    ORDER BY CREATE_TIME DESC
  </select>

  <select id="selectSegRedeemRecord" resultMap="BaseResultMap">
    SELECT *
    FROM T_REDEEM_ACT_REDEEM_RECORD
    WHERE (POINT_ENUM_TYPE = 'SEG_NORMAL' OR POINT_ENUM_TYPE = 'SEG_PRO')
      AND ROWNUM &lt; 1000
    <if test="actStartTime != null and actStartTime != ''">
      AND to_date(#{actStartTime,jdbcType=VARCHAR}, 'yyyy-MM-dd HH24:MI:SS') &lt; CREATE_TIME
    </if>
    <if test="actEndTime != null and actEndTime != '' != ''">
      AND to_date(#{actEndTime,jdbcType=VARCHAR}, 'yyyy-MM-dd HH24:MI:SS') &gt; CREATE_TIME
    </if>
    <if test="ffpNo != null and ffpNo != ''">
      AND FFP_NO = #{ffpNo,jdbcType=VARCHAR}
    </if>
    ORDER BY CREATE_TIME DESC
  </select>
</mapper>