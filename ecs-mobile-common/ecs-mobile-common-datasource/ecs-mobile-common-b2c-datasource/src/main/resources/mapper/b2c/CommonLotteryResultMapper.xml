<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.CommonLotteryResultMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.CommonLotteryResultResp">
            <result property="createDate" column="CREATE_TIME" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createDate" jdbcType="VARCHAR" />
        <result column="PRIZE_POOL_CODE" property="poolCode" jdbcType="VARCHAR" />
        <result column="PRIZE_POOL_NAME" property="poolName" jdbcType="VARCHAR" />
        <result column="FFP_ID" property="ffpId" jdbcType="VARCHAR" />
        <result column="PRIZE_NAME" property="prizeName" jdbcType="VARCHAR" />
        <result column="PRIZE_CONTENT" property="prizeContent" jdbcType="VARCHAR" />
        <result column="SEND_STATUS" property="sendStatus" jdbcType="VARCHAR" />
        <result column="FAILED_REASON" property="failedReason" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List">
        ID,FFP_ID,FFP_CARD_NO,
        PRIZE_POOL_CODE,PRIZE_POOL_NAME,PRIZE_CODE,
        PRIZE_NAME,PRIZE_CONTENT,SEND_STATUS,
        FAILED_REASON,CREATE_TIME,UPDATE_TIME
    </sql>
    <select id="toGainAllLotteryRecords" parameterType="com.juneyaoair.manage.b2c.entity.activity.CommonLotteryResultReq" resultMap="BaseResultMap">
        select CREATE_TIME,
        PRIZE_POOL_CODE,
        PRIZE_POOL_NAME,
        FFP_CARD_NO AS FFP_ID,
        PRIZE_NAME,
        PRIZE_CONTENT,
        (CASE
        WHEN t.SEND_STATUS = 'T' then
        '成功'
        WHEN t.SEND_STATUS = 'F' then
        '失败'
        WHEN t.SEND_STATUS = 'P' then
        '部分成功'
        WHEN t.SEND_STATUS = 'U' then
        '状态未知'
        ELSE
        '状态未知'
        END ) AS SEND_STATUS,
        NVL(t.FAILED_REASON,'-') FAILED_REASON
        from T_PRIZE_WINNING_RECORD t
        <where>
            <if test=" lotteryPoolName != null and lotteryPoolName != ''">
                AND t.PRIZE_POOL_NAME like '%${lotteryPoolName}%'
            </if>
            <if test=" lotteryPoolCode != null and lotteryPoolCode != ''">
                AND t.PRIZE_POOL_CODE like '%${lotteryPoolCode}%'
            </if>
            <if test="startDate!=null and  startDate!=''">
                AND t.CREATE_TIME &gt; to_date(#{startDate,jdbcType=VARCHAR}, 'yyyy-MM-dd HH24:MI:SS')
            </if>
            <if test="endDate!=null and  endDate!=''">
                AND t.CREATE_TIME &lt; to_date(#{endDate,jdbcType=VARCHAR}, 'yyyy-MM-dd HH24:MI:SS')
            </if>
            <if test="ffpCardNo != null and ffpCardNo != ''">
                AND FFP_CARD_NO = #{ffpCardNo,jdbcType=VARCHAR}
            </if>
            <if test="sendStatus != null and sendStatus != ''">
                AND SEND_STATUS in
                <foreach collection="sendStatus" item="sendStatus" index="index" open="(" close=")" separator=",">
                    #{sendStatus}
                </foreach>
            </if>
        </where>
        order by t.CREATE_TIME DESC
    </select>


</mapper>
