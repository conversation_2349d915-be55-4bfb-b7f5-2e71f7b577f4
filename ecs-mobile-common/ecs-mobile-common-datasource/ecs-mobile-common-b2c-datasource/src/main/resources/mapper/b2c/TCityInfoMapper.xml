<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TCityInfoMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.CityInfoPO">
        <id column="CITY_CODE" jdbcType="VARCHAR" property="cityCode"/>
        <result column="CITY_NAME" jdbcType="VARCHAR" property="cityName"/>
        <result column="CITY_E_NAME" jdbcType="VARCHAR" property="cityEName"/>
        <result column="COUNTRY_CODE" jdbcType="VARCHAR" property="countryCode"/>
        <result column="CITY_PIN_YIN" jdbcType="VARCHAR" property="cityPinYin"/>
        <result column="CITY_PIN_YIN_ABB" jdbcType="VARCHAR" property="cityPinYinAbb"/>
        <result column="PROVINCE_ID" jdbcType="DECIMAL" property="provinceId"/>
        <result column="PROVINCE_NAME" jdbcType="VARCHAR" property="provinceName"/>
        <result column="IS_HOT_CITY" jdbcType="VARCHAR" property="isHotCity"/>
        <result column="IS_TOP_CITY" jdbcType="VARCHAR" property="isTopCity"/>
        <result column="OFFICE_ADDRESS" jdbcType="VARCHAR" property="officeAddress"/>
        <result column="OFFICE_TEL" jdbcType="VARCHAR" property="officeTel"/>
        <result column="CREATE_DATETIME" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="CREATE_ID" jdbcType="DECIMAL" property="createId"/>
        <result column="CITY_TIME_ZONE" jdbcType="VARCHAR" property="cityTimeZone"/>
        <result column="NAME_ABB" jdbcType="VARCHAR" property="nameAbb"/>
        <result column="ENGLISH_NAME_ABB" jdbcType="VARCHAR" property="englishNameAbb"/>
        <result column="IS_INTERNATIONAL" jdbcType="VARCHAR" property="isInternational"/>
        <result column="BAIDUMAPPOINT" jdbcType="VARCHAR" property="baidumappoint"/>
        <result column="OFFICE_FAX" jdbcType="VARCHAR" property="officeFax"/>
        <result column="DELFLAG" jdbcType="CHAR" property="delflag"/>
        <result column="URL" jdbcType="VARCHAR" property="url"/>
        <result column="CITY_HOT_ORDER" jdbcType="DECIMAL" property="cityHotOrder"/>
        <result column="ZONE_HOT_ORDER" jdbcType="DECIMAL" property="zoneHotOrder"/>
        <result column="ICON_URL" jdbcType="VARCHAR" property="iconUrl"/>
        <result column="DST_WT_ID" jdbcType="VARCHAR" property="dstWtId"/>
        <result column="CITY_KO_NAME" jdbcType="VARCHAR" property="cityKoName"/>
        <result column="CITY_JP_NAME" jdbcType="VARCHAR" property="cityJpName"/>
        <result column="CITY_TH_NAME" jdbcType="VARCHAR" property="cityThName"/>
        <result column="CITY_TC_NAME" jdbcType="VARCHAR" property="cityTcName"/>
        <result column="CITY_KEY_WORDS" jdbcType="VARCHAR" property="cityKeyWords"/>
        <result column="IS_HOT_REGION" jdbcType="VARCHAR" property="isHotRegion"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="LONGITUDE" jdbcType="VARCHAR" property="longitude"/>
        <result column="LATITUDE" jdbcType="VARCHAR" property="latitude"/>
        <result column="IS_OFTEN_CITY" jdbcType="VARCHAR" property="isOftenCity"/>
        <result column="CITY_PICTURE_PC_URL" jdbcType="VARCHAR" property="cityPicturePcUrl"/>
        <result column="NEARBY_AIRPORT" jdbcType="VARCHAR" property="nearbyAirport"/>
    </resultMap>
    <sql id="Base_Column_List">
        CITY_CODE,
        CITY_NAME,
        CITY_E_NAME,
        COUNTRY_CODE,
        CITY_PIN_YIN,
        CITY_PIN_YIN_ABB,
        PROVINCE_ID,
        PROVINCE_NAME,
        IS_HOT_CITY,
        IS_TOP_CITY,
        OFFICE_ADDRESS,
        OFFICE_TEL,
        CREATE_DATETIME,
        CREATE_ID,
        CITY_TIME_ZONE,
        NAME_ABB,
        ENGLISH_NAME_ABB,
        IS_INTERNATIONAL,
        BAIDUMAPPOINT,
        OFFICE_FAX,
        DELFLAG,
        URL,
        CITY_HOT_ORDER,
        ZONE_HOT_ORDER,
        ICON_URL,
        DST_WT_ID,
        CITY_KO_NAME,
        CITY_JP_NAME,
        CITY_TH_NAME,
        CITY_TC_NAME,
        CITY_KEY_WORDS,
        IS_HOT_REGION,
        "STATUS",
        LONGITUDE,
        LATITUDE,
        IS_OFTEN_CITY,
        CITY_PICTURE_PC_URL,
        NEARBY_AIRPORT
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CITY_INFO
        where CITY_CODE = #{cityCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from T_CITY_INFO
        where CITY_CODE = #{cityCode,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.juneyaoair.manage.b2c.entity.CityInfoPO">
        insert into T_CITY_INFO (CITY_CODE, CITY_NAME, CITY_E_NAME,
                                 COUNTRY_CODE, CITY_PIN_YIN, CITY_PIN_YIN_ABB,
                                 PROVINCE_ID, PROVINCE_NAME, IS_HOT_CITY,
                                 IS_TOP_CITY, OFFICE_ADDRESS, OFFICE_TEL,
                                 CREATE_DATETIME, CREATE_ID, CITY_TIME_ZONE,
                                 NAME_ABB, ENGLISH_NAME_ABB, IS_INTERNATIONAL,
                                 BAIDUMAPPOINT, OFFICE_FAX, DELFLAG,
                                 URL, CITY_HOT_ORDER, ZONE_HOT_ORDER,ICON_URL,
                                 DST_WT_ID, CITY_KO_NAME, CITY_JP_NAME,
                                 CITY_TH_NAME, CITY_TC_NAME, CITY_KEY_WORDS,
                                 IS_HOT_REGION, "STATUS", LONGITUDE,
                                 LATITUDE, IS_OFTEN_CITY,
                                 CITY_PICTURE_PC_URL, NEARBY_AIRPORT)
        values (#{cityCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, #{cityEName,jdbcType=VARCHAR},
                #{countryCode,jdbcType=VARCHAR}, #{cityPinYin,jdbcType=VARCHAR}, #{cityPinYinAbb,jdbcType=VARCHAR},
                #{provinceId,jdbcType=DECIMAL}, #{provinceName,jdbcType=VARCHAR}, #{isHotCity,jdbcType=VARCHAR},
                #{isTopCity,jdbcType=VARCHAR}, #{officeAddress,jdbcType=VARCHAR}, #{officeTel,jdbcType=VARCHAR},
                #{createDatetime,jdbcType=TIMESTAMP}, #{createId,jdbcType=DECIMAL}, #{cityTimeZone,jdbcType=VARCHAR},
                #{nameAbb,jdbcType=VARCHAR}, #{englishNameAbb,jdbcType=VARCHAR}, #{isInternational,jdbcType=VARCHAR},
                #{baidumappoint,jdbcType=VARCHAR}, #{officeFax,jdbcType=VARCHAR}, #{delflag,jdbcType=CHAR},
                #{url,jdbcType=VARCHAR}, #{cityHotOrder,jdbcType=DECIMAL}, #{zoneHotOrder,jdbcType=DECIMAL}, #{iconUrl,jdbcType=VARCHAR},
                #{dstWtId,jdbcType=VARCHAR}, #{cityKoName,jdbcType=VARCHAR}, #{cityJpName,jdbcType=VARCHAR},
                #{cityThName,jdbcType=VARCHAR}, #{cityTcName,jdbcType=VARCHAR}, #{cityKeyWords,jdbcType=VARCHAR},
                #{isHotRegion,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{longitude,jdbcType=VARCHAR},
                #{latitude,jdbcType=VARCHAR}, #{isOftenCity,jdbcType=VARCHAR}, #{cityPicturePcUrl,jdbcType=VARCHAR},
                #{nearbyAirport,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.juneyaoair.manage.b2c.entity.CityInfoPO">
        insert into T_CITY_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cityCode != null">
                CITY_CODE,
            </if>
            <if test="cityName != null">
                CITY_NAME,
            </if>
            <if test="cityEName != null">
                CITY_E_NAME,
            </if>
            <if test="countryCode != null">
                COUNTRY_CODE,
            </if>
            <if test="cityPinYin != null">
                CITY_PIN_YIN,
            </if>
            <if test="cityPinYinAbb != null">
                CITY_PIN_YIN_ABB,
            </if>
            <if test="provinceId != null">
                PROVINCE_ID,
            </if>
            <if test="provinceName != null">
                PROVINCE_NAME,
            </if>
            <if test="isHotCity != null">
                IS_HOT_CITY,
            </if>
            <if test="isTopCity != null">
                IS_TOP_CITY,
            </if>
            <if test="officeAddress != null">
                OFFICE_ADDRESS,
            </if>
            <if test="officeTel != null">
                OFFICE_TEL,
            </if>
            <if test="createDatetime != null">
                CREATE_DATETIME,
            </if>
            <if test="createId != null">
                CREATE_ID,
            </if>
            <if test="cityTimeZone != null">
                CITY_TIME_ZONE,
            </if>
            <if test="nameAbb != null">
                NAME_ABB,
            </if>
            <if test="englishNameAbb != null">
                ENGLISH_NAME_ABB,
            </if>
            <if test="isInternational != null">
                IS_INTERNATIONAL,
            </if>
            <if test="baidumappoint != null">
                BAIDUMAPPOINT,
            </if>
            <if test="officeFax != null">
                OFFICE_FAX,
            </if>
            <if test="delflag != null">
                DELFLAG,
            </if>
            <if test="url != null">
                URL,
            </if>
            <if test="cityHotOrder != null">
                CITY_HOT_ORDER,
            </if>
            <if test="zoneHotOrder != null">
                ZONE_HOT_ORDER,
            </if>
            <if test="iconUrl != null">
                ICON_URL,
            </if>
            <if test="dstWtId != null">
                DST_WT_ID,
            </if>
            <if test="cityKoName != null">
                CITY_KO_NAME,
            </if>
            <if test="cityJpName != null">
                CITY_JP_NAME,
            </if>
            <if test="cityThName != null">
                CITY_TH_NAME,
            </if>
            <if test="cityTcName != null">
                CITY_TC_NAME,
            </if>
            <if test="cityKeyWords != null">
                CITY_KEY_WORDS,
            </if>
            <if test="isHotRegion != null">
                IS_HOT_REGION,
            </if>
            <if test="status != null">
                "STATUS",
            </if>
            <if test="longitude != null">
                LONGITUDE,
            </if>
            <if test="latitude != null">
                LATITUDE,
            </if>
            <if test="isOftenCity != null">
                IS_OFTEN_CITY,
            </if>
            <if test="nearbyAirport != null">
                NEARBY_AIRPORT,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null">
                #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="cityEName != null">
                #{cityEName,jdbcType=VARCHAR},
            </if>
            <if test="countryCode != null">
                #{countryCode,jdbcType=VARCHAR},
            </if>
            <if test="cityPinYin != null">
                #{cityPinYin,jdbcType=VARCHAR},
            </if>
            <if test="cityPinYinAbb != null">
                #{cityPinYinAbb,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                #{provinceId,jdbcType=DECIMAL},
            </if>
            <if test="provinceName != null">
                #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="isHotCity != null">
                #{isHotCity,jdbcType=VARCHAR},
            </if>
            <if test="isTopCity != null">
                #{isTopCity,jdbcType=VARCHAR},
            </if>
            <if test="officeAddress != null">
                #{officeAddress,jdbcType=VARCHAR},
            </if>
            <if test="officeTel != null">
                #{officeTel,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createId != null">
                #{createId,jdbcType=DECIMAL},
            </if>
            <if test="cityTimeZone != null">
                #{cityTimeZone,jdbcType=VARCHAR},
            </if>
            <if test="nameAbb != null">
                #{nameAbb,jdbcType=VARCHAR},
            </if>
            <if test="englishNameAbb != null">
                #{englishNameAbb,jdbcType=VARCHAR},
            </if>
            <if test="isInternational != null">
                #{isInternational,jdbcType=VARCHAR},
            </if>
            <if test="baidumappoint != null">
                #{baidumappoint,jdbcType=VARCHAR},
            </if>
            <if test="officeFax != null">
                #{officeFax,jdbcType=VARCHAR},
            </if>
            <if test="delflag != null">
                #{delflag,jdbcType=CHAR},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="cityHotOrder != null">
                #{cityHotOrder,jdbcType=DECIMAL},
            </if>
            <if test="zoneHotOrder != null">
                #{zoneHotOrder,jdbcType=DECIMAL},
            </if>
            <if test="iconUrl != null">
                #{iconUrl,jdbcType=VARCHAR},
            </if>
            <if test="dstWtId != null">
                #{dstWtId,jdbcType=VARCHAR},
            </if>
            <if test="cityKoName != null">
                #{cityKoName,jdbcType=VARCHAR},
            </if>
            <if test="cityJpName != null">
                #{cityJpName,jdbcType=VARCHAR},
            </if>
            <if test="cityThName != null">
                #{cityThName,jdbcType=VARCHAR},
            </if>
            <if test="cityTcName != null">
                #{cityTcName,jdbcType=VARCHAR},
            </if>
            <if test="cityKeyWords != null">
                #{cityKeyWords,jdbcType=VARCHAR},
            </if>
            <if test="isHotRegion != null">
                #{isHotRegion,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="longitude != null">
                #{longitude,jdbcType=VARCHAR},
            </if>
            <if test="latitude != null">
                #{latitude,jdbcType=VARCHAR},
            </if>
            <if test="isOftenCity != null">
                #{isOftenCity,jdbcType=VARCHAR},
            </if>
            <if test="nearbyAirport != null">
                #{nearbyAirport,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.CityInfoPO">
        update T_CITY_INFO
        <set>
            <if test="cityName != null">
                CITY_NAME = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="cityEName != null">
                CITY_E_NAME = #{cityEName,jdbcType=VARCHAR},
            </if>
            <if test="countryCode != null">
                COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR},
            </if>
            <if test="cityPinYin != null">
                CITY_PIN_YIN = #{cityPinYin,jdbcType=VARCHAR},
            </if>
            <if test="cityPinYinAbb != null">
                CITY_PIN_YIN_ABB = #{cityPinYinAbb,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                PROVINCE_ID = #{provinceId,jdbcType=DECIMAL},
            </if>
            <if test="provinceName != null">
                PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="isHotCity != null">
                IS_HOT_CITY = #{isHotCity,jdbcType=VARCHAR},
            </if>
            <if test="isTopCity != null">
                IS_TOP_CITY = #{isTopCity,jdbcType=VARCHAR},
            </if>
            <if test="officeAddress != null">
                OFFICE_ADDRESS = #{officeAddress,jdbcType=VARCHAR},
            </if>
            <if test="officeTel != null">
                OFFICE_TEL = #{officeTel,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                CREATE_DATETIME = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createId != null">
                CREATE_ID = #{createId,jdbcType=DECIMAL},
            </if>
            <if test="cityTimeZone != null">
                CITY_TIME_ZONE = #{cityTimeZone,jdbcType=VARCHAR},
            </if>
            <if test="nameAbb != null">
                NAME_ABB = #{nameAbb,jdbcType=VARCHAR},
            </if>
            <if test="englishNameAbb != null">
                ENGLISH_NAME_ABB = #{englishNameAbb,jdbcType=VARCHAR},
            </if>
            <if test="isInternational != null">
                IS_INTERNATIONAL = #{isInternational,jdbcType=VARCHAR},
            </if>
            <if test="baidumappoint != null">
                BAIDUMAPPOINT = #{baidumappoint,jdbcType=VARCHAR},
            </if>
            <if test="officeFax != null">
                OFFICE_FAX = #{officeFax,jdbcType=VARCHAR},
            </if>
            <if test="delflag != null">
                DELFLAG = #{delflag,jdbcType=CHAR},
            </if>
            <if test="url != null">
                URL = #{url,jdbcType=VARCHAR},
            </if>
            <if test="cityHotOrder != null">
                CITY_HOT_ORDER = #{cityHotOrder,jdbcType=DECIMAL},
            </if>
            <if test="zoneHotOrder != null">
                ZONE_HOT_ORDER = #{zoneHotOrder,jdbcType=DECIMAL},
            </if>
            <if test="iconUrl != null">
                ICON_URL = #{iconUrl,jdbcType=VARCHAR},
            </if>
            <if test="dstWtId != null">
                DST_WT_ID = #{dstWtId,jdbcType=VARCHAR},
            </if>
            <if test="cityKoName != null">
                CITY_KO_NAME = #{cityKoName,jdbcType=VARCHAR},
            </if>
            <if test="cityJpName != null">
                CITY_JP_NAME = #{cityJpName,jdbcType=VARCHAR},
            </if>
            <if test="cityThName != null">
                CITY_TH_NAME = #{cityThName,jdbcType=VARCHAR},
            </if>
            <if test="cityTcName != null">
                CITY_TC_NAME = #{cityTcName,jdbcType=VARCHAR},
            </if>
            <if test="cityKeyWords != null">
                CITY_KEY_WORDS = #{cityKeyWords,jdbcType=VARCHAR},
            </if>
            <if test="isHotRegion != null">
                IS_HOT_REGION = #{isHotRegion,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                "STATUS" = #{status,jdbcType=VARCHAR},
            </if>
            <if test="longitude != null">
                LONGITUDE = #{longitude,jdbcType=VARCHAR},
            </if>
            <if test="latitude != null">
                LATITUDE = #{latitude,jdbcType=VARCHAR},
            </if>
            <if test="isOftenCity != null">
                IS_OFTEN_CITY = #{isOftenCity,jdbcType=VARCHAR},
            </if>
            <if test="nearbyAirport != null">
                NEARBY_AIRPORT = #{nearbyAirport,jdbcType=VARCHAR},
            </if>
        </set>
        where CITY_CODE = #{cityCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.juneyaoair.manage.b2c.entity.CityInfoPO">
        update T_CITY_INFO
        set CITY_NAME        = #{cityName,jdbcType=VARCHAR},
            CITY_E_NAME      = #{cityEName,jdbcType=VARCHAR},
            COUNTRY_CODE     = #{countryCode,jdbcType=VARCHAR},
            CITY_PIN_YIN     = #{cityPinYin,jdbcType=VARCHAR},
            CITY_PIN_YIN_ABB = #{cityPinYinAbb,jdbcType=VARCHAR},
            PROVINCE_ID      = #{provinceId,jdbcType=DECIMAL},
            PROVINCE_NAME    = #{provinceName,jdbcType=VARCHAR},
            IS_HOT_CITY      = #{isHotCity,jdbcType=VARCHAR},
            IS_TOP_CITY      = #{isTopCity,jdbcType=VARCHAR},
            OFFICE_ADDRESS   = #{officeAddress,jdbcType=VARCHAR},
            OFFICE_TEL       = #{officeTel,jdbcType=VARCHAR},
            CREATE_DATETIME  = #{createDatetime,jdbcType=TIMESTAMP},
            CREATE_ID        = #{createId,jdbcType=DECIMAL},
            CITY_TIME_ZONE   = #{cityTimeZone,jdbcType=VARCHAR},
            NAME_ABB         = #{nameAbb,jdbcType=VARCHAR},
            ENGLISH_NAME_ABB = #{englishNameAbb,jdbcType=VARCHAR},
            IS_INTERNATIONAL = #{isInternational,jdbcType=VARCHAR},
            BAIDUMAPPOINT    = #{baidumappoint,jdbcType=VARCHAR},
            OFFICE_FAX       = #{officeFax,jdbcType=VARCHAR},
            DELFLAG          = #{delflag,jdbcType=CHAR},
            URL              = #{url,jdbcType=VARCHAR},
            CITY_HOT_ORDER   = #{cityHotOrder,jdbcType=DECIMAL},
            ZONE_HOT_ORDER   = #{zoneHotOrder,jdbcType=DECIMAL},
            ICON_URL         = #{iconUrl,jdbcType=VARCHAR},
            DST_WT_ID        = #{dstWtId,jdbcType=VARCHAR},
            CITY_KO_NAME     = #{cityKoName,jdbcType=VARCHAR},
            CITY_JP_NAME     = #{cityJpName,jdbcType=VARCHAR},
            CITY_TH_NAME     = #{cityThName,jdbcType=VARCHAR},
            CITY_TC_NAME     = #{cityTcName,jdbcType=VARCHAR},
            CITY_KEY_WORDS   = #{cityKeyWords,jdbcType=VARCHAR},
            IS_HOT_REGION    = #{isHotRegion,jdbcType=VARCHAR},
            "STATUS"         = #{status,jdbcType=VARCHAR},
            LONGITUDE        = #{longitude,jdbcType=VARCHAR},
            LATITUDE         = #{latitude,jdbcType=VARCHAR},
            IS_OFTEN_CITY    = #{isOftenCity,jdbcType=VARCHAR},
            CITY_PICTURE_PC_URL = #{cityPicturePcUrl,jdbcType=VARCHAR},
            NEARBY_AIRPORT = #{nearbyAirport,jdbcType=VARCHAR}
        where CITY_CODE = #{cityCode,jdbcType=VARCHAR}
    </update>

    <update id="updateStatusByCityCode">
        update T_CITY_INFO
        set STATUS = #{status,jdbcType=VARCHAR}
        where CITY_CODE = #{cityCode,jdbcType=VARCHAR}
    </update>
    <delete id="deleteByCityCode">
        delete
        from T_CITY_INFO
        where CITY_CODE = #{cityCode,jdbcType=VARCHAR}
    </delete>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CITY_INFO
    </select>
    <select id="selectByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CITY_INFO
        <where>
            <if test="cityCode != null">
                and CITY_CODE = #{cityCode,jdbcType=VARCHAR}
            </if>
            <if test="cityName != null">
                and CITY_NAME = #{cityName,jdbcType=VARCHAR}
            </if>
            <if test="cityEName != null">
                and CITY_E_NAME = #{cityEName,jdbcType=VARCHAR}
            </if>
            <if test="countryCode != null">
                and COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
            </if>
            <if test="cityPinYin != null">
                and CITY_PIN_YIN = #{cityPinYin,jdbcType=VARCHAR}
            </if>
            <if test="cityPinYinAbb != null">
                and CITY_PIN_YIN_ABB = #{cityPinYinAbb,jdbcType=VARCHAR}
            </if>
            <if test="provinceId != null">
                and PROVINCE_ID = #{provinceId,jdbcType=DECIMAL}
            </if>
            <if test="provinceName != null">
                and PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR}
            </if>
            <if test="isHotCity != null">
                and IS_HOT_CITY = #{isHotCity,jdbcType=VARCHAR}
            </if>
            <if test="isTopCity != null">
                and IS_TOP_CITY = #{isTopCity,jdbcType=VARCHAR}
            </if>
            <if test="officeAddress != null">
                and OFFICE_ADDRESS = #{officeAddress,jdbcType=VARCHAR}
            </if>
            <if test="officeTel != null">
                and OFFICE_TEL = #{officeTel,jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                and CREATE_DATETIME = #{createDatetime,jdbcType=TIMESTAMP}
            </if>
            <if test="createId != null">
                and CREATE_ID = #{createId,jdbcType=DECIMAL}
            </if>
            <if test="cityTimeZone != null">
                and CITY_TIME_ZONE = #{cityTimeZone,jdbcType=VARCHAR}
            </if>
            <if test="nameAbb != null">
                and NAME_ABB = #{nameAbb,jdbcType=VARCHAR}
            </if>
            <if test="englishNameAbb != null">
                and ENGLISH_NAME_ABB = #{englishNameAbb,jdbcType=VARCHAR}
            </if>
            <if test="isInternational != null">
                and IS_INTERNATIONAL = #{isInternational,jdbcType=VARCHAR}
            </if>
            <if test="baidumappoint != null">
                and BAIDUMAPPOINT = #{baidumappoint,jdbcType=VARCHAR}
            </if>
            <if test="officeFax != null">
                and OFFICE_FAX = #{officeFax,jdbcType=VARCHAR}
            </if>
            <if test="delflag != null">
                and DELFLAG = #{delflag,jdbcType=CHAR}
            </if>
            <if test="url != null">
                and URL = #{url,jdbcType=VARCHAR}
            </if>
            <if test="cityHotOrder != null">
                and CITY_HOT_ORDER = #{cityHotOrder,jdbcType=DECIMAL}
            </if>
            <if test="zoneHotOrder != null">
                and ZONE_HOT_ORDER = #{zoneHotOrder,jdbcType=DECIMAL}
            </if>
            <if test="iconUrl != null">
                and ICON_URL = #{iconUrl,jdbcType=VARCHAR}
            </if>
            <if test="dstWtId != null">
                and DST_WT_ID = #{dstWtId,jdbcType=VARCHAR}
            </if>
            <if test="cityKoName != null">
                and CITY_KO_NAME = #{cityKoName,jdbcType=VARCHAR}
            </if>
            <if test="cityJpName != null">
                and CITY_JP_NAME = #{cityJpName,jdbcType=VARCHAR}
            </if>
            <if test="cityThName != null">
                and CITY_TH_NAME = #{cityThName,jdbcType=VARCHAR}
            </if>
            <if test="cityTcName != null">
                and CITY_TC_NAME = #{cityTcName,jdbcType=VARCHAR}
            </if>
            <if test="cityKeyWords != null">
                and CITY_KEY_WORDS = #{cityKeyWords,jdbcType=VARCHAR}
            </if>
            <if test="isHotRegion != null">
                and IS_HOT_REGION = #{isHotRegion,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and `STATUS` = #{status,jdbcType=VARCHAR}
            </if>
            <if test="longitude != null">
                and LONGITUDE = #{longitude,jdbcType=VARCHAR}
            </if>
            <if test="latitude != null">
                and LATITUDE = #{latitude,jdbcType=VARCHAR}
            </if>
            <if test="isOftenCity != null">
                and IS_OFTEN_CITY = #{isOftenCity,jdbcType=VARCHAR}
            </if>
            <if test="nearbyAirport != null">
                and NEARBY_AIRPORT = #{nearbyAirport,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectAllByCityCodeAndCityName" parameterType="com.juneyaoair.manage.b2c.entity.CityInfoPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CITY_INFO
        <where>
            <if test="cityCode != null and cityCode != ''">
                CITY_CODE = #{cityCode,jdbcType=VARCHAR}
            </if>
            <if test="cityName != null and cityName != ''">
                AND CITY_NAME = #{cityName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>