<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TAirportWarnInfoMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.AirportWarnInfoPO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="AIRPORT_CODE" jdbcType="VARCHAR" property="airportCode"/>
        <result column="DEP_ARR_FLAT" jdbcType="VARCHAR" property="depArrFlat"/>
        <result column="START_DATE" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="END_DATE" jdbcType="TIMESTAMP" property="endDate"/>
        <result column="WARN_TITLE" jdbcType="VARCHAR" property="warnTitle"/>
        <result column="WARN_CONTENT" jdbcType="CLOB" property="warnContent"/>
        <result column="DELETE_FLAT" jdbcType="VARCHAR" property="deleteFlat"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="SORT_ORDER" jdbcType="DECIMAL" property="sortOrder"/>
    </resultMap>
    <sql id="Base_Column_List">

        ID, AIRPORT_CODE, DEP_ARR_FLAT, START_DATE, END_DATE, WARN_TITLE, WARN_CONTENT, DELETE_FLAT,
        CREATED_BY, CREATED_TIME, UPDATED_BY, UPDATED_TIME, SORT_ORDER
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">

        select
        <include refid="Base_Column_List"/>
        from T_AIRPORT_WARN_INFO
        where ID = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">

        delete
        from T_AIRPORT_WARN_INFO
        where ID = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.juneyaoair.manage.b2c.entity.AirportWarnInfoPO">

        insert into T_AIRPORT_WARN_INFO (ID, AIRPORT_CODE, DEP_ARR_FLAT,
                                         START_DATE, END_DATE, WARN_TITLE,
                                         WARN_CONTENT, DELETE_FLAT, CREATED_BY,
                                         CREATED_TIME, UPDATED_BY, UPDATED_TIME,
                                         SORT_ORDER)
        values (#{id,jdbcType=VARCHAR}, #{airportCode,jdbcType=VARCHAR}, #{depArrFlat,jdbcType=VARCHAR},
                #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, #{warnTitle,jdbcType=VARCHAR},
                #{warnContent,jdbcType=CLOB}, #{deleteFlat,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
                #{createdTime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedTime,jdbcType=TIMESTAMP},
                #{sortOrder,jdbcType=DECIMAL})
    </insert>
    <insert id="insertSelective" parameterType="com.juneyaoair.manage.b2c.entity.AirportWarnInfoPO">

        insert into T_AIRPORT_WARN_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="airportCode != null">
                AIRPORT_CODE,
            </if>
            <if test="depArrFlat != null">
                DEP_ARR_FLAT,
            </if>
            <if test="startDate != null">
                START_DATE,
            </if>
            <if test="endDate != null">
                END_DATE,
            </if>
            <if test="warnTitle != null">
                WARN_TITLE,
            </if>
            <if test="warnContent != null">
                WARN_CONTENT,
            </if>
            <if test="deleteFlat != null">
                DELETE_FLAT,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdTime != null">
                CREATED_TIME,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedTime != null">
                UPDATED_TIME,
            </if>
            <if test="sortOrder != null">
                SORT_ORDER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="airportCode != null">
                #{airportCode,jdbcType=VARCHAR},
            </if>
            <if test="depArrFlat != null">
                #{depArrFlat,jdbcType=VARCHAR},
            </if>
            <if test="startDate != null">
                #{startDate,jdbcType=TIMESTAMP},
            </if>
            <if test="endDate != null">
                #{endDate,jdbcType=TIMESTAMP},
            </if>
            <if test="warnTitle != null">
                #{warnTitle,jdbcType=VARCHAR},
            </if>
            <if test="warnContent != null">
                #{warnContent,jdbcType=CLOB},
            </if>
            <if test="deleteFlat != null">
                #{deleteFlat,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sortOrder != null">
                #{sortOrder,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.AirportWarnInfoPO">

        update T_AIRPORT_WARN_INFO
        <set>
            <if test="airportCode != null">
                AIRPORT_CODE = #{airportCode,jdbcType=VARCHAR},
            </if>
            <if test="depArrFlat != null">
                DEP_ARR_FLAT = #{depArrFlat,jdbcType=VARCHAR},
            </if>
            <if test="startDate != null">
                START_DATE = #{startDate,jdbcType=TIMESTAMP},
            </if>
            <if test="endDate != null">
                END_DATE = #{endDate,jdbcType=TIMESTAMP},
            </if>
            <if test="warnTitle != null">
                WARN_TITLE = #{warnTitle,jdbcType=VARCHAR},
            </if>
            <if test="warnContent != null">
                WARN_CONTENT = #{warnContent,jdbcType=CLOB},
            </if>
            <if test="deleteFlat != null">
                DELETE_FLAT = #{deleteFlat,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                CREATED_TIME = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                UPDATED_TIME = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sortOrder != null">
                SORT_ORDER = #{sortOrder,jdbcType=DECIMAL},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.juneyaoair.manage.b2c.entity.AirportWarnInfoPO">

        update T_AIRPORT_WARN_INFO
        set AIRPORT_CODE = #{airportCode,jdbcType=VARCHAR},
            DEP_ARR_FLAT = #{depArrFlat,jdbcType=VARCHAR},
            START_DATE   = #{startDate,jdbcType=TIMESTAMP},
            END_DATE     = #{endDate,jdbcType=TIMESTAMP},
            WARN_TITLE   = #{warnTitle,jdbcType=VARCHAR},
            WARN_CONTENT = #{warnContent,jdbcType=CLOB},
            DELETE_FLAT  = #{deleteFlat,jdbcType=VARCHAR},
            CREATED_BY   = #{createdBy,jdbcType=VARCHAR},
            CREATED_TIME = #{createdTime,jdbcType=TIMESTAMP},
            UPDATED_BY   = #{updatedBy,jdbcType=VARCHAR},
            UPDATED_TIME = #{updatedTime,jdbcType=TIMESTAMP},
            SORT_ORDER   = #{sortOrder,jdbcType=DECIMAL}
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <select id="selectByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AIRPORT_WARN_INFO
        <where>
            <if test="id != null">
                and ID = #{id,jdbcType=VARCHAR}
            </if>
            <if test="airportCode != null">
                and AIRPORT_CODE = #{airportCode,jdbcType=VARCHAR}
            </if>
            <if test="depArrFlat != null">
                and DEP_ARR_FLAT = #{depArrFlat,jdbcType=VARCHAR}
            </if>
            <if test="startDate != null">
                and START_DATE = #{startDate,jdbcType=TIMESTAMP}
            </if>
            <if test="endDate != null">
                and END_DATE = #{endDate,jdbcType=TIMESTAMP}
            </if>
            <if test="warnTitle != null">
                and WARN_TITLE = #{warnTitle,jdbcType=VARCHAR}
            </if>
            <if test="warnContent != null">
                and WARN_CONTENT = #{warnContent,jdbcType=CLOB}
            </if>
            <if test="deleteFlat != null">
                and DELETE_FLAT = #{deleteFlat,jdbcType=VARCHAR}
            </if>
            <if test="createdBy != null">
                and CREATED_BY = #{createdBy,jdbcType=VARCHAR}
            </if>
            <if test="createdTime != null">
                and CREATED_TIME = #{createdTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updatedBy != null">
                and UPDATED_BY = #{updatedBy,jdbcType=VARCHAR}
            </if>
            <if test="updatedTime != null">
                and UPDATED_TIME = #{updatedTime,jdbcType=TIMESTAMP}
            </if>
            <if test="sortOrder != null">
                and SORT_ORDER = #{sortOrder,jdbcType=DECIMAL}
            </if>
        </where>
    </select>
</mapper>