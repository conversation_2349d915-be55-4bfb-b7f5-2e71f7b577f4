<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.CommonLotteryPrizeMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.CommonLotteryPrizePO">
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="prizePoolCode" column="PRIZE_POOL_CODE" jdbcType="VARCHAR"/>
            <result property="prizeCode" column="PRIZE_CODE" jdbcType="VARCHAR"/>
            <result property="prizeName" column="PRIZE_NAME" jdbcType="VARCHAR"/>
            <result property="prizeAmount" column="PRIZE_AMOUNT" jdbcType="DECIMAL"/>
            <result property="totalAmount" column="TOTAL_AMOUNT" jdbcType="DECIMAL"/>
            <result property="iconUrl" column="ICON_URL" jdbcType="VARCHAR"/>
            <result property="sort" column="SORT" jdbcType="VARCHAR"/>
            <result property="deleted" column="DELETED" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="CREATE_USER" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="UPDATE_USER" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PRIZE_POOL_CODE,PRIZE_CODE,
        PRIZE_NAME,PRIZE_AMOUNT,ICON_URL,
        SORT,DELETED,CREATE_TIME,
        CREATE_USER,UPDATE_TIME,UPDATE_USER,
        WINNING_RATE,TOTAL_AMOUNT
    </sql>
    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.activity.CommonLotteryPrizePO">
        UPDATE T_PRIZE_ENTITY
        SET DELETED = #{deleted,jdbcType=VARCHAR},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        UPDATE_USER = #{updateUser,jdbcType=VARCHAR}
        <where>
            <if test="prizePoolCode != null and prizePoolCode != ''">
                PRIZE_POOL_CODE = #{prizePoolCode,jdbcType=VARCHAR}
            </if>
            <if test="prizeCode != null and prizeCode != ''">
               AND PRIZE_CODE = #{prizeCode,jdbcType=VARCHAR}
            </if>
        </where>
    </update>
    <select id="toGainAllPrizeRecords" parameterType="com.juneyaoair.manage.b2c.entity.activity.CommonLotteryPrizePO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_PRIZE_ENTITY
        <where>
            <if test="prizePoolCode != null and prizePoolCode != ''">
                PRIZE_POOL_CODE = #{prizePoolCode,jdbcType=VARCHAR}
            </if>
            <if test="deleted != null and deleted != ''">
                AND DELETED = #{deleted,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <update id="toUpdatePrizeRecord" parameterType="com.juneyaoair.manage.b2c.entity.activity.CommonLotteryPrizePO">
         update T_PRIZE_ENTITY
            set PRIZE_POOL_CODE = #{prizePoolCode},
                PRIZE_CODE = #{prizeCode},
                PRIZE_NAME = #{prizeName},
                PRIZE_AMOUNT = #{prizeAmount},
                WINNING_RATE = #{winningRate},
                ICON_URL = #{iconUrl},
                SORT = #{sort},
                UPDATE_TIME = #{updateTime},
                UPDATE_USER = #{updateUser},
                TOTAL_AMOUNT = TOTAL_AMOUNT + #{prizeAmount} - PRIZE_AMOUNT
          where ID = #{id}
    </update>
</mapper>
