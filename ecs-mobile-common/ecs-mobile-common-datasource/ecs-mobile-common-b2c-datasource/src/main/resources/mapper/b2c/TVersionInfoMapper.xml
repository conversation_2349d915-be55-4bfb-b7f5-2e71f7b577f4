<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TVersionInfoMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.VersionInfoPO">
        <!--@mbg.generated-->
        <!--@Table T_VERSION_INFO-->
        <id column="VERSION_ID" jdbcType="DECIMAL" property="versionId"/>
        <result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode"/>
        <result column="VERSION_TYPE" jdbcType="VARCHAR" property="versionType"/>
        <result column="VERSION_NO" jdbcType="VARCHAR" property="versionNo"/>
        <result column="UPDATE_DETAILS" jdbcType="VARCHAR" property="updateDetails"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        VERSION_ID, CHANNEL_CODE, VERSION_TYPE, VERSION_NO, UPDATE_DETAILS
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_VERSION_INFO
        where VERSION_ID = #{versionId,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from T_VERSION_INFO
        where VERSION_ID = #{versionId,jdbcType=DECIMAL}
    </delete>
    <insert id="insert" parameterType="com.juneyaoair.manage.b2c.entity.VersionInfoPO">
        <selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="versionID">
            SELECT seq_win_type.nextval as versionID from DUAL
        </selectKey>
        insert into T_VERSION_INFO (VERSION_ID, CHANNEL_CODE, VERSION_TYPE,
                                    VERSION_NO, UPDATE_DETAILS)
        values (#{versionId,jdbcType=DECIMAL}, #{channelCode,jdbcType=VARCHAR}, #{versionType,jdbcType=VARCHAR},
                #{versionNo,jdbcType=VARCHAR}, #{updateDetails,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.juneyaoair.manage.b2c.entity.VersionInfoPO">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="versionId">
            SELECT seq_win_type.nextval as versionId from DUAL
        </selectKey>
        insert into T_VERSION_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            VERSION_ID,
            <if test="channelCode != null">
                CHANNEL_CODE,
            </if>
            <if test="versionType != null">
                VERSION_TYPE,
            </if>
            <if test="versionNo != null">
                VERSION_NO,
            </if>
            <if test="updateDetails != null">
                UPDATE_DETAILS,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            seq_win_type.currval,
            <if test="channelCode != null">
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="versionType != null">
                #{versionType,jdbcType=VARCHAR},
            </if>
            <if test="versionNo != null">
                #{versionNo,jdbcType=VARCHAR},
            </if>
            <if test="updateDetails != null">
                #{updateDetails,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.VersionInfoPO">
        <!--@mbg.generated-->
        update T_VERSION_INFO
        <set>
            <if test="channelCode != null">
                CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="versionType != null">
                VERSION_TYPE = #{versionType,jdbcType=VARCHAR},
            </if>
            <if test="versionNo != null">
                VERSION_NO = #{versionNo,jdbcType=VARCHAR},
            </if>
            <if test="updateDetails != null">
                UPDATE_DETAILS = #{updateDetails,jdbcType=VARCHAR},
            </if>
        </set>
        where VERSION_ID = #{versionId,jdbcType=DECIMAL}
    </update>
    <update id="updateVersionNoByVersionType">
        update T_VERSION_INFO
        set VERSION_NO=#{updatedVersionNo,jdbcType=VARCHAR}
        where VERSION_TYPE = #{versionType,jdbcType=VARCHAR}
    </update>
</mapper>