<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TpFileInfoMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.FileInfoPO">
        <!--@Table TP_FILE_INFO-->
        <id column="FILEID" jdbcType="VARCHAR" property="fileid"/>
        <result column="LINKID" jdbcType="VARCHAR" property="linkid"/>
        <result column="FILE_NAME" jdbcType="VARCHAR" property="fileName"/>
        <result column="FILE_URL" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="FILE_SIZE" jdbcType="VARCHAR" property="fileSize"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CREATOR_ID" jdbcType="VARCHAR" property="creatorId"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="REALNAME" jdbcType="VARCHAR" property="realname"/>
        <result column="IMGSERVICEURL" jdbcType="VARCHAR" property="imgserviceurl"/>
        <result column="HOSTNAME" jdbcType="VARCHAR" property="hostname"/>
        <result column="FILE_PATH" jdbcType="VARCHAR" property="filePath"/>
        <result column="FILE_TYPE" jdbcType="VARCHAR" property="fileType"/>
    </resultMap>
    <resultMap id="BaseResultMapV2" type="com.juneyaoair.manage.b2c.entity.FileInfoPOO">
        <!--@Table TP_FILE_INFO-->
        <id column="FILEID" jdbcType="VARCHAR" property="fileId"/>
        <result column="LINKID" jdbcType="VARCHAR" property="linkId"/>
        <result column="FILE_NAME" jdbcType="VARCHAR" property="fileName"/>
        <result column="FILE_URL" jdbcType="VARCHAR" property="fileURL"/>
        <result column="FILE_SIZE" jdbcType="VARCHAR" property="fileSize"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CREATOR_ID" jdbcType="VARCHAR" property="creatorId"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="REALNAME" jdbcType="VARCHAR" property="realName"/>
        <result column="IMGSERVICEURL" jdbcType="VARCHAR" property="imgServiceUrl"/>
        <result column="HOSTNAME" jdbcType="VARCHAR" property="hostName"/>
        <result column="FILE_PATH" jdbcType="VARCHAR" property="filePath"/>
        <result column="FILE_TYPE" jdbcType="VARCHAR" property="fileType"/>
    </resultMap>
    <sql id="Base_Column_List">
        FILEID,
        LINKID,
        FILE_NAME,
        FILE_URL,
        FILE_SIZE,
        CREATE_TIME,
        CREATOR_ID,
        MODIFY_TIME,
        REALNAME,
        IMGSERVICEURL,
        HOSTNAME,
        FILE_PATH,
        FILE_TYPE
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TP_FILE_INFO
        where FILEID = #{fileid,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from TP_FILE_INFO
        where FILEID = #{fileid,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.juneyaoair.manage.b2c.entity.FileInfoPO">
        insert into TP_FILE_INFO (FILEID, LINKID, FILE_NAME,
                                  FILE_URL, FILE_SIZE, CREATE_TIME,
                                  CREATOR_ID, MODIFY_TIME, REALNAME,
                                  IMGSERVICEURL, HOSTNAME, FILE_PATH,
                                  FILE_TYPE)
        values (#{fileid,jdbcType=VARCHAR}, #{linkid,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR},
                #{fileUrl,jdbcType=VARCHAR}, #{fileSize,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{creatorId,jdbcType=VARCHAR}, #{modifyTime,jdbcType=TIMESTAMP}, #{realname,jdbcType=VARCHAR},
                #{imgserviceurl,jdbcType=VARCHAR}, #{hostname,jdbcType=VARCHAR}, #{filePath,jdbcType=VARCHAR},
                #{fileType,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.juneyaoair.manage.b2c.entity.FileInfoPO">
        insert into TP_FILE_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileid != null">
                FILEID,
            </if>
            <if test="linkid != null">
                LINKID,
            </if>
            <if test="fileName != null">
                FILE_NAME,
            </if>
            <if test="fileUrl != null">
                FILE_URL,
            </if>
            <if test="fileSize != null">
                FILE_SIZE,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="creatorId != null">
                CREATOR_ID,
            </if>
            <if test="modifyTime != null">
                MODIFY_TIME,
            </if>
            <if test="realname != null">
                REALNAME,
            </if>
            <if test="imgserviceurl != null">
                IMGSERVICEURL,
            </if>
            <if test="hostname != null">
                HOSTNAME,
            </if>
            <if test="filePath != null">
                FILE_PATH,
            </if>
            <if test="fileType != null">
                FILE_TYPE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileid != null">
                #{fileid,jdbcType=VARCHAR},
            </if>
            <if test="linkid != null">
                #{linkid,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="fileUrl != null">
                #{fileUrl,jdbcType=VARCHAR},
            </if>
            <if test="fileSize != null">
                #{fileSize,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creatorId != null">
                #{creatorId,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="realname != null">
                #{realname,jdbcType=VARCHAR},
            </if>
            <if test="imgserviceurl != null">
                #{imgserviceurl,jdbcType=VARCHAR},
            </if>
            <if test="hostname != null">
                #{hostname,jdbcType=VARCHAR},
            </if>
            <if test="filePath != null">
                #{filePath,jdbcType=VARCHAR},
            </if>
            <if test="fileType != null">
                #{fileType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.FileInfoPO">
        update TP_FILE_INFO
        <set>
            <if test="linkid != null">
                LINKID = #{linkid,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                FILE_NAME = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="fileUrl != null">
                FILE_URL = #{fileUrl,jdbcType=VARCHAR},
            </if>
            <if test="fileSize != null">
                FILE_SIZE = #{fileSize,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creatorId != null">
                CREATOR_ID = #{creatorId,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="realname != null">
                REALNAME = #{realname,jdbcType=VARCHAR},
            </if>
            <if test="imgserviceurl != null">
                IMGSERVICEURL = #{imgserviceurl,jdbcType=VARCHAR},
            </if>
            <if test="hostname != null">
                HOSTNAME = #{hostname,jdbcType=VARCHAR},
            </if>
            <if test="filePath != null">
                FILE_PATH = #{filePath,jdbcType=VARCHAR},
            </if>
            <if test="fileType != null">
                FILE_TYPE = #{fileType,jdbcType=VARCHAR},
            </if>
        </set>
        where FILEID = #{fileid,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.juneyaoair.manage.b2c.entity.FileInfoPO">
        update TP_FILE_INFO
        set LINKID        = #{linkid,jdbcType=VARCHAR},
            FILE_NAME     = #{fileName,jdbcType=VARCHAR},
            FILE_URL      = #{fileUrl,jdbcType=VARCHAR},
            FILE_SIZE     = #{fileSize,jdbcType=VARCHAR},
            CREATE_TIME   = #{createTime,jdbcType=TIMESTAMP},
            CREATOR_ID    = #{creatorId,jdbcType=VARCHAR},
            MODIFY_TIME   = #{modifyTime,jdbcType=TIMESTAMP},
            REALNAME      = #{realname,jdbcType=VARCHAR},
            IMGSERVICEURL = #{imgserviceurl,jdbcType=VARCHAR},
            HOSTNAME      = #{hostname,jdbcType=VARCHAR},
            FILE_PATH     = #{filePath,jdbcType=VARCHAR},
            FILE_TYPE     = #{fileType,jdbcType=VARCHAR}
        where FILEID = #{fileid,jdbcType=VARCHAR}
    </update>
    <select id="selectAllByLinkid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TP_FILE_INFO
        where LINKID = #{linkid,jdbcType=VARCHAR}
    </select>
    <delete id="delByLinkid">
        delete
        from TP_FILE_INFO
        where LINKID = #{linkid,jdbcType=VARCHAR}
    </delete>
    <select id="selectByLinkidIn" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from TP_FILE_INFO
        where LINKID in
        <foreach item="item" index="index" collection="linkidCollection"
                    open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectALL" resultMap="BaseResultMapV2">
        select
        <include refid="Base_Column_List"/>
        from TP_FILE_INFO
    </select>
</mapper>