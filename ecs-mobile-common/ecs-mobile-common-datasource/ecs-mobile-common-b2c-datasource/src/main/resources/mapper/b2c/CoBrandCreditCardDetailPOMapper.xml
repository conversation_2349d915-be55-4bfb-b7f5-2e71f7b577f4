<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.CoBrandCreditCardDetailPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.CoBrandCreditCardDetailPO">
    <!--@mbg.generated-->
    <!--@Table CO_BRAND_CREDIT_CARD_DETAIL-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="FRONT_END_TYPE" jdbcType="VARCHAR" property="frontEndType" />
    <result column="SUPPORT_JUMP" jdbcType="VARCHAR" property="supportJump" />
    <result column="JUMP_URL" jdbcType="VARCHAR" property="jumpUrl" />
    <result column="JUMP_TEXT" jdbcType="VARCHAR" property="jumpText" />
    <result column="DETAIL_TITLE" jdbcType="VARCHAR" property="detailTitle" />
    <result column="SORT_NUM" jdbcType="DECIMAL" property="sortNum" />
    <result column="THEME_ID" jdbcType="VARCHAR" property="themeId" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, FRONT_END_TYPE, SUPPORT_JUMP, JUMP_URL, JUMP_TEXT, DETAIL_TITLE, SORT_NUM, THEME_ID, 
    CREATED_BY, CREATED_TIME, UPDATED_BY, UPDATED_TIME
  </sql>
</mapper>