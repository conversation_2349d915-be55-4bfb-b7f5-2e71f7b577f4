<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.MemberLevelManageMapper">

    <sql id="Base_Column_List">
        RULE_ID,
        RULE_NAME,
        RULE_NAME_REMARK,
        WATER_MARK,
        SERIAL_NUMBER,
        TYPE,
        IMG_URL
    </sql>

    <insert id="insertSelective">
        INSERT INTO PP_MEMBER_LEVEL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">RULE_ID,</if>
            <if test="ruleName != null">RULE_NAME,</if>
            <if test="ruleNameRemark != null">RULE_NAME_REMARK,</if>
            <if test="waterMark != null">WATER_MARK,</if>
            <if test="serialNumber != null">SERIAL_NUMBER,</if>
            <if test="type != null">TYPE,</if>
            <if test="imgUrl != null">IMG_URL,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">#{ruleId, jdbcType=VARCHAR},</if>
            <if test="ruleName != null">#{ruleName, jdbcType=VARCHAR},</if>
            <if test="ruleNameRemark != null">#{ruleNameRemark, jdbcType=VARCHAR},</if>
            <if test="waterMark != null">#{waterMark, jdbcType=VARCHAR},</if>
            <if test="serialNumber != null">#{serialNumber, jdbcType=INTEGER},</if>
            <if test="type != null">#{type, jdbcType=VARCHAR},</if>
            <if test="imgUrl != null">#{imgUrl, jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective">
        UPDATE PP_MEMBER_LEVEL
        <set>
            <if test="ruleName != null">
                RULE_NAME = #{ruleName, jdbcType=VARCHAR},
            </if>
            <if test="ruleNameRemark != null">
                RULE_NAME_REMARK = #{ruleNameRemark, jdbcType=VARCHAR},
            </if>
            <if test="waterMark != null">
                WATER_MARK = #{waterMark, jdbcType=VARCHAR},
            </if>
            <if test="serialNumber != null">
                SERIAL_NUMBER = #{serialNumber, jdbcType=INTEGER},
            </if>
            <if test="type != null">
                TYPE = #{type, jdbcType=VARCHAR},
            </if>
            <if test="imgUrl != null">
                IMG_URL = #{imgUrl, jdbcType=VARCHAR},
            </if>
        </set>
        WHERE RULE_ID = #{ruleId, jdbcType=VARCHAR}
    </update>

    <select id="selectAll" resultType="com.juneyaoair.manage.b2c.entity.MemberLevelManagePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM PP_MEMBER_LEVEL
    </select>

    <select id="selectByPrimaryKey" resultType="com.juneyaoair.manage.b2c.entity.MemberLevelManagePO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM PP_MEMBER_LEVEL
        WHERE RULE_ID = #{ruleId}
    </select>
</mapper>