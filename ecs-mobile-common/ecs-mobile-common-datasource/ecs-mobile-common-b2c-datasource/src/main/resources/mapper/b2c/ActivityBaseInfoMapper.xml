<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.ActivityBaseInfoMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.ActivityBaseInfoPO">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="area" column="AREA" jdbcType="VARCHAR"/>
            <result property="jobStartDate" column="JOB_START_DATE" jdbcType="VARCHAR"/>
            <result property="jobEndDate" column="JOB_END_DATE" jdbcType="VARCHAR"/>
            <result property="createMan" column="CREATE_MAN" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="VARCHAR"/>
            <result property="updateMan" column="UPDATE_MAN" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="VARCHAR"/>
            <result property="activityParam" column="ACTIVITY_PARAM" jdbcType="VARCHAR"/>
            <result property="shareUrl" column="SHARE_URL" jdbcType="VARCHAR"/>
            <result property="shareIconUrl" column="SHARE_ICON_URL" jdbcType="VARCHAR"/>
            <result property="shareTitle" column="SHARE_TITLE" jdbcType="VARCHAR"/>
            <result property="shareDesc" column="SHARE_DESC" jdbcType="VARCHAR"/>
            <result property="pcHeadPicture" column="PC_HEAD_PICTURE" jdbcType="VARCHAR"/>
            <result property="appHeadPicture" column="APP_HEAD_PICTURE" jdbcType="VARCHAR"/>
            <result property="btnColorCode" column="BTN_COLOR_CODE" jdbcType="VARCHAR"/>
            <result property="bgColorCode" column="BG_COLOR_CODE" jdbcType="VARCHAR"/>
            <result property="seckillStartTime" column="SECKILL_START_TIME" jdbcType="VARCHAR"/>
            <result property="seckillEndTime" column="SECKILL_END_TIME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,AREA,JOB_START_DATE,
        JOB_END_DATE,CREATE_MAN,CREATE_TIME,
        UPDATE_MAN,UPDATE_TIME,ACTIVITY_PARAM,
        SHARE_URL,SHARE_ICON_URL,SHARE_TITLE,
        SHARE_DESC,PC_HEAD_PICTURE,APP_HEAD_PICTURE,
        BTN_COLOR_CODE,BG_COLOR_CODE,SECKILL_START_TIME,
        SECKILL_END_TIME,ACTIVITY_RULES
    </sql>
    <select id="searchAllRecords" parameterType="com.juneyaoair.manage.b2c.entity.activity.ActivityBaseInfoPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_ACTIVITY_BASEINFO
        <where>
            <if test="id != null and id != ''">
                ID != #{id,jdbcType=VARCHAR}
            </if>
            <if test="nowTime != null and nowTime != ''">
                AND JOB_END_DATE >= #{nowTime,jdbcType=VARCHAR}
                AND JOB_START_DATE &lt;= #{nowTime,jdbcType=VARCHAR}
            </if>
            <if test="activityParam != null and activityParam != ''">
                AND ACTIVITY_PARAM = #{activityParam,jdbcType=VARCHAR}
            </if>
        </where>
        order by update_time desc
    </select>
    <select id="selectAllById" parameterType="com.juneyaoair.manage.b2c.entity.activity.ActivityBaseInfoPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_ACTIVITY_BASEINFO
        <where>
            <if test="id != null and id != ''">
                ID = #{id,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>
