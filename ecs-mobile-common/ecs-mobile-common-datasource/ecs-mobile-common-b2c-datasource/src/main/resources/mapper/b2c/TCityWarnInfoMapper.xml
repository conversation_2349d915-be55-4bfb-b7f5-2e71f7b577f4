<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TCityWarnInfoMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.CityWarnInfoPO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode"/>
        <result column="DEP_START_DATE" jdbcType="VARCHAR" property="depStartDate"/>
        <result column="DEP_END_DATE" jdbcType="VARCHAR" property="depEndDate"/>
        <result column="DEP_WARN_TITLE" jdbcType="VARCHAR" property="depWarnTitle"/>
        <result column="DEP_WARN_CONTENT" jdbcType="VARCHAR" property="depWarnContent"/>
        <result column="ARR_START_DATE" jdbcType="VARCHAR" property="arrStartDate"/>
        <result column="ARR_END_DATE" jdbcType="VARCHAR" property="arrEndDate"/>
        <result column="ARR_WARN_TITLE" jdbcType="VARCHAR" property="arrWarnTitle"/>
        <result column="ARR_WARN_CONTENT" jdbcType="VARCHAR" property="arrWarnContent"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="DEP_ROUTE_TYPE" jdbcType="VARCHAR" property="depRouteType"/>
        <result column="ARR_ROUTE_TYPE" jdbcType="VARCHAR" property="arrRouteType"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID, CITY_CODE, DEP_START_DATE, DEP_END_DATE, DEP_WARN_TITLE, DEP_WARN_CONTENT, ARR_START_DATE,
        ARR_END_DATE, ARR_WARN_TITLE, ARR_WARN_CONTENT, CREATE_TIME, CREATE_USER, UPDATE_USER,
        UPDATE_TIME, DEP_ROUTE_TYPE, ARR_ROUTE_TYPE
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">

        select
        <include refid="Base_Column_List"/>
        from T_CITY_WARN_INFO
        where ID = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from T_CITY_WARN_INFO
        where ID = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.juneyaoair.manage.b2c.entity.CityWarnInfoPO">

        insert into T_CITY_WARN_INFO (ID, CITY_CODE, DEP_START_DATE,
                                      DEP_END_DATE, DEP_WARN_TITLE, DEP_WARN_CONTENT,
                                      ARR_START_DATE, ARR_END_DATE, ARR_WARN_TITLE,
                                      ARR_WARN_CONTENT, CREATE_TIME, CREATE_USER,
                                      UPDATE_USER, UPDATE_TIME, DEP_ROUTE_TYPE,
                                      ARR_ROUTE_TYPE)
        values (#{id,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{depStartDate,jdbcType=VARCHAR},
                #{depEndDate,jdbcType=VARCHAR}, #{depWarnTitle,jdbcType=VARCHAR}, #{depWarnContent,jdbcType=VARCHAR},
                #{arrStartDate,jdbcType=VARCHAR}, #{arrEndDate,jdbcType=VARCHAR}, #{arrWarnTitle,jdbcType=VARCHAR},
                #{arrWarnContent,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR},
                #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{depRouteType,jdbcType=VARCHAR},
                #{arrRouteType,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.juneyaoair.manage.b2c.entity.CityWarnInfoPO">
        insert into T_CITY_WARN_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="cityCode != null">
                CITY_CODE,
            </if>
            <if test="depStartDate != null">
                DEP_START_DATE,
            </if>
            <if test="depEndDate != null">
                DEP_END_DATE,
            </if>
            <if test="depWarnTitle != null">
                DEP_WARN_TITLE,
            </if>
            <if test="depWarnContent != null">
                DEP_WARN_CONTENT,
            </if>
            <if test="arrStartDate != null">
                ARR_START_DATE,
            </if>
            <if test="arrEndDate != null">
                ARR_END_DATE,
            </if>
            <if test="arrWarnTitle != null">
                ARR_WARN_TITLE,
            </if>
            <if test="arrWarnContent != null">
                ARR_WARN_CONTENT,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="createUser != null">
                CREATE_USER,
            </if>
            <if test="updateUser != null">
                UPDATE_USER,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="depRouteType != null">
                DEP_ROUTE_TYPE,
            </if>
            <if test="arrRouteType != null">
                ARR_ROUTE_TYPE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="depStartDate != null">
                #{depStartDate,jdbcType=VARCHAR},
            </if>
            <if test="depEndDate != null">
                #{depEndDate,jdbcType=VARCHAR},
            </if>
            <if test="depWarnTitle != null">
                #{depWarnTitle,jdbcType=VARCHAR},
            </if>
            <if test="depWarnContent != null">
                #{depWarnContent,jdbcType=VARCHAR},
            </if>
            <if test="arrStartDate != null">
                #{arrStartDate,jdbcType=VARCHAR},
            </if>
            <if test="arrEndDate != null">
                #{arrEndDate,jdbcType=VARCHAR},
            </if>
            <if test="arrWarnTitle != null">
                #{arrWarnTitle,jdbcType=VARCHAR},
            </if>
            <if test="arrWarnContent != null">
                #{arrWarnContent,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="depRouteType != null">
                #{depRouteType,jdbcType=VARCHAR},
            </if>
            <if test="arrRouteType != null">
                #{arrRouteType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.CityWarnInfoPO">
        update T_CITY_WARN_INFO
        <set>
            <if test="cityCode != null">
                CITY_CODE = #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="depStartDate != null">
                DEP_START_DATE = #{depStartDate,jdbcType=VARCHAR},
            </if>
            <if test="depEndDate != null">
                DEP_END_DATE = #{depEndDate,jdbcType=VARCHAR},
            </if>
            <if test="depWarnTitle != null">
                DEP_WARN_TITLE = #{depWarnTitle,jdbcType=VARCHAR},
            </if>
            <if test="depWarnContent != null">
                DEP_WARN_CONTENT = #{depWarnContent,jdbcType=VARCHAR},
            </if>
            <if test="arrStartDate != null">
                ARR_START_DATE = #{arrStartDate,jdbcType=VARCHAR},
            </if>
            <if test="arrEndDate != null">
                ARR_END_DATE = #{arrEndDate,jdbcType=VARCHAR},
            </if>
            <if test="arrWarnTitle != null">
                ARR_WARN_TITLE = #{arrWarnTitle,jdbcType=VARCHAR},
            </if>
            <if test="arrWarnContent != null">
                ARR_WARN_CONTENT = #{arrWarnContent,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="depRouteType != null">
                DEP_ROUTE_TYPE = #{depRouteType,jdbcType=VARCHAR},
            </if>
            <if test="arrRouteType != null">
                ARR_ROUTE_TYPE = #{arrRouteType,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.juneyaoair.manage.b2c.entity.CityWarnInfoPO">
        update T_CITY_WARN_INFO
        set CITY_CODE        = #{cityCode,jdbcType=VARCHAR},
            DEP_START_DATE   = #{depStartDate,jdbcType=VARCHAR},
            DEP_END_DATE     = #{depEndDate,jdbcType=VARCHAR},
            DEP_WARN_TITLE   = #{depWarnTitle,jdbcType=VARCHAR},
            DEP_WARN_CONTENT = #{depWarnContent,jdbcType=VARCHAR},
            ARR_START_DATE   = #{arrStartDate,jdbcType=VARCHAR},
            ARR_END_DATE     = #{arrEndDate,jdbcType=VARCHAR},
            ARR_WARN_TITLE   = #{arrWarnTitle,jdbcType=VARCHAR},
            ARR_WARN_CONTENT = #{arrWarnContent,jdbcType=VARCHAR},
            CREATE_TIME      = #{createTime,jdbcType=TIMESTAMP},
            CREATE_USER      = #{createUser,jdbcType=VARCHAR},
            UPDATE_USER      = #{updateUser,jdbcType=VARCHAR},
            UPDATE_TIME      = #{updateTime,jdbcType=TIMESTAMP},
            DEP_ROUTE_TYPE   = #{depRouteType,jdbcType=VARCHAR},
            ARR_ROUTE_TYPE   = #{arrRouteType,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <select id="selectByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CITY_WARN_INFO
        <where>
            <if test="id != null">
                and ID = #{id,jdbcType=VARCHAR}
            </if>
            <if test="cityCode != null">
                and CITY_CODE = #{cityCode,jdbcType=VARCHAR}
            </if>
            <if test="depStartDate != null">
                and DEP_START_DATE = #{depStartDate,jdbcType=VARCHAR}
            </if>
            <if test="depEndDate != null">
                and DEP_END_DATE = #{depEndDate,jdbcType=VARCHAR}
            </if>
            <if test="depWarnTitle != null">
                and DEP_WARN_TITLE = #{depWarnTitle,jdbcType=VARCHAR}
            </if>
            <if test="depWarnContent != null">
                and DEP_WARN_CONTENT = #{depWarnContent,jdbcType=VARCHAR}
            </if>
            <if test="arrStartDate != null">
                and ARR_START_DATE = #{arrStartDate,jdbcType=VARCHAR}
            </if>
            <if test="arrEndDate != null">
                and ARR_END_DATE = #{arrEndDate,jdbcType=VARCHAR}
            </if>
            <if test="arrWarnTitle != null">
                and ARR_WARN_TITLE = #{arrWarnTitle,jdbcType=VARCHAR}
            </if>
            <if test="arrWarnContent != null">
                and ARR_WARN_CONTENT = #{arrWarnContent,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createUser != null">
                and CREATE_USER = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="updateUser != null">
                and UPDATE_USER = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="depRouteType != null">
                and DEP_ROUTE_TYPE = #{depRouteType,jdbcType=VARCHAR}
            </if>
            <if test="arrRouteType != null">
                and ARR_ROUTE_TYPE = #{arrRouteType,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <insert id="insertList">
        INSERT INTO T_CITY_WARN_INFO(
            ID,
            CITY_CODE,
            DEP_START_DATE,
            DEP_END_DATE,
            DEP_WARN_TITLE,
            DEP_WARN_CONTENT,
            ARR_START_DATE,
            ARR_END_DATE,
            ARR_WARN_TITLE,
            ARR_WARN_CONTENT,
            CREATE_TIME,
            CREATE_USER,
            UPDATE_USER,
            UPDATE_TIME,
            DEP_ROUTE_TYPE,
            ARR_ROUTE_TYPE
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
        (
            #{element.id,jdbcType=VARCHAR},
            #{element.cityCode,jdbcType=VARCHAR},
            #{element.depStartDate,jdbcType=VARCHAR},
            #{element.depEndDate,jdbcType=VARCHAR},
            #{element.depWarnTitle,jdbcType=VARCHAR},
            #{element.depWarnContent,jdbcType=VARCHAR},
            #{element.arrStartDate,jdbcType=VARCHAR},
            #{element.arrEndDate,jdbcType=VARCHAR},
            #{element.arrWarnTitle,jdbcType=VARCHAR},
            #{element.arrWarnContent,jdbcType=VARCHAR},
            #{element.createTime,jdbcType=TIMESTAMP},
            #{element.createUser,jdbcType=VARCHAR},
            #{element.updateUser,jdbcType=VARCHAR},
            #{element.updateTime,jdbcType=TIMESTAMP},
            #{element.depRouteType,jdbcType=VARCHAR},
            #{element.arrRouteType,jdbcType=VARCHAR}
        )
        </foreach>
</insert>
</mapper>