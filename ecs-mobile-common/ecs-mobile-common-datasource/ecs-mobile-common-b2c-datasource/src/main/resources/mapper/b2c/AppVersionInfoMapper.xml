<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.AppVersionInfoMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.AppVersionInfoPO">
        <id property="innerId" column="INNER_ID" jdbcType="VARCHAR"/>
        <id property="deviceType" column="DEVICE_TYPE" jdbcType="VARCHAR"/>
        <id property="appVersionNo" column="APP_VERSION_NO" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateDetails" column="UPDATE_DETAILS" jdbcType="VARCHAR"/>
        <result property="forceUpdate" column="FORCE_UPDATE" jdbcType="VARCHAR"/>
        <result property="loginFlag" column="LOGIN_FLAG" jdbcType="VARCHAR"/>
        <result property="versionTimestamp" column="VERSION_TIMESTAMP" jdbcType="VARCHAR"/>
        <result property="thirdPartyLoginFlag" column="THIRD_PARTY_LOGIN_FLAG" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        INNER_ID,
        DEVICE_TYPE,
        APP_VERSION_NO,
        STATUS,
        CREATE_TIME,
        UPDATE_DETAILS,
        FORCE_UPDATE,
        LOGIN_FLAG,
        VERSION_TIMESTAMP,
        THIRD_PARTY_LOGIN_FLAG
    </sql>
    <select id="checkAppVersion" resultMap="BaseResultMap">
        SELECT *
        from app_version_info
        <where>
            1 = 1
            <if test="innerId != null and innerId != ''">
                and INNER_ID = #{innerId}
            </if>
            <if test="appVersionNo != null and appVersionNo != ''">
                and APP_VERSION_NO = #{appVersionNo}
            </if>
            <if test="deviceType != null and deviceType != ''">
                and DEVICE_TYPE = #{deviceType}
            </if>
        </where>
    </select>

    <!--
     insert：编写插入语句
     id:插入语句的唯一标识
     parameterType：插入语句的参数类型，可以省略。
     useGeneratedKeys：开启主键自增回显，将自增长的主键值回显到形参中（即封装到User对象中）
     keyColumn：数据库中主键的字段名称
     keyProperty：pojo中主键对应的属性
     useGeneratedKeys="true" keyColumn="APP_VERSION_ID" keyProperty="appVersionID"
  -->
    <insert id="insertAppVersion" parameterType="com.juneyaoair.manage.b2c.entity.AppVersionInfoPO">
        insert into app_version_info(INNER_ID,
                                     DEVICE_TYPE,
                                     APP_VERSION_NO,
                                     STATUS,
                                     CREATE_TIME,
                                     UPDATE_DETAILS,
                                     FORCE_UPDATE,
                                     LOGIN_FLAG,
                                     THIRD_PARTY_LOGIN_FLAG,
                                     VERSION_TIMESTAMP)
        values (#{innerId,jdbcType=VARCHAR},
                #{deviceType,jdbcType=VARCHAR},
                #{appVersionNo,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR},
                sysdate,
                #{updateDetails,jdbcType=VARCHAR},
                #{forceUpdate,jdbcType=VARCHAR},
                #{loginFlag,jdbcType=VARCHAR},
                #{thirdPartyLoginFlag,jdbcType=VARCHAR},
                #{versionTimestamp,jdbcType=VARCHAR})
    </insert>

    <delete id="deleteAppVersion" parameterType="com.juneyaoair.manage.b2c.entity.AppVersionInfoPO">
        DELETE
        FROM app_version_info
        WHERE INNER_ID = #{innerId}
          AND DEVICE_TYPE = #{deviceType}
    </delete>
    <update id="updateByInnerIdAndDeviceType">
        update app_version_info
        <set>
            <if test="updated.appVersionNo != null">
                APP_VERSION_NO = #{updated.appVersionNo,jdbcType=VARCHAR},
            </if>
            <if test="updated.status != null">
                STATUS = #{updated.status,jdbcType=VARCHAR},
            </if>
            <if test="updated.updateDetails != null">
                UPDATE_DETAILS = #{updated.updateDetails,jdbcType=VARCHAR},
            </if>
            <if test="updated.forceUpdate != null">
                FORCE_UPDATE = #{updated.forceUpdate,jdbcType=VARCHAR},
            </if>

            <if test="updated.loginFlag != null">
                LOGIN_FLAG = #{updated.loginFlag},
            </if>
            <if test="updated.thirdPartyLoginFlag != null">
                THIRD_PARTY_LOGIN_FLAG = #{updated.thirdPartyLoginFlag},
            </if>
            <if test="updated.versionTimestamp != null">
                VERSION_TIMESTAMP = #{updated.versionTimestamp}
            </if>
        </set>
        where INNER_ID = #{innerId,jdbcType=VARCHAR}
          and DEVICE_TYPE = #{deviceType,jdbcType=VARCHAR}
    </update>
    <select id="queryByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_version_info
        <where>
            <if test="innerId != null">
                and INNER_ID = #{innerId,jdbcType=VARCHAR}
            </if>
            <if test="deviceType != null">
                and DEVICE_TYPE = #{deviceType,jdbcType=VARCHAR}
            </if>
            <if test="appVersionNo != null">
                and APP_VERSION_NO = #{appVersionNo,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and "STATUS" = #{status,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateDetails != null">
                and UPDATE_DETAILS = #{updateDetails,jdbcType=VARCHAR}
            </if>
            <if test="forceUpdate != null">
                and FORCE_UPDATE = #{forceUpdate,jdbcType=VARCHAR}
            </if>
            <if test="loginFlag != null">
                and LOGIN_FLAG = #{loginFlag,jdbcType=VARCHAR}
            </if>
            <if test="versionTimestamp != null">
                and VERSION_TIMESTAMP = #{versionTimestamp,jdbcType=VARCHAR}
            </if>
            <if test="thirdPartyLoginFlag != null">
                and THIRD_PARTY_LOGIN_FLAG = #{thirdPartyLoginFlag,jdbcType=VARCHAR}
            </if>
        </where> order by CREATE_TIME desc
    </select>
</mapper>
