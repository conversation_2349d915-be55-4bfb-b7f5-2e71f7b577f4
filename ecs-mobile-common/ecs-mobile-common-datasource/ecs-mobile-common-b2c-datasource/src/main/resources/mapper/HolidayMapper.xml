<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.HolidayMapper">

    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.activity.HolidayPO">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="holidayDate" column="HOLIDAY_DATE" jdbcType="VARCHAR"/>
            <result property="restOrWork" column="REST_OR_WORK" jdbcType="VARCHAR"/>
            <result property="holidayName" column="HOLIDAY_NAME" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="createdBy" column="CREATED_BY" jdbcType="VARCHAR"/>
            <result property="createdTime" column="CREATED_TIME" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="UPDATED_BY" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="UPDATED_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,HOLIDAY_DATE,REST_OR_WORK,
        HOLIDAY_NAME,REMARK,CREATED_BY,
        CREATED_TIME,UPDATED_BY,UPDATED_TIME
    </sql>
</mapper>
