package com.juneyaoair.manage.flight.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <p>
 * 航班信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
@TableName("t_flightinfo")
@ApiModel(value = "Flightinfo对象", description = "航班信息表")
public class Flightinfo extends HoBaseEntity {
    @ApiModelProperty("记录主键")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("航班号")
    private String flightNo;

    @ApiModelProperty("航班日期")
    private String flightDate;

    @ApiModelProperty("航班出发日期")
    private String flightDateOrg;

    @ApiModelProperty("航班到达日期")
    private String flightArrDate;

    @ApiModelProperty("英文航班日期")
    private String flightDateEn;

    @ApiModelProperty("出发城市")
    private String depCity;

    @ApiModelProperty("出发机场")
    private String depAirport;

    @ApiModelProperty("出发时刻")
    private String depDateTime;

    @ApiModelProperty("出发时间时区")
    private String depDateLocaltimezone;

    @ApiModelProperty("北京出发时间")
    private String depDateChinatime;

    @ApiModelProperty("出发航站楼")
    private String depAirportTerminal;

    @ApiModelProperty("到达城市")
    private String arrCity;

    @ApiModelProperty("到达机场")
    private String arrAirport;

    @ApiModelProperty("到达时刻")
    private String arrDateTime;

    @ApiModelProperty("到达时间时区")
    private String arrDateLocaltimezone;

    @ApiModelProperty("北京到达时间")
    private String arrDateChinatime;

    @ApiModelProperty("到达航站楼")
    private String arrAirportTerminal;

    @ApiModelProperty("是否主航班")
    private String isMain;

    @ApiModelProperty("是否经停航班")
    private String isStop;

    @ApiModelProperty("经停机场")
    private String stopAirport;

    @ApiModelProperty("经停点出发时刻")
    private String stopDepTime;

    @ApiModelProperty("经停点所在时区")
    private String stopDepLocaltimezone;

    @ApiModelProperty("经停点北京出发时间")
    private String stopDepChinatime;

    @ApiModelProperty("经停点航站楼")
    private String stopAirportTerminal;

    @ApiModelProperty("公务舱级别代码")
    private String firstClass;

    @ApiModelProperty("公务舱数量")
    private Integer firstClassVal;

    @ApiModelProperty("经济舱级别代码")
    private String touristClass;

    @ApiModelProperty("经济舱数量")
    private Integer touristClassVal;

    @ApiModelProperty("国内国际标志")
    private String interFlag;

    @ApiModelProperty("最近更新时间")
    private Date updatetime;

    @ApiModelProperty("最近更新时间戳")
    private Long timestamp;

    @ApiModelProperty("机型")
    private String planType;

    private String meal;

    @ApiModelProperty("餐食代码")
    private String mealCode;

    @ApiModelProperty("记录插入时间")
    private Date createtime;

    @ApiModelProperty("来源")
    private String dataSource;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }

    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        this.flightDate = flightDate;
    }

    public String getFlightDateOrg() {
        return flightDateOrg;
    }

    public void setFlightDateOrg(String flightDateOrg) {
        this.flightDateOrg = flightDateOrg;
    }

    public String getFlightArrDate() {
        return flightArrDate;
    }

    public void setFlightArrDate(String flightArrDate) {
        this.flightArrDate = flightArrDate;
    }

    public String getFlightDateEn() {
        return flightDateEn;
    }

    public void setFlightDateEn(String flightDateEn) {
        this.flightDateEn = flightDateEn;
    }

    public String getDepCity() {
        return depCity;
    }

    public void setDepCity(String depCity) {
        this.depCity = depCity;
    }

    public String getDepAirport() {
        return depAirport;
    }

    public void setDepAirport(String depAirport) {
        this.depAirport = depAirport;
    }

    public String getDepDateTime() {
        return depDateTime;
    }

    public void setDepDateTime(String depDateTime) {
        this.depDateTime = depDateTime;
    }

    public String getDepDateLocaltimezone() {
        return depDateLocaltimezone;
    }

    public void setDepDateLocaltimezone(String depDateLocaltimezone) {
        this.depDateLocaltimezone = depDateLocaltimezone;
    }

    public String getDepDateChinatime() {
        return depDateChinatime;
    }

    public void setDepDateChinatime(String depDateChinatime) {
        this.depDateChinatime = depDateChinatime;
    }

    public String getDepAirportTerminal() {
        return depAirportTerminal;
    }

    public void setDepAirportTerminal(String depAirportTerminal) {
        this.depAirportTerminal = depAirportTerminal;
    }

    public String getArrCity() {
        return arrCity;
    }

    public void setArrCity(String arrCity) {
        this.arrCity = arrCity;
    }

    public String getArrAirport() {
        return arrAirport;
    }

    public void setArrAirport(String arrAirport) {
        this.arrAirport = arrAirport;
    }

    public String getArrDateTime() {
        return arrDateTime;
    }

    public void setArrDateTime(String arrDateTime) {
        this.arrDateTime = arrDateTime;
    }

    public String getArrDateLocaltimezone() {
        return arrDateLocaltimezone;
    }

    public void setArrDateLocaltimezone(String arrDateLocaltimezone) {
        this.arrDateLocaltimezone = arrDateLocaltimezone;
    }

    public String getArrDateChinatime() {
        return arrDateChinatime;
    }

    public void setArrDateChinatime(String arrDateChinatime) {
        this.arrDateChinatime = arrDateChinatime;
    }

    public String getArrAirportTerminal() {
        return arrAirportTerminal;
    }

    public void setArrAirportTerminal(String arrAirportTerminal) {
        this.arrAirportTerminal = arrAirportTerminal;
    }

    public String getIsMain() {
        return isMain;
    }

    public void setIsMain(String isMain) {
        this.isMain = isMain;
    }

    public String getIsStop() {
        return isStop;
    }

    public void setIsStop(String isStop) {
        this.isStop = isStop;
    }

    public String getStopAirport() {
        return stopAirport;
    }

    public void setStopAirport(String stopAirport) {
        this.stopAirport = stopAirport;
    }

    public String getStopDepTime() {
        return stopDepTime;
    }

    public void setStopDepTime(String stopDepTime) {
        this.stopDepTime = stopDepTime;
    }

    public String getStopDepLocaltimezone() {
        return stopDepLocaltimezone;
    }

    public void setStopDepLocaltimezone(String stopDepLocaltimezone) {
        this.stopDepLocaltimezone = stopDepLocaltimezone;
    }

    public String getStopDepChinatime() {
        return stopDepChinatime;
    }

    public void setStopDepChinatime(String stopDepChinatime) {
        this.stopDepChinatime = stopDepChinatime;
    }

    public String getStopAirportTerminal() {
        return stopAirportTerminal;
    }

    public void setStopAirportTerminal(String stopAirportTerminal) {
        this.stopAirportTerminal = stopAirportTerminal;
    }

    public String getFirstClass() {
        return firstClass;
    }

    public void setFirstClass(String firstClass) {
        this.firstClass = firstClass;
    }

    public Integer getFirstClassVal() {
        return firstClassVal;
    }

    public void setFirstClassVal(Integer firstClassVal) {
        this.firstClassVal = firstClassVal;
    }

    public String getTouristClass() {
        return touristClass;
    }

    public void setTouristClass(String touristClass) {
        this.touristClass = touristClass;
    }

    public Integer getTouristClassVal() {
        return touristClassVal;
    }

    public void setTouristClassVal(Integer touristClassVal) {
        this.touristClassVal = touristClassVal;
    }

    public String getInterFlag() {
        return interFlag;
    }

    public void setInterFlag(String interFlag) {
        this.interFlag = interFlag;
    }

    public Date getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getPlanType() {
        return planType;
    }

    public void setPlanType(String planType) {
        this.planType = planType;
    }

    public String getMeal() {
        return meal;
    }

    public void setMeal(String meal) {
        this.meal = meal;
    }

    public String getMealCode() {
        return mealCode;
    }

    public void setMealCode(String mealCode) {
        this.mealCode = mealCode;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public String toString() {
        return "Flightinfo{" +
            "id = " + id +
            ", flightNo = " + flightNo +
            ", flightDate = " + flightDate +
            ", flightDateOrg = " + flightDateOrg +
            ", flightArrDate = " + flightArrDate +
            ", flightDateEn = " + flightDateEn +
            ", depCity = " + depCity +
            ", depAirport = " + depAirport +
            ", depDateTime = " + depDateTime +
            ", depDateLocaltimezone = " + depDateLocaltimezone +
            ", depDateChinatime = " + depDateChinatime +
            ", depAirportTerminal = " + depAirportTerminal +
            ", arrCity = " + arrCity +
            ", arrAirport = " + arrAirport +
            ", arrDateTime = " + arrDateTime +
            ", arrDateLocaltimezone = " + arrDateLocaltimezone +
            ", arrDateChinatime = " + arrDateChinatime +
            ", arrAirportTerminal = " + arrAirportTerminal +
            ", isMain = " + isMain +
            ", isStop = " + isStop +
            ", stopAirport = " + stopAirport +
            ", stopDepTime = " + stopDepTime +
            ", stopDepLocaltimezone = " + stopDepLocaltimezone +
            ", stopDepChinatime = " + stopDepChinatime +
            ", stopAirportTerminal = " + stopAirportTerminal +
            ", firstClass = " + firstClass +
            ", firstClassVal = " + firstClassVal +
            ", touristClass = " + touristClass +
            ", touristClassVal = " + touristClassVal +
            ", interFlag = " + interFlag +
            ", updatetime = " + updatetime +
            ", timestamp = " + timestamp +
            ", planType = " + planType +
            ", meal = " + meal +
            ", mealCode = " + mealCode +
            ", createtime = " + createtime +
            ", dataSource = " + dataSource +
        "}";
    }
}
