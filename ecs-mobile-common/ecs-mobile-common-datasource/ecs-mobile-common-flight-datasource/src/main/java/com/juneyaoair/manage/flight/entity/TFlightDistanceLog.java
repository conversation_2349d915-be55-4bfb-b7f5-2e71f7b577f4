package com.juneyaoair.manage.flight.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 航距修改日志表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_flight_distance_log")
public class TFlightDistanceLog {
    /**
     * 记录号
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     * 航距关联ID
     */
    @TableField(value = "FLIGHT_DISTANCE_ID")
    private Integer flightDistanceId;

    /**
     * 变更类型;枚举值：新增;修改，删除
     */
    @TableField(value = "CHANGE_TYPE")
    private String changeType;

    /**
     * 变更内容
     */
    @TableField(value = "CHANGE_COMMENT")
    private String changeComment;

    /**
     * 消息通知状态;Y-已通知;N-未通知
     */
    @TableField(value = "NOTICE_FLAG")
    private String noticeFlag;

    /**
     * 创建人
     */
    @TableField(value = "CREATED_USER")
    private String createdUser;

    /**
     * 创建时间
     */
    @TableField(value = "CREATED_TIME")
    private Date createdTime;
}