package com.juneyaoair.manage.flight.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.ecs.manage.dto.flight.request.FightRequestDto;
import com.juneyaoair.manage.flight.entity.Flightinfo;

import java.util.List;

/**
 * <p>
 * 航班信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
public interface IFlightinfoService extends IService<Flightinfo> {

  /**
   * 航班列表查询
   * @param fightRequestDto
   * @return
   */
  List<Flightinfo> selectFlightList(FightRequestDto fightRequestDto);

}
