package com.juneyaoair.manage.flight.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.juneyaoair.ecs.manage.dto.flightdistance.FlightDistancePricedDTO;
import com.juneyaoair.manage.flight.annotation.Flight;
import com.juneyaoair.manage.flight.entity.TFlightDistance;

import java.util.List;

@Flight
public interface TFlightDistanceMapper extends BaseMapper<TFlightDistance> {
    List<TFlightDistance> select(FlightDistancePricedDTO param);
}