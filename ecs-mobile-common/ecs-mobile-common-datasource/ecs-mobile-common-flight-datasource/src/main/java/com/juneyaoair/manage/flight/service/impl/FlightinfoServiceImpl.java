package com.juneyaoair.manage.flight.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.ecs.manage.dto.flight.request.FightRequestDto;
import com.juneyaoair.manage.flight.entity.Flightinfo;
import com.juneyaoair.manage.flight.mapper.FlightinfoMapper;
import com.juneyaoair.manage.flight.service.IFlightinfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 航班信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
@Service
public class FlightinfoServiceImpl extends ServiceImpl<FlightinfoMapper, Flightinfo> implements IFlightinfoService {
    @Autowired
    private FlightinfoMapper flightinfoMapper;

    private static String FLIGHT_DATE_START = "flightDateStart";
    private static String FLIGHT_DATE_END = "flightDateEnd";


    @Override
    public List<Flightinfo> selectFlightList(FightRequestDto fightRequestDto) {
        Flightinfo flightinfo = new Flightinfo();
        flightinfo.setFlightNo(fightRequestDto.getFlightNo());
        flightinfo.setDepCity(fightRequestDto.getDepCityCode());
        flightinfo.setArrCity(fightRequestDto.getArrCityCode());
        flightinfo.setDepAirport(fightRequestDto.getDepAirport());
        flightinfo.setArrAirport(fightRequestDto.getArrAirport());
        flightinfo.setIsStop(fightRequestDto.getStop());
        Map<String,Object> params = new HashMap<>();
        params.put(FLIGHT_DATE_START,fightRequestDto.getFlightDateStart());
        params.put(FLIGHT_DATE_END,fightRequestDto.getFlightDateEnd());
        flightinfo.setParams(params);
        flightinfo.setDataSource(fightRequestDto.getDataSource());
        return flightinfoMapper.selectFlightList(flightinfo);
    }
}
