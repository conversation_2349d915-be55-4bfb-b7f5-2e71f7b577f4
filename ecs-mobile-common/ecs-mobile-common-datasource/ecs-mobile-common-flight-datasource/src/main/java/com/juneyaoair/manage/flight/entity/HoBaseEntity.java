package com.juneyaoair.manage.flight.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/5 17:07
 */
@Data
public class HoBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> params;

    @TableField(exist = false)
    private String searchValue;

    public Map<String, Object> getParams() {
        if (this.params == null) {
            this.params = new HashMap();
        }

        return this.params;
    }

}
