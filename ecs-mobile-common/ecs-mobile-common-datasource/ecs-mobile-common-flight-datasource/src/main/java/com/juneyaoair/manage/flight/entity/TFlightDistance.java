package com.juneyaoair.manage.flight.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 航距表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_flight_distance")
public class TFlightDistance {
    /**
     * 记录编号
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     * 出发城市三字码
     */
    @TableField(value = "DEP_CITY_CODE")
    private String depCityCode;

    /**
     * 到达城市三字码
     */
    @TableField(value = "ARR_CITY_CODE")
    private String arrCityCode;

    /**
     * 城市距离(KM)
     */
    @TableField(value = "MILEAGE")
    private BigDecimal mileage;

    /**
     * Y舱公布价
     */
    @TableField(value = "Y_PRICE")
    private BigDecimal yPrice;

    /**
     * J舱公布价
     */
    @TableField(value = "J_PRICE")
    private BigDecimal jPrice;

    /**
     * 状态Y-启用，N禁用
     */
    @TableField(value = "`STATUS`")
    private String status;

    /**
     * 创建人
     */
    @TableField(value = "CREATED_USER")
    private String createdUser;

    /**
     * 创建时间
     */
    @TableField(value = "CREATED_TIME")
    private Date createdTime;

    /**
     * 更新人
     */
    @TableField(value = "UPDATED_USER")
    private String updatedUser;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATED_TIME")
    private Date updatedTime;
}