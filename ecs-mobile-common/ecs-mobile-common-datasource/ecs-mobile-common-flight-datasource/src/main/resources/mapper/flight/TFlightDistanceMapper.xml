<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.flight.mapper.TFlightDistanceMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.flight.entity.TFlightDistance">
    <!--@mbg.generated-->
    <!--@Table t_flight_distance-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="DEP_CITY_CODE" jdbcType="VARCHAR" property="depCityCode" />
    <result column="ARR_CITY_CODE" jdbcType="VARCHAR" property="arrCityCode" />
    <result column="MILEAGE" jdbcType="DECIMAL" property="mileage" />
    <result column="Y_PRICE" jdbcType="DECIMAL" property="yPrice" />
    <result column="J_PRICE" jdbcType="DECIMAL" property="jPrice" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="CREATED_USER" jdbcType="VARCHAR" property="createdUser" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_USER" jdbcType="VARCHAR" property="updatedUser" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, DEP_CITY_CODE, ARR_CITY_CODE, MILEAGE, Y_PRICE, J_PRICE, `STATUS`, CREATED_USER, 
    CREATED_TIME, UPDATED_USER, UPDATED_TIME
  </sql>

  <select id="select" resultMap="BaseResultMap">
    SELECT *
    FROM t_flight_distance
    <where>
      <if test="id != null and id != ''">
        and ID = #{id}
      </if>
      <if test="depCity != null and depCity != ''">
        and DEP_CITY_CODE = #{depCity}
      </if>
      <if test="arrCity != null and arrCity != ''">
        and ARR_CITY_CODE = #{arrCity}
      </if>
    </where>
  </select>
</mapper>