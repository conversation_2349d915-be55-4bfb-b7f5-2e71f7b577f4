<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.flight.mapper.FlightinfoMapper">
    <resultMap type="com.juneyaoair.manage.flight.entity.Flightinfo" id="FlightResult">
        <id column="ID" property="id"></id>
        <result column="FLIGHT_NO" property="flightNo" jdbcType="VARCHAR"></result>
        <result column="PLAN_TYPE" property="planType" jdbcType="VARCHAR"></result>
        <result column="FLIGHT_DATE" property="flightDate" jdbcType="VARCHAR"></result>
        <result column="FLIGHT_ARR_DATE" property="flightArrDate"></result>
        <result column="FLIGHT_DATE_ORG" property="flightDateOrg"></result>
        <result column="FLIGHT_DATE_EN" property="flightDateEn"></result>
        <result column="DEP_DATE_TIME" property="depDateTime"></result>
        <result column="DEP_DATE_LOCALTIMEZONE" property="depDateLocaltimezone"></result>
        <result column="DEP_DATE_CHINATIME" property="depDateChinatime"></result>
        <result column="ARR_DATE_TIME" property="arrDateTime"></result>
        <result column="ARR_DATE_LOCALTIMEZONE" property="arrDateLocaltimezone"></result>
        <result column="ARR_DATE_CHINATIME" property="arrDateChinatime"></result>
        <result column="DEP_CITY" property="depCity"></result>
        <result column="ARR_CITY" property="arrCity"></result>
        <result column="DEP_AIRPORT" property="depAirport"></result>
        <result column="ARR_AIRPORT" property="arrAirport"></result>
        <result column="IS_MAIN" property="isMain"></result>
        <result column="IS_STOP" property="isStop"></result>
        <result column="FIRST_CLASS" property="firstClass"></result>
        <result column="FIRST_CLASS_VAL" property="firstClassVal"></result>
        <result column="TOURIST_CLASS" property="touristClass"></result>
        <result column="TOURIST_CLASS_VAL" property="touristClassVal"></result>
        <result column="INTER_FLAG" property="interFlag"></result>
        <result column="UPDATETIME" property="updatetime"></result>
        <result column="STOP_AIRPORT" property="stopAirport"></result>
        <result column="STOP_DEP_TIME" property="stopDepTime"></result>
        <result column="STOP_DEP_LOCALTIMEZONE" property="stopDepLocaltimezone"></result>
        <result column="STOP_DEP_CHINATIME" property="stopDepChinatime"></result>
        <result column="DEP_AIRPORT_TERMINAL" property="depAirportTerminal"></result>
        <result column="STOP_AIRPORT_TERMINAL" property="stopAirportTerminal"></result>
        <result column="ARR_AIRPORT_TERMINAL" property="arrAirportTerminal"></result>
        <result column="TIMESTAMP" property="timestamp"></result>
        <result column="MEAL" property="meal"></result>
        <result column="MEAL_CODE" property="mealCode"></result>
        <result column="CREATETIME" property="createtime"></result>
        <result column="DATA_SOURCE" property="dataSource"></result>
    </resultMap>

    <sql id="selectFlightSql">
        SELECT ID,
        flight_no,
        flight_date,
        flight_date_org,
        flight_arr_date,
        flight_date_en,
        dep_city,
        dep_airport,
        dep_date_time,
        dep_date_localtimezone,
        dep_date_chinatime,
        dep_airport_terminal,
        arr_city,
        arr_airport,
        arr_date_time,
        arr_date_localtimezone,
        arr_date_chinatime,
        arr_airport_terminal,
        is_main,
        is_stop,
        stop_airport,
        stop_dep_time,
        stop_dep_localtimezone,
        stop_dep_chinatime,
        stop_airport_terminal,
        first_class,
        first_class_val,
        tourist_class,
        tourist_class_val,
        inter_flag,
        updatetime,
        timestamp,
        plan_type,
        meal,
        meal_code,
        createtime,
        data_source
        FROM t_flightinfo
    </sql>

    <select id="selectFlightList" parameterType="com.juneyaoair.manage.flight.entity.Flightinfo" resultMap="FlightResult">
        <include refid="selectFlightSql"/>
        <where>
            <if test="params.flightDateStart != null and params.flightDateStart != ''" >
                and FLIGHT_DATE &gt;= #{params.flightDateStart}
            </if>
            <if test="params.flightDateEnd != null and params.flightDateEnd != ''" >
                and FLIGHT_DATE &lt;= #{params.flightDateEnd}
            </if>
            <if test="depCity != null and depCity != ''" >
                and DEP_CITY = #{depCity,jdbcType=VARCHAR}
            </if>
            <if test="arrCity != null and arrCity != ''" >
                and ARR_CITY = #{arrCity,jdbcType=VARCHAR}
            </if>
            <if test="depAirport != null and depAirport != ''" >
                and DEP_AIRPORT = #{depAirport,jdbcType=VARCHAR}
            </if>
            <if test="arrAirport != null and arrAirport != ''" >
                and ARR_AIRPORT = #{arrAirport,jdbcType=VARCHAR}
            </if>
            <if test="flightNo != null and flightNo != ''" >
                and FLIGHT_NO = #{flightNo,jdbcType=VARCHAR}
            </if>
            <if test="isStop != null and isStop != ''" >
                and IS_STOP = #{isStop,jdbcType=VARCHAR}
            </if>
            <if test="dataSource != null and dataSource != ''">
                and DATA_SOURCE = #{dataSource}
            </if>
        </where>
    </select>
</mapper>
