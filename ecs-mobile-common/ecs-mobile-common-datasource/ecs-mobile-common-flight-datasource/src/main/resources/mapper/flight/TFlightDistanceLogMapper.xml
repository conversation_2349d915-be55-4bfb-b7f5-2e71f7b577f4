<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.flight.mapper.TFlightDistanceLogMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.flight.entity.TFlightDistanceLog">
    <!--@mbg.generated-->
    <!--@Table t_flight_distance_log-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="FLIGHT_DISTANCE_ID" jdbcType="INTEGER" property="flightDistanceId" />
    <result column="CHANGE_TYPE" jdbcType="VARCHAR" property="changeType" />
    <result column="CHANGE_COMMENT" jdbcType="VARCHAR" property="changeComment" />
    <result column="NOTICE_FLAG" jdbcType="VARCHAR" property="noticeFlag" />
    <result column="CREATED_USER" jdbcType="VARCHAR" property="createdUser" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, FLIGHT_DISTANCE_ID, CHANGE_TYPE, CHANGE_COMMENT, NOTICE_FLAG, CREATED_USER, CREATED_TIME
  </sql>

  <select id="selectByDistanceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_flight_distance_log
    where FLIGHT_DISTANCE_ID = #{id,jdbcType=VARCHAR}
  </select>
</mapper>