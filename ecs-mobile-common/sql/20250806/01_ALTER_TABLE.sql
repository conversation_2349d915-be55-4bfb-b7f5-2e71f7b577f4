ALTER TABLE PP_MODULAR ADD GROUP_TYPE VARCHAR2(32);
COMMENT ON COLUMN PP_MODULAR.GROUP_TYPE IS '模块分组';

UPDATE PP_MODULAR SET GROUP_TYPE = 'I',MODULAR_BM = 'AIRPORT_TRAFFIC' WHERE name LIKE '%交通%' AND MODULAR_BM = 'AIRPORT_SERVICE' AND IS_USED = 'Y' AND EVENTTYPE = 'Service_MEL';
UPDATE PP_MODULAR SET GROUP_TYPE = 'D',MODULAR_BM = 'AIRPORT_TRAFFIC'  WHERE name LIKE '%交通%' AND MODULAR_BM = 'AIRPORT_SERVICE' AND IS_USED = 'Y' AND EVENTTYPE IN ('Service_TFJC','Service_HQJC','Service_DLJC','Service_CQJC','Service_HKJC','Service_SYJC','Service_CSJC','Service_HZJC','Service_GYJC','Service_WLMQJC','Service_YKJC','Service_FZJC');
