ALTER TABLE T_MAINTAIN_INFO ADD LANGUAGE VARCHAR2(6) DEFAULT 'ZH_CN';
COMMENT ON COLUMN T_MAINTAIN_INFO.LANGUAGE IS '语言 参照字典表：language';

UPDATE T_MAINTAIN_INFO SET LANGUAGE = 'EN_US' WHERE FILE_NAMES IN ('juneyaoairseat_EN_US', 'checkin_EN_US', 'seatAgreement_EN_US')
UPDATE T_MAINTAIN_INFO SET LANGUAGE = 'JA_JP' WHERE FILE_NAMES IN ('seatAgreement_JA_<PERSON>', 'juneyaoairseat_JA_JP', 'checkin_JA_<PERSON>')
UPDATE T_MAINTAIN_INFO SET LANGUAGE = 'ZH_HK' WHERE FILE_NAMES IN ('juneyaoairseat_ZH_HK', 'seatAgreement_ZH_HK', 'checkin_ZH_HK')

ALTER TABLE T_NOTICE ADD LANGUAGE VARCHAR2(6) DEFAULT 'ZH_CN';
COMMENT ON COLUMN T_NOTICE.LANGUAGE IS '语言 参照字典表：language';

UPDATE T_NOTICE t SET t.LANGUAGE = 'EN_US' WHERE EXISTS (SELECT 1 FROM TP_DICTVALUE t1 WHERE t.DICT_ID = t1.DVID AND t1.DVCODE = 'CHECK_AGREEMENT_EN_US');
UPDATE T_NOTICE t SET t.LANGUAGE = 'JA_JP' WHERE EXISTS (SELECT 1 FROM TP_DICTVALUE t1 WHERE t.DICT_ID = t1.DVID AND t1.DVCODE = 'CHECK_AGREEMENT_JA_JP');
UPDATE T_NOTICE t SET t.LANGUAGE = 'ZH_HK' WHERE EXISTS (SELECT 1 FROM TP_DICTVALUE t1 WHERE t.DICT_ID = t1.DVID AND t1.DVCODE = 'CHECK_AGREEMENT_ZH_HK');
