INSERT INTO T_NOTICE (NT_<PERSON>,DICT_ID,NT_NAME,NT_CODE,NT_DESCRIPTION,CAN_SHOW,"LANGUAGE") VALUES (rawtohex(sys_guid()),(SELECT DVID FROM TP_DICTVALUE WHERE DVCODE = 'CHECK_AGREEMENT'),'preferred seat product agreement',NULL,NULL,'Y','EN_US');
INSERT INTO T_NOTICE (NT_ID,DICT_ID,NT_NAME,NT_CODE,NT_DESCRIPTION,CAN_SHOW,"LANGUAGE") VALUES (rawtohex(sys_guid()),(SELECT DVID FROM TP_DICTVALUE WHERE DVCODE = 'CHECK_AGREEMENT'),'check in related agreements',NULL,NULL,'Y','EN_US');
INSERT INTO T_NOTICE (NT_ID,DICT_<PERSON>,NT_NAME,NT_CODE,NT_DESC<PERSON><PERSON><PERSON><PERSON>,CAN_SHOW,"LANGUAGE") VALUES (rawtohex(sys_guid()),(SELECT DVID FROM TP_DICTVALUE WHERE DVCODE = 'CHECK_AGREEMENT'),'チェックインに関する規約','1','值机相关协议（日文）','Y','JA_JP');
INSERT INTO T_NOTICE (NT_ID,DICT_ID,NT_NAME,NT_CODE,NT_DESCRIPTION,CAN_SHOW,"LANGUAGE") VALUES (rawtohex(sys_guid()),(SELECT DVID FROM TP_DICTVALUE WHERE DVCODE = 'CHECK_AGREEMENT'),'優先座席指定に関する規約','2','优选座位产品协议（日文）','Y','JA_JP');
INSERT INTO T_NOTICE (NT_ID,DICT_ID,NT_NAME,NT_CODE,NT_DESCRIPTION,CAN_SHOW,"LANGUAGE") VALUES (rawtohex(sys_guid()),(SELECT DVID FROM TP_DICTVALUE WHERE DVCODE = 'CHECK_AGREEMENT'),'值機相關協定',NULL,'值机相关协议-繁体中文','Y','ZH_HK');
INSERT INTO T_NOTICE (NT_ID,DICT_ID,NT_NAME,NT_CODE,NT_DESCRIPTION,CAN_SHOW,"LANGUAGE") VALUES (rawtohex(sys_guid()),(SELECT DVID FROM TP_DICTVALUE WHERE DVCODE = 'CHECK_AGREEMENT'),'優選座位產品協定',NULL,'优选座位产品协议-繁体中文','Y','ZH_HK');
