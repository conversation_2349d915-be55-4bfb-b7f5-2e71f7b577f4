package com.juneyaoair.ecs.upg.enums;

import com.ruoyi.common.core.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 升舱类型
 * @created 2023/8/22 9:27
 */
public enum UPG_TYPE_ENUM {
    /** type desc */
    TYPE_1("1", "登机口升舱（两场）"),
    TYPE_2("2","竞价升舱"),
    TYPE_3("3", "机上升舱"),
    TYPE_4("4", "登机口升舱（外站）"),
    NONE("-1", "未知"),
    ;

    private final String type;
    private final String desc;

    UPG_TYPE_ENUM(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static UPG_TYPE_ENUM getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return map.getOrDefault(type, NONE);
    }

    private static Map<String, UPG_TYPE_ENUM> map = new HashMap<>();

    static {
        for (UPG_TYPE_ENUM value : UPG_TYPE_ENUM.values()) {
            map.put(value.getType(), value);
        }
    }

}
