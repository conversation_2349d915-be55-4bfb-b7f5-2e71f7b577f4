package com.juneyaoair.ecs.manage.dto.activity.request.prizepool;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ImportPrizeSubEntry {

    @ApiModelProperty(value = "子奖品编码(奖品发放编码)")
    private String subPrizeCode;

    @ApiModelProperty(value = "子奖品名称")
    private String subPrizeName;

    @ApiModelProperty(value = "子奖品类别 参照：PRIZE_TYPE_ENUM")
    private String subPrizeCategory;

    @ApiModelProperty(value = "子奖品数量 默认为1 如果奖品类别为积分 则为发送的积分数量")
    private Integer subPrizeAmount;

}
