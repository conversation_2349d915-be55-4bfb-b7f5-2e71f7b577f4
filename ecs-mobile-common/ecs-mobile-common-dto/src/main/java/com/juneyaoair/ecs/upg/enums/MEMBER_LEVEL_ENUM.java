package com.juneyaoair.ecs.upg.enums;

/**
 * <AUTHOR>
 * @Description 会员等级
 * @created 2023/8/22 13:09
 */
public enum MEMBER_LEVEL_ENUM {

    /** code message */
    UN_KNOW(0, "未知"),
    LUCKY_CARD(1, "福卡"),
    SILVER_CARD(2, "银卡"),
    SPECIAL_SILVER_CARD(3, "特批银卡"),
    GOLDEN_CARD(4, "金卡"),
    SPECIAL_GOLDEN_CARD(5, "特批金卡"),
    PLATINUM_CARD(6, "白金卡"),
    SPECIAL_VIP_PLATINUM_CARD(7, "特批VIP白金卡"),
    SPECIAL_PLATINUM_CARD(8, "特批白金卡"),
    VISA_PLATINUM_CARD(9, "VISA白金卡");

    /** code */
    private final Integer code;
    /** desc */
    private final String desc;

    MEMBER_LEVEL_ENUM(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer code() {
        return code;
    }

    public String desc() {
        return desc;
    }
}
