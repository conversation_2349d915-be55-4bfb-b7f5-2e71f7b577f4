package com.juneyaoair.ecs.manage.dto.activity.enums;

import lombok.Getter;

/**
 * @Author: caolei
 * @Description: 奖品类型
 * @Date: 2022/1/7 10:52
 * @Modified by:
 */
public enum PRIZE_TYPE_ENUM {

    /** prizeType message */
    PRIZE_TYPE1("1", "积分"),
    PRIZE_TYPE2("2", "优惠券"),
    PRIZE_TYPE3("3", "产品券"),
    PRIZE_TYPE4("4", "定级航段"),
    PRIZE_TYPE5("5", "实物奖品"),
    PRIZE_TYPE6("6", "谢谢参与"),
    PRIZE_TYPE7("7","会员等级调整"),
    PRIZE_TYPE8("8","其他虚拟奖品"),
    PRIZE_TYPE9("9","券包多奖品"),
    ;

    PRIZE_TYPE_ENUM(String prizeType, String message) {
        this.prizeType = prizeType;
        this.message = message;
    }

    @Getter
    private final String prizeType;

    @Getter
    private final String message;

    /**
     * 获取奖品类型枚举
     * @param prizeType
     * @return
     */
    public static PRIZE_TYPE_ENUM getEnum(String prizeType) {
        for (PRIZE_TYPE_ENUM prizeTypeEnum : PRIZE_TYPE_ENUM.values()) {
            if (prizeTypeEnum.prizeType.equals(prizeType)) {
                return prizeTypeEnum;
            }
        }
        return null;
    }

}