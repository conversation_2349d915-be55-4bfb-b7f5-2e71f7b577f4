package com.juneyaoair.ecs.upg.param;

import com.juneyaoair.ecs.manage.dto.base.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 创建航线规则请求参数
 * @created 2023/8/21 9:43
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class QueryAirlineRuleParam extends PageBase {

    @ApiModelProperty(value = "航线规则名称")
    private String airlineRuleName;

}
