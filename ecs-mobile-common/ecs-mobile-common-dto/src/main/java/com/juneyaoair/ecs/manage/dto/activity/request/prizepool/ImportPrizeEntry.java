package com.juneyaoair.ecs.manage.dto.activity.request.prizepool;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ImportPrizeEntry {

    @ApiModelProperty(value = "奖品名称")
    private String prizeName;

    @ApiModelProperty(value = "奖品总数量")
    private Integer totalAmount;

    @ApiModelProperty(value = "子奖品信息清单")
    private List<ImportPrizeSubEntry> prizeSubEntityList;

}
