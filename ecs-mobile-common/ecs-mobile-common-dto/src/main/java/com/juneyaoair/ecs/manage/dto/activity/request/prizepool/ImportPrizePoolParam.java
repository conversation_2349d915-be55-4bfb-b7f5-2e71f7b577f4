package com.juneyaoair.ecs.manage.dto.activity.request.prizepool;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 上传活动奖品奖池
 * <AUTHOR>
 */
@Data
public class ImportPrizePoolParam {

    @NotBlank(message = "奖池编码不能为空")
    @ExcelProperty(index = 0, value = "奖池编码")
    @ApiModelProperty(value = "奖池编码", required = true)
    private String prizePoolCode;

    @NotBlank(message = "奖池名称不能为空")
    @ExcelProperty(index = 1, value = "奖池名称")
    @ApiModelProperty(value = "奖池名称", required = true)
    private String prizePoolName;

    @NotNull(message = "开始时间不能为空")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(index = 2, value = "开始时间")
    @ApiModelProperty(value = "开始时间", required = true)
    private Date startTime;

    @NotNull(message = "结束时间不能为空")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(index = 3, value = "结束时间")
    @ApiModelProperty(value = "结束时间", required = true)
    private Date endTime;

    @NotBlank(message = "奖品编码不能为空")
    @ExcelProperty(index = 4, value = "奖品编码")
    @ApiModelProperty(value = "奖品编码", required = true)
    private String prizeCode;

    @NotBlank(message = "奖品名称不能为空")
    @ExcelProperty(index = 5, value = "奖品名称")
    @ApiModelProperty(value = "奖品名称", required = true)
    private String prizeName;

    @NotNull(message = "奖品总数不能为空")
    @ExcelProperty(index = 6, value = "奖品总数")
    @ApiModelProperty(value = "奖品总数", required = true)
    private Integer totalAmount;

    @NotBlank(message = "子奖品编码不能为空")
    @ExcelProperty(index = 7, value = "子奖品编码")
    @ApiModelProperty(value = "子奖品编码", required = true)
    private String subPrizeCode;

    @NotBlank(message = "子奖品名称不能为空")
    @ExcelProperty(index = 8, value = "子奖品名称")
    @ApiModelProperty(value = "子奖品名称", required = true)
    private String subPrizeName;

    @NotBlank(message = "子奖品类别不能为空")
    @ExcelProperty(index = 9, value = "子奖品类别")
    @ApiModelProperty(value = "子奖品类别", required = true)
    private String subPrizeCategory;

    @NotNull(message = "子奖品数量不能为空")
    @ExcelProperty(index = 10, value = "子奖品数量")
    @ApiModelProperty(value = "子奖品数量", required = true)
    private Integer subPrizeAmount;

}
