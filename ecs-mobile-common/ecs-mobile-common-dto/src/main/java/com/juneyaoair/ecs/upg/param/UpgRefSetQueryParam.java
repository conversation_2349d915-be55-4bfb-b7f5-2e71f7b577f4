package com.juneyaoair.ecs.upg.param;

import com.juneyaoair.ecs.manage.dto.base.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 票证查询
 * @created 2023/9/21 13:30
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class UpgRefSetQueryParam extends PageBase {

    @ApiModelProperty(value = "票证类型 I：国际,D：国内")
    private String ticketType;

    @ApiModelProperty(value = "起始票证号")
    private String startTicketNo;

    @ApiModelProperty(value = "截止票证号")
    private String endTicketNo;

}
