package com.juneyaoair.ecs.upg.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @Author: caolei
 * @Description: 升舱价格查询请求参数
 * @Date:Created in 14:01 2023/09/06
 * @Modified by:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryUpgPriceParam {

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @NotBlank(message = "航班日期不能为空")
    @ApiModelProperty(value = "航班日期 格式：yyyy-MM-dd", required = true)
    private String flightDate;

    @NotBlank(message = "升舱类型不能为空")
    @ApiModelProperty(value = "升舱类型 ENUM_UPG_KIND", required = true)
    private String upgType;

    @NotBlank(message = "出发机场不能为空")
    @ApiModelProperty(value = "出发机场", required = true)
    private String depAirportCode;

    @NotBlank(message = "到达机场不能为空")
    @ApiModelProperty(value = "到达机场", required = true)
    private String arrAirportCode;

    @NotBlank(message = "币种不能为空")
    @ApiModelProperty(value = "币种", required = true)
    private String currency;

}
