package com.juneyaoair.ecs.upg.enums;

/**
 * <AUTHOR>
 * @Description 升舱航班时刻
 * @created 2023/8/22 9:27
 */
public enum UPG_FLIGHT_TIME_ENUM {
    /** flightTime */
    A("00:00-06:00"),
    B("06:00-12:00"),
    C("12:00-18:00"),
    D("18:00-24:00"),
    ;

    private final String flightTime;

    UPG_FLIGHT_TIME_ENUM(String flightTime) {
        this.flightTime = flightTime;
    }

    public String getFlightTime() {
        return flightTime;
    }

    public static UPG_FLIGHT_TIME_ENUM getEnum(String flightTime) {
        if (null == flightTime) {
            return null;
        }
        for (UPG_FLIGHT_TIME_ENUM upgFlightTimeEnum : UPG_FLIGHT_TIME_ENUM.values()) {
            if (upgFlightTimeEnum.getFlightTime().equals(flightTime)) {
                return upgFlightTimeEnum;
            }
        }
        return null;
    }
}
