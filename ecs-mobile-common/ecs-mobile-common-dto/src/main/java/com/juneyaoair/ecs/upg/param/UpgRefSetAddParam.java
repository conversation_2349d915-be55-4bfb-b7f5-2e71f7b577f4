package com.juneyaoair.ecs.upg.param;

import com.juneyaoair.ecs.valid.EnumValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @Description 添加票证
 * @created 2023/9/21 13:30
 */
@Data
public class UpgRefSetAddParam {

    @NotBlank(message = "票证类型不能为空")
    @EnumValue(value = {"D", "I"}, message = "票证类型格式不正确")
    @ApiModelProperty(value = "票证类型 I：国际,D：国内")
    private String flightType;

    @NotBlank(message = "票证类型不能为空")
    @ApiModelProperty(value = "票证类型(依据字典ref_type_info)")
    private String ticketType;

    @ApiModelProperty(value = "起始票证号")
    @NotBlank(message = "起始票证号不能为空")
    @Size(min = 13,max = 13,message = "起始票证号长度为13位")
    private String startTicketNo;

    @ApiModelProperty(value = "截止票证号")
    @NotBlank(message = "截止票证号不能为空")
    @Size(min = 13,max = 13,message = "截止票证号长度为13位")
    private String endTicketNo;
}
