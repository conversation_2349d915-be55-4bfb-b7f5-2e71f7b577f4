package com.juneyaoair.ecs.http.service;

import com.alibaba.fastjson2.JSON;
import com.juneyaoair.ecs.http.bean.HttpResult;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.http.client.pool.PoolManagerHttpClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2023/3/24  17:10
 */
@Component
@Slf4j
public class HttpClientService {

    @Autowired
    private PoolManagerHttpClient poolManagerHttpClient;

    private static final String SERVICE_CHARSET = "utf-8";

    /**
     * 发送json的post请求 基于gson转字符串
     * @param req
     * @param url
     * @param headMap
     * @return
     * @throws IOException
     */
    public HttpResult doPostJson(Object req, String url, Map<String, String> headMap) {
        String param;
        if (req instanceof String) {
            param = (String) req;
        } else {
            param = JsonUtil.objectToJson(req);
        }
        StringEntity s = new StringEntity(param, SERVICE_CHARSET);
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(s);
        httpPost.setHeader("Content-Type", "application/json;charset=utf-8");
        if (headMap != null && !headMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headMap.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        //发送http请求
        return executeHttp(httpPost, param);
    }

    /**
     * 发送json的post请求 基于fastjson转字符串
     * @param req
     * @param url
     * @param headMap
     * @return
     * @throws IOException
     */
    public HttpResult doPost(Object req, String url, Map<String, String> headMap) {
        String param;
        if (req instanceof String) {
            param = (String) req;
        } else {
            param = JSON.toJSONString(req);
        }
        StringEntity s = new StringEntity(param, SERVICE_CHARSET);
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(s);
        httpPost.setHeader("Content-Type", "application/json;charset=utf-8");
        if (headMap != null && !headMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headMap.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        //发送http请求
        return executeHttp(httpPost, param);
    }

    /**
     * 发送Get请求
     * @param url
     * @param headMap
     * @return
     * @throws IOException
     */
    public HttpResult doGet(String url, Map<String, String> headMap) {
        HttpGet httpGet = new HttpGet(url);
        if (headMap != null && !headMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headMap.entrySet()) {
                httpGet.setHeader(entry.getKey(), entry.getValue());
            }
        }
        //发送http请求
        return executeHttp(httpGet, "");
    }

    /**
     * 发送Delete请求
     * @param url
     * @param headMap
     * @return
     * @throws IOException
     */
    public HttpResult doDelete(String url, Map<String, String> headMap) {
        HttpDelete httpDelete = new HttpDelete(url);
        if (headMap != null && !headMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headMap.entrySet()) {
                httpDelete.setHeader(entry.getKey(), entry.getValue());
            }
        }
        //发送http请求
        return executeHttp(httpDelete, "");
    }

    private HttpResult executeHttp(HttpRequestBase httpRequestBase, String param){
        long t1 = System.currentTimeMillis();
        log.info("时间戳:{},请求的地址:{}，请求的参数：{}", t1, httpRequestBase.getURI(), param);
        HttpResult httpResult = new HttpResult();
        CloseableHttpClient httpClient = poolManagerHttpClient.getCloseableHttpClient();
        //调用httpResponse.close(); 释放回连接池,否则一直占用
        try (CloseableHttpResponse httpResponse = httpClient.execute(httpRequestBase)) {
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (HttpStatus.SC_OK != statusCode) {
                httpResult.setResult(false);
                httpResult.setResponse("服务器内部错误(" + statusCode + ")");
                httpResult.setServerCode("" + statusCode);
            } else {
                String responseString;
                httpResult.setResult(true);
                HttpEntity entity = httpResponse.getEntity();
                if (entity != null) {
                    responseString = EntityUtils.toString(entity, SERVICE_CHARSET);
                } else {
                    responseString = "";
                }
                httpResult.setResponse(responseString);
                httpResult.setServerCode("" + statusCode);
            }
            log.info("时间戳:{},请求的地址:{}，响应耗时:{}ms,状态码:{},返回的结果:{}", t1, httpRequestBase.getURI(), System.currentTimeMillis() - t1, statusCode, httpResult.getResponse());
            httpRequestBase.releaseConnection();
            return httpResult;
        }catch (Exception e){
            log.error("时间戳:{},请求的地址:{}，异常信息:", t1, httpRequestBase.getURI(),e);
            httpResult.setResult(false);
            httpResult.setResponse("网络请求发生异常，请稍候再试！");
            httpResult.setServerCode("500");
        }
        return httpResult;
    }
}
