package com.juneyaoair.ecs.http.client.pool;

import com.juneyaoair.http.client.pool.PoolManagerHttpClient;
import com.juneyaoair.http.client.pool.PoolManagerHttpClientParamsBuilder;
import com.juneyaoair.http.client.pool.PoolManagerHttpClientTimeParams;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by yaocf on 2023/3/23  15:00.
 */
@Configuration
@Data
public class HttpClientPoolConfig {
    @Value("${http.client.pool.keepAliveTimeToLive:120}")
    private  int keepAliveTimeToLive = 120;
    /**
     * 设置连接器最多同时支持链接数 sum(maxConnPreRoute) <= maxConnections
     */
    @Value("${http.client.pool.maxConnections:1000}")
    private  int maxConnections;

    /**
     *设置每个路由最多支持个链接数(url中的协议部分+主机或域名部分+端口称之为一个路由，也可以认为是Set中的Key)
     */
    @Value("${http.client.pool.maxConnPreRoute:100}")
    private  int maxConnPreRoute;
    /**
     * 从池中获取连接超时时间
     */
    @Value("${http.client.pool.connectionRequestTimeout:500}")
    private  int connectionRequestTimeout;
    @Value("${http.client.pool.connectTimeout:2000}")
    /**
     * 连接超时时间
     */
    private  int connectTimeout;
    /**
     * 读超时时间（等待数据超时时间）
     */
    @Value("${http.client.pool.socketTimeout:15000}")
    private  int socketTimeout;
    /**
     * Pool中最长空闲时间
     */
    @Value("${http.client.pool.connMaxIdleTime:120000}")
    private  int connMaxIdleTime;

    @Bean
    public PoolManagerHttpClient poolManagerHttpClient(){
        PoolManagerHttpClientParamsBuilder builder = new PoolManagerHttpClientParamsBuilder();
        builder.setMaxConnections(maxConnections);
        builder.setMaxConnPreRoute(maxConnPreRoute);
        builder.setConnectionRequestTimeout(connectionRequestTimeout);
        builder.setConnectTimeout(connectTimeout);
        builder.setSocketTimeout(socketTimeout);
        builder.setConnMaxIdleTime(connMaxIdleTime);
        PoolManagerHttpClientTimeParams poolManagerHttpClientTimeParams = builder.build();
        return new PoolManagerHttpClient(poolManagerHttpClientTimeParams);
    }

}
