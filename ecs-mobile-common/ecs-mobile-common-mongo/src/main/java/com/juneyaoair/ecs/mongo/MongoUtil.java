package com.juneyaoair.ecs.mongo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class MongoUtil {

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 保存或更新对象
     */
    public <T> T saveOrUpdate(T entity) {
        return mongoTemplate.save(entity);
    }

    /**
     * 根据ID查询
     */
    public <T> T findById(String id, Class<T> entityClass) {
        return mongoTemplate.findById(id, entityClass);
    }

    /**
     * 根据条件查询单个对象
     */
    public <T> T findOne(Query query, Class<T> entityClass) {
        return mongoTemplate.findOne(query, entityClass);
    }

    /**
     * 根据条件查询列表
     */
    public <T> List<T> find(Query query, Class<T> entityClass) {
        return mongoTemplate.find(query, entityClass);
    }

    /**
     * 查询所有
     */
    public <T> List<T> findAll(Class<T> entityClass) {
        return mongoTemplate.findAll(entityClass);
    }

    /**
     * 根据条件分页查询
     */
    public <T> List<T> findByPage(Query query, Class<T> entityClass, int pageNum, int pageSize) {
        query.skip((pageNum - 1) * pageSize).limit(pageSize);
        return mongoTemplate.find(query, entityClass);
    }

    /**
     * 根据条件更新
     */
    public <T> void update(Query query, Update update, Class<T> entityClass) {
        mongoTemplate.updateMulti(query, update, entityClass);
    }

    /**
     * 根据条件更新第一条记录
     */
    public <T> void updateFirst(Query query, Update update, Class<T> entityClass) {
        mongoTemplate.updateFirst(query, update, entityClass);
    }

    /**
     * 根据ID删除
     */
    public <T> void deleteById(String id, Class<T> entityClass) {
        Query query = new Query(Criteria.where("id").is(id));
        mongoTemplate.remove(query, entityClass);
    }

    /**
     * 根据条件删除
     */
    public <T> void delete(Query query, Class<T> entityClass) {
        mongoTemplate.remove(query, entityClass);
    }

    /**
     * 获取集合记录数
     */
    public <T> long count(Query query, Class<T> entityClass) {
        return mongoTemplate.count(query, entityClass);
    }

    /**
     * 根据Map构建Query对象
     */
    public Query buildQuery(Map<String, Object> params) {
        Query query = new Query();
        if (params != null && !params.isEmpty()) {
            params.forEach((key, value) -> {
                if (value != null) {
                    query.addCriteria(Criteria.where(key).is(value));
                }
            });
        }
        return query;
    }

    /**
     * 根据Map构建Update对象
     */
    public Update buildUpdate(Map<String, Object> params) {
        Update update = new Update();
        if (params != null && !params.isEmpty()) {
            params.forEach((key, value) -> {
                if (value != null) {
                    update.set(key, value);
                }
            });
        }
        return update;
    }
}