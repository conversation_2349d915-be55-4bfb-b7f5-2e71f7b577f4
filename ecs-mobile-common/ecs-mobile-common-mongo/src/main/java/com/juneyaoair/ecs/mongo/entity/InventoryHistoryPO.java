package com.juneyaoair.ecs.mongo.entity;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/8 13:23
 */
@Data
@Document(collection = "inventoryHistoryPO")
public class InventoryHistoryPO {
    private String airline;
    private int flightNumber;
    private String flightSuffix;
    //组合后的航班号
    private String flightNo;
    private String flightDate;
    private String historyId;
    //代理人所属航司
    private String agentAirline;
    //代理人名称
    private String agentName;
    //代理人工号
    private String agentOffice;
    //操作类型
    private String opType;
    //操作时间
    private String opTime;
    private String operation;
    private String remark;
    //数据插入批次时间
    private String createTime;
}
