package com.juneyaoair.ecs.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/16 14:37
 */
@Slf4j
public class VersionNoUtilTest {

    @Test
    void toVerInt() {
        log.error("7.9.2-->{}",VersionNoUtil.toVerInt("7.9.2"));
        log.error("7.10.0-->{}",VersionNoUtil.toVerInt("7.10.0"));
        log.error("7.10.20-->{}",VersionNoUtil.toVerInt("7.10.20"));
        log.error("7.10.200-->{}",VersionNoUtil.toVerInt("7.10.200"));
        log.error("7.10.2000-->{}",VersionNoUtil.toVerInt("7.10.2000"));
        log.error("7.11.2000-->{}",VersionNoUtil.toVerInt("7.11.2000"));
        log.error("8.0.0-->{}",VersionNoUtil.toVerInt("8.0.0"));
    }

    @Test
    void toVerStr(){
        log.error("7090002-->{}",VersionNoUtil.toVersionStr("7090002"));
        Assert.assertEquals("7.9.2", VersionNoUtil.toVersionStr("7090002"));
        log.error("7100000-->{}",VersionNoUtil.toVersionStr("7100000"));
        Assert.assertEquals("7.10.0", VersionNoUtil.toVersionStr("7100000"));
        log.error("7112000-->{}",VersionNoUtil.toVersionStr("7112000"));
        Assert.assertEquals("7.11.2000", VersionNoUtil.toVersionStr("7112000"));
        log.error("8000000-->{}",VersionNoUtil.toVersionStr("8000000"));
        Assert.assertEquals("8.0.0", VersionNoUtil.toVersionStr("8000000"));
    }

    @Test
    void toVerStr2(){
        log.error("7112000-->{}",VersionNoUtil.toVersionStr("7112000"));
        Assert.assertEquals("7.11.2000", VersionNoUtil.toVersionStr("7112000"));
    }

    @Test
    public void compareVersions() {
        int c = VersionNoUtil.compareVersions("7.5.6","7.2");
        log.error("比较大小结果:{}",c);
        Assert.assertEquals(1,c);
    }

    @Test
    public void compareVersions2() {
        int c = VersionNoUtil.compareVersions("7.5.6","7.2.0.1");
        log.error("比较大小结果:{}",c);
        Assert.assertEquals(1,c);
    }

    @Test
    public void compareVersions3() {
        int c = VersionNoUtil.compareVersions("7.5.6","7.4.0");
        log.error("比较大小结果:{}",c);
        Assert.assertEquals(1,c);
    }

    @Test
    void compareVersions4() {
        int c = VersionNoUtil.compareVersions("7.5.6","7.5.6.1");
        log.error("比较大小结果:{}",c);
        Assert.assertEquals(-1,c);
    }
}