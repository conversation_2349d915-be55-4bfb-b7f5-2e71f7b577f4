package com.juneyaoair.ecs.utils;

import org.junit.jupiter.api.Test;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.*;

class DateUtilTest {

    @Test
    void getTimestamp() {
        Timestamp timestamp = DateUtil.getTimestamp();
        assertNotNull(timestamp);
        System.out.println(timestamp);
    }

    @Test
    void convertDate2Str() {
        String s  = DateUtil.convertDate2Str(new Date(),DateUtil.DATE_FORMATE_DD_MMM_YYYY, Locale.ENGLISH);
        assertNotNull(s);
        System.out.println(s);
    }
}