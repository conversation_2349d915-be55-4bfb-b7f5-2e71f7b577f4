package com.juneyaoair.ecs.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DateTimeException;
import java.util.*;

/**
 * <AUTHOR>
 * @description 日期工具类
 * @date 2023/5/8 10:48
 */
@Slf4j
public class DateUtil {
    public static final String DATE_FORMATE = "yyyy-MM-dd";
    public static final String DATE_FORMATE_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMATE_YYYY_MM_DD = "yyyy-MM-dd";
    public static final String DATE_FORMATE_YYYY_MM_CHINA = "yyyy年MM月dd日";
    public static final String DATE_FORMATE_DD_MMM_YYYY = "dd MMM yyyy";
    public static final String DATE_FORMATE_YYYYMM = "yyyyMM";


    /**
     * 根据时间范围，输出明细日期
     *
     * @param beginDateStr
     * @param endDateStr
     * @return
     */
    public static List<String> getDateByTimeRange(String beginDateStr, String endDateStr) {
        List dateList = new ArrayList();
        try {
            Date beginDate = new SimpleDateFormat(DATE_FORMATE).parse(beginDateStr);
            Date endDate = new SimpleDateFormat(DATE_FORMATE).parse(endDateStr);

            dateList.add(new SimpleDateFormat(DATE_FORMATE).format(beginDate));
            Calendar beginCalendar = Calendar.getInstance();
            // 使用给定的 Date 设置此 Calendar 的时间
            beginCalendar.setTime(beginDate);
            Calendar endCalendar = Calendar.getInstance();
            // 使用给定的 Date 设置此 Calendar 的时间
            endCalendar.setTime(endDate);
            // 测试此日期是否在指定日期之后
            while (endDate.after(beginCalendar.getTime())) {
                // 根据日历的规则,为给定的日历字段添加或减去指定的时间量
                beginCalendar.add(Calendar.DAY_OF_MONTH, 1);
                dateList.add(new SimpleDateFormat(DATE_FORMATE).format(beginCalendar.getTime()));
            }
        } catch (Exception e) {
            return null;
        }
        return dateList;
    }

    /**
     * 将date类型转换为指定的日期字符串格式
     *
     * @param date
     * @param format
     * @return
     */
    public static String convertDate2Str(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    /**
     * 将date类型转换为指定的日期字符串格式
     *
     * @param date
     * @param format
     * @param locale 语言格式
     * @return
     */
    public static String convertDate2Str(Date date, String format,Locale locale) {
        SimpleDateFormat sdf = new SimpleDateFormat(format, locale);
        return sdf.format(date);
    }

    /**
     * 字符串转日期
     *
     * @param str    待转换的字符串
     * @param format 转换成的格式
     * @return
     */
    public static Date StringToDate(String str, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            return sdf.parse(str);
        } catch (ParseException e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 系统当前的时间戳
     */
    public static Timestamp getTimestamp() {
        return new Timestamp(new Date().getTime());
    }

    /**
     * time format:yyyyMMddHHmmss
     */
    public static String getCurrentTimeStr() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * date format:yyyy-MM-dd
     */
    public static String getCurrentDateStr() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = formatter.format(currentTime);
        return dateString;
    }
    /**
     * date format:yyyy-MM-dd HH:mm:ss
     */
    public static String getDateStringAllDate(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(date);

    }

    /**
     * <AUTHOR>
     * @Description 两个时间差，单位天数(包含当天)
     * @Date 14:03 2025/3/17
     **/
    public static int dateDiff(String startTime, String endTime, String format) {
        SimpleDateFormat sd = new SimpleDateFormat(format);
        long diff;
        try {
            diff = sd.parse(endTime).getTime() - sd.parse(startTime).getTime();
        } catch (ParseException e) {
            return 0;
        }
        return (int) (diff / (1000 * 24 * 60 * 60) + 1);
    }

    /**
     * <AUTHOR>
     * @Description 两个时间差，单位天数（不包含当天）
     * @Date 14:03 2025/3/17
     **/
    public static int dateDiff(Date startTime, Date endTime) {
        try {
            long diff = startTime.getTime() - endTime.getTime();
            return (int) (diff / (1000 * 24 * 60 * 60));
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * @param dateString
     * @return java.util.Date
     * <AUTHOR>
     * @Description 当只有日期信息时 填补上开始时间 00:00:00
     * @Date 14:51 2023/7/31
     **/
    public static Date toDatePlusBeginTime(String dateString) {
        if (StringUtils.isNotEmpty(dateString)) {
            dateString = dateString + " 00:00:00";
        }
        return toDate(dateString, DATE_FORMATE_YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * @param dateString
     * @return java.util.Date
     * <AUTHOR>
     * @Description 当只有日期信息时 填补上结束时间 23:59:59
     * @Date 14:53 2023/7/31
     **/
    public static Date toDatePlusEndTime(String dateString) {
        if (StringUtils.isNotEmpty(dateString)) {
            dateString = dateString + " 23:59:59";
        }
        return toDate(dateString, DATE_FORMATE_YYYY_MM_DD_HH_MM_SS);
    }


    /**
     * <AUTHOR>
     * @Description Convert String to Date according to the format string
     * @Date 10:32 2025/3/18
     **/
    public static Date toDate(String dateString, String format) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(format);
            return formatter.parse(dateString);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * <AUTHOR>
     * @Description 计算日期是否重叠
     * @param s1
     * @param e1
     * @param s2
     * @param e2
     */
    public static boolean isOverlap(Date s1, Date e1, Date s2, Date e2) {
        if (s1.after(e1) || s2.after(e2)) {
            throw new DateTimeException("endDate不能小于startDate");
        }

        if (s1.compareTo(e2) >0 || e1.compareTo(s2) < 0) {
            return false;
        }

        return true;
    }

}
