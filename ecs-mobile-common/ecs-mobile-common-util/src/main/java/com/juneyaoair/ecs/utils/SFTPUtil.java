package com.juneyaoair.ecs.utils;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Properties;
import java.util.Vector;

/**
 * Created by yaocf on 2017/10/9.
 * sftp相关的工具方法
 */
@Slf4j
public class SFTPUtil {
    Session sshSession = null;
    ChannelSftp sftp = null;

    public Session getSshSession() {
        return sshSession;
    }

    public void setSshSession(Session sshSession) {
        this.sshSession = sshSession;
    }

    public ChannelSftp getSftp() {
        return sftp;
    }

    public void setSftp(ChannelSftp sftp) {
        this.sftp = sftp;
    }


    /**
     * 获取连接
     * @param host
     * @param port
     * @param username
     * @param password
     * @return
     * @throws Exception
     */
    public  ChannelSftp connect(String host, int port, String username, String password) throws Exception {
        JSch jsch = new JSch();
        //获取包含主机,用户名,和端口的sshSession
        sshSession = jsch.getSession(username, host, port);
        //设置密码
        sshSession.setPassword(password);
        Properties sshConfig = new Properties();
        //不需要加密证书
        sshConfig.put("StrictHostKeyChecking", "no");
        sshSession.setConfig(sshConfig);
        //连接sshSession
        sshSession.connect();
        Channel channel = sshSession.openChannel("sftp");
        channel.connect();
        sftp = (ChannelSftp) channel;
        log.info("获取sftp连接成功！");
        return sftp;
    }

    /**
     * 进入指定的目录并设置为当前目录
     *
     * @param sftpPath
     * @throws Exception
     */
    private void cd(String sftpPath) throws SftpException {
        sftp.cd(sftpPath);
    }

    /**
     * 断开连接
     */
    public void closeChannel(){
        try {
            if (sftp != null && sftp.isConnected()) {
                sftp.disconnect();
            }
            if (sshSession != null && sshSession.isConnected()) {
                sshSession.disconnect();
            }
            log.info("sftp断开连接成功！");
        }catch (Exception e){
            log.error("sftp断开连接异常！");
        }
    }

    /**
     * sftp上传文件  文件数据流
     * @param is 上传文件的数据流
     * @param directory 上传文件的目录
     * @param fileName 上传文件的名称
     * @return
     */
    public  boolean upload(InputStream is, String directory, String fileName){
         //上传图片到SFTP服务器
        try {
            //查看目录是否存在，不存在则创建目录
            createDir(directory);
            sftp.cd(directory);
            sftp.put(is, fileName);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * sftp上传文件 文件路径
     * @param  filePath  文件所处的路径
     * @param  directory  文件需要上传至的目录
     * @return
     */
    public  boolean upload(String filePath,String directory,String fileName){
         File file = new File(filePath);
         if(file.exists()){
             return upload(file,directory,fileName);
         }else{
             return false;
         }
    }

    /**
     * sftp上传文件  File文件
     * @param file
     * @param directory 文件需要上传至的目录
     * @return
     */
    public boolean upload(File file,String directory,String fileName){
        //查看目录是否存在，不存在则创建目录
        try(FileInputStream fileInputStream = new FileInputStream(file)){
            return upload(fileInputStream,directory,fileName);
        }catch (Exception e) {
            return false;
        }
    }

    /**
     * 创建远程目录
     *
     * @param sftpDirPath
     * @return 返回创建成功或者失败的代码和信息
     * @throws SftpException
     */
    public  String createDir(String sftpDirPath) throws SftpException {
        cd("/");
        if (isDirExist(sftpDirPath)) {
            return "0:dir  is  exist  !";
        }
        String pathArry[] = sftpDirPath.split("/");
        for (String path : pathArry) {
            if ("".equals(path)) {
                continue;
            }
            if (isDirExist(path)) {
                cd(path);
            } else {
                //建立目录
                sftp.mkdir(path);
                //进入并设置为当前目录
                sftp.cd(path);
            }
        }
        cd("/");
        return "1:创建目录成功";
    }

    /**
     * 判断文件夹是否存在
     *
     * @param directory
     * @return
     * @throws SftpException
     */
    private  boolean isDirExist(String directory) throws SftpException {
        boolean isDirExistFlag = false;
        try {
            SftpATTRS sftpAttrs = sftp.lstat(directory);
            isDirExistFlag = true;
            return sftpAttrs.isDir();
        } catch (Exception e) {
            if ("no such file".equals(e.getMessage().toLowerCase())) {
                isDirExistFlag = false;
            }
        }
        return isDirExistFlag;
    }
    /**
     * 判断某个文件是否存在
     *
     * @param filePath  文件的全路径
     * @return
     * @throws SftpException
     */
    public  boolean isFileExist(String filePath){
        boolean isFileExistFlag = false;
        try {
            sftp.stat(filePath);
            isFileExistFlag = true;
        }catch (SftpException sf){
            isFileExistFlag = false;
        }catch (Exception e) {
            if ("no such file".equals(e.getMessage().toLowerCase())) {
                isFileExistFlag = false;
            }
        }
        return isFileExistFlag;
    }

    /**
     * 删除文件
     * @param filePath
     */
    public void deleteFile(String filePath){
        Vector<ChannelSftp.LsEntry> vector = null;
        try {
            vector = sftp.ls(filePath);
            // 文件，直接删除
            if (vector.size() == 1) {
                sftp.rm(filePath);
            } else {
            }
        } catch (SftpException e) {
            log.error(e.getMessage());
        }
    }
}
