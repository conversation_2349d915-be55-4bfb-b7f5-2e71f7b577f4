package com.juneyaoair.ecs.utils;


import org.apache.commons.lang3.StringUtils;

//版本比较处理
public class VersionNoUtil {
    //版本第一位步长
    private final static int major = 1000000;
    //版本第二位步长 目前第二位只能是两位
    private final static int minor = 10000;

    /**
     * 手机版本调整为数值型
     *
     * @param versionNo
     * @return
     */
    public static int toVerInt(String versionNo) {
        int verInt = 0;
        try {
            if (StringUtils.isEmpty(versionNo)) {
                verInt = 0;
            } else {
                String[] vers = versionNo.split("\\.");
                for (int i = 0; i < vers.length; i++) {
                    String ver = vers[i];
                    if (i == 0) {//第一位
                        verInt = Integer.parseInt(ver) * major;
                    } else if (i == 1) {//中间
                        verInt = verInt + Integer.parseInt(ver) * minor;
                    } else if (i == 2) {//末尾
                        verInt = verInt + Integer.parseInt(ver);
                    }
                }
            }
        } catch (Exception e) {
            verInt = 0;
        }
        return verInt;
    }

    /**
     * 手机数值版本变换为5.0.0版本格式
     *
     * @param versionNo
     * @return
     */
    public static String toVersionStr(String versionNo) {
        String versionStr = "0.0.0";
        if (StringUtils.isBlank(versionNo)) {
            return versionStr;
        }
        try {
            int versionInt = Integer.parseInt(versionNo);
            //取整，取出主版本号
            int majorInt = versionInt / major;
            //取整，取出次版本号
            int minorInt = (versionInt - major * majorInt)/minor;
            int revisionInt = versionInt - major * majorInt - minor * minorInt;
            return majorInt + "." + minorInt + "." + revisionInt;
        } catch (Exception e) {
            versionStr = "0.0.0";
        }
        return versionStr;
    }

    /**
     * 比较两个版本号的大小
     *
     * @param version1 第一个版本号
     * @param version2 第二个版本号
     * @return 如果 version1 < version2，则返回负数；如果 version1 > version2，则返回正数；如果相等，则返回0
     */
    public static int compareVersions(String version1, String version2) {
        String[] parts1 = version1.split("\\.");
        String[] parts2 = version2.split("\\.");
        int length = Math.max(parts1.length, parts2.length);
        for (int i = 0; i < length; i++) {
            int num1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
            int num2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;
            if (num1 < num2) {
                return -1;
            } else if (num1 > num2) {
                return 1;
            }
        }
        return 0;
    }

}
