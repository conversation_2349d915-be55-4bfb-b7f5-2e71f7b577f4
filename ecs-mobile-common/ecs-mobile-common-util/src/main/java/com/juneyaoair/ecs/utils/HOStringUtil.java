package com.juneyaoair.ecs.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description 字符串常用工具类
 * @date 2023/5/10 13:56
 */
public class HOStringUtil extends StringUtils {

    public static String newGUID() {
        UUID uuid = UUID.randomUUID();
        return uuid.toString().replace("-", "");
    }

    /**
     * 字符串首字母自动转大写
     * @param str
     * @return
     */
    public static String capitalizeFirstLetter(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        char firstChar = str.charAt(0);
        char capitalizedChar = Character.toUpperCase(firstChar);
        String rest = str.length() > 1 ? str.substring(1) : "";
        return capitalizedChar + rest;
    }

    public static boolean isNullOrEmpty(List list) {
        return list == null || list.size() == 0;
    }
}
