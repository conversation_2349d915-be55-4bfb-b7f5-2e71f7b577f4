package com.juneyaoair.ecs.utils;


import com.juneyaoair.ecs.manage.enums.EnvEnum;

/**
 * @description 系统工具类
 */
public class SystemUtil {
    /**
     * 判断当前操作系统是否是Windows
     *
     * @return
     */
    public static boolean isWin() {
        try {
            String os = System.getProperty("os.name");
            if (os.toLowerCase().startsWith("win")) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    //生产环境不做处理
    public static String envRedisDir() {
        if (EnvEnum.PRO == SpringContextUtil.ENV || SpringContextUtil.ENV == null) {
            return "";
        } else {
            return (SpringContextUtil.ENV + ":").toLowerCase();
        }
    }
}
