package com.juneyaoair.ecs.utils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @description 图片增加水印工具类
 * @date 2024/7/8 10:08
 */
public class ImageWatermarkUtil {
    /**
     * 给图片添加图片水印
     *
     * @param inputImg         原始图片文件流
     * @param watermarkImgStream 水印图片文件流
     * @param alpha            水印图片的透明度（0.0-1.0）
     * @throws IOException
     */
    public static InputStream addImageWatermark(InputStream inputImg, InputStream watermarkImgStream, float alpha) throws IOException {
        BufferedImage srcImg = ImageIO.read(inputImg);
        BufferedImage watermarkImg = ImageIO.read(watermarkImgStream);
        // 创建一个与原始图片相同大小的图像缓冲区
/*        BufferedImage bufferedImage = new BufferedImage(
                srcImg.getWidth(),
                srcImg.getHeight(),
                srcImg.getType());*/
        // 将原始图片复制到缓冲区
        //Graphics2D g2d = bufferedImage.createGraphics();
        Graphics2D g2d = (Graphics2D) srcImg.getGraphics();
        // 设置透明度
        AlphaComposite alphaChannel = AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha);
        g2d.setComposite(alphaChannel);
        // 距离右侧边缘10像素
        int x = srcImg.getWidth() - watermarkImg.getWidth() - 1;
        // 距离底部边缘10像素
        int y = srcImg.getHeight() - watermarkImg.getHeight() - 1;
        // 绘制水印图片
        // 设置水印图片的位置和大小
        g2d.drawImage(watermarkImg, x, y, null);
        // 释放资源
        g2d.dispose();
        // 写入文件
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ImageIO.write(srcImg, "png", os);
        InputStream newIs = new ByteArrayInputStream(os.toByteArray());
        return newIs;
    }
}
