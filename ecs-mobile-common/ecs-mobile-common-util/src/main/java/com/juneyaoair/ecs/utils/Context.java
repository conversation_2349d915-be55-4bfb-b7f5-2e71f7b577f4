package com.juneyaoair.ecs.utils;


import java.util.concurrent.ConcurrentHashMap;

/**
 * 用于装取线程内数据
 * 暂时不支持多线程
 */
public class Context {

    public Context() {
        this(HOStringUtil.newGUID());
    }

    public Context(String id) {
        _id = id;
        _attr = new ConcurrentHashMap<>();
    }

    public static void setContext(Context context) {
        s_current.set(context);
    }

    public static void remove() {
        s_current.remove();
    }

    public static Context getContext() {
        return s_current.get();
    }


    public String getId() {
        return _id;
    }

    private final String _id;
    private static final ThreadLocal<Context> s_current = new ThreadLocal<>();
    private ConcurrentHashMap<String, Object> _attr;
}