
/**
 * @标题: SpringContextUtil.java
 * @包名： com.cares.mcp.util
 * @功能描述：TODO
 * @作者： jason
 * @创建时间： 2014年10月6日 下午11:42:34
 * @version v0.0.1
 */
package com.juneyaoair.ecs.utils;

import com.juneyaoair.ecs.manage.enums.EnvEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;


/**
 * 用于从spring context取bean
 */
@Component
public class SpringContextUtil implements ApplicationContextAware {

    public static EnvEnum ENV;
    //Spring应用上下文环境
//	@Resource
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext)
            throws BeansException {
        SpringContextUtil.applicationContext = applicationContext;
        ENV = getActiveProfile();
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    public static Object getBean(String name) throws BeansException {
        return applicationContext.getBean(name);
    }

    public static <T> T getBean(String name, Class<T> requiredType) throws BeansException {
        return applicationContext.getBean(name, requiredType);
    }

    public static EnvEnum getActiveProfile() {
        String[] activeProfiles = applicationContext.getEnvironment().getActiveProfiles();
        return activeProfiles.length > 0 ? EnvEnum.getEnumByEnv(activeProfiles[0]) : null;
    }
}
