<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ruoyi</groupId>
        <artifactId>ruoyi</artifactId>
        <version>3.6.1-SNAPSHOT</version>
    </parent>
    <modules>
        <module>ecs-mobile-common</module>
        <module>ecs-mobile-modules</module>
        <module>ecs-mobile-third-api</module>
    </modules>
    <groupId>com.juneyaoair.ecs</groupId>
    <artifactId>ecs-mobile-manage</artifactId>
    <version>${revision}</version>
    <name>ecs-mobile-manage</name>
    <url>http://www.juneyaoair.com</url>
    <description>新一代管理后台</description>
    <packaging>pom</packaging>
    <!--属性版本声明-->
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <revision>1.0.0</revision>
        <oracle.driver.version>12.0.2</oracle.driver.version>
        <mysql.connect.version>8.0.28</mysql.connect.version>
        <dynamic-ds.version>3.5.1</dynamic-ds.version>
        <gson.version>2.6.2</gson.version>
        <hutool.version>4.6.8</hutool.version>
        <guava.version>20.0</guava.version>
        <jsch.version>0.1.54</jsch.version>
        <ho-client-pool.version>1.0.0-SNAPSHOT</ho-client-pool.version>
        <jexcelapi.version>2.6.12</jexcelapi.version>
        <easyexcel.version>3.3.2</easyexcel.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <!--<mybatis-plus.version>3.3.2</mybatis-plus.version>-->
        <!--配置私服地址 -->
        <releases.repo>http://nexus.juneyaoair.com:8081/repository/maven-releases/</releases.repo>
        <snapshots.repo>http://nexus.juneyaoair.com:8081/repository/maven-snapshots/</snapshots.repo>
    </properties>

    <repositories>
        <repository>
            <id>nexus</id>
            <name>nexus</name>
            <url>http://nexus.juneyaoair.com:8081/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <!--声明依赖管理-->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc7</artifactId>
                <version>${oracle.driver.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connect.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-ds.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>${jsch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <scope>provided</scope>
                <version>${mapstruct.version}</version>
            </dependency>
            <!--以下为HO公有组件-->
            <dependency>
                <groupId>com.juneyaoair.http</groupId>
                <artifactId>ho-client-pool</artifactId>
                <version>${ho-client-pool.version}</version>
            </dependency>
            <!--以下为项目自有模块引用-->
            <dependency>
                <groupId>com.juneyaoair.ecs</groupId>
                <artifactId>ecs-mobile-common-security</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.ecs</groupId>
                <artifactId>ecs-mobile-common-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.ecs</groupId>
                <artifactId>ecs-mobile-common-dto</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.ecs</groupId>
                <artifactId>ecs-mobile-common-exception</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.ecs</groupId>
                <artifactId>ecs-mobile-common-log</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.ecs</groupId>
                <artifactId>ecs-mobile-common-b2c-datasource</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.ecs</groupId>
                <artifactId>ecs-mobile-common-flight-datasource</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.ecs</groupId>
                <artifactId>ecs-mobile-common-cki-datasource</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.ecs</groupId>
                <artifactId>ecs-mobile-common-b2c2014-datasource</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.ecs</groupId>
                <artifactId>ecs-mobile-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.ecs</groupId>
                <artifactId>ecs-mobile-common-mongo</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.ecs</groupId>
                <artifactId>ecs-mobile-common-util</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.ecs</groupId>
                <artifactId>ecs-mobile-common-http</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair.ecs</groupId>
                <artifactId>ecs-mobile-third-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sourceforge.jexcelapi</groupId>
                <artifactId>jxl</artifactId>
                <version>${jexcelapi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join</artifactId>
                <version>1.4.5</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <!-- 本地开发环境 -->
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <!-- 测试环境 -->
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- 预发布环境 -->
            <id>uat</id>
            <properties>
                <profiles.active>uat</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- 生产环境 -->
            <id>prd</id>
            <properties>
                <profiles.active>prd</profiles.active>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!--打包时先排除profiles文件夹-->
                <excludes>
                    <exclude>profiles/**</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources/profiles/${profiles.active}</directory>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <fork>true</fork>
                </configuration>
            </plugin>
            <!--maven 灵活版本，多节点打包-->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.2.2</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.0.0-M3</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>


    <!-- 配置私服仓库 -->
    <distributionManagement>
        <repository>
            <!-- 注意：这里的id一定要和setting.xml文件中server下的id保持一致，下同 -->
            <id>releases</id>
            <url>${releases.repo}</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>${snapshots.repo}</url>
        </snapshotRepository>
    </distributionManagement>
</project>
