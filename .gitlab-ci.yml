workflow:
  rules:
    - if: '$CI_COMMIT_BRANCH == "release"'
      variables:
        NEXUS_URL: http://mirror.juneyaoair.com:8081/repository/UAT/${CI_PROJECT_NAME}/
        BUILD_CMD: mvn clean package -Dmaven.test.skip=true -Puat
    - if: '$CI_COMMIT_TAG'
      variables:
        NEXUS_URL: http://mirror.juneyaoair.com:8081/repository/p-release/${CI_PROJECT_NAME}/$CI_COMMIT_TAG/
        BUILD_CMD: mvn clean package -Dmaven.test.skip=true -Pprd
    - if: '$CI_COMMIT_BRANCH == "test"'


stages:
  - build
  - push
  - sonarqube-check

# 将 Nexus 仓库地址添加到 settings.xml 中
before_script:
  - echo '<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
    http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <servers>
    <server>
    <id>maven-release</id>
    <username>deploy</username>
    <password>deploy</password>
    </server>
    </servers>
    
    <mirrors>
    <mirror>
    <id>nexus-repo-mirror</id>
    <mirrorOf>*</mirrorOf>
    <url>http://nexus.juneyaoair.com:8081/repository/maven-public/</url>
    </mirror>
    </mirrors>
    </settings>'> /root/.m2/settings.xml

# 运行 Maven 编译命令
tags_trigger_build:
  image: harbor.hoair.cn/base/maven:3.9.4-eclipse-temurin-8
  stage: build
  script:
    - $BUILD_CMD
  artifacts:
    paths:
      - ecs-mobile-modules/ecs-mobile-admin/target/ecs-mobile-admin.jar
  tags:
    - build
  rules:
    - if: '$CI_COMMIT_BRANCH == "release"'
    - if: '$CI_COMMIT_TAG'

tags_trigger_push:
  image: harbor.hoair.cn/base/maven:3.9.4-eclipse-temurin-8
  stage: push
  script:
    - echo "upload artifacts"
    - find . -name "*.jar" -exec curl -u deploy:deploy --upload-file {} $NEXUS_URL \;
  allow_failure: false
  dependencies:
    - tags_trigger_build
  tags:
    - build
  rules:
    - if: '$CI_COMMIT_BRANCH == "release"'
    - if: '$CI_COMMIT_TAG'

sonarqube-check:
  stage: sonarqube-check
  image: harbor.hoair.cn/base/maven:3.6.3-jdk-11
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
    GIT_DEPTH: "0"
  script:
    - mvn verify sonar:sonar -Dmaven.test.skip=true -Dsonar.projectKey=ecs-mobile-manage
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache

  allow_failure: true
  rules:
    - if: '$CI_COMMIT_BRANCH == "test"'
  tags:
    - build
  when: manual
