package com.juneyaoair.ecs.manage;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.dsop.sdk.client.OpenClient;
import com.juneyaoair.dsop.sdk.request.CommonRequest;
import com.juneyaoair.dsop.sdk.response.CommonResponse;
import com.juneyaoair.ecs.excel.bean.ThirdPartPrize;
import com.juneyaoair.ecs.excel.bean.TicketInfoQueryResponse;
import com.juneyaoair.ecs.excel.bean.Traveller;
import com.juneyaoair.ecs.excel.listener.TravellerListener;
import com.juneyaoair.ecs.manage.dto.common.PtBIgDataResponse;
import com.juneyaoair.ecs.utils.EncoderHandler;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.ecs.utils.MdcUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/3 12:48
 */
@Slf4j
public class TravellerTest {
    @Test
    public void matchTravellerTest(){
        TravellerListener trackingListener = new TravellerListener();
        String inputPathName = "E:\\data.xlsx";
        String outPathName = "E:\\result.xlsx";
        EasyExcel.read(inputPathName,Traveller.class,trackingListener).sheet().doRead();
        String dsop = "https://dsop.juneyaoair.com";
        String appId = "20220622989108735957794816";
        String privateKeyIsv = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCtiPLHVu1472MHSIksOG/jdsEu1wXOKYv52pnzVCJTCSvB0nKIflTNHR0TI2Ly8WzgxBr5PmP2CmoH7qjfyMPk2kfCG1TtRkaX6b7F5uj3dhetj62XSf0RMj3OeoJu0ULCHkncDT2nG2dhbZ6Q+OwDSbQDCk23e2T1Sqa6pUPhoDQSaP3oMi0kdYSa61YaTmSlaqhiG8bUTlo1fpAcvLHWfLlf4GwdT4BGBHm/YAPPuzX95O741+r8LWpai3BiyjXTV7KXon+z0L07Ic/td6zwdZ8HvHoz2gIt7/2744C2ohO/KNwZ/5PZajJySngmthaGR8vZVO5v3BaeqhLK/1U5AgMBAAECggEAcXGUnMH0DloYV9uQaQsgAghRM+i3T9YTPqwsdkjrA3N8WoHUA+zqTQiaSDea6Qzwy2MH6yZhPKgENtlLJrhdb49O19L8SvzlC0xwPXm9lIBr7DeAGOXyfTpl80RdXkqIpNQeHq+9PTAJ/kQmI8nGqEOXXWFv+uG81yoNIvkFDkqP/BCMkBbXmP0IozK5jPTj32QWgquGJhot910t1cGPRe6NUK0fHVPXCF6kWi1/Xr+mYqQ4HKuKczWSUCkBD3syUte4bq5GoqOMl6Q5y5b3GXoS7AazhLSzHLck/MQB1o1UTSbBtGt4R7QC6dqPxFvYuPJCFhPd4Fynq9aW39UJvQKBgQD6NMAZv6tBHHtWwT92OiQtQE5eu/iMwkHuNI0UoLkymfAV7ILmo9Pl42SmTe9SFHw1mdtLgV7VRo4XutuCTg+1NWpfXXG9HTwNxpeZoRrbDzfhNWeJAFEbMiSd5/ieZEZobknUsgLXCbl5LlAqSJGB7g0SoJgeZ+i562zdHpOVFwKBgQCxja7c4GcWYphP0xEGprvOQ4YG++OVvvEn+kGM3fJRXfz8FLtXRVMcvDtgcw93gCf73aAiUS3opSwV+2Tx0Ajt8NupE/35Xszbmwo03YWoLPD36C4Fkx1M0Xa96+V9oOWAoB2IDzeSey+R2/Twy/+J/uWuyu+E8zH6JYDYijZ6LwKBgFbhnFcUlD3+yuFcJ1JBbDB+ZPhGY09VYRl7DAY+rTBh/dlPEEFQYBMyu893voOM83iVXW3BlZGPd2XMD88WliPBUZed1hHqJeFOtXO2WqH4jdnY9oO/JWrBJa/jThSKE1zN9zsxOQU8rjfjGhkkq8onREjUlR3xzQFKO7oolOyvAoGBAK8nMOgt6yQXEWA1vCcOllhtHiuHk0Rm/6lLZBzd2izQhj/7B7CY79t6CS0ldI2TfF4njNAtuxc00d/BhxxQ+G4Z2+fYlAPLtFWzhYcPPMnhw7kbb0MKlK3yOKw0qMicz6pqyZcuQ8kaApxmGyTsaWMtdFFRWqWR76xwEMIM3p77AoGBAOHweQfdChLRKms2R+mDs48k4+oMSmWWSIuCs6yHPqDKKGUJrUkwRUKgscUdU7JgF9jR7SRjhnEeZOaQ7ffT+V6bTzF5CxV/QznH/lszGGHsIqRc78JpuxwO5F50ChIpZ8vacpCiv5K9LxbmOvBxNfC6RoxA7GHj3/bQJIf8L022";
        List<Traveller> outData = new ArrayList<>();
        // 创建请求对象
        //String serviceName = "traveller.itinerary.info";
        String serviceName = "traveller.ticket.info";
        for(Traveller traveller : trackingListener.getCachedDataList()){
            Map<String,String> param = new HashMap();
            param.put("travellerNumber",traveller.getCert());
            //param.put("segmentOptFlightNumber",traveller.getFlightNo());
            param.put("segmentDepaDate",traveller.getFlightDate().substring(0,10));
            param.put("signature", EncoderHandler.encodeByMD5("CUXIAO" + traveller.getCert() + "CUXIAO2018"));
            CommonRequest request = new CommonRequest(serviceName,"1.0");
            OpenClient client = new OpenClient(dsop, appId, privateKeyIsv, "");
            request.setBizContent(JSON.toJSONString(param));
            // 发送请求
            CommonResponse response = client.execute(request);
            log.info("请求号:{},服务名:{},响应参数参数:{}",MdcUtils.getRequestId(),serviceName,JsonUtil.objectToJson(response));
            outData.add(traveller);
            if("000000".equals(response.getCode())){
                Type type = new TypeToken<PtBIgDataResponse<TicketInfoQueryResponse>>() {}.getType();
                PtBIgDataResponse<TicketInfoQueryResponse> ptBIgDataResponse =  JsonUtil.fromJson( response.getBody(),type);
                if(CollectionUtils.isNotEmpty(ptBIgDataResponse.getData())){
                    TicketInfoQueryResponse ticketInfoQueryResponse = ptBIgDataResponse.getData().get(0);
                    traveller.setTktNo(ticketInfoQueryResponse.getTicket_ticketnumber());
                    traveller.setResultFlightNo(ticketInfoQueryResponse.getSegment_opt_airlinecode()+ticketInfoQueryResponse.getSegment_opt_flightnumber());
                    traveller.setOfficeNo(ticketInfoQueryResponse.getTicket_issueoffice());
                    traveller.setTicketOutTime(ticketInfoQueryResponse.getTicket_out_time());
                }
                EasyExcel.write(outPathName, Traveller.class)
                        .sheet("匹配行程")
                        .doWrite(outData);
            }

        }
    }
}
