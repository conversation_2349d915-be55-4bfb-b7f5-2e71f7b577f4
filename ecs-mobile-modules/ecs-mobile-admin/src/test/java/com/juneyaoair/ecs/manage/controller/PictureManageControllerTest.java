package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.picture.PictureConsoleSearchReq;
import com.juneyaoair.manage.b2c.entity.PicturePO;
import com.ruoyi.common.core.domain.R;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("test")
class PictureManageControllerTest {
    @Resource
    PictureManageController controller;
    @Test
    void getPage() {
        PictureConsoleSearchReq req = new PictureConsoleSearchReq();
        req.setCatalogId("0");
        R<PageResult<PicturePO>> page = controller.getPage(req);
        assertEquals(R.SUCCESS,page.getCode());
    }
}