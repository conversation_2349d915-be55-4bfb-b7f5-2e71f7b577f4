package com.juneyaoair.ecs.upg.service.impl;

import com.juneyaoair.ecs.upg.service.IUpgAirlineGuidePriceService;
import com.juneyaoair.manage.cki.entity.UpgAirlineGuidePricePO;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.Date;

@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("test")
public class UpgAirlineGuidePriceServiceImplTest {

    @Resource
    IUpgAirlineGuidePriceService iUpgAirlineGuidePriceService;

    @Test
    void add() {
        UpgAirlineGuidePricePO pricePO = new UpgAirlineGuidePricePO();
        pricePO.airlineBaseSetId = "00d2182e-94a7-41ff-a656-a1e69c28a545";
        pricePO.airlineType = "I";
        pricePO.createTime = new Date();
        pricePO.createUser = "gdqi";
        pricePO.guidePriceDesc = "5";
        pricePO.productType = "1";
        pricePO.updateTime = new Date();
        pricePO.updateUser = "gdqi";

        boolean add = iUpgAirlineGuidePriceService.testAddDSTransactional(pricePO);
        System.out.println(add);
    }
}