package com.juneyaoair.ecs.manage.service.cityinfo.impl;

import com.juneyaoair.manage.b2c.service.ICityInfoService;
import com.juneyaoair.manage.b2c.entity.CityLabelInfoPO;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Collections;

@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
class CityInfoServiceImplTest {

    @Autowired
    ICityInfoService ICityInfoServiceImpl;

    @Test
    void updateCityLabel() {
        CityLabelInfoPO labelInfoPO = new CityLabelInfoPO();
        labelInfoPO.cityLabelName = "test";
        labelInfoPO.labelIntroduce = "test";
        labelInfoPO.cityLabelUrl = "test";
        boolean wdy = ICityInfoServiceImpl.updateCityLabel(Collections.singletonList(labelInfoPO), "WDY");
        Assert.assertTrue(wdy);
    }
}