package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.citymanage.CityManageDTO;
import com.ruoyi.common.core.domain.R;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("test")
class CityManageControllerTest {
    @Resource
    CityManageController controller;

    @Test
    void add() {
        CityManageDTO cityManageDTO = new CityManageDTO();
        cityManageDTO.cityName = "test";
        cityManageDTO.cityCode = "tes";
        cityManageDTO.isHotCity = "Y";
        cityManageDTO.isTopCity = "Y";
        cityManageDTO.isInternational = "Y";
        R add = controller.add(cityManageDTO);
        assertEquals(R.SUCCESS,add.getCode());
    }
}