package com.juneyaoair.ecs.manage.mapstruct;

import com.juneyaoair.ecs.manage.dto.airline.routelabel.AddOrUpdateRouteLabelRequestDTO;
import com.juneyaoair.ecs.manage.dto.airline.routelabel.AddOrUpdateRuleRequestDTO;
import com.juneyaoair.ecs.manage.dto.airline.routelabel.RouteLabelDTO;
import com.juneyaoair.ecs.manage.dto.airline.routelabel.bo.RouteLabelBO;
import com.juneyaoair.manage.b2c.entity.RouteLabelChannelPO;
import com.juneyaoair.manage.b2c.entity.RouteLabelPO;
import com.juneyaoair.manage.b2c.entity.RouteLabelRulePO;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(builder = @Builder(disableBuilder = true))
public interface RouteLabelMapStruct {

    RouteLabelMapStruct INSTANCE = org.mapstruct.factory.Mappers.getMapper(RouteLabelMapStruct.class);
//
    @Mapping(source = "startDate", target = "startDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "endDate", target = "endDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "ruleBOList",target = "ruleDTOList")
    RouteLabelDTO dtoToRouteLabelDTO(RouteLabelBO bo);

    @Mapping(source = "startDate", target = "startDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "endDate", target = "endDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "enableStatus", expression = "java(dto.isEnableStatus()? \"1\":\"0\")")
    RouteLabelPO requestDtoToRouteLabelPO(AddOrUpdateRouteLabelRequestDTO dto);

    @Mapping(source = "routeStartDate", target = "routeStartDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "routeEndDate", target = "routeEndDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "transit", target = "isTransit")
    @Mapping(target = "enableStatus", expression = "java(dto.isEnableStatus()? \"1\":\"0\")")
    RouteLabelRulePO requestDtoToRulePO(AddOrUpdateRuleRequestDTO dto);

    RouteLabelChannelPO requestDtoToChannelPO(AddOrUpdateRouteLabelRequestDTO.RouteLabelChannelDTO channelList);
}
