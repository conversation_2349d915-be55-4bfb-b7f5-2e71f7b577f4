package com.juneyaoair.ecs.manage.service.airline;

import com.juneyaoair.ecs.manage.dto.airline.AirLineExportDTO;
import com.juneyaoair.ecs.manage.dto.airline.AirLineRequestDTO;
import com.juneyaoair.manage.b2c.entity.AirlineAPO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface IAirlineAggrService {
    void parseExcel(MultipartFile excelFIle);

    List<AirLineExportDTO> exportList(AirLineRequestDTO requestDTO);

    boolean syncJsonAndJs();

    boolean syncJs();

    /**
     * 添加航线
     * @param airLine
     * @param needBack
     * @return
     */
    boolean add(AirlineAPO airLine, boolean needBack);
}
