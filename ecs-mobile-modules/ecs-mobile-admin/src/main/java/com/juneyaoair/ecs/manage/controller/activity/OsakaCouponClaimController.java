package com.juneyaoair.ecs.manage.controller.activity;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.controller.HoBaseController;
import com.juneyaoair.ecs.manage.dto.activity.request.osaka.OsakaCouponClaimQueryRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.osaka.OsakaCouponClaimResponse;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.osaka.IOsakaCouponClaimService;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * @ClassName OsakaCouponClaimController
 * @Description 大阪接送机券领取
 * <AUTHOR>
 * @Date 2025/6/23 13:50
 * @Version 1.0
 */
@RequestMapping("osakaTransfer")
@RestController
@RequiredArgsConstructor
@Api(value = "OsakaCouponClaimController", tags = "大阪接送机券领取API")
@Slf4j
public class OsakaCouponClaimController extends HoBaseController {

    @Autowired
    private IOsakaCouponClaimService osakaCouponClaimService;

    @PostMapping(value = "doCouponClaimListQuery")
    @ApiOperation(value = "大阪接送机券领取列表", httpMethod = "POST")
    public R<PageResult<OsakaCouponClaimResponse>> doCouponClaimListQuery(@RequestBody @Validated OsakaCouponClaimQueryRequest osakaCouponClaimQueryRequest, BindingResult bindingResult) {
        try {
            if (bindingResult.hasErrors()) {
                throw new HoServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
            }
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(osakaCouponClaimQueryRequest));
            PageDomain pageDomain = TableSupport.buildPageRequest();
            startPage(pageDomain);
            Page<OsakaCouponClaimResponse> localPage = PageHelper.getLocalPage();
            List<OsakaCouponClaimResponse> thirdCouponResponseList = osakaCouponClaimService.doCouponClaimListQuery(osakaCouponClaimQueryRequest);
            R<PageResult<OsakaCouponClaimResponse>> pageData = getPageData(thirdCouponResponseList, localPage);
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(pageData));
            return pageData;
        } catch (Exception exception) {
            log.error("入参:[{}]大阪接送机券领取列表查询失败:", JSON.toJSONString(osakaCouponClaimQueryRequest), exception);
            return R.fail(exception.getMessage());
        } finally {
            Context.setContext(null);
        }
    }

    @ApiOperation(value = "大阪接送机流水下载")
    @PostMapping(value = "downloadCouponClaimList")
    public void downloadCouponClaimList(HttpServletResponse response, @RequestBody @Validated OsakaCouponClaimQueryRequest osakaCouponClaimQueryRequest, BindingResult bindingResult) throws IOException {
        if (bindingResult.hasErrors()) {
            throw new HoServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        List<OsakaCouponClaimResponse> osakaCouponClaimResponseList = osakaCouponClaimService.doCouponClaimListQuery(osakaCouponClaimQueryRequest);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "大阪接送机流水数据.xlsx";
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
        EasyExcel.write(response.getOutputStream(), OsakaCouponClaimResponse.class).sheet("大阪接送机流水数据").doWrite(osakaCouponClaimResponseList);
    }


}
