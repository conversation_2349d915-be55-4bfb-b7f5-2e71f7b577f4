package com.juneyaoair.ecs.manage.external.flightbaisc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @Description 机型信息
 * @created 2023/9/8 14:49
 */
@Data
public class AircraftTypeInfo {

    @ApiModelProperty(value = "机型ID")
    private String aircraftTypeId;

    @ApiModelProperty(value = "机型")
    private String aircraftTypeCode;

    @ApiModelProperty(value = "机型名称")
    private String aircraftTypeName;

    @ApiModelProperty("机型图片")
    private String aircraftIcon;

    @ApiModelProperty(value = "公务舱数量")
    private Integer businessClassNum;

    @ApiModelProperty(value = "经济舱数量")
    private Integer economyClassNum;

    @ApiModelProperty(value = "子机型清单")
    private Set<String> aircraftCodeSet;

}
