package com.juneyaoair.ecs.manage.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Sets;
import com.juneyaoair.ecs.manage.aop.UpdVerAnno;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.picture.*;
import com.juneyaoair.ecs.manage.service.sys.RuoyiSystemService;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.SystemConstants;
import com.juneyaoair.ecs.utils.VersionNoUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.AirlineAPO;
import com.juneyaoair.manage.b2c.entity.dict.DicValueWithDicType;
import com.juneyaoair.manage.b2c.entity.dict.DictvaluePO;
import com.juneyaoair.manage.b2c.service.*;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.juneyaoair.manage.b2c.entity.PicturePO;
import com.juneyaoair.manage.b2c.service.IPicService;
import com.ruoyi.system.api.domain.SysDictData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequestMapping("pictureManage")
@RestController
@RequiredArgsConstructor
@Api(value = "PictureManageController", tags = "轮播广告位管理")
@Slf4j
public class PictureManageController extends HoBaseController {

    @Resource
    private IPicCatalogueService picCatalogueService;
    @Resource
    private IDictvalueService dictvalueService;
    @Autowired
    private RuoyiSystemService ruoyiSystemService;

    @Resource
    private IPicService pictureService;

    /**
     * 获取图片位置一览
     */
    @ApiOperation("获取图片位置一览")
    @PostMapping(value = "getSelectConLocation")
    public R<PictureJson> getSelectConLocation() {
        PictureJson pictureJson = new PictureJson();
        DictvaluePO value = new DictvaluePO();
        value.setDtCode(SystemConstants.POSITION);//位置
        List<DictvaluePO> list = dictvalueService.findDICTValueList(value);
        if (list != null && list.size() > 0) {
            pictureJson.location = list.stream().map(i -> {
                LocationDTO locationDTO = new LocationDTO();
                locationDTO.dvCode = i.dvcode;
                locationDTO.dvName = i.dvname;
                return locationDTO;
            }).collect(Collectors.toList());
        }
        DictvaluePO value1 = new DictvaluePO();
        value1.setDtCode(SystemConstants.USERC_STATUS);
        List<DictvaluePO> list1 = dictvalueService.findDICTValueList(value1);
        if (list1 != null && list1.size() > 0) {
            pictureJson.statusList = list1.stream().map(i -> {
                LocationDTO locationDTO = new LocationDTO();
                locationDTO.dvCode = i.dvcode;
                locationDTO.dvName = i.dvname;
                return locationDTO;
            }).collect(Collectors.toList());
        }
        return R.ok(pictureJson);
    }

    /**
     * 获取图片位置一览 过滤掉目录已经存在的
     */
    @ApiOperation("获取图片位置一览 过滤掉目录已经存在的")
    @PostMapping(value = "getSelectConLocationLeft")
    public R<PictureJson> getSelectConLocationLeft() {
        PictureJson pictureJson = new PictureJson();
        DictvaluePO value = new DictvaluePO();
        value.setDtCode(SystemConstants.POSITION);//位置
        List<DictvaluePO> list = dictvalueService.findDICTValueList(value);
        GetCatalogueListReqDTO catalogueReq = new GetCatalogueListReqDTO();
        catalogueReq.deletedFlag = "0"/*没删的*/;
        catalogueReq.type = 2/**/;
        List<IndexInfoDTO> indexInfoDTOS = picCatalogueService.searchPage(catalogueReq);

        if (list == null || list.size() == 0) {
            return R.ok();
        }
        pictureJson.location = list.stream().map(i -> {
            LocationDTO locationDTO = new LocationDTO();
            locationDTO.dvCode = i.dvcode;
            locationDTO.dvName = i.dvname;
            return locationDTO;
        }).collect(Collectors.toList());
        if (CollUtil.isEmpty(pictureJson.location)) {
            return R.ok();
        }
        if (CollUtil.isNotEmpty(indexInfoDTOS)) {
            pictureJson.location = pictureJson.location.stream().filter(i ->
                    !indexInfoDTOS.stream().map(index -> index.pic_location).collect(Collectors.toList()).contains(i.dvCode)
            ).collect(Collectors.toList());
        }
        return R.ok(pictureJson);
    }

    @ApiOperation(value = "分页获取图片信息", notes = "")
    @PostMapping(value = "getPage")
    public R<PageResult<PicturePO>> getPage(@RequestBody PictureConsoleSearchReq pictureConsole) {
        List<String> picLocationList = null;
        if (StrUtil.isNotBlank(pictureConsole.getCatalogId())) {
            GetCatalogueListReqDTO reqDTO = new GetCatalogueListReqDTO();
            reqDTO.curIndexId = Long.parseLong(pictureConsole.getCatalogId());
            reqDTO.deletedFlag = "0";
            List<IndexInfoDTO> indexInfoDTOS = picCatalogueService.searchPage(reqDTO);
            picLocationList = getPicLocation(indexInfoDTOS);
        }
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        Page<AirlineAPO> localPage = PageHelper.getLocalPage();
        PicturePO picture = new PicturePO();
        BeanUtils.copyProperties(pictureConsole, picture);
        picture.picLocationList = picLocationList;
        picture.setVerInt(VersionNoUtil.toVerInt(pictureConsole.getMinVer()));
        return getPageData(pictureService.getList(picture), localPage);
    }

    private List<String> getPicLocation(List<IndexInfoDTO> indexInfoDTOS) {
        if (CollUtil.isEmpty(indexInfoDTOS)) {
            return CollUtil.newArrayList();
        }
        List<String> collect = indexInfoDTOS.stream().map(i -> i.pic_location).collect(Collectors.toList());
        List<IndexInfoDTO> collect1 = indexInfoDTOS.stream().filter(i->i!=null && CollUtil.isNotEmpty(i.indexInfoList))
                .flatMap(i -> i.indexInfoList.stream()).collect(Collectors.toList());
        List<String> picLocation = getPicLocation(collect1);
        if (CollUtil.isEmpty(collect1)) {
            return collect;
        }
        collect.addAll(picLocation);
        return collect;
    }

    /**
     * 获取图片信息
     */
    @ApiOperation(value = "getPic", notes = "根据如下获取图片：picId title status picEndtime picLocation channelCode platforminfo verInt")
    @PostMapping(value = "getPic")
    public R<List<PicturePO>> getPic(@RequestBody PicturePO po) {
        po.setVerInt(VersionNoUtil.toVerInt(po.getMinVer()));
        return R.ok(pictureService.getList(po));
    }

    /**
     * 根据id获取图片信息
     */
    @ApiOperation("getPicById")
    @PostMapping(value = "getPicById")
    public R<PicturePO> getPicById(@RequestBody PicturePO pictureConsole, HttpServletRequest request) {
        PicturePO picture = new PicturePO();
        BeanUtils.copyProperties(pictureConsole, picture);
        PicturePO picture1 = pictureService.getPicture(request, picture);
        if (picture1 == null) {
            return R.ok(null);
        }
        if (StringUtils.isNotEmpty(picture1.getMinVer())) {
            picture1.setMinVer(VersionNoUtil.toVersionStr(picture1.getMinVer()));
        }
        if (StringUtils.isNotEmpty(picture1.getMaxVer())) {
            picture1.setMaxVer(VersionNoUtil.toVersionStr(picture1.getMaxVer()));
        }
        return R.ok(picture1);
    }


    /**
     * 图片内容添加
     */
    @ApiOperation("addPic")
    @PostMapping(value = "addPic")
    @UpdVerAnno
    public R<Boolean> addPic(@RequestBody PicturePO picture) {
        Date date = new Date();
        String createMan = SecurityUtils.getUsername();
        if (StringUtils.isBlank(picture.getLanguage())) {
            throw new HoServiceException("语言不能为空");
        }
        // 检查语言是否在字段中
        ruoyiSystemService.checkDictValue("language", picture.getLanguage());
        picture.setStatus("N");
        picture.setPicId(UUID.randomUUID().toString());
        picture.setCreateMan(createMan);
        picture.setCreateTime(DateUtil.getCurrentDateStr());
        picture.setUpdateTime(DateUtil.getDateStringAllDate(date));
        if (StringUtils.isNotEmpty(picture.getUrl())) {
            picture.setUrl(picture.getUrl().trim());
        }
        if (StringUtils.isNotEmpty(picture.getShareUrl())) {
            picture.setShareUrl(picture.getShareUrl());
        }
        if (StringUtils.isNotEmpty(picture.getMinVer())) {
            picture.setMinVer(VersionNoUtil.toVerInt(picture.getMinVer()) + "");
        }
        if (StringUtils.isNotEmpty(picture.getMaxVer())) {
            picture.setMaxVer(VersionNoUtil.toVerInt(picture.getMaxVer()) + "");
        }
        // 广告位图片类型
        Map<String, SysDictData> sysDictDataMap = ruoyiSystemService.dictType("advertising_image_type");
        // 允许的图片类型
        Set<String> imageTypeSet = null == sysDictDataMap || sysDictDataMap.isEmpty() ? Sets.newHashSet() : sysDictDataMap.keySet();
        return R.ok(pictureService.addPicture(picture, imageTypeSet));
    }

    @ApiOperation("根据id物理删除图片")
    @PostMapping(value = "delPic")
    @UpdVerAnno
    public R delPic(@RequestParam String picId) {
        return R.ok(pictureService.removeById(picId));
    }

    /**
     * 图片更新
     */
    @ApiOperation(value = "图片更新", notes = "更新status statusCode enableMode," +
            "传title非空后可以多更新其他所有值，并且可以感知null")
    @PostMapping(value = "updatePic")
    @UpdVerAnno
    public R<Boolean> updatePic(@RequestBody PicturePO picture) {
        Date date = new Date();
        String updateMan = SecurityUtils.getUsername();
        picture.setUpdateMan(updateMan);
        picture.setUpdateTime(DateUtil.getDateStringAllDate(date));
        if (StringUtils.isNotEmpty(picture.getMinVer()) && picture.getMinVer().contains(".")) {
            picture.setMinVer(VersionNoUtil.toVerInt(picture.getMinVer()) + "");
        }
        if (StringUtils.isNotEmpty(picture.getMaxVer()) && picture.getMaxVer().contains(".")) {
            picture.setMaxVer(VersionNoUtil.toVerInt(picture.getMaxVer()) + "");
        }
        if (StringUtils.isNotEmpty(picture.getUrl())) {
            picture.setUrl(picture.getUrl().trim());
        }
        if (StringUtils.isNotEmpty(picture.getShareUrl())) {
            picture.setShareUrl(picture.getShareUrl());
        }
        // 不允许修改语言
        picture.setLanguage(null);
        // 广告位图片类型
        Map<String, SysDictData> sysDictDataMap = ruoyiSystemService.dictType("advertising_image_type");
        // 允许的图片类型
        Set<String> imageTypeSet = null == sysDictDataMap || sysDictDataMap.isEmpty() ? Sets.newHashSet() : sysDictDataMap.keySet();
        return R.ok(pictureService.updatePic(picture, imageTypeSet));
    }


    /**
     * 获取平台信息一览
     */
    @ApiOperation("获取平台信息一览")
    @PostMapping(value = "getSelectPlatformInfo")
    public R<PictureJson> getSelectPlatformInfo() {
        PictureJson pictureJson = new PictureJson();
        DictvaluePO value = new DictvaluePO();
        value.setDtCode(SystemConstants.PLATFORMINFO);//位置
        List<DictvaluePO> list = dictvalueService.findDICTValueList(value);
        List<LocationDTO> listjson = new ArrayList<>();
        if (list != null && list.size() > 0) {
            for (DictvaluePO string : list) {
                LocationDTO locationDTO = new LocationDTO();
                locationDTO.dvCode = (string.dvcode);
                locationDTO.dvName = (string.dvname);
                listjson.add(locationDTO);
            }
            pictureJson.setPlatformInfo(listjson);
        }
        DictvaluePO value1 = new DictvaluePO();
        value1.setDtCode(SystemConstants.USERC_STATUS);
        List<DictvaluePO> list1 = dictvalueService.findDICTValueList(value1);
        List<LocationDTO> listjson1 = new ArrayList<>();
        if (list1 != null && list1.size() > 0) {
            for (DictvaluePO string : list1) {
                LocationDTO locationDTO = new LocationDTO();
                locationDTO.dvCode = (string.dvcode);
                locationDTO.dvName = (string.dvname);
                listjson1.add(locationDTO);
            }
            pictureJson.setStatusList(listjson1);
        }
        return R.ok(pictureJson);
    }


    /**
     * 获取平台信息一览
     */
    @ApiOperation("获取渠道号")
    @PostMapping(value = "getChannelList")
    public R<List<DicValueWithDicType>> getChannelList() {
        return R.ok(pictureService.getChannelList(SystemConstants.CHANNEL));
    }

    /**
     * 增加图片目录
     */
    @ApiOperation("addPicCatalogue")
    @PostMapping(value = "addPicCatalogue")
    public R<Boolean> addPicCatalogue(@RequestBody UpsertPicCatalogueReqDTO request) {
        Date date = new Date();
        request.createTime = date;
        request.updateTime = date;
        String createMan = SecurityUtils.getUsername();
        request.createUser = createMan;
        request.updateUser = createMan;
        if (picCatalogueService.add(request)) {
            return R.ok();
        }
        return R.fail();
    }

    /**
     * 物理删除图片目录
     */
    @ApiOperation("物理删除图片目录")
    @PostMapping(value = "delPicCatalogue")
    public R<Boolean> delPicCatalogue(@RequestBody DelPicCatalogueReqDTO request) {
        return R.ok(picCatalogueService.delete(request));
    }

    /**
     * 修改图片目录
     */
    @ApiOperation("修改图片目录")
    @PostMapping(value = "updatePicCatalogue")
    public R<Boolean> updatePicCatalogue(@RequestBody UpsertPicCatalogueReqDTO req) {
        req.updateUser = SecurityUtils.getUsername();
        req.updateTime = new Date();
        if (picCatalogueService.update(req)) {
            return R.ok();
        }
        return R.fail();
    }

    /**
     * 全排列获取非删除目录及其下非删除子目录 example:
     * parentId 0 b2c
     *          -1 pc
     *          -2 yundu
     */
    @ApiOperation("全排列获取非删除目录及其下非删除子目录")
    @PostMapping(value = "getCatalogueList")
    public R<GetCatalogueListRespDTO> getCatalogue(@RequestBody GetCatalogueListReqDTO req) {
        GetCatalogueListRespDTO ret = new GetCatalogueListRespDTO();
        req.deletedFlag = "0";
        List<IndexInfoDTO> indexInfos = picCatalogueService.searchPage(req);
        if (CollectionUtil.isEmpty(indexInfos)) {
            return R.ok();
        }
        ret.indexInfoList = indexInfos;
        return R.ok(ret);
    }

}
