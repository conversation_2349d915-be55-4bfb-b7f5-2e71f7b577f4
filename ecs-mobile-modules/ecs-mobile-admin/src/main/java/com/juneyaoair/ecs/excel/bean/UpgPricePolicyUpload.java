package com.juneyaoair.ecs.excel.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.juneyaoair.ecs.valid.EnumValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 升舱价格政策航线
 * @created 2023/8/21 13:19
 */
@Data
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.LEFT, dataFormat = 49)
public class UpgPricePolicyUpload {

    @ExcelProperty(index = 0, value = "价格政策名称")
    @NotBlank(message = "价格政策名称不能为空")
    @ApiModelProperty(value = "价格政策名称", required = true)
    private String pricePolicyName;

    @ExcelProperty(index = 1, value = "升舱类型(1:登机口升舱（两场） 2:竞价升舱 3:机上升舱 4:登机口升舱（外站）),)")
    @NotBlank(message = "升舱类型不能为空")
    @EnumValue(value = {"1", "2", "3", "4"}, message = "升舱类型格式不正确")
    @ApiModelProperty(value = "升舱类型 参照：UPG_TYPE_ENUM")
    private String upgType;

    @ExcelProperty(index = 2, value = "短信通知标识 Y：发送 N：不发送(只有升舱类型为登机口升舱（两场）允许发送短信)")
    @NotBlank(message = "短信通知标识不能为空")
    @EnumValue(value = {"Y", "N"}, message = "短信通知标识格式不正确")
    @ApiModelProperty(value = "短信通知标识 Y：发送 N：不发送", required = true)
    private String sendSmsFlag;

    @ExcelProperty(index = 3, value = "出发机场三字码")
    @Length(min = 3, max = 3, message = "出发机场格式不正确")
    @NotBlank(message = "出发机场不能为空")
    @ApiModelProperty(value = "出发机场")
    private String depAirportCode;

    @ExcelProperty(index = 4, value = "到达机场三字码")
    @Length(min = 3, max = 3, message = "到达机场格式不正确")
    @NotBlank(message = "到达机场不能为空")
    @ApiModelProperty(value = "到达机场")
    private String arrAirportCode;

    @ExcelProperty(index = 5, value = "航班开始日期 格式：yyyy-MM-dd")
    @NotNull(message = "航班开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "航班开始日期 格式：yyyy-MM-dd", required = true)
    private Date flightStartDate;

    @ExcelProperty(index = 6, value = "航班结束日期 格式：yyyy-MM-dd")
    @NotNull(message = "航班结束日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "航班结束日期 格式：yyyy-MM-dd", required = true)
    private Date flightEndDate;

    @ExcelProperty(index = 7, value = "生效周期： 空代表不校验 1~7表示周一到周日(多个用,分割)")
    @ApiModelProperty(value = "生效周期： 空代表不校验 1~7表示周一到周日(多个用,分割)")
    private String weekDays;

    @ExcelProperty(index = 8, value = "航班时刻 A:0-6点 B:6-12点 C:12-18点 D:18-24(多个用,分割)")
    @ApiModelProperty(value = "航班时刻 hh:mm-hh:mm格式")
    private String flightTimes;

    @ExcelProperty(index = 9, value = "航班号（多个以,分割）")
    @ApiModelProperty(value = "航班号（多个以,分割）")
    private String flightNos;

    @ExcelProperty(index = 10, value = "标准价格")
    @NotNull(message = "标准价格不能为空")
    @ApiModelProperty(value = "标准价格", required = true)
    private Integer basePrice;

}
