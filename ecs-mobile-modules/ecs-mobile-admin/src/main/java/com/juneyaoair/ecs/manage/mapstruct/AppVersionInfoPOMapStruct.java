package com.juneyaoair.ecs.manage.mapstruct;

import com.juneyaoair.ecs.manage.dto.appversion.AppVersionInfoDTO;
import com.juneyaoair.manage.b2c.entity.AppVersionInfoPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/15 15:46
 */
@Mapper
public interface AppVersionInfoPOMapStruct {
    AppVersionInfoPOMapStruct INSTANCE = Mappers.getMapper(AppVersionInfoPOMapStruct.class);

    AppVersionInfoDTO toAppVersionInfoDTO(AppVersionInfoPO appVersionInfoPO);

    List<AppVersionInfoDTO> toAppVersionInfoDTOList(List<AppVersionInfoPO> appVersionInfoPOList);
}