package com.juneyaoair.ecs.manage.service.message.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.juneyaoair.ecs.manage.dto.message.request.MessageDto;
import com.juneyaoair.ecs.manage.dto.tongdun.TongDunRequestDto;
import com.juneyaoair.ecs.manage.dto.tongdun.TongDunTextRequest;
import com.juneyaoair.ecs.manage.enums.NoticeTemplateEnum;
import com.juneyaoair.ecs.manage.enums.YorNEnum;
import com.juneyaoair.ecs.manage.external.FlightBasicService;
import com.juneyaoair.ecs.manage.service.message.IMessageAggrService;
import com.juneyaoair.ecs.manage.service.sys.RuoyiSystemService;
import com.juneyaoair.ecs.manage.service.thymeleaf.IThymeleafService;
import com.juneyaoair.ecs.manage.service.tongdun.ITongdunService;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.ChannelPO;
import com.juneyaoair.manage.b2c.entity.MessagePO;
import com.juneyaoair.manage.b2c.entity.ServicePO;
import com.juneyaoair.manage.b2c.service.IChannelService;
import com.juneyaoair.manage.b2c.service.IMessageService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/10 13:17
 */
@Service
public class MessageAggrServiceImpl implements IMessageAggrService {
    @Autowired
    private IMessageService messageService;
    @Autowired
    private ITongdunService tongdunService;
    @Autowired
    private IThymeleafService thymeleafService;
    @Autowired
    private IChannelService channelService;
    @Autowired
    private FlightBasicService flightBasicService;
    @Autowired
    private RuoyiSystemService ruoyiSystemService;

    @Override
    public boolean addMessage(MessageDto messageDto) {
        if (StringUtils.isBlank(messageDto.getLanguage())) {
            throw new HoServiceException("语言不能为空");
        }
        // 检查语言是否在字段中
        ruoyiSystemService.checkDictValue("language", messageDto.getLanguage());
        //同盾风控值检查
        TongDunTextRequest tongDunTextRequest = new TongDunTextRequest();
        tongDunTextRequest.setPostingTitle(messageDto.getMessageTitle());
        tongDunTextRequest.setPostingContent(messageDto.getMessagePlaintxt());
        TongDunRequestDto tongDunRequestDto = new TongDunRequestDto();
        tongDunRequestDto.setText(tongDunTextRequest);
        tongdunService.textContent(tongDunRequestDto);
        //生成html静态资源模板,返回静态url地址
        Date date = new Date();
        String messageId = HOStringUtil.newGUID();
        messageDto.setMessageid(messageId);
        messageDto.setMessagePublishtime(DateUtil.convertDate2Str(date, DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        //默认同步生成移动端与官网静态资源
        String mobileUrl = thymeleafService.createMessageHtml(messageDto, NoticeTemplateEnum.MOBILE, date);
        String b2cUrl = thymeleafService.createMessageHtml(messageDto, NoticeTemplateEnum.B2C, date);
        //保存message数据
        MessagePO message = new MessagePO();
        String[] ignoreProperties = {"messageContent"};
        BeanUtils.copyNotNullProperties(messageDto, message, ignoreProperties);
        if (StringUtils.isNotBlank(messageDto.getMessageContent())) {
            try {
                message.setMessageContent2(messageDto.getMessageContent().getBytes("GBK"));
            } catch (UnsupportedEncodingException e) {
                throw new HoServiceException("不支持的编码格式GBK");
            }
        }
        message.setMessagePlaintxt(messageDto.getMessagePlaintxt().length()>200?messageDto.getMessagePlaintxt().substring(0,200):messageDto.getMessagePlaintxt());
        String userName = SecurityUtils.getUsername();
        message.setMessageUrl(mobileUrl);
        message.setB2cMessageUrl(b2cUrl);
        message.setMessageAuth(userName);
        message.setMessageCreatetime(date);
        message.setMessageUpdateman(userName);
        message.setMessageUpdatetime(date);
        message.setMessageSendornot(YorNEnum.N.getStr());
        message.setMessageStatus(YorNEnum.N.getStr());
        //渠道信息处理
        if (CollectionUtils.isNotEmpty(messageDto.getChannelList())) {
            message.setChannelPOList(toChannelPOList(messageDto.getChannelList(), messageId));
        }
        //服务信息处理
        if (CollectionUtils.isNotEmpty(messageDto.getServiceIdList())) {
            message.setServicePOList(toServicePOList(messageDto.getServiceIdList(), messageId));
        }
        boolean flag = messageService.saveMessage(message);
        //异步清理缓存
        flightBasicService.clearNoticeCache();
        return flag;
    }

    @Override
    public boolean updateMessage(MessageDto messageDto) {
        //同盾风控值检查
        TongDunTextRequest tongDunTextRequest = new TongDunTextRequest();
        tongDunTextRequest.setPostingTitle(messageDto.getMessageTitle());
        tongDunTextRequest.setPostingContent(messageDto.getMessagePlaintxt());
        TongDunRequestDto tongDunRequestDto = new TongDunRequestDto();
        tongDunRequestDto.setText(tongDunTextRequest);
        LambdaQueryWrapper<MessagePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessagePO::getMessageid, messageDto.getMessageid());
        MessagePO msgOld = messageService.getOne(queryWrapper);
        if (msgOld == null) {
            throw new HoServiceException("messageId:" + messageDto.getMessageid() + "不存在");
        }
        tongdunService.textContent(tongDunRequestDto);
        Date date = msgOld.getMessageCreatetime();
        messageDto.setMessagePublishtime(DateUtil.convertDate2Str(date, DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        //默认同步生成移动端与官网静态资源
        String mobileUrl = thymeleafService.createMessageHtml(messageDto, NoticeTemplateEnum.MOBILE, date);
        String b2cUrl = thymeleafService.createMessageHtml(messageDto, NoticeTemplateEnum.B2C, date);
        MessagePO message = new MessagePO();
        String[] ignoreProperties = {"messageContent", "language"};
        BeanUtils.copyNotNullProperties(messageDto, message, ignoreProperties);
        if (StringUtils.isNotBlank(messageDto.getMessageContent())) {
            try {
                message.setMessageContent2(messageDto.getMessageContent().getBytes("GBK"));
            } catch (UnsupportedEncodingException e) {
                throw new HoServiceException("不支持的编码格式GBK");
            }
        }
        if (CollectionUtils.isNotEmpty(messageDto.getChannelList())) {
            message.setChannelPOList(toChannelPOList(messageDto.getChannelList(), messageDto.getMessageid()));
        }
        //服务信息处理
        if (CollectionUtils.isNotEmpty(messageDto.getServiceIdList())) {
            message.setServicePOList(toServicePOList(messageDto.getServiceIdList(), messageDto.getMessageid()));
        }
        message.setMessagePlaintxt(messageDto.getMessagePlaintxt().length()>200?messageDto.getMessagePlaintxt().substring(0,200):messageDto.getMessagePlaintxt());
        String userName = SecurityUtils.getUsername();
        message.setMessageUrl(mobileUrl);
        message.setB2cMessageUrl(b2cUrl);
        message.setMessageUpdateman(userName);
        message.setMessageUpdatetime(date);
        boolean flag = messageService.updateMessageByMessageId(message);
        if (!flag) {
            throw new HoServiceException("更新失败");
        }
        //异步清理缓存
        flightBasicService.clearNoticeCache();
        return flag;
    }

    /**
     * 渠道信息处理
     *
     * @param channelList
     * @param messageId
     * @return
     */
    private List<ChannelPO> toChannelPOList(List<String> channelList, String messageId) {
        List<ChannelPO> channelPOList = new ArrayList<>();
        for (String channel : channelList) {
            ChannelPO channelPO = new ChannelPO();
            channelPO.setId(HOStringUtil.newGUID());
            channelPO.setProId(messageId);
            channelPO.setChannelType(channel);
            channelPOList.add(channelPO);
        }
        return channelPOList;
    }

    /**
     * 服务信息处理
     *
     * @param serviceList
     * @param messageId
     * @return
     */
    private List<ServicePO> toServicePOList(List<String> serviceList, String messageId) {
        List<ServicePO> servicePOList = new ArrayList<>();
        for (String serviceId : serviceList) {
            ServicePO servicePO = new ServicePO();
            servicePO.setId(HOStringUtil.newGUID());
            servicePO.setProId(messageId);
            servicePO.setModularId(serviceId);
            servicePOList.add(servicePO);
        }
        return servicePOList;
    }

    /**
     * 公告详情
     *
     * @param messageId
     * @return
     */
    @Override
    public MessageDto messageDetail(String messageId) {
        QueryWrapper<MessagePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("MESSAGEID", messageId);
        MessagePO messagePO = messageService.getOne(queryWrapper);
        if (messagePO == null) {
            throw new HoServiceException("公告信息不存在");
        }
        MessageDto messageDto = new MessageDto();
        BeanUtils.copyNotNullProperties(messagePO, messageDto);
        if (messagePO.getMessageContent2() != null && messagePO.getMessageContent2().length > 0) {
            try {
                messageDto.setMessageContent(new String(messagePO.getMessageContent2(), "GBK"));
            } catch (UnsupportedEncodingException e) {
                throw new HoServiceException("不支持的编码格式GBK");
            }
        }
        //channel信息查询
        QueryWrapper<ChannelPO> channelPOQueryWrapper = new QueryWrapper<>();
        channelPOQueryWrapper.eq("PRO_ID", messageId);
        List<ChannelPO> channelPOList = channelService.list(channelPOQueryWrapper);
        messageDto.setChannelList(new ArrayList<>());
        if (CollectionUtils.isNotEmpty(channelPOList)) {
            List<String> strList = new ArrayList<>();
            for (ChannelPO channelPO : channelPOList) {
                strList.add(channelPO.getChannelType());
                messageDto.setChannelList(strList);
            }
        }
        //服务信息查询
        return messageDto;
    }

    /**
     * 公告发布与下线
     *
     * @param messageDto
     * @return
     */
    @Override
    public boolean publishOrOffline(MessageDto messageDto) {
        if (StringUtils.isBlank(messageDto.getMessageid())) {
            throw new HoServiceException("操作失败，缺少参数");
        }
        if (!YorNEnum.checkEnum(messageDto.getMessageStatus())) {
            throw new HoServiceException("操作失败，超过限定值");
        }
        MessagePO messagePO = new MessagePO();
        String userName = SecurityUtils.getUsername();
        messagePO.setMessagePublishman(userName);
        messagePO.setMessageStatus(messageDto.getMessageStatus());
        UpdateWrapper<MessagePO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(MessagePO::getMessageid, messageDto.getMessageid());
        boolean flag = messageService.update(messagePO, updateWrapper);
        if (!flag) {
            throw new HoServiceException("操作失败");
        }
        //异步清理缓存
        flightBasicService.clearNoticeCache();
        return flag;
    }
}
