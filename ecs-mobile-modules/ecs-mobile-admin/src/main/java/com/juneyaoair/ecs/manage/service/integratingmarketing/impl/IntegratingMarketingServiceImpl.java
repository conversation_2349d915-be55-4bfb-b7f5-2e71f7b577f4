package com.juneyaoair.ecs.manage.service.integratingmarketing.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.juneyaoair.ecs.manage.config.AdminConfig;
import com.juneyaoair.ecs.manage.dto.activity.request.integratingmarketingController.IntegratingMarketingDetailQueryRequest;
import com.juneyaoair.ecs.manage.dto.activity.request.integratingmarketingController.IntegratingMarketingRewardsRequest;
import com.juneyaoair.ecs.manage.dto.activity.request.integratingmarketingController.IntegratingMarketingTotalQueryRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingDetailQueryResponse;
import com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingRewardResponse;
import com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingTotalQueryResponse;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.integratingmarketing.IIntegratingMarketingService;
import com.juneyaoair.ecs.manage.util.TimeRangeUtils;
import com.juneyaoair.ecs.utils.DateRangeUtils;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.mapper.MarketingIncentivePaymentPOMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName IntegratingMarketingServiceImpl
 * @Description 全员营销数据统计
 * <AUTHOR>
 * @Date 2025/6/29 11:01
 * @Version 1.0
 */

@Service
@SuppressWarnings("all")
public class IntegratingMarketingServiceImpl implements IIntegratingMarketingService {

    @Resource
    private AdminConfig config;

    @Resource
    private MarketingIncentivePaymentPOMapper marketingIncentivePaymentPOMapper;

    @Override
    public PageResult<IntegratingMarketingTotalQueryResponse> doTripSummaryQueryWithPage(IntegratingMarketingTotalQueryRequest request) {
        // 时间范围校验
        validateDateRange(request);

        // 设置时间格式
        String[] timeRange = TimeRangeUtils.getIntersectionTimeRange(config.getCountStartTime(), config.getCountEndTimeTime(),
                DateUtil.getDateStringAllDate(DateUtil.toDatePlusBeginTime(request.getAssociatedStartTime())), DateUtil.getDateStringAllDate(DateUtil.toDatePlusEndTime(request.getAssociatedEndTime())));
        if (null == timeRange) {
            return toGenerateRepsonse(request);
        }
        request.setAssociatedStartTime(timeRange[0]);
        request.setAssociatedEndTime(timeRange[1]);

        // 构建分页对象
        Page<IntegratingMarketingTotalQueryResponse> page = new Page<>(request.getPageNum(), request.getPageSize());

        // 查询主数据
        List<IntegratingMarketingTotalQueryResponse> dataList = marketingIncentivePaymentPOMapper.queryMarketingData(page, request);

        // 查询总数
        Long total = marketingIncentivePaymentPOMapper.queryMarketingDataCount(request);

        // 构建分页结果
        page.setTotal(total);
        return getPageData(dataList, page);
    }

    /**
     * @param request
     * @return com.juneyaoair.ecs.manage.dto.base.PageResult<com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingTotalQueryResponse>
     * <AUTHOR>
     * @Description 生成空的返回体
     * @Date 13:06 2025/7/16
     **/

    private PageResult<IntegratingMarketingTotalQueryResponse> toGenerateRepsonse(IntegratingMarketingTotalQueryRequest request) {
        Page<IntegratingMarketingTotalQueryResponse> totalQueryResponsePage = new Page<>(request.getPageNum(), request.getPageSize());
        totalQueryResponsePage.setTotal(0L);
        return getPageData(Collections.emptyList(), totalQueryResponsePage);
    }

    @Override
    public List<IntegratingMarketingTotalQueryResponse> queryMarketingDataForExport(IntegratingMarketingTotalQueryRequest integratingMarketingTotalQueryRequest) {
        String[] timeRange = TimeRangeUtils.getIntersectionTimeRange(config.getCountStartTime(), config.getCountEndTimeTime(),
                DateUtil.getDateStringAllDate(DateUtil.toDatePlusBeginTime(integratingMarketingTotalQueryRequest.getAssociatedStartTime())), DateUtil.getDateStringAllDate(DateUtil.toDatePlusEndTime(integratingMarketingTotalQueryRequest.getAssociatedEndTime())));
        if (null == timeRange) {
            return null;
        }
        integratingMarketingTotalQueryRequest.setAssociatedStartTime(timeRange[0]);
        integratingMarketingTotalQueryRequest.setAssociatedEndTime(timeRange[1]);

        return marketingIncentivePaymentPOMapper.queryMarketingDataForExport(integratingMarketingTotalQueryRequest);
    }

    @Override
    public List<IntegratingMarketingDetailQueryResponse> doTripDetailQuery(IntegratingMarketingDetailQueryRequest integratingMarketingDetailQueryRequest) {
        //查询时间间隔是否正确 是否小于0或者大于3个月
  /*      int associatedTimePeriod = DateUtil.dateDiff(integratingMarketingDetailQueryRequest.getAssociatedStartTime(), integratingMarketingDetailQueryRequest.getAssociatedEndTime(), DateUtil.DATE_FORMATE);
        if (associatedTimePeriod <= 0) {
            throw new HoServiceException("关联时间间隔非法");
        }
        if (associatedTimePeriod > 31) {
            throw new HoServiceException("关联时间间隔过大(已超过31个自然日),请缩小查询范围.");
        }
        int tripTimePeriod = DateUtil.dateDiff(integratingMarketingDetailQueryRequest.getTripStartTime(), integratingMarketingDetailQueryRequest.getTripEndTime(), DateUtil.DATE_FORMATE);
        if (tripTimePeriod <= 0) {
            throw new HoServiceException("出行时间时间间隔非法");
        }
        if (tripTimePeriod > 31) {
            throw new HoServiceException("出行时间间隔过大(已超过31个自然日),请缩小查询范围.");
        }*/
        String[] timeRange = TimeRangeUtils.getIntersectionTimeRange(config.getCountStartTime(), config.getCountEndTimeTime(), DateUtil.getDateStringAllDate(DateUtil.toDatePlusBeginTime(integratingMarketingDetailQueryRequest.getAssociatedStartTime())), DateUtil.getDateStringAllDate(DateUtil.toDatePlusEndTime(integratingMarketingDetailQueryRequest.getAssociatedEndTime())));
        if (null == timeRange) {
            return Collections.emptyList();
        }
        integratingMarketingDetailQueryRequest.setAssociatedStartTime(timeRange[0]);
        integratingMarketingDetailQueryRequest.setAssociatedEndTime(timeRange[1]);
        return marketingIncentivePaymentPOMapper.getTripIncentiveStatistics(integratingMarketingDetailQueryRequest);
    }

    @Override
    public List<IntegratingMarketingRewardResponse> downloadTripRewardList(IntegratingMarketingRewardsRequest integratingMarketingRewardsRequest) {
        Map<String, Date> monthRange = DateRangeUtils.getMonthRange(Integer.parseInt(integratingMarketingRewardsRequest.getYear()), Integer.parseInt(integratingMarketingRewardsRequest.getMonth()));
        return marketingIncentivePaymentPOMapper.getMonthlyIncentiveStatistics(monthRange.get("firstDayOfMonth"), monthRange.get("firstDayOfNextMonth"));
    }

    private void validateDateRange(IntegratingMarketingTotalQueryRequest request) {
/*        int associatedDays = DateUtil.dateDiff(request.getAssociatedStartTime(), request.getAssociatedEndTime(), DateUtil.DATE_FORMATE);
        if (associatedDays <= 0) {
            throw new HoServiceException("关联时间间隔非法");
        }
        if (associatedDays > 31) {
            throw new HoServiceException("关联时间间隔过大(已超过31个自然日),请缩小查询范围.");
        }

        int tripDays = DateUtil.dateDiff(request.getTripStartTime(), request.getTripEndTime(), DateUtil.DATE_FORMATE);
        if (tripDays <= 0) {
            throw new HoServiceException("出行时间间隔非法");
        }
        if (tripDays > 31) {
            throw new HoServiceException("出行时间间隔过大(已超过31个自然日),请缩小查询范围.");
        }*/
        if (null == request.getPageNum()) {
            throw new HoServiceException("页码值不可为空.");
        }
        if (request.getPageNum() < 1) {
            throw new HoServiceException("页码值最小为1.");
        }
        if (null == request.getPageSize()) {
            throw new HoServiceException("页大小不可为空.");
        }
        if (request.getPageSize() < 1) {
            throw new HoServiceException("页大小值最小为1.");
        }
    }

    private PageResult<IntegratingMarketingTotalQueryResponse> getPageData(List<IntegratingMarketingTotalQueryResponse> list, Page<IntegratingMarketingTotalQueryResponse> page) {
        PageResult<IntegratingMarketingTotalQueryResponse> result = new PageResult<>();
        result.setRows(list);
        result.setTotal(page.getTotal());
        result.setPageNum((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        return result;
    }
}
