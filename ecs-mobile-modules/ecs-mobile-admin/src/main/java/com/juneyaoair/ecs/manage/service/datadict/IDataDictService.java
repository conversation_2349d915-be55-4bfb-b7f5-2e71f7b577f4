package com.juneyaoair.ecs.manage.service.datadict;

import com.juneyaoair.ecs.manage.dto.datadict.DICTType;
import com.juneyaoair.ecs.manage.dto.notice.DICTValue;

import java.util.List;

/**
 * @ClassName IDataDictService
 * @Description
 * <AUTHOR>
 * @Date 2024/1/23 14:36
 * @Version 1.0
 */
public interface IDataDictService {
    List<DICTType> toCatchDICTTypeList(DICTType dICTType);

    void toAddDICTType(DICTType dICTType);

    void toUpdateDICTType(DICTType dICTType);

    void toDeleteDICTType(String ids);

    List<DICTValue> toCatchDICTValueList(DICTValue dictValue);

    void toAddDICTValue(DICTValue dictValue);

    void toUpdateDICTValue(DICTValue dictValue);

    void toDeleteDICTValue(String ids);
}
