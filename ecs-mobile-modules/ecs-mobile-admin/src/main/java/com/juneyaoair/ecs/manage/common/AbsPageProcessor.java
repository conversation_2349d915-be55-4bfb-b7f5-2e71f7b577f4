package com.juneyaoair.ecs.manage.common;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.controller.HoBaseController;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;

import java.util.List;

public abstract class AbsPageProcessor<REQ, RESItem> extends HoBaseController {
    private final REQ _req;

    public AbsPageProcessor(REQ req) {
        this._req = req;
    }


    public abstract List<RESItem> process();

    public R<PageResult<RESItem>> execute() {
        R<PageResult<RESItem>> ret = null;
        try {
            initContext();
            PageDomain pageDomain = TableSupport.buildPageRequest();
            startPage(pageDomain);
            Page<RESItem> localPage = PageHelper.getLocalPage();
            List<RESItem> list = process();
            ret = getPageData(list, localPage);
            if (ret != null) {
                ret.setMsg(ret.getMsg() + "_traceId:" + Context.getContext().getId());
            }
        } catch (Exception e) {
            throw new HoServiceException(e.getMessage() + "_traceId:" + Context.getContext().getId());
        } finally {
            Context.remove();
        }
        return ret;
    }
}
