package com.juneyaoair.ecs.manage.service.MemberRightsManage;

import com.juneyaoair.ecs.manage.dto.base.PageDataResponse;
import com.juneyaoair.ecs.manage.dto.memberRights.MemberLevelDTO;
import com.juneyaoair.ecs.manage.dto.memberRights.MemberRightsDTO;
import com.juneyaoair.ecs.manage.dto.memberRights.MemberRightsPageReq;
import com.juneyaoair.manage.b2c.entity.MemberLevelManagePO;
import com.juneyaoair.manage.b2c.entity.MemberRightsPO;
import com.juneyaoair.manage.thirdapi.crm.PtApiCRMRequest;
import org.springframework.web.multipart.MultipartFile;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/8/11 10:04
 * @Version 1.0
 */
public interface MemberRightsManageService {

    PageDataResponse memberRightsListPageQuery(MemberRightsPageReq memberRightsPageReq);

    String fileUpload(MultipartFile file);

    void addRights(MemberRightsDTO memberRightsDTO);

    void deleteRightsById(String id);

    void updateRightsById(MemberRightsDTO memberRightsDTO);

    MemberRightsPO getRightsById(String id);

    List<MemberLevelManagePO> searchAllMemberLevel();

    String syncCRM(PtApiCRMRequest<Map<String, String[]>> ptApiCRMRequest, String[] strings, String crmUrl) throws SQLException;

    MemberLevelManagePO searchLevelById(String id);

    void updateMemberLevel(MemberLevelDTO memberLevelDTO);
}

