package com.juneyaoair.ecs.manage.external;

import com.juneyaoair.ecs.manage.dto.activity.response.event.EventInfoPO;
import com.juneyaoair.ecs.manage.dto.activity.request.event.EventInfoQueryParam;
import com.juneyaoair.ecs.manage.dto.activity.response.event.EventPrizePO;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ActivityPrizeEntryParam;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ActivityPrizeEntryQueryParam;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ActivityPrizePoolParam;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ActivityPrizePoolQueryParam;
import com.juneyaoair.ecs.manage.dto.activity.response.prizepool.ActivityPrizeEntryInfo;
import com.juneyaoair.manage.b2c.entity.activity.ActivityPrizePoolPO;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.external.flightbaisc.AircraftTypeInfo;
import com.juneyaoair.ecs.manage.external.flightbaisc.AirlineMileageInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description FlightBasicService
 * @created 2023/8/17 9:22
 */
public interface FlightBasicService {

    /**
     * 航线航距数据
     * @param minMileage
     * @param maxMileage
     * @param depCity
     * @param arrCity
     * @return
     */
    List<AirlineMileageInfo> getAirLineMileage(String minMileage, String maxMileage, String depCity, String arrCity);

    /**
     * 查询机型信息
     * @return
     */
    List<AircraftTypeInfo> getAircraftTypeInfo();

    /**
     * <AUTHOR>
     * @Description 刷新特惠航线缓存信息
     * @Date 15:08 2023/12/8
     * @return void
     **/
    void refreshPriceCache();

    /**
     * 清空redis缓存信息
     */
    void clearNoticeCache();

    /**
     * 新增奖池
     * @param activityPrizePoolParam
     */
    void createPrizePool(ActivityPrizePoolParam activityPrizePoolParam);

    /**
     * 修改奖池信息
     * @param activityPrizePoolParam
     */
    void updatePrizePool(ActivityPrizePoolParam activityPrizePoolParam);

    /**
     * 变更奖池状态
     * @param activityPrizePoolParam
     */
    void changePrizePoolStatus(ActivityPrizePoolParam activityPrizePoolParam);

    /**
     * 新增奖池奖品
     * @param activityPrizeEntryParam
     */
    void createPrizeEntity(ActivityPrizeEntryParam activityPrizeEntryParam);

    /**
     * 更新奖池奖品
     * @param activityPrizeEntryParam
     */
    void updatePrizeEntity(ActivityPrizeEntryParam activityPrizeEntryParam);

    /**
     * 变更奖品状态
     * @param activityPrizeEntryParam
     */
    void changePrizeEntityStatus(ActivityPrizeEntryParam activityPrizeEntryParam);

    /**
     * 查询奖池列表
     * @param activityPrizePoolQueryParam
     * @return
     */
    PageResult<ActivityPrizePoolPO> getPrizePoolList(ActivityPrizePoolQueryParam activityPrizePoolQueryParam);

    /**
     * 查询奖品信息
     * @param activityPrizeEntryQueryParam
     * @return
     */
    List<ActivityPrizeEntryInfo> getPrizeEntityInfo(ActivityPrizeEntryQueryParam activityPrizeEntryQueryParam);

    /**
     * 刷新i18n缓存
     * @param dictionaryType
     * @param dictionaryMap
     * @return
     */
    void refreshI18nCache(String dictionaryType, Map<String, Map<String, String>> dictionaryMap);

    /**
     * 创建事件
     * @param eventInfo
     */
    void createEventInfo(EventInfoPO eventInfo);

    /**
     * 更新事件
     * @param eventInfo
     */
    void updateEventInfo(EventInfoPO eventInfo);

    /**
     * 新增事件奖品
     * @param eventPrize
     */
    void createPrize(EventPrizePO eventPrize);

    /**
     * 更新事件奖品
     * @param eventPrize
     */
    void updatePrize(EventPrizePO eventPrize);

    /**
     * 删除奖品
     * @param eventPrizeId
     */
    void deletePrize(String eventPrizeId);

    /**
     * 更新事件状态
     * @param eventInfoId
     * @param status
     */
    void updateEventStatus(String eventInfoId, String status);

    /**
     * 查询事件列表
     * @param eventInfoQueryParam
     * @return
     */
    PageResult<EventInfoPO> getEventInfoList(EventInfoQueryParam eventInfoQueryParam);

    /**
     * 查询事件奖品列表
     * @param eventInfoId
     * @return
     */
    List<EventPrizePO> getEventPrizeList(String eventInfoId);

}
