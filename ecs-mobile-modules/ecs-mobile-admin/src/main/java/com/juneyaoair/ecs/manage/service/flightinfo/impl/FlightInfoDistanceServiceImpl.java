package com.juneyaoair.ecs.manage.service.flightinfo.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.juneyaoair.ecs.manage.dto.flightdistance.FlightDistancePricedDTO;
import com.juneyaoair.ecs.manage.dto.flightdistance.FlightDistancePricedLogsDTO;
import com.juneyaoair.ecs.manage.mapstruct.FlightDistanceMapStruct;
import com.juneyaoair.ecs.manage.service.flightinfo.FlightInfoDistanceService;
import com.juneyaoair.manage.flight.entity.TFlightDistance;
import com.juneyaoair.manage.flight.entity.TFlightDistanceLog;
import com.juneyaoair.manage.flight.mapper.TFlightDistanceLogMapper;
import com.juneyaoair.manage.flight.mapper.TFlightDistanceMapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FlightInfoDistanceServiceImpl implements FlightInfoDistanceService {

    @Resource
    TFlightDistanceMapper tFlightDistanceMapper;
    @Resource
    TFlightDistanceLogMapper tFlightDistanceLogMapper;


    @Override
    public List<FlightDistancePricedDTO> select(FlightDistancePricedDTO param) {
        List<TFlightDistance> poList = tFlightDistanceMapper.select(param);
        return poList.stream().map(FlightDistanceMapStruct.INSTANCE::toFlightDistancePricedDTO)
                .collect(Collectors.toList());
    }

    @Override
    @DSTransactional
    public void add(FlightDistancePricedDTO param) {

        //
        TFlightDistance po = FlightDistanceMapStruct.INSTANCE.toTFlightDistancePO(param);
        po.setCreatedUser(SecurityUtils.getUsername());
        po.setCreatedTime(new Date());
        tFlightDistanceMapper.insert(po);

        //
        TFlightDistanceLog log = new TFlightDistanceLog();
        log.setFlightDistanceId(po.getId());
        log.setChangeType("新增");
        log.setNoticeFlag("N");
        log.setChangeComment(param.getDepCity() + "-" + param.getArrCity() + "航距:" + param.getMileage());
        log.setCreatedUser(SecurityUtils.getUsername());
        log.setCreatedTime(new Date());
        tFlightDistanceLogMapper.insert(log);

    }

    @Override
    @DSTransactional
    public void update(FlightDistancePricedDTO param) {

        //
        TFlightDistance poUpdate = FlightDistanceMapStruct.INSTANCE.toTFlightDistancePO(param);
        TFlightDistance po = tFlightDistanceMapper.selectById(param.getId());
        poUpdate.setUpdatedUser(SecurityUtils.getUsername());
        poUpdate.setUpdatedTime(new Date());
        tFlightDistanceMapper.updateById(poUpdate);

        //

        if (po.getMileage().compareTo(new BigDecimal(param.getMileage())) != 0) {
            TFlightDistanceLog log = new TFlightDistanceLog();
            log.setFlightDistanceId(poUpdate.getId());
            log.setChangeType("修改");
            log.setNoticeFlag("N");
            log.setChangeComment(param.getDepCity() + "-" + param.getArrCity() + "航距:" + po.getMileage() + "->" + param.getMileage());
            log.setCreatedUser(SecurityUtils.getUsername());
            log.setCreatedTime(new Date());
            tFlightDistanceLogMapper.insert(log);
        }


    }

    @Override
    public void pause(FlightDistancePricedDTO param) {
        TFlightDistance po = tFlightDistanceMapper.selectById(param.getId());
        po.setStatus(param.getStatus());
        tFlightDistanceMapper.updateById(po);
    }

    @Override
    public List<FlightDistancePricedLogsDTO> queryLog(FlightDistancePricedDTO param) {
        List<TFlightDistanceLog> logPOList = tFlightDistanceLogMapper.selectByDistanceId(param.getId());
        return logPOList.stream().map(FlightDistanceMapStruct.INSTANCE::logToFlightDistancePricedLogsDTO)
                .collect(Collectors.toList());
    }
}

