package com.juneyaoair.ecs.manage.service.activity.impl;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.juneyaoair.ecs.manage.dto.activity.redeem.segact.*;
import com.juneyaoair.ecs.manage.dto.activity.redeem.signact.SignActListRequest;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.enums.activity.redeem.RedeemActEnum;
import com.juneyaoair.ecs.manage.enums.activity.redeem.RedeemActPointEnum;
import com.juneyaoair.ecs.manage.enums.activity.redeem.RedeemActSegStateEnum;
import com.juneyaoair.ecs.manage.enums.activity.redeem.RedeemActStateEnum;
import com.juneyaoair.ecs.manage.service.activity.RedeemSegActService;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.*;
import com.juneyaoair.manage.b2c.mapper.*;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RedeemSegActServiceImpl implements RedeemSegActService {


    public static final String[] SEG_PRO_POINT = {"J", "C", "D", "Y", "B", "M"};

    @Resource
    RedeemActPOMapper actPOMapper;
    @Resource
    RedeemActAwardPOMapper awardPOMapper;
    @Resource
    RedeemActRedeemRecordPOMapper recordPOMapper;
    @Resource
    RedeemActPointPOMapper pointPOMapper;
    @Resource
    RedeemActSegPOMapper segPOMapper;

    @Override
    @DSTransactional
    public PageResult addSegAct(SegAddActRequest requestDto) {

        // 活动Id
        String actId = IdUtil.simpleUUID();

        // 配置活动
        RedeemActPO redeemActPO = RedeemActPO.builder()
                .id(actId)
                .actName(DateUtil.formatDateTime(requestDto.getActStartTime()) + "-" + DateUtil.formatDateTime(requestDto.getActEndTime()))
                .actStartTime(requestDto.getActStartTime())
                .actEndTime(requestDto.getActEndTime())
                .actType(RedeemActEnum.SEGMENT_2023.getActType())
                .actState(RedeemActStateEnum.NEW.getActState())
                .createdBy(SecurityUtils.getUsername())
                .updatedBy(SecurityUtils.getUsername())
                .createdTime(new Date())
                .updatedTime(new Date())
                .build();
        actPOMapper.insert(redeemActPO);

        // 配置活动奖品
        List<SegActAwardDTO> awardDTOS = requestDto.getAwardList();

        for (int i = 0; i < awardDTOS.size(); i++) {
            // 同一飞行券 飞行券pro 使用同一奖品Id
            String awardId = IdUtil.simpleUUID();

            if (awardDTOS.get(i).getPointCount() == null && awardDTOS.get(i).getProPointCount() == null) {
                throw new HoServiceException("至少配置一种消耗积分数量");
            }
            // 飞行券
            if (awardDTOS.get(i).getPointCount() != null) {
                RedeemActAwardPO redeemActAwardPO = RedeemActAwardPO.builder()
                        // 主键
                        .id(IdUtil.simpleUUID())
                        .actId(actId)
                        .awardId(awardId)
                        .awardName(awardDTOS.get(i).getAwardName())
                        .awardDesc(awardDTOS.get(i).getAwardDesc())
                        .monthlyLimit(awardDTOS.get(i).getMonthlyLimit())
                        .awardEnumType(RedeemActEnum.SEGMENT_2023.getActType())
                        .awardEnumTypeAttr(awardDTOS.get(i).getPrizePoolCode())
                        .createdBy(SecurityUtils.getUsername())
                        .updatedBy(SecurityUtils.getUsername())
                        .createdTime(new Date())
                        .updatedTime(new Date())
                        //
                        .consumedPointType(RedeemActPointEnum.SEG_NORMAL.getPointType())
                        .consumedPoint(String.valueOf(awardDTOS.get(i).getPointCount()))
                        .build();
                awardPOMapper.insert(redeemActAwardPO);
            }
            // 飞行券pro
            if (awardDTOS.get(i).getProPointCount()!=null) {
                RedeemActAwardPO redeemActAwardPO = RedeemActAwardPO.builder()
                        // 主键
                        .id(IdUtil.simpleUUID())
                        .actId(actId)
                        .awardId(awardId)
                        .awardName(awardDTOS.get(i).getAwardName())
                        .awardDesc(awardDTOS.get(i).getAwardDesc())
                        .monthlyLimit(awardDTOS.get(i).getMonthlyLimit())
                        .awardEnumType(RedeemActEnum.SEGMENT_2023.getActType())
                        .awardEnumTypeAttr(awardDTOS.get(i).getPrizePoolCode())
                        .createdBy(SecurityUtils.getUsername())
                        .updatedBy(SecurityUtils.getUsername())
                        .createdTime(new Date())
                        .updatedTime(new Date())
                        //
                        .consumedPointType(RedeemActPointEnum.SEG_PRO.getPointType())
                        .consumedPoint(String.valueOf(awardDTOS.get(i).getProPointCount()))
                        .build();
                awardPOMapper.insert(redeemActAwardPO);
            }
        }
        return null;
    }

    @Override
    @DSTransactional
    public void updateSegAct(SegUpdateActRequest requestDto) {

        RedeemActPO actPO = actPOMapper.selectById(requestDto.getId());

        // 活动Id
        String actId = actPO.getId();

        // 校验 活动状态
        if (actPO.getActState().equals(RedeemActStateEnum.AUDITED.getActState())
                || actPO.getActState().equals(RedeemActStateEnum.DELETE.getActState())) {
            throw new HoServiceException("已审核活动不可更新");
        }


        // Update ACT
        actPO.setActStartTime(requestDto.getActStartTime());
        actPO.setActEndTime(requestDto.getActEndTime());
        actPO.setUpdatedBy(SecurityUtils.getUsername());
        actPO.setUpdatedTime(new Date());
        actPOMapper.updateById(actPO);

        // Update ACT AWARD

        // DELETE OLD
        List<RedeemActAwardPO> awardPOS = awardPOMapper.selectByActId(actPO.getId());
        awardPOMapper.deleteBatchIds(awardPOS.stream().map(RedeemActAwardPO::getId).collect(Collectors.toList()));

        // INSERT NEW
        List<SegActAwardDTO> awardDTOS = requestDto.getAwardList();

        for (int i = 0; i < awardDTOS.size(); i++) {
            // 同一飞行券 飞行券pro 使用同一奖品Id
            String awardId = IdUtil.simpleUUID();
            if (awardDTOS.get(i).getPointCount() == null && awardDTOS.get(i).getProPointCount() == null) {
                throw new HoServiceException("至少配置一种消耗积分数量");
            }
            // 飞行券
            if (awardDTOS.get(i).getPointCount() != null) {
                RedeemActAwardPO redeemActAwardPO = RedeemActAwardPO.builder()
                        // 主键
                        .id(IdUtil.simpleUUID())
                        .actId(actId)
                        .awardId(awardId)
                        .awardName(awardDTOS.get(i).getAwardName())
                        .awardDesc(awardDTOS.get(i).getAwardDesc())
                        .monthlyLimit(awardDTOS.get(i).getMonthlyLimit())
                        .awardEnumType(RedeemActEnum.SEGMENT_2023.getActType())
                        .awardEnumTypeAttr(awardDTOS.get(i).getPrizePoolCode())
                        .createdBy(SecurityUtils.getUsername())
                        .updatedBy(SecurityUtils.getUsername())
                        .createdTime(new Date())
                        .updatedTime(new Date())
                        //
                        .consumedPointType(RedeemActPointEnum.SEG_NORMAL.getPointType())
                        .consumedPoint(String.valueOf(awardDTOS.get(i).getPointCount()))
                        .build();
                awardPOMapper.insert(redeemActAwardPO);
            }
            // 飞行券pro
            if (awardDTOS.get(i).getProPointCount() != null) {
                RedeemActAwardPO redeemActAwardPO = RedeemActAwardPO.builder()
                        // 主键
                        .id(IdUtil.simpleUUID())
                        .actId(actId)
                        .awardId(awardId)
                        .awardName(awardDTOS.get(i).getAwardName())
                        .awardDesc(awardDTOS.get(i).getAwardDesc())
                        .monthlyLimit(awardDTOS.get(i).getMonthlyLimit())
                        .awardEnumType(RedeemActEnum.SEGMENT_2023.getActType())
                        .awardEnumTypeAttr(awardDTOS.get(i).getPrizePoolCode())
                        .createdBy(SecurityUtils.getUsername())
                        .updatedBy(SecurityUtils.getUsername())
                        .createdTime(new Date())
                        .updatedTime(new Date())
                        //
                        .consumedPointType(RedeemActPointEnum.SEG_PRO.getPointType())
                        .consumedPoint(String.valueOf(awardDTOS.get(i).getProPointCount()))
                        .build();
                awardPOMapper.insert(redeemActAwardPO);
            }
        }
    }


    @Override
    public void commonDelAct(DeleteActRequest requestDto) {

        RedeemActPO actPO = actPOMapper.selectById(requestDto.getId());

        // 校验 活动状态
        if (actPO.getActState().equals(RedeemActStateEnum.AUDITED.getActState())
                || actPO.getActState().equals(RedeemActStateEnum.DELETE.getActState())) {
            throw new HoServiceException("已审核活动不可删除");
        }

        // UPDATE DEL STATE
        actPO.setActState(RedeemActStateEnum.DELETE.getActState());
        actPO.setUpdatedBy(SecurityUtils.getUsername());
        actPO.setUpdatedTime(new Date());
        actPOMapper.updateById(actPO);
    }

    @Override
    public List<SegActUser> segActUserList(SegActUserListRequest requestDto) {

        List<SegActUser> segActUserList = pointPOMapper.selectActUserList(requestDto);

        segActUserList.forEach(v -> {

            List<RedeemActSegPO> computedList = segPOMapper.selectByFfpNoComputed(v.getFfpNo());

            long pointNextExpiredCount = computedList.stream().filter(seg -> {
                boolean isPro = Arrays.asList(SEG_PRO_POINT).contains(seg.getBookingClass());
                boolean isWillExpire = isInvalidNextMonth(seg.getFlightDate());
                return !isPro && isWillExpire;
            }).count();

            long proNextExpiredCount = computedList.stream().filter(seg -> {
                boolean isPro = Arrays.asList(SEG_PRO_POINT).contains(seg.getBookingClass());
                boolean isWillExpire = isInvalidNextMonth(seg.getFlightDate());
                return isPro && isWillExpire;
            }).count();


            // 过期数量
            v.setPointNextExpiredCount(String.valueOf(pointNextExpiredCount));
            v.setProPointNextExpiredCount(String.valueOf(proNextExpiredCount));
        });


        return segActUserList;
    }

    /**
     * 是否下月失效
     *
     * @param flightDate
     * @return
     */
    private boolean isInvalidNextMonth(String flightDate) {
        Date segDate = DateUtil.parse(flightDate);
        Date date = new Date();
        return DateUtil.isIn(
                segDate,
                segNextExpireStatDate(date, true),
                segNextExpireStatDate(date, false));
    }


    /**
     * 返回 下月将失效航段 时间范围起/始
     * 超过N月25日  统计 （N-14）月1日 0:0:0 ~ （N-14）月最后一日 23:59:59
     * 未超过N月25日  统计 （N-15）月1日 0:0:0 ~ （N-15）月最后一日 23:59:59
     *
     *
     * @param date        指定日期
     * @param returnStart returnStart true 返回开始日期
     * @return
     */
    private Date segNextExpireStatDate(Date date, boolean returnStart) {

        Calendar c = the25ThOfMonth(date);
        // 超过当月25日
        if (date.after(c.getTime())) {
            Date nowOffset14 = DateUtil.offsetMonth(date, -14);
            //
            Calendar begin = Calendar.getInstance(TimeZone.getTimeZone("GMT+8"));
            begin.setTime(nowOffset14);
            begin.set(Calendar.DAY_OF_MONTH, 1);
            begin.set(Calendar.HOUR_OF_DAY, 0);
            begin.set(Calendar.MINUTE, 0);
            begin.set(Calendar.SECOND, 0);
            begin.set(Calendar.MILLISECOND, 0);
            //
            Calendar end = Calendar.getInstance(TimeZone.getTimeZone("GMT+8"));
            end.setTime(nowOffset14);
            end.set(Calendar.HOUR_OF_DAY, 23);
            end.set(Calendar.MINUTE, 59);
            end.set(Calendar.SECOND, 59);
            end.set(Calendar.MILLISECOND, 59);
            end.set(Calendar.DAY_OF_MONTH, end.getActualMaximum(Calendar.DAY_OF_MONTH));
            //
            return returnStart ? begin.getTime() : end.getTime();
        } else {
            Date nowOffset15 = DateUtil.offsetMonth(date, -15);
            //
            Calendar begin = Calendar.getInstance(TimeZone.getTimeZone("GMT+8"));
            begin.setTime(nowOffset15);
            begin.set(Calendar.DAY_OF_MONTH, 1);
            begin.set(Calendar.HOUR_OF_DAY, 0);
            begin.set(Calendar.MINUTE, 0);
            begin.set(Calendar.SECOND, 0);
            begin.set(Calendar.MILLISECOND, 0);
            //
            Calendar end = Calendar.getInstance(TimeZone.getTimeZone("GMT+8"));
            end.setTime(nowOffset15);
            end.set(Calendar.HOUR_OF_DAY, 23);
            end.set(Calendar.MINUTE, 59);
            end.set(Calendar.SECOND, 59);
            end.set(Calendar.MILLISECOND, 59);
            end.set(Calendar.DAY_OF_MONTH, end.getActualMaximum(Calendar.DAY_OF_MONTH));
            //
            return returnStart ? begin.getTime() : end.getTime();
        }
    }


    /**
     * 当月25日
     *
     * @return
     */
    public Calendar the25ThOfMonth(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DAY_OF_MONTH, 25);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        return c;
    }


    @Override
    public List<SegRedeemDTO> segRedeemRecord(SegRedeemRecordRequest requestDto) {

        List<RedeemActRedeemRecordPO> redeemRecordPOS = recordPOMapper.selectSegRedeemRecord(requestDto);

        return redeemRecordPOS.stream().map(v -> SegRedeemDTO.builder()
                .redeemTime(v.getCreateTime())
                .awardName(v.getAwardName())
                .awardDes(v.getAwardDesc())
                .ffpNo(v.getFfpNo())
                .pointCount(v.getPoint())
                .redeemState(v.getIssuanceState())
                .ticketNo(v.getTicketNumber())
                .build()).collect(Collectors.toList());
    }

    @Override
    public void commonAuditAct(ActAuditRequest request) {

        RedeemActPO actPO = actPOMapper.selectById(request.getId());
        List<RedeemActPO> allAudit = actPOMapper.selectAllAudit(actPO.getActType());

        // 其他已审核活动时间 活动时间冲突
        allAudit.forEach(v -> {
            if (DateUtil.isIn(actPO.getActStartTime(), v.getActStartTime(), v.getActEndTime())
                    || DateUtil.isIn(actPO.getActEndTime(), v.getActStartTime(), v.getActEndTime())) {
                throw new HoServiceException("与已审核活动"
                        + DateUtil.formatDateTime(v.getActStartTime())
                        + DateUtil.formatDateTime(v.getActEndTime())
                        + "活动时间冲突");
            }
        });

        // 校验非本人操作
        String updateUser = SecurityUtils.getUsername();
        if (updateUser.equals(actPO.getUpdatedBy())) {
            throw new HoServiceException("不能由同一人审核");
        }

        // 更新状态
        actPO.setActState(RedeemActStateEnum.AUDITED.getActState());
        actPO.setAuditedBy(updateUser);
        actPO.setUpdatedBy(updateUser);
        actPO.setUpdatedTime(new Date());

        //
        actPOMapper.updateById(actPO);
    }

    @Override
    public List<SegExcelBO> exportSegActExcel(SegExportRequest requestDto) {

        List<RedeemActSegPO> segPOList = segPOMapper.selectByFfpNo(requestDto.getFfpNo());

        return segPOList.stream().filter(v ->
                        !v.getActState().equals(RedeemActSegStateEnum.UNABLE.getActSegState())
                                && !v.getActState().equals(RedeemActSegStateEnum.EXPIRED.getActSegState()))
                .map(v -> SegExcelBO.builder()
                        .ffpNo(v.getFfpNo())
                        .bookingClass(v.getBookingClass())
                        .ticketNumber(v.getTicketNumber())
                        .flightDate(v.getFlightDate())
                        .airlineCode(v.getAirlineCode())
                        .flightNumber(v.getFlightNumber())
                        .isPro(Arrays.asList(SEG_PRO_POINT).contains(v.getBookingClass()))
                        .build()).collect(Collectors.toList());


    }

    @Override
    public List<SegActBO> segActList(SignActListRequest request) {

        List<SegActBO> resData = new ArrayList<>();

        List<RedeemActPO> actPOS = actPOMapper.selectActByTime(request, RedeemActEnum.SEGMENT_2023.getActType());

        for (RedeemActPO actPO : actPOS) {

            List<RedeemActAwardPO> awardPOS = awardPOMapper.selectByActId(actPO.getId());

            // 竖表 合并awardId
            List<SegAwardBO> awardList = new ArrayList<>(awardPOS.stream().collect(Collectors.toMap(
                    RedeemActAwardPO::getAwardId,
                    v -> SegAwardBO.builder()
                            .id(v.getId())
                            .awardName(v.getAwardName())
                            .awardDesc(StringEscapeUtils.unescapeHtml(v.getAwardDesc()))
                            .monthlyLimit(v.getMonthlyLimit())
                            .consumedPoint(v.getConsumedPoint())
                            .consumedPointType(v.getConsumedPointType())
                            //
                            .pointCount(RedeemActPointEnum.SEG_NORMAL.getPointType().equals(v.getConsumedPointType()) ? v.getConsumedPoint() : null)
                            .proPointCount(RedeemActPointEnum.SEG_PRO.getPointType().equals(v.getConsumedPointType()) ? v.getConsumedPoint() : null)
                            //
                            .prizePoolCode(v.getAwardEnumTypeAttr())
                            .build(),
                    (v1, v2) -> {
                        if (RedeemActPointEnum.SEG_PRO.getPointType().equals(v1.getConsumedPointType())) {
                            v1.setPointCount(v2.getConsumedPoint());
                        }
                        if (RedeemActPointEnum.SEG_NORMAL.getPointType().equals(v1.getConsumedPointType())) {
                            v1.setProPointCount(v2.getConsumedPoint());
                        }
                        return v1;
                    })).values());

            resData.add(SegActBO.builder()
                    .id(actPO.getId())
                    .actStartTime(actPO.getActStartTime())
                    .actEndTime(actPO.getActEndTime())
                    .auditedBy(actPO.getAuditedBy())
                    .createBy(actPO.getCreatedBy())
                    .updateBy(actPO.getUpdatedBy())
                    .awardList(awardList)
                    .build());
        }
        return resData;
    }
}
