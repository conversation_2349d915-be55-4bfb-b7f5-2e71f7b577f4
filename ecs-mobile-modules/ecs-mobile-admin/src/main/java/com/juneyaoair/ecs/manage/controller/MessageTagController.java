package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.message.request.MessageTagDto;
import com.juneyaoair.ecs.manage.service.message.IMessageTagAggrService;
import com.juneyaoair.manage.b2c.service.IMessageTagService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@RestController
@Api(value = "MessageTagController", tags = "消息标签管理")
@RequestMapping("/messageTag")
public class MessageTagController extends HoBaseController {
    @Autowired
    private IMessageTagService messageTagService;
    @Autowired
    private IMessageTagAggrService messageTagAggrService;

    @PostMapping("/list")
    @ApiOperation(value = "消息标签", notes = "")
    public R list(){
        return R.ok(messageTagService.list());
    }

    @PostMapping("/add")
    @ApiOperation(value = "新增消息标签", notes = "")
    public R add(@RequestBody @Validated MessageTagDto messageTagDto){
        return R.ok(messageTagAggrService.addMessageTag(messageTagDto));
    }
}
