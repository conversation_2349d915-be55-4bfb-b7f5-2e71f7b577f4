package com.juneyaoair.ecs.manage.external;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.SysDictData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 若依系统服务
 * @created 2024/3/19 10:08
 */
@FeignClient(value = "ruoyi-system")
public interface RuoyiSystemBaseService {

    /**
     * 根据字典类型查询字典数据信息
     * @param dictType
     * @return
     */
    @GetMapping({"/dict/data/type/{dictType}"})
    R<List<SysDictData>> dictType(@PathVariable("dictType") String dictType);

}
