package com.juneyaoair.ecs.manage.controller.activity;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.controller.HoBaseController;
import com.juneyaoair.ecs.manage.dto.activity.redeem.segact.ActAuditRequest;
import com.juneyaoair.ecs.manage.dto.activity.redeem.segact.DeleteActRequest;
import com.juneyaoair.ecs.manage.dto.activity.redeem.signact.*;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.activity.RedeemSegActService;
import com.juneyaoair.ecs.manage.service.activity.RedeemSignActService;
import com.juneyaoair.ecs.utils.ExcelUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.RedeemActPO;
import com.juneyaoair.manage.b2c.entity.RedeemActRedeemRecordPO;
import com.juneyaoair.manage.b2c.entity.RedeemActSignPO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 2023 - 积分兑换活动 - 每日签到
 */
@RequestMapping("signRedeem")
@RestController
@RequiredArgsConstructor
@Api(value = "SignRedeemController", tags = "2023-积分兑换活动-每日签到兑换(每日签到)")
@Slf4j
public class SignRedeemController extends HoBaseController {


    @Autowired
    RedeemSegActService redeemSegActService;
    @Resource
    RedeemSignActService redeemSignActService;

    @ApiOperation(value = "会员资产查询(每日签到)", notes = "", httpMethod = "POST")
    @PostMapping(value = "userList")
    public R<PageResult<SignActUser>> userList(@RequestBody SignActUserListRequest request) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        Page<SignActUser> pageInfo = PageHelper.getLocalPage();
        List<SignActUser> list = redeemSignActService.signActUserList(request);
        return getPageData(list, pageInfo);
    }

    @ApiOperation(value = "导出单个用户月度签到明细(每日签到)", notes = "", httpMethod = "POST")
    @PostMapping(value = "exportActExcel")
    public void exportExcel(@RequestBody SignExportRequest request, HttpServletResponse response) {
        try {
            List<RedeemActSignPO> sheetList = redeemSignActService.exportSignActExcel(request);
            LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
            fieldMap.put("ffpNo", "会员卡号");
            fieldMap.put("monthStr", "月度");
            fieldMap.put("punchInTime", "签到日期");
            String fileName = request.getFfpNo() + "_" + request.getMonth();
            ExcelUtil.listToExcel(sheetList, fieldMap, fileName, response);
        } catch (Exception e) {
            throw new HoServiceException("导出用户月度签到明细异常");
        }
    }

    @ApiOperation(value = "查询活动配置(每日签到)", notes = "", httpMethod = "POST")
    @PostMapping(value = "actList")
    public R<PageResult<SignActBO>> querySignAct(@RequestBody SignActListRequest request) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        Page<RedeemActPO> pageInfo = PageHelper.getLocalPage();
        List<SignActBO> list = redeemSignActService.signActList(request);
        return getPageData(list, pageInfo);
    }

    @ApiOperation(value = "新增活动配置(每日签到)", notes = "", httpMethod = "POST")
    @PostMapping(value = "addAct")
    public R addAct(@RequestBody SignAddActRequest request) {
        redeemSignActService.addSignAct(request);
        return R.ok();
    }

    @ApiOperation(value = "编辑活动配置(每日签到)", notes = "", httpMethod = "POST")
    @PostMapping(value = "updateAct")
    public R updateAct(@RequestBody SignUpdateActRequest request) {
        redeemSignActService.updateSignAct(request);
        return R.ok();
    }

    @ApiOperation(value = "删除活动配置(每日签到)", notes = "", httpMethod = "POST")
    @PostMapping(value = "delAct")
    public R delAct(@RequestBody DeleteActRequest request) {
        // 通用删除
        redeemSegActService.commonDelAct(request);
        return R.ok();
    }

    @ApiOperation(value = "审核活动配置(每日签到)", notes = "另一用户点击确认活动", httpMethod = "POST")
    @PostMapping(value = "auditAct")
    public R auditAct(@RequestBody ActAuditRequest request) {
        // 通用审核
        redeemSegActService.commonAuditAct(request);
        return R.ok();
    }

    @ApiOperation(value = "兑换记录(每日签到)", notes = "兑换记录", httpMethod = "POST")
    @PostMapping(value = "redeemRecord")
    public R<PageResult<SignRedeemRecordBO>> redeemRecord(@RequestBody SignRedeemRecordRequest request) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        Page<RedeemActRedeemRecordPO> pageInfo = PageHelper.getLocalPage();
        List<SignRedeemRecordBO> list = redeemSignActService.redeemRecord(request);
        return getPageData(list, pageInfo);
    }

    @ApiOperation(value = "导出兑换记录(每日签到)", notes = "", httpMethod = "POST")
    @PostMapping(value = "exportRedeemRecordExcel")
    public void exportExcel(@RequestBody SignRedeemRecordRequest request, HttpServletResponse response) {
        try {
            List<SignRedeemRecordBO> sheetList = redeemSignActService.redeemRecord(request);
            //
            LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
            fieldMap.put("redeemTime", "兑换时间");
            fieldMap.put("awardName", "奖品名称");
            fieldMap.put("awardDes", "奖品描述");
            fieldMap.put("ffpNo", "会员卡号");
            fieldMap.put("pointCount", "消耗货币");
            fieldMap.put("redeemState", "兑换状态");
            //
            String fileName = DateUtils.getTime() + "_" + "兑换记录";
            ExcelUtil.listToExcel(sheetList, fieldMap, fileName, response);
        } catch (Exception e) {
            throw new HoServiceException("导出每日签到兑换记录异常");
        }
    }

}