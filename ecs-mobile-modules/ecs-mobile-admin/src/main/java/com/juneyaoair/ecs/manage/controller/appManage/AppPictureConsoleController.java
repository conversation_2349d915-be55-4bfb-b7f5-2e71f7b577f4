package com.juneyaoair.ecs.manage.controller.appManage;

import com.juneyaoair.ecs.manage.controller.HoBaseController;
import com.juneyaoair.ecs.manage.dto.activity.response.AppPicture;
import com.juneyaoair.ecs.manage.dto.activity.response.AppPictureConsole;
import com.juneyaoair.ecs.manage.dto.activity.response.DICTValue;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.apppictureconsole.IAppPictureConsoleAggrService;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.ecs.utils.SystemConstants;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.service.IAppPicService;
import com.juneyaoair.manage.b2c.service.IDictvalueService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Api(value = "AppPictureConsoleController", tags = "app版本管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("manageAppPicture")
@Slf4j
public class AppPictureConsoleController extends HoBaseController {
    @Resource
    private IAppPicService appPictureService;

    @Resource
    IAppPictureConsoleAggrService appPictureConsoleService;
    @Resource
    IDictvalueService dictvalueService;

    /**
     * 图片信息一览获取
     *
     * @param appPicture
     * @return
     */
    @RequestMapping(value = "getList", method = RequestMethod.POST)
    @ApiOperation(value = "分页获取图片信息", notes = "")
    public R<PageResult<AppPicture>> getList(@RequestBody AppPicture appPicture) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        List<AppPicture> appPictures = appPictureService.toGainRecordsByCondition(appPicture);
        return getPageData(appPictures, pageDomain);
    }

    /**
     * 根据id获取图片信息
     *
     * @param req
     * @param request
     * @return
     */
    @RequestMapping(value = "getPicById", method = RequestMethod.POST)
    @ApiOperation(value = "根据id获取图片信息", notes = "")
    public R<AppPicture> getPicById(@RequestBody AppPictureConsole req, HttpServletRequest request) {
        if (null == req) {
            throw new HoServiceException("请求参数不可为空！");
        }
        AppPicture picture = new AppPicture();
        BeanUtils.copyProperties(req,picture);
        return R.ok(appPictureConsoleService.getPicture(picture, request));
    }

    /**
     * 图片内容添加
     *
     * @param appPicture
     * @return
     */
    @RequestMapping(value = "addPic", method = RequestMethod.POST)
    @ApiOperation(value = "图片内容添加", notes = "")
    public R<Boolean> addPic(@RequestBody AppPicture appPicture) {
        return R.ok(appPictureConsoleService.addPicture(appPicture));
    }

    /**
     * 图片删除
     *
     * @param pictureConsole
     * @return
     */
    @RequestMapping(value = "delPic", method = RequestMethod.POST)
    @ApiOperation(value = "根据id删除图片信息", notes = "")
    public R<Boolean> delPic(@RequestBody AppPictureConsole pictureConsole) {
        AppPicture appPicture = new AppPicture();
        BeanUtils.copyProperties(pictureConsole, appPicture);
        return R.ok(appPictureConsoleService.delPicture(appPicture));
    }

    /**
     * 图片发布下线
     *
     * @param appPicture
     * @return
     */
    @RequestMapping(value = "upPic", method = RequestMethod.POST)
    @ApiOperation(value = "主要更新status(上下线)", notes = "")
    public R<Boolean> upPic(@RequestBody AppPicture appPicture) {
        return R.ok(appPictureConsoleService.releaseAndOfflinePicture(appPicture));
    }

    /**
     * 图片更新
     *
     * @param appPicture
     * @return
     */
    @RequestMapping(value = "updatePic", method = RequestMethod.POST)
    @ApiOperation(value = "图片更新", notes = "")
    public Object updatePic(@RequestBody AppPicture appPicture) {
        return R.ok(appPictureConsoleService.updatePicture(appPicture));
    }

    @RequestMapping(value = "getChannelList", method = RequestMethod.POST)
    @ApiOperation(value = "", notes = "")
    @SuppressWarnings("rawtypes")
    public R getChannelList() {
        DICTValue value = new DICTValue();
        value.setDtCode(SystemConstants.MOBILEMODEL);
        List<DICTValue> dictValueListV2 = dictvalueService.findDICTValueListV2(value);
        List<Map<String, String>> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dictValueListV2)) {
            for (DICTValue string : dictValueListV2) {
                Map<String, String> resultMap = new HashMap<>();
                resultMap.put("mobileCode", string.getDvCode());
                resultMap.put("mobileName", string.getDvName());
                result.add(resultMap);
            }
        }
        return R.ok(result);
    }
}
