package com.juneyaoair.ecs.manage.aop;

import com.juneyaoair.ecs.manage.dto.datadict.VersionManage;
import com.juneyaoair.ecs.redis.service.PrimaryRedisService;
import com.juneyaoair.manage.b2c.mapper.VersionManageMapper;
import com.juneyaoair.manage.thirdapi.IFlightBasicCacheService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 模块信息维护后触发的切面
 */
@Aspect
@Component
public class ModularAOP {
    private static final Logger log = LoggerFactory.getLogger(ModularAOP.class);

    @Resource
    private VersionManageMapper versionManageMapper;

    @Resource
    private PrimaryRedisService primaryRedisService;

    @Autowired
    private IFlightBasicCacheService flightBasicCacheService;

    @AfterReturning(
            pointcut = "execution(* com.juneyaoair.manage.b2c.service.impl.ModularServiceImpl.save*(..)) || " +
                    "execution(* com.juneyaoair.manage.b2c.service.impl.ModularServiceImpl.update*(..)) || " +
                    "execution(* com.juneyaoair.manage.b2c.service.impl.ModularServiceImpl.delete*(..))",
            returning = "result")
    public synchronized void afterModularOperation(JoinPoint point, Object result) {
        if (!(result instanceof Boolean) || !((Boolean) result)) {
            return;
        }

        try {
            // 更新版本号
            VersionManage versionManage = new VersionManage();
            versionManage.setVersionType("SERVICE_INDEX");
            String current = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            versionManage.setVersionNo(current);
            versionManage.setChannelCode("MOBILE");
            versionManageMapper.updateVersion(versionManage);
            
            // 清除缓存
            try {
                log.info("模块管理缓存清除开始!");
                flightBasicCacheService.clearModuleTabCache();
                log.info("模块管理缓存清除成功!");
            } catch (Exception e) {
                log.error("模块管理缓存清除失败! 异常信息：", e);
            }
        } catch (Exception e) {
            log.error("ModularAOP 处理异常", e);
        }
    }
} 