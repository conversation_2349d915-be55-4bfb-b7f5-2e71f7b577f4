package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.I18n.*;
import com.juneyaoair.ecs.manage.service.i18n.I18nDictionaryService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.http.MediaType;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;

import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import com.juneyaoair.ecs.manage.enums.LanguageEnum;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayInputStream;

@RequestMapping("i18nDictionary")
@RestController
@RequiredArgsConstructor
@Api(value = "I18nDictionaryController", tags = "国际化语言字典")
@Slf4j
public class I18nDictionaryController extends HoBaseController {

    @Resource
    private I18nDictionaryService i18nDictionaryService;

    @ApiOperation(value = "新增字典", notes = "", httpMethod = "POST")
    @PostMapping(value = "add")
    public R add(@RequestBody AddI18nDictionaryRequestDTO request) {
        i18nDictionaryService.add(request);
        return R.ok();
    }

    @ApiOperation(value = "新增字典KEY,VALUE", notes = "", httpMethod = "POST")
    @PostMapping(value = "addKV")
    public R addKV(@RequestBody AddI18nDictionaryKVRequestDTO request) {
        i18nDictionaryService.addKV(request);
        return R.ok();
    }

    @ApiOperation(value = "更新字典KEY,VALUE", notes = "", httpMethod = "POST")
    @PostMapping(value = "updateKV")
    public R updateKV(@RequestBody UpdateI18nDictionaryKVRequestDTO request) {
        i18nDictionaryService.updateKV(request);
        return R.ok();
    }

    @ApiOperation(value = "删除字典", notes = "", httpMethod = "POST")
    @PostMapping(value = "delete")
    public R delete(@RequestBody QueryI18nDictionaryKVRequestDTO request) {
        i18nDictionaryService.delete(request.getDictionaryId());
        return R.ok();
    }

    @ApiOperation(value = "查询字典", notes = "", httpMethod = "POST")
    @PostMapping(value = "query")
    public R<PageResult<I18nDictionaryDTO>> query(@RequestBody QueryI18nDictionaryRequestDTO request) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        List<I18nDictionaryDTO> list = i18nDictionaryService.query(request.getDictionaryName(), request.getDictionaryType());
        return getPageData(list, pageDomain);
    }

    @ApiOperation(value = "查询字典KV", notes = "", httpMethod = "POST")
    @PostMapping(value = "queryKV")
    public R<PageResult<I18nDictionaryKVDTO>> queryKV(@RequestBody QueryI18nDictionaryKVRequestDTO request) {
        PageDomain pageDomain = TableSupport.buildPageRequest();

        // 如果需要通过dictionaryId查询dictionaryType，先处理这个逻辑
        String finalDictionaryType = request.getDictionaryType();
        if (StringUtils.isBlank(finalDictionaryType) && StringUtils.isNotBlank(request.getDictionaryId())) {
            // 这里不使用分页，直接查询字典信息获取类型
            finalDictionaryType = i18nDictionaryService.getDictionaryTypeById(request.getDictionaryId());
            if (StringUtils.isBlank(finalDictionaryType)) {
                return R.ok(new PageResult<>(new ArrayList<>(), 0L, pageDomain.getPageNum(), pageDomain.getPageSize()));
            }
        }

        if (StringUtils.isBlank(finalDictionaryType)) {
            return R.ok(new PageResult<>(new ArrayList<>(), 0L, pageDomain.getPageNum(), pageDomain.getPageSize()));
        }

        // 现在开始分页查询
        startPage(pageDomain);
        List<I18nDictionaryKVDTO> list = i18nDictionaryService.queryKVByType(
            finalDictionaryType,
            request.getOriginal(),
            request.getLanguageTag(),
            request.getTranslation(),
            request.getFuzzyOriginal()
        );
        return getPageData(list, pageDomain);
    }

    @ApiOperation(value = "获取语言列表", notes = "获取系统支持的语言列表", httpMethod = "POST")
    @PostMapping(value = "languages")
    public R<List<LanguageEnumDTO>> getLanguages() {
        List<LanguageEnumDTO> languages = Arrays.stream(LanguageEnum.values())
                .map(lang -> {
                    LanguageEnumDTO dto = new LanguageEnumDTO();
                    dto.setCode(lang.name());
                    dto.setDesc(lang.getDesc());
                    return dto;
                })
                .collect(Collectors.toList());
        return R.ok(languages);
    }

    @ApiOperation(value = "更新字典", notes = "更新字典信息，如果字典类型变化会同步更新关联的翻译", httpMethod = "POST")
    @PostMapping(value = "update")
    public R update(@RequestBody UpdateI18nDictionaryRequestDTO request) {
        i18nDictionaryService.update(request);
        return R.ok();
    }

    @ApiOperation(value = "删除字典KV", notes = "删除字典键值对", httpMethod = "POST")
    @PostMapping(value = "deleteKV")
    public R deleteKV(@RequestBody UpdateI18nDictionaryKVRequestDTO request) {
        i18nDictionaryService.deleteKV(request.getId());
        return R.ok();
    }

    @ApiOperation(value = "批量导入字典", notes = "Excel无需标题行，从第一行开始读取\n" +
            "列顺序：字典类型、原文、译文、语言标签\n" +
            "示例：BUSINESS_DEPARTMENT    MFM.NAME    澳門營業部    ZH_HK\n" +
            "语言标签：ZH_CN, ZH_HK, EN_US, JA_JP, TH_TH, KO_KR", 
            httpMethod = "POST")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R importData(@ApiParam(value = "Excel文件", required = true) @RequestPart("file") MultipartFile file) {
        if (file.isEmpty()) {
            return R.fail("请选择要上传的文件");
        }
        try {
            ByteArrayInputStream inputStream = new ByteArrayInputStream(file.getBytes());
            i18nDictionaryService.importData(inputStream);
            return R.ok("导入成功");
        } catch (Exception e) {
            log.error("导入失败", e);
            return R.fail(e.getMessage());
        }
    }
}
