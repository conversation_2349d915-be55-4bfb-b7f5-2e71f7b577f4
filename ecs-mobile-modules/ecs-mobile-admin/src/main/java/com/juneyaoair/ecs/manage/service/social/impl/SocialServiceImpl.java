package com.juneyaoair.ecs.manage.service.social.impl;

import com.juneyaoair.ecs.manage.dto.social.request.SocialReportRequest;
import com.juneyaoair.ecs.manage.dto.social.response.SocialResponsibilityReport;
import com.juneyaoair.ecs.manage.enums.AdvertisementCategoryEnum;
import com.juneyaoair.ecs.manage.enums.DeleteENUM;
import com.juneyaoair.ecs.manage.service.social.ISocialService;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.manage.b2c2014.entity.SocialResponsibilityReportPO;
import com.juneyaoair.manage.b2c2014.service.SocialResponsibilityReportService;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName SocialServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/6/12 16:45
 * @Version 1.0
 */

@Service
@RefreshScope
@Slf4j
public class SocialServiceImpl implements ISocialService {

    @Autowired
    private SocialResponsibilityReportService socialResponsibilityReportService;

    private final static String IS_USING = "Y";

    private final static Integer TEMPLATE_TYPE = 1;


    @Override
    public List<SocialResponsibilityReport> toCatchSocialReports(SocialReportRequest socialReportRequest) {

        List<SocialResponsibilityReport> response = new ArrayList<>();

        SocialResponsibilityReportPO socialResponsibilityReports = SocialResponsibilityReportPO.builder().id(socialReportRequest.getId())
                .adType(AdvertisementCategoryEnum.SOCIALRESPONSIBILITYREPORT.getCode()).channelcode(socialReportRequest.getChannelCode()).delflag(socialReportRequest.getStatus()).build();
        List<SocialResponsibilityReportPO> allSocialResponsibilityReports = socialResponsibilityReportService.toCatchAllByCondition(socialResponsibilityReports);

        if (CollectionUtils.isEmpty(allSocialResponsibilityReports)) {
            return response;
        }

        allSocialResponsibilityReports.forEach(el -> {
            SocialResponsibilityReport report = SocialResponsibilityReport.builder().id(el.getId()).channelCode(el.getChannelcode())
                    .title(el.getAlt()).srcUrl(el.getSrc()).picSequence(el.getPicSeqs()).remark(el.getRemark()).createName(el.getCreateName())
                    .createTime(null != el.getCreateTime() ? DateUtil.convertDate2Str(el.getCreateTime(), DateUtil.DATE_FORMATE) : null).build();
            response.add(report);
        });

        return response.stream().sorted(Comparator.comparing(SocialResponsibilityReport::getPicSequence, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
    }

    @Override
    public void toIncreaseSocialReport(SocialResponsibilityReport socialReportRequest) {
        //确认数据库中是否已存在此ID
        try {
            if (0 == socialReportRequest.getId()) {
                throw new ServiceException("ID不可为0");
            }

            SocialResponsibilityReportPO reports = SocialResponsibilityReportPO.builder().id(socialReportRequest.getId()).build();
            List<SocialResponsibilityReportPO> catchAllByCondition = socialResponsibilityReportService.toCatchAllByCondition(reports);
            if (CollectionUtils.isNotEmpty(catchAllByCondition)) {
                throw new ServiceException("ID已存在，请重新输入");
            }

            SocialResponsibilityReportPO socialReport = SocialResponsibilityReportPO.builder().id(socialReportRequest.getId())
                    .adType(AdvertisementCategoryEnum.SOCIALRESPONSIBILITYREPORT.getCode()).alt(socialReportRequest.getTitle())
                    .src(socialReportRequest.getSrcUrl()).templatetype(TEMPLATE_TYPE).isusing(IS_USING).picSeqs(socialReportRequest.getPicSequence())
                    .adId(HOStringUtil.newGUID()).channelcode(socialReportRequest.getChannelCode()).delflag(DeleteENUM.UN_DELETED.getCode())
                    .createId(convertLongToInteger(SecurityUtils.getUserId())).createName(SecurityUtils.getUsername()).createTime(new Date()).build();

            socialResponsibilityReportService.save(socialReport);
        } catch (ServiceException serviceException) {
            log.error("新增社会责任报告出错：", serviceException);
            throw serviceException;
        } catch (Exception e) {
            log.error("新增社会责任报告出错：", e);
            throw new ServiceException("保存出错");
        }
    }

    @Override
    public void toUpdateSocialReport(SocialResponsibilityReport socialResponsibilityReport) {
        try {
            SocialResponsibilityReportPO reports = SocialResponsibilityReportPO.builder().id(socialResponsibilityReport.getId()).build();
            List<SocialResponsibilityReportPO> catchAllByCondition = socialResponsibilityReportService.toCatchAllByCondition(reports);
            if (CollectionUtils.isEmpty(catchAllByCondition)) {
                throw new ServiceException("未找到可供修改的记录");
            }
            SocialResponsibilityReportPO socialResponsibilityReports = catchAllByCondition.get(0);
            socialResponsibilityReports.setAlt(socialResponsibilityReport.getTitle());
            socialResponsibilityReports.setSrc(socialResponsibilityReport.getSrcUrl());
            socialResponsibilityReports.setPicSeqs(socialResponsibilityReport.getPicSequence());
            socialResponsibilityReports.setRemark(socialResponsibilityReport.getRemark());
            socialResponsibilityReportService.updateById(socialResponsibilityReports);
        } catch (ServiceException serviceException) {
            log.error("修改社会责任报告出错：", serviceException);
            throw serviceException;
        } catch (Exception e) {
            log.error("修改社会责任报告出错：", e);
            throw new ServiceException("修改出错");
        }
    }

    @Override
    public void toRemoveSocialReport(SocialResponsibilityReport socialResponsibilityReport) {
        try {
            SocialResponsibilityReportPO reports = SocialResponsibilityReportPO.builder().id(socialResponsibilityReport.getId()).build();
            List<SocialResponsibilityReportPO> catchAllByCondition = socialResponsibilityReportService.toCatchAllByCondition(reports);
            if (CollectionUtils.isEmpty(catchAllByCondition)) {
                throw new ServiceException("未找到可供删除的记录");
            }

            SocialResponsibilityReportPO socialResponsibilityReports = catchAllByCondition.get(0);
            socialResponsibilityReports.setDelflag(DeleteENUM.DELETED.getCode());
            socialResponsibilityReports.setDelId(convertLongToInteger(SecurityUtils.getUserId()));
            socialResponsibilityReports.setDelName(SecurityUtils.getUsername());
            socialResponsibilityReports.setDelTime(new Date());
            socialResponsibilityReportService.updateById(socialResponsibilityReports);
        } catch (ServiceException serviceException) {
            log.error("删除社会责任报告出错：", serviceException);
            throw serviceException;
        } catch (Exception e) {
            log.error("删除社会责任报告出错：", e);
            throw new ServiceException("删除出错");
        }

    }

    public int longToInt(Long l) {
        if (l == null) {
            throw new NullPointerException("The Long value is null");
        }
        if (l < Integer.MIN_VALUE || l > Integer.MAX_VALUE) {
            throw new IllegalArgumentException(l + " cannot be cast to int for its value is invalid.");
        }
        return l.intValue();
    }

    public Integer convertLongToInteger(Long l) {
        return longToInt(l);
    }
}
