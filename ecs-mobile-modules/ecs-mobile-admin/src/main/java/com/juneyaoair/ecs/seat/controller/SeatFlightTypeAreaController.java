package com.juneyaoair.ecs.seat.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.juneyaoair.ecs.excel.listener.SeatFlightTypeListener;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.external.CussService;
import com.juneyaoair.ecs.seat.param.SeatFlightTypeAreaParam;
import com.juneyaoair.ecs.seat.param.SeatFlightTypeAreaQuery;
import com.juneyaoair.ecs.seat.result.SeatFlightType;
import com.juneyaoair.ecs.seat.result.SeatFlightTypeArea;
import com.juneyaoair.ecs.seat.result.SeatFlightTypeAreaDetail;
import com.juneyaoair.ecs.seat.service.SeatFlightTypeAreaService;
import com.juneyaoair.ecs.seat.utils.SeatUtils;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 值机选座-机型区域划分
 * @created 2024/3/19 14:48
 */

@Slf4j
@Api(value = "SeatFlightTypeAreaController", tags = "值机选座-机型区域划分")
@RestController
public class SeatFlightTypeAreaController {

    @Autowired
    private CussService cussService;
    @Autowired
    private SeatFlightTypeAreaService seatFlightTypeAreaService;

    @ApiOperation(value = "值机选座-机型区域划分查询", httpMethod = "POST")
    @PostMapping("/seatFlightTypeArea/getFlightTypeAreaList")
    public R<PageResult<SeatFlightTypeArea>> getFlightTypeAreaList(@RequestBody @Validated SeatFlightTypeAreaQuery seatFlightTypeAreaQuery) {
        PageResult<SeatFlightTypeArea> pageInfo = seatFlightTypeAreaService.getFlightTypeAreaList(seatFlightTypeAreaQuery);
        return R.ok(pageInfo);
    }

    @ApiOperation(value = "值机选座-指定机型区域划分查询", httpMethod = "GET")
    @GetMapping("/seatFlightTypeArea/getFlightTypeAreaDetail")
    public R<SeatFlightTypeAreaDetail> getFlightTypeAreaDetail(@RequestParam(value = "areaSetId") Long areaSetId) {
        SeatFlightTypeAreaDetail seatFlightTypeAreaDetail = seatFlightTypeAreaService.getFlightTypeAreaDetail(areaSetId);
        return R.ok(seatFlightTypeAreaDetail);
    }

    @ApiOperation(value = "值机选座-保存机型区域划分信息", httpMethod = "POST")
    @PostMapping("/seatFlightTypeArea/saveFlightTypeArea")
    public R<String> saveFlightTypeArea(@RequestBody @Validated SeatFlightTypeAreaParam seatFlightTypeAreaParam) {
        seatFlightTypeAreaService.saveFlightTypeArea(seatFlightTypeAreaParam);
        return R.ok("OK");
    }

    /**
     * 接口下线
     * @param file
     * @param flightTypeId
     * @return
     */
    @ApiOperation(value = "值机选座-解析上传的机型区域划分座位图", httpMethod = "POST")
    public R<SeatFlightTypeAreaDetail> upload(@RequestPart(value = "file") MultipartFile file, @RequestParam(value = "flightTypeId") Long flightTypeId) {
        SeatFlightTypeListener seatFlightTypeListener = new SeatFlightTypeListener();
        try {
            EasyExcel.read(file.getInputStream(), seatFlightTypeListener).sheet().doRead();
        } catch (Exception e) {
            log.error("解析机型座位图异常，异常信息：", e);
            throw new HoServiceException("解析机型座位图文件出现异常");
        }
        List<Map<String, String>> list = seatFlightTypeListener.getList();
        // 解析区域信息
        Map<String, String> codeNames = Maps.newHashMap();
        boolean isAreaNow = false;
        for (Map<String, String> row : list) {
            String firstCellValue = row.get("0");
            // 处理区域名称
            if (isAreaNow) {
                if ("AREAEND".equals(firstCellValue)) {
                    break;
                }
                codeNames.put(firstCellValue, row.get("1"));
                continue;
            }
            // 标记区域数据开始
            if ("AREA".equals(firstCellValue)) {
                isAreaNow = true;
            }
        }
        // 解析座位数据
        // 区域座位清单
        Map<String, List<String>> areaCodeSeatListMap = Maps.newHashMap();
        // 座位所在区域
        Map<String, String> seatNoAreaMap = Maps.newHashMap();
        for (String areaCode : codeNames.keySet()) {
            areaCodeSeatListMap.put(areaCode, Lists.newArrayList());
        }
        Map<String, String> map = Maps.newHashMap();
        int maxColumns = 0;
        for (Map<String, String> row : list) {
            String firstCellValue = row.get("0");
            if ("AREA".equals(firstCellValue)) {
                break;
            }
            boolean setSeatColumn = false;
            if (firstCellValue.equals("F") || firstCellValue.equals("Y")) {
                if (map.size() > 0) {
                    map.clear();
                }
                setSeatColumn = true;
            }
            if (row.size() > maxColumns) {
                maxColumns = row.size();
            }
            String currentSeatRow = "";
            for (int j = 0; j < row.size(); j++) {
                if (j == 1) {
                    currentSeatRow = row.get("1");
                }
                String value = row.get(String.valueOf(j));
                if (setSeatColumn && StringUtils.isNotBlank(value) && j != 0 && j != row.size() - 1) {
                    map.put(String.valueOf(j), value);
                }
                if (StringUtils.isNotBlank(currentSeatRow) && map.containsKey(String.valueOf(j)) && StringUtils.isNotBlank(value)) {
                    // 座位号
                    String seatNo = currentSeatRow + map.get(String.valueOf(j));
                    if (areaCodeSeatListMap.containsKey(value)) {
                        areaCodeSeatListMap.get(value).add(seatNo);
                    }
                    seatNoAreaMap.put(seatNo, value);
                }
            }
        }
        // 查询开放选座机型区域划分信息
        SeatFlightType seatFlightType = cussService.getFlightType(flightTypeId);
        if (null == seatFlightType) {
            throw new HoServiceException("获取机型信息失败");
        }
        // 座位图
      /*  String flightTypeSeatJson = seatFlightType.getPlaneSeatJson();
        JSONObject flightTypeSeat = JSON.parseObject(flightTypeSeatJson);
        int maxColumnsCount = flightTypeSeat.getInteger("maxColumns");
        JSONArray seatMapStrList = flightTypeSeat.getJSONArray("data");
        SeatUtils.generateFlightAreaMap(seatNoAreaMap, maxColumnsCount, seatMapStrList);*/

        SeatFlightTypeAreaDetail seatFlightTypeAreaDetail = new SeatFlightTypeAreaDetail();
        seatFlightTypeAreaDetail.setAreaSeats(areaCodeSeatListMap);
        seatFlightTypeAreaDetail.setCodeNames(codeNames);
    /*    seatFlightTypeAreaDetail.setMaxColumns(maxColumnsCount);
        seatFlightTypeAreaDetail.setData(seatMapStrList);*/
        return R.ok(seatFlightTypeAreaDetail);
    }
}
