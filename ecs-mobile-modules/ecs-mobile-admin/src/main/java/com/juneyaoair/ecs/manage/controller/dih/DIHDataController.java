package com.juneyaoair.ecs.manage.controller.dih;

import com.juneyaoair.ecs.manage.controller.HoBaseController;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.dih.InventoryHistoryDownloadParam;
import com.juneyaoair.ecs.manage.dto.dih.InventoryHistoryParam;
import com.juneyaoair.ecs.manage.service.dih.IDIHDataService;
import com.juneyaoair.ecs.mongo.entity.InventoryHistoryPO;
import com.juneyaoair.ecs.utils.DateUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/8 10:37
 */
@Api("DIH数据下载")
@RequestMapping
@RestController
public class DIHDataController extends HoBaseController {
    @Autowired
    private IDIHDataService diHDataService;
    @ApiOperation(value = "查询航班库存操作历史")
    @PostMapping(value = "queryInventoryHistory")
    public R<PageResult<InventoryHistoryPO>> queryInventoryHistory(@RequestBody @Validated InventoryHistoryParam inventoryHistoryParam){
        PageDomain pageDomain = TableSupport.buildPageRequest();
        String flightDate = inventoryHistoryParam.getFlightDate();
        if(StringUtils.isBlank(flightDate)){
            flightDate = DateUtil.getCurrentDateStr();
        }
        return R.ok(diHDataService.queryInventoryHistoryByPage(flightDate,pageDomain.getPageNum(), pageDomain.getPageSize()));
    }

    @ApiOperation(value = "下载航班库存操作历史")
    @PostMapping(value = "downloadInventoryHistory")
    public void downloadInventoryHistory(@RequestBody @Validated InventoryHistoryDownloadParam inventoryHistoryDownloadParam, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String flightDateStart = inventoryHistoryDownloadParam.getFlightDateStart();
        String flightDateEnd = inventoryHistoryDownloadParam.getFlightDateEnd();
        String currentDate = DateUtil.getCurrentDateStr();
        if(StringUtils.isBlank(flightDateStart)){
            flightDateStart = currentDate;
        }
        if(StringUtils.isBlank(flightDateEnd)){
            flightDateEnd = currentDate;
        }
        diHDataService.downloadInventoryHistory(flightDateStart,flightDateEnd,response);
    }

    @ApiOperation(value = "删除指定航班日期库存操作历史")
    @PostMapping(value = "cancelInventoryHistory")
    public R cancelInventoryHistory(@RequestBody @Validated InventoryHistoryDownloadParam inventoryHistoryDownloadParam, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String flightDateStart = inventoryHistoryDownloadParam.getFlightDateStart();
        String flightDateEnd = inventoryHistoryDownloadParam.getFlightDateEnd();
        String currentDate = DateUtil.getCurrentDateStr();
        if(StringUtils.isBlank(flightDateStart)){
            flightDateStart = currentDate;
        }
        if(StringUtils.isBlank(flightDateEnd)){
            flightDateEnd = currentDate;
        }
        diHDataService.cancelInventoryHistory(flightDateStart,flightDateEnd);
        return R.ok();
    }
}
