package com.juneyaoair.ecs.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.juneyaoair.ecs.manage.dto.activity.enums.PRIZE_TYPE_ENUM;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ImportPrizeEntry;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ImportPrizePoolInfo;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ImportPrizePoolParam;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ImportPrizeSubEntry;
import com.juneyaoair.ecs.utils.ValidatorUtils;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @created 2023/8/21 13:18
 */
public class ImportPrizePoolListener extends AnalysisEventListener<ImportPrizePoolParam> {

    @Getter
    private Map<String, ImportPrizePoolInfo> prizePoolInfoMap = Maps.newHashMap();

    public ImportPrizePoolListener() {
        super();
        prizePoolInfoMap.clear();
    }

    @Override
    public void invoke(ImportPrizePoolParam importPrizePoolParam, AnalysisContext analysisContext) {
        ValidatorUtils.valid(importPrizePoolParam);
        PRIZE_TYPE_ENUM prizeTypeEnum = PRIZE_TYPE_ENUM.getEnum(importPrizePoolParam.getSubPrizeCategory());
        if (null == prizeTypeEnum) {
            throw new HoServiceException(importPrizePoolParam.getSubPrizeCategory() + "：暂不支持当前奖品类型");
        }
        ImportPrizePoolInfo importPrizePoolInfo = prizePoolInfoMap.get(importPrizePoolParam.getPrizePoolCode());
        // 奖池不存在新增奖池配置信息
        if (null == importPrizePoolInfo) {
            importPrizePoolInfo = new ImportPrizePoolInfo();
            // 奖池信息
            importPrizePoolInfo.setPrizePoolName(importPrizePoolParam.getPrizePoolName());
            importPrizePoolInfo.setStartTime(importPrizePoolParam.getStartTime());
            importPrizePoolInfo.setEndTime(importPrizePoolParam.getEndTime());
            // 奖品信息
            Map<String, ImportPrizeEntry> prizeEntryMap = Maps.newHashMap();
            ImportPrizeEntry importPrizeEntry = new ImportPrizeEntry();
            importPrizeEntry.setPrizeName(importPrizePoolParam.getPrizeName());
            importPrizeEntry.setTotalAmount(importPrizePoolParam.getTotalAmount());
            // 子奖品信息
            ImportPrizeSubEntry importPrizeSubEntry = new ImportPrizeSubEntry();
            importPrizeSubEntry.setSubPrizeCode(importPrizePoolParam.getSubPrizeCode());
            importPrizeSubEntry.setSubPrizeName(importPrizePoolParam.getSubPrizeName());
            importPrizeSubEntry.setSubPrizeCategory(importPrizePoolParam.getSubPrizeCategory());
            importPrizeSubEntry.setSubPrizeAmount(importPrizePoolParam.getSubPrizeAmount());
            importPrizeEntry.setPrizeSubEntityList(Lists.newArrayList(importPrizeSubEntry));
            prizeEntryMap.put(importPrizePoolParam.getPrizeCode(), importPrizeEntry);
            importPrizePoolInfo.setPrizeEntryMap(prizeEntryMap);
            prizePoolInfoMap.put(importPrizePoolParam.getPrizePoolCode(), importPrizePoolInfo);
            return;
        }
        // 同一奖池编码 奖池名称、开始时间、结束时间需一致
        if (!importPrizePoolParam.getPrizePoolName().equals(importPrizePoolInfo.getPrizePoolName())) {
            throw new HoServiceException(importPrizePoolParam.getPrizePoolCode() + "：同一奖池编码奖池名称需一致");
        }
        if (!importPrizePoolParam.getStartTime().equals(importPrizePoolInfo.getStartTime())) {
            throw new HoServiceException(importPrizePoolParam.getPrizePoolCode() + "：同一奖池编码开始时间需一致");
        }
        if (!importPrizePoolParam.getEndTime().equals(importPrizePoolInfo.getEndTime())) {
            throw new HoServiceException(importPrizePoolParam.getPrizePoolCode() + "：同一奖池编码结束时间需一致");
        }
        // 获取奖池下奖品信息
        Map<String, ImportPrizeEntry> prizeEntryMap = importPrizePoolInfo.getPrizeEntryMap();
        ImportPrizeEntry importPrizeEntry = prizeEntryMap.get(importPrizePoolParam.getPrizeCode());
        if (null != importPrizeEntry) {
            // 存在奖品 同一奖品编码 奖品名称、奖品总数需一致
            if (!importPrizePoolParam.getPrizeName().equals(importPrizeEntry.getPrizeName())) {
                throw new HoServiceException(importPrizePoolParam.getPrizePoolCode() + "：同一奖品编码奖品名称需一致");
            }
            if (!importPrizePoolParam.getTotalAmount().equals(importPrizeEntry.getTotalAmount())) {
                throw new HoServiceException(importPrizePoolParam.getPrizePoolCode() + "：同一奖品编码奖品总数需一致");
            }
        } else {
            importPrizeEntry = new ImportPrizeEntry();
            importPrizeEntry.setPrizeName(importPrizePoolParam.getPrizeName());
            importPrizeEntry.setTotalAmount(importPrizePoolParam.getTotalAmount());
        }
        // 子奖品信息
        ImportPrizeSubEntry importPrizeSubEntry = new ImportPrizeSubEntry();
        importPrizeSubEntry.setSubPrizeCode(importPrizePoolParam.getSubPrizeCode());
        importPrizeSubEntry.setSubPrizeName(importPrizePoolParam.getSubPrizeName());
        importPrizeSubEntry.setSubPrizeCategory(importPrizePoolParam.getSubPrizeCategory());
        importPrizeSubEntry.setSubPrizeAmount(importPrizePoolParam.getSubPrizeAmount());
        List<ImportPrizeSubEntry> prizeSubEntityList = importPrizeEntry.getPrizeSubEntityList();
        if (null == prizeSubEntityList) {
            prizeSubEntityList = Lists.newArrayList();
        }
        prizeSubEntityList.add(importPrizeSubEntry);
        importPrizeEntry.setPrizeSubEntityList(prizeSubEntityList);
        prizeEntryMap.put(importPrizePoolParam.getPrizeCode(), importPrizeEntry);
        importPrizePoolInfo.setPrizeEntryMap(prizeEntryMap);
        prizePoolInfoMap.put(importPrizePoolParam.getPrizePoolCode(), importPrizePoolInfo);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

}
