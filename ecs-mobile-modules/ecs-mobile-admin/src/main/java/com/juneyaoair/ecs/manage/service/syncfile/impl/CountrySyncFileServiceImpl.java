package com.juneyaoair.ecs.manage.service.syncfile.impl;

import com.juneyaoair.ecs.manage.service.file.IFileUploadService;
import com.juneyaoair.ecs.manage.service.i18n.I18nDictionaryService;
import com.juneyaoair.ecs.manage.service.syncfile.CountrySyncFileService;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.CountryJsonInfo;
import com.juneyaoair.manage.b2c.service.ICountryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class CountrySyncFileServiceImpl implements CountrySyncFileService {

    @Lazy
    @Resource
    private IFileUploadService fileUploadService;

    @Autowired
    private I18nDictionaryService i18nDictionaryService;
    @Autowired
    private ICountryService countryService;

    @Override
    public void syncJson() {
        List<CountryJsonInfo> countryJsonInfoList = countryService.getCountryJsonInfo();
        if (CollectionUtils.isEmpty(countryJsonInfoList)) {
            throw new HoServiceException("未获取到国际信息");
        }
        Map<String, Map<String, String>> countryMap = i18nDictionaryService.fetchDictData("COUNTRY");
        for (CountryJsonInfo countryJsonInfo : countryJsonInfoList) {
            countryJsonInfo.setCountryNameMap(countryMap.get(countryJsonInfo.getCountryCode()));
        }
        String fileName = "country.json";
        fileUploadService.upload(fileName, "flightBasicStatic", countryJsonInfoList);
    }

}
