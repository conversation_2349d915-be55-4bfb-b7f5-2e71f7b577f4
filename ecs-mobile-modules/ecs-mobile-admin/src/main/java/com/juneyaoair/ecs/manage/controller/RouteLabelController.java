package com.juneyaoair.ecs.manage.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.juneyaoair.ecs.manage.dto.airline.routelabel.*;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.airline.impl.RouteLabelServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("routeLabel")
@RestController
@RequiredArgsConstructor
@Api(value = "RouteLabelController", tags = "航线标签")
@Slf4j
public class RouteLabelController extends HoBaseController {


    @Resource
    private RouteLabelServiceImpl routeLabelService;

    @PostMapping(value = "list")
    @ApiOperation(value = "航线标签列表", notes = "航线标签列表", httpMethod = "POST")
    public R<PageResult<RouteLabelDTO>> routeLabelList(@RequestBody ListRouteLabelRequestDTO request) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        List<RouteLabelDTO> list = routeLabelService.list(request);
        PageInfo<RouteLabelDTO> pageInfo = dealPageInfo(pageDomain.getPageNum(), pageDomain.getPageSize(), list);
        return getPageData(pageInfo, pageDomain);
    }

    @PostMapping(value = "add")
    @ApiOperation(value = "新增航线标签", notes = "新增航线标签规则", httpMethod = "POST")
    public R addRouteLabel(@RequestBody @Validated AddOrUpdateRouteLabelRequestDTO request) {
        routeLabelService.addRouteLabel(request);
        return R.ok();
    }

    @PostMapping(value = "update")
    @ApiOperation(value = "修改航线标签", notes = "修改航线标签规则", httpMethod = "POST")
    public R updateRouteLabel(@RequestBody @Validated AddOrUpdateRouteLabelRequestDTO request) {
        routeLabelService.updateRouteLabel(request);
        return R.ok();
    }

    @PostMapping(value = "delete")
    @ApiOperation(value = "删除航线标签", notes = "删除航线标签规则", httpMethod = "POST")
    public R deleteRouteLabel(@RequestBody RouteLabelIdRequestDTO request) {
        routeLabelService.deleteRouteLabel(request);
        return R.ok();
    }

    @PostMapping(value = "publish")
    @ApiOperation(value = "禁用/启用航线标签", notes = "发布/禁用航线标签规则", httpMethod = "POST")
    public R publishRouteLabel(@RequestBody RouteLabelIdRequestDTO request) {
        routeLabelService.publishRouteLabel(request);
        return R.ok();
    }

    @PostMapping(value = "/rule/add")
    @ApiOperation(value = "新增规则", notes = "新增一条规则", httpMethod = "POST")
    public R addRouteLabelRule(@RequestBody @Validated AddOrUpdateRuleRequestDTO request) {
        routeLabelService.addRule(request);
        return R.ok();
    }

    @PostMapping(value = "/rule/update")
    @ApiOperation(value = "修改规则", notes = "修改一条规则", httpMethod = "POST")
    public R updateRouteLabelRule(@RequestBody @Validated AddOrUpdateRuleRequestDTO request) {
        routeLabelService.updateRule(request);
        return R.ok();
    }

    @PostMapping(value = "/rule/delete")
    @ApiOperation(value = "删除规则", notes = "删除一条规则", httpMethod = "POST")
    public R deleteRouteLabelRule(@RequestBody RouteLabelIdRequestDTO request) {
        routeLabelService.deleteRule(request);
        return R.ok();
    }

    @PostMapping(value = "/rule/publish")
    @ApiOperation(value = "禁用/启用规则", notes = "发布/禁用一条规则", httpMethod = "POST")
    public R publishRouteLabelRule(@RequestBody RouteLabelIdRequestDTO request) {
        routeLabelService.publishRule(request);
        return R.ok();
    }


}
