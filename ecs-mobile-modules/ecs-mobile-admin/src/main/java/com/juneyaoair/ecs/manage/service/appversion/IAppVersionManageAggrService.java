package com.juneyaoair.ecs.manage.service.appversion;

import com.juneyaoair.ecs.manage.dto.appversion.AppVersionInfoDTO;
import com.juneyaoair.manage.b2c.entity.AppVersionInfoPO;

import java.util.List;

public interface IAppVersionManageAggrService {
    int insertAppVersion(AppVersionInfoPO appVersionManage);

    int deleteAppVersion(AppVersionInfoPO appVersionManage);

    boolean releaseAppVersion(AppVersionInfoPO appVersionManage);

    AppVersionInfoPO queryReleaseAppVersion(AppVersionInfoPO appVersionManage);

    int updateAndroidVersionManage(AppVersionInfoPO appVersionManage1);

    int updateIosVersionManage(AppVersionInfoPO appVersionManage1);

    int offlineAppVersion(AppVersionInfoPO appVersionManage);

    int forceupdateAppVersion(AppVersionInfoPO appVersionManage);

    int modifyAppManage(AppVersionInfoPO appVersionManage);

    List<AppVersionInfoDTO> list(AppVersionInfoPO appVersionManage);

}
