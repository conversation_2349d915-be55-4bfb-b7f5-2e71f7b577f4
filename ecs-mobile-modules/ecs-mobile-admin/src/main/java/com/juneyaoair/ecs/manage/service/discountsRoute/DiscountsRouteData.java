package com.juneyaoair.ecs.manage.service.discountsRoute;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.juneyaoair.ecs.manage.dto.activity.common.ActivityAirLine;
import com.juneyaoair.ecs.manage.dto.discount.vo.DiscountsRouteActivityVo;
import com.juneyaoair.ecs.manage.service.discountsrouteactivity.IDiscountsRouteActivityService;
import com.juneyaoair.ecs.redis.service.PrimaryRedisService;
import com.juneyaoair.manage.b2c.entity.activity.ActivityAirlinePO;
import com.juneyaoair.manage.b2c.entity.activity.ActivityChildInfoPO;
import com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteActivityPO;
import com.juneyaoair.manage.b2c.service.IActivityAirlineService;
import com.juneyaoair.manage.b2c.service.IActivityChildInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
@Slf4j
public class DiscountsRouteData {

    @Resource
    PrimaryRedisService primaryRedisService;

    @Autowired
    private IDiscountsRouteActivityService discountsRouteActivityService;

    @Autowired
    private IActivityChildInfoService activityChildInfoService;

    @Autowired
    private IActivityAirlineService activityAirlineService;

    /**
     * 特价航线
     */
    private final static String DISCOUNTS_ROUTE_ACTIVITY = "Activity:cacheDiscountsRoute";


    public void replaceRedis() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        String nowTime = simpleDateFormat.format(new Date());
        primaryRedisService.delete(DISCOUNTS_ROUTE_ACTIVITY);
        List<DiscountsRouteActivityVo> discountsRouteActivityVos = new ArrayList<>();
        List<DiscountsRouteActivityPO> activityBaseInfos = discountsRouteActivityService.getList("Y", "Y", nowTime);
        if (CollectionUtils.isNotEmpty(activityBaseInfos)) {
            activityBaseInfos.forEach(discounts -> {
                DiscountsRouteActivityVo discountsRouteActivityVo = discountsRouteActivityService.getDetails(discounts.getId());
                if (CollectionUtil.isNotEmpty(discountsRouteActivityVo.getTwoTitleDataRoList())) {
                    discountsRouteActivityVo.getTwoTitleDataRoList().forEach(twoTitleDataRo -> {
                        if (CollectionUtil.isNotEmpty(twoTitleDataRo.getThreeTitleData())) {
                            twoTitleDataRo.getThreeTitleData().forEach(threeTitleDataRo -> {
                                ActivityChildInfoPO activityChildInfoPO = new ActivityChildInfoPO();
                                activityChildInfoPO.setBaseinfoId(discountsRouteActivityVo.getId());
                                activityChildInfoPO.setDataType("5");
                                activityChildInfoPO.setDataName(threeTitleDataRo.getDataSetName());
                                List<ActivityChildInfoPO> activityChildInfos = activityChildInfoService.toGainAllChildRecords(activityChildInfoPO);
                                if (CollectionUtil.isNotEmpty(activityChildInfos)) {
                                    activityChildInfos.forEach(activityChildInfo -> {
                                        if (threeTitleDataRo.getDataSetName().equals(activityChildInfo.getDataName())) {
                                            ActivityAirlinePO airlinePO = new ActivityAirlinePO();
                                            airlinePO.setChildInfoId(activityChildInfo.getId());
                                            List<ActivityAirlinePO> activityAirLines = activityAirlineService.toGainAllById(airlinePO);
                                            List<ActivityAirLine> activityAirLineList = new ArrayList<>();
                                            for (ActivityAirlinePO airline : activityAirLines
                                                 ) {
                                                ActivityAirLine activityAirLine = new ActivityAirLine();
                                                BeanUtils.copyProperties(airline,activityAirLine);
                                                activityAirLineList.add(activityAirLine);
                                            }
                                            threeTitleDataRo.setAirLinDataSet(activityAirLineList);
                                        }
                                    });
                                }
                            });
                        }

                    });

                }
                discountsRouteActivityVos.add(discountsRouteActivityVo);
            });

        }
        log.info("获取有效的特惠航线:" + JSON.toJSONString(discountsRouteActivityVos));
        primaryRedisService.put(DISCOUNTS_ROUTE_ACTIVITY, JSON.toJSONString(discountsRouteActivityVos), 86400);
    }

    public void removeCacheDiscountsRoute(String key) {
        log.info("删除特价航线 key ：{}", DISCOUNTS_ROUTE_ACTIVITY + ":" + key);
        primaryRedisService.delete(DISCOUNTS_ROUTE_ACTIVITY + ":" + key);
        log.info("删除成功  key ：{}", DISCOUNTS_ROUTE_ACTIVITY + ":" + key);
    }

}
