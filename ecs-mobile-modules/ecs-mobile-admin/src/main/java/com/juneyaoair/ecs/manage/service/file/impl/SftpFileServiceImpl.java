package com.juneyaoair.ecs.manage.service.file.impl;

import com.alibaba.fastjson2.JSON;
import com.jcraft.jsch.ChannelSftp;
import com.juneyaoair.ecs.manage.dto.file.HoFile;
import com.juneyaoair.ecs.manage.enums.AdvertisementWaterEnum;
import com.juneyaoair.ecs.manage.enums.FileModuleName;
import com.juneyaoair.ecs.manage.enums.FileSuffixNameEnum;
import com.juneyaoair.ecs.manage.enums.ResultEnum;
import com.juneyaoair.ecs.manage.service.file.IFileUploadService;
import com.juneyaoair.ecs.utils.*;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.ruoyi.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.juneyaoair.ecs.manage.service.resource.impl.ResourceManagerServiceImpl.uploadResourceFileModuleName;

/**
 * <AUTHOR>
 * @description sftp文件上传服务
 * @date 2023/5/12 10:41
 */
@RefreshScope
@Service
@Slf4j
public class SftpFileServiceImpl implements IFileUploadService {
    /**
     * 网络服务地址
     */
    @Value("${sftp.sftpUrl:}")
    private String sftpUrl;
    @Value("${sftp.sftpServerIp:}")
    private String sftpServerIp;
    @Value("${sftp.sftpServerPort:}")
    private String sftpServerPort;
    @Value("${sftp.sftpUserName:}")
    private String sftpUserName;
    @Value("${sftp.sftpPassword:}")
    private String sftpPassword;
    /**
     * 物理存放目录
     */
    @Value("${sftp.sftpPath:}")
    private String sftpPath;
    /**
     * 资源映射路径 前缀
     */
    @Value("${sftp.sftpFilePrefix:/upload}")
    private String sftpFilePrefix;

    @Autowired
    private ResourceLoader resourceLoader;

    private static final String separator = "/";

    @Override
    public HoFile uploadFile(MultipartFile file, String moduleName, String fileName, AdvertisementWaterEnum advertisementWater, boolean specifyDate) throws IOException {
        checkModuleName(moduleName);
        //广告水印处理
        ClassPathResource resource = new ClassPathResource(advertisementWater.getPath());
        if (!resource.exists()) {
            throw new FileNotFoundException("Resource not found: " + advertisementWater.getPath());
        }
        InputStream inputStream = ImageWatermarkUtil.addImageWatermark(file.getInputStream(), resource.getInputStream(), 1f);
        return uploadFunc(file.getOriginalFilename(), fileName, moduleName, specifyDate, inputStream);
    }

    /**
     * 上传文件
     *
     * @param file
     * @param moduleName 模块名称
     * @return
     */
    @Override
    public HoFile uploadFile(MultipartFile file, String moduleName, String fileName, boolean specifyDate) throws IOException {
        checkModuleName(moduleName);
        return uploadFunc(file.getOriginalFilename(), fileName, moduleName, specifyDate, file.getInputStream());
    }

    /**
     * 根据本地文件上传至资源服务器
     *
     * @param srcFile
     * @param moduleName
     * @return
     */
    @Override
    public HoFile uploadLocalFile(File srcFile, String moduleName, String fileName) {
        //模块名称不为空的情况下，检验传值是否在枚举范围内
        checkModuleName(moduleName);
        try (FileInputStream fileInputStream = new FileInputStream(srcFile)) {
            return uploadFunc(srcFile.getName(), fileName, moduleName, true, fileInputStream);
        } catch (Exception e) {
            log.error("文件上传异常:", e);
            throw new ServiceException("文件上传失败");
        }
    }

    @Override
    public HoFile uploadFile(File srcFile, String fileFormat/*eg:"airline.json"*/, String remoteUrl) {
        String absoluteUrl = sftpPath
                + ((StringUtils.isNotEmpty(sftpPath) && sftpPath.charAt(sftpPath.length() - 1) == '/') ? "" : separator)
                + remoteUrl;
        HoFile ret = new HoFile();
        HoFile hoFile = new HoFile();
        hoFile.setFileId(HOStringUtil.newGUID());
        hoFile.setUrl(absoluteUrl);
        hoFile.setName(fileFormat);
        SFTPUtil sftpUtil = new SFTPUtil();
        InputStream inputStream = null;
        boolean error = false;
        try {
            sftpUtil.connect(sftpServerIp, Integer.parseInt(sftpServerPort), sftpUserName, sftpPassword);
            ChannelSftp sftp = sftpUtil.getSftp();
            sftp.cd(absoluteUrl);
            inputStream = Files.newInputStream(srcFile.toPath());
            sftp.put(inputStream, srcFile.getName());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ServiceException("sftp文件上传失败");
        } finally {
            sftpUtil.closeChannel();
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage());
                error = true;
            }
        }
        if (error) {
            throw new ServiceException("文件上传失败");
        }
        return ret;
    }

    @Override
    public HoFile uploadByte(byte[] srcFileByte, String fileFormat/*eg:"airline.json"*/, String remoteUrl) {
        String absoluteUrl = sftpPath
                + ((StringUtils.isNotEmpty(sftpPath) && sftpPath.charAt(sftpPath.length() - 1) == '/') ? "" : separator)
                + remoteUrl;
        HoFile ret = new HoFile();
        HoFile hoFile = new HoFile();
        hoFile.setFileId(HOStringUtil.newGUID());
        hoFile.setUrl(absoluteUrl);
        hoFile.setName(fileFormat);
        SFTPUtil sftpUtil = new SFTPUtil();
        InputStream inputStream = null;
        boolean error = false;
        try {
            sftpUtil.connect(sftpServerIp, Integer.parseInt(sftpServerPort), sftpUserName, sftpPassword);
            ChannelSftp sftp = sftpUtil.getSftp();
            sftp.cd(absoluteUrl);
            inputStream = new ByteArrayInputStream(srcFileByte);
            sftp.put(inputStream, fileFormat);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ServiceException("sftp文件上传失败");
        } finally {
            sftpUtil.closeChannel();
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage());
                error = true;
            }
        }
        if (error) {
            throw new ServiceException("文件上传失败");
        }
        return ret;
    }

    /**
     * 检验业务层级合法性
     *
     * @param moduleName
     */
    private void checkModuleName(String moduleName) {
        //模块名称不为空的情况下，检验传值是否在枚举范围内
        if (StringUtils.isNotBlank(moduleName) && !FileModuleName.checkModule(moduleName)) {
            throw new HoServiceException("请联系开发人员进行模块配置");
        }
    }

    /**
     * @param originFileName 原始文件名
     * @param fileName       新文件名称 不包含后缀
     * @param moduleName     业务模块层级
     * @param specifyDate    包含<年月>层级
     * @param inputStream    文件输入流
     */
    private HoFile uploadFunc(String originFileName, String fileName, String moduleName, boolean specifyDate, InputStream inputStream) {
        if (StringUtils.isBlank(originFileName)) {
            throw new HoServiceException("文件不存在");
        }
        String suffix = originFileName.substring(originFileName.lastIndexOf("."));
        String newFileName;
        String fileId = HOStringUtil.newGUID();
        if (StringUtils.isEmpty(fileName)) {
            newFileName = fileId + suffix;
        } else {
            newFileName = fileName + suffix;
        }
        try {
            //目录生成规则模块名+年月
            String newModuleName = StringUtils.isNotBlank(moduleName) ? separator + moduleName : "";
            //文件路径中是否包含<年月>层级
            String dateStr = "";
            if (specifyDate) {
                dateStr = separator + DateUtil.convertDate2Str(new Date(), DateUtil.DATE_FORMATE_YYYYMM);
            }
            FileSuffixNameEnum fileSuffixNameEnum = FileSuffixNameEnum.fileDirectory(suffix);
            //文件存放全路径
            String directory = "";
            if(uploadResourceFileModuleName.equals(moduleName)){
                //资源管理模块上传的文件路径
                directory = sftpPath + newModuleName + fileSuffixNameEnum.getDirectory() + dateStr;
            }else {
                directory = sftpPath + newModuleName + dateStr + fileSuffixNameEnum.getDirectory();
            }
            SFTPUtil sftpUtil = new SFTPUtil();
            sftpUtil.connect(sftpServerIp, Integer.parseInt(sftpServerPort), sftpUserName, sftpPassword);
            boolean flag = sftpUtil.upload(inputStream, directory, newFileName);
            sftpUtil.closeChannel();
            if (flag) {
                //网络地址访问路径
                String path = "";
                if(uploadResourceFileModuleName.equals(moduleName)){
                    //资源管理模块上传文件的网络地址访问路径
                    path  = sftpUrl + sftpFilePrefix + newModuleName + fileSuffixNameEnum.getDirectory() + dateStr + separator + newFileName;
                }else {
                    path = sftpUrl + sftpFilePrefix + newModuleName + dateStr + fileSuffixNameEnum.getDirectory() + separator + newFileName;
                }
                HoFile hoFile = new HoFile();
                hoFile.setFileId(fileId);
                hoFile.setUrl(path);
                hoFile.setName(FileUtil.getName(path));
                return hoFile;
            } else {
                throw new ServiceException("sftp文件上传失败");
            }
        } catch (Exception e) {
            log.error("文件上传异常:", e);
            throw new ServiceException("文件上传失败");
        }
    }

    @Override
    public void upload(String fileName, String remoteUrl, Object obj) {
        File file = new File(String.format("%s%s", new cn.hutool.core.io.resource.ClassPathResource("/").getAbsolutePath(), fileName));
        cn.hutool.core.io.FileUtil.writeUtf8String(JSON.toJSONString(getResultMap(obj)), file);
        this.uploadFile(file, fileName, remoteUrl);
        cn.hutool.core.io.FileUtil.del(file);
    }

    private Map<String, Object> getResultMap(Object obj) {
        Map<String, Object> result = new HashMap<>();
        Timestamp timestamp = DateUtil.getTimestamp();
        result.put("objData", obj);
        result.put("loginFlag", false);
        result.put("timeStamp", timestamp.getTime());
        result.put("resultCode", ResultEnum.S10001.getResultCode());
        result.put("resultInfo", ResultEnum.S10001.getResultInfo());
        return result;
    }
}
