package com.juneyaoair.ecs.manage.service.i18n;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.juneyaoair.ecs.manage.dto.I18n.*;
import com.juneyaoair.ecs.manage.enums.LanguageEnum;
import com.juneyaoair.ecs.manage.external.FlightBasicService;
import com.juneyaoair.manage.b2c.entity.I18nDictionaryPO;
import com.juneyaoair.manage.b2c.entity.I18nDictionaryValuePO;
import com.juneyaoair.manage.b2c.mapper.I18nDictionaryPOMapper;
import com.juneyaoair.manage.b2c.mapper.I18nDictionaryValuePOMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class I18nDictionaryServiceImpl implements I18nDictionaryService {

    @Resource
    private I18nDictionaryPOMapper i18nDictMapper;

    @Resource
    private I18nDictionaryValuePOMapper i18KVMapper;

    @Resource
    private FlightBasicService flightBasicService;

    // 验证语言标签是否合法
    private void validateLanguageTag(String languageTag, int rowIndex) {
        try {
            LanguageEnum.valueOf(languageTag);
        } catch (IllegalArgumentException e) {
            throw new RuntimeException(String.format("第%d行数据错误：语言标签[%s]不在系统支持范围内，支持的语言标签：%s", 
                rowIndex, 
                languageTag,
                Arrays.stream(LanguageEnum.values())
                    .map(Enum::name)
                    .collect(Collectors.joining(", "))
            ));
        }
    }

    // 验证字典类型是否存在
    private void validateDictionaryType(String dictionaryType, int rowIndex, Set<String> existingDictionaryTypes) {
        if (!existingDictionaryTypes.contains(dictionaryType)) {
            throw new RuntimeException(String.format("第%d行数据错误：字典类型[%s]不存在，请先创建该字典类型", 
                rowIndex,
                dictionaryType));
        }
    }

    @Override
    public void add(AddI18nDictionaryRequestDTO request) {
        i18nDictMapper.insert(I18nDictionaryPO.builder()
                .id(IdUtil.fastUUID())
                .dictionaryName(request.getDictionaryName())
                .dictionaryType(request.getDictionaryType())
                .remark(request.getRemark())
                .createdTime(new Date())
                .build());
    }

    @Override
    @DSTransactional
    public void addKV(AddI18nDictionaryKVRequestDTO request) {
        I18nDictionaryPO i18nDictionaryPo = i18nDictMapper.selectById(request.getDictionaryId());

        if (i18nDictionaryPo != null && StringUtils.isNotBlank(request.getTranslation())) {
            i18KVMapper.insertByUnicode(buildPO(request, i18nDictionaryPo, request.getTranslation(), request.getLanguageTag()));
            
            // 刷新缓存
            refreshCache(i18nDictionaryPo.getDictionaryType());
        }
    }

    @Override
    @DSTransactional
    public void updateKV(UpdateI18nDictionaryKVRequestDTO request) {
        // 先查询记录是否存在
        I18nDictionaryValuePO existingValue = i18KVMapper.selectById(request.getId());
        if (existingValue != null && StringUtils.isNotBlank(request.getTranslation())) {
            // 更新所有字段
            existingValue.setOriginal(request.getKey());
            existingValue.setLanguageTag(request.getLanguageTag());
            existingValue.setTranslation(request.getTranslation());
            existingValue.setRemark(request.getRemark());
            i18KVMapper.updateByUnicode(existingValue);
            // 刷新缓存
            refreshCache(existingValue.getDictionaryType());
        }
    }

    private static I18nDictionaryValuePO buildPO(AddI18nDictionaryKVRequestDTO request, I18nDictionaryPO i18nDictionaryPo, String trans, String languageEnum) {
        return I18nDictionaryValuePO.builder()
                .id(IdUtil.fastUUID())
                .dictionaryType(i18nDictionaryPo.getDictionaryType())
                .original(request.getKey())
                .translation(trans)
                .languageTag(languageEnum)
                .remark(request.getRemark())
                .createdTime(new Date())
                .build();
                

    }

    /**
     * 基于字典表转换格式
     * @param dictionaryValueList
     * @return
     */
    private Map<String, Map<String, String>> convertValueToDictMap(List<I18nDictionaryValuePO> dictionaryValueList) {
        Map<String, Map<String, String>> dictionaryValueMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(dictionaryValueList)) {
            return Maps.newHashMap();
        }
        dictionaryValueList.forEach(dictionaryValue -> {
            Map<String, String> valueMap = dictionaryValueMap.get(dictionaryValue.getOriginal());
            if (null == valueMap) {
                valueMap = Maps.newHashMap();
            }
            valueMap.put(dictionaryValue.getLanguageTag(), dictionaryValue.getTranslation());
            dictionaryValueMap.put(dictionaryValue.getOriginal(), valueMap);
        });
        return dictionaryValueMap;
    }

    @Override
    @DSTransactional
    public void delete(String id) {
        I18nDictionaryPO dictionary = i18nDictMapper.selectById(id);
        if (dictionary != null) {
            // 先删除关联的KV记录
            LambdaQueryWrapper<I18nDictionaryValuePO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(I18nDictionaryValuePO::getDictionaryType, dictionary.getDictionaryType());
            i18KVMapper.delete(wrapper);
            
            // 再删除字典记录
            i18nDictMapper.deleteById(id);
            
            // 刷新缓存
            flightBasicService.refreshI18nCache(dictionary.getDictionaryType(), new HashMap<>());
        }
    }

    @Override
    public List<I18nDictionaryDTO> query(String dictionaryName, String dictionaryType) {
        LambdaQueryWrapper<I18nDictionaryPO> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(dictionaryName)) {
            wrapper.like(I18nDictionaryPO::getDictionaryName, dictionaryName);
        }
        if (StringUtils.isNotBlank(dictionaryType)) {
            wrapper.like(I18nDictionaryPO::getDictionaryType, dictionaryType);
        }
        wrapper.orderByDesc(I18nDictionaryPO::getCreatedTime);
        
        return i18nDictMapper.selectList(wrapper).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<I18nDictionaryKVDTO> queryKV(String dictionaryId, String dictionaryType, String original, String languageTag, String translation, Boolean fuzzyOriginal) {
        // 优先使用 dictionaryType，如果没有则通过 dictionaryId 查询
        String finalDictionaryType = dictionaryType;
        if (StringUtils.isBlank(finalDictionaryType) && StringUtils.isNotBlank(dictionaryId)) {
            I18nDictionaryPO dictionary = i18nDictMapper.selectById(dictionaryId);
            if (dictionary == null) {
                return new ArrayList<>();
            }
            finalDictionaryType = dictionary.getDictionaryType();
        }

        if (StringUtils.isBlank(finalDictionaryType)) {
            return new ArrayList<>();
        }

        List<I18nDictionaryValuePO> kvList = i18KVMapper.queryByDictionaryTypeAndOriginal(
                finalDictionaryType,
                original,
                languageTag,
                translation,
                fuzzyOriginal != null ? fuzzyOriginal : false
        );

        return kvList.stream()
                .map(po -> {
                    I18nDictionaryKVDTO dto = new I18nDictionaryKVDTO();
                    dto.setId(po.getId());
                    dto.setDictionaryType(po.getDictionaryType());
                    dto.setOriginal(po.getOriginal());
                    dto.setTranslation(po.getTranslation());
                    dto.setLanguageTag(po.getLanguageTag());
                    dto.setRemark(po.getRemark());
                    dto.setCreatedTime(po.getCreatedTime());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<I18nDictionaryKVDTO> queryKVByType(String dictionaryType, String original, String languageTag, String translation) {
        List<I18nDictionaryValuePO> kvList = i18KVMapper.queryByDictionaryTypeAndOriginal(
            dictionaryType,
            original,
            languageTag,
            translation,
            true  // 保持原有的模糊查询行为
        );

        return kvList.stream()
            .map(po -> {
                I18nDictionaryKVDTO dto = new I18nDictionaryKVDTO();
                dto.setId(po.getId());
                dto.setDictionaryType(po.getDictionaryType());
                dto.setOriginal(po.getOriginal());
                dto.setTranslation(po.getTranslation());
                dto.setLanguageTag(po.getLanguageTag());
                dto.setRemark(po.getRemark());
                dto.setCreatedTime(po.getCreatedTime());
                return dto;
            })
            .collect(Collectors.toList());
    }

    @Override
    public String getDictionaryTypeById(String dictionaryId) {
        if (StringUtils.isBlank(dictionaryId)) {
            return null;
        }
        I18nDictionaryPO dictionary = i18nDictMapper.selectById(dictionaryId);
        return dictionary != null ? dictionary.getDictionaryType() : null;
    }

    @Override
    public List<I18nDictionaryKVDTO> queryKVByType(String dictionaryType, String original, String languageTag, String translation, Boolean fuzzyOriginal) {
        if (StringUtils.isBlank(dictionaryType)) {
            return new ArrayList<>();
        }

        List<I18nDictionaryValuePO> kvList = i18KVMapper.queryByDictionaryTypeAndOriginal(
            dictionaryType,
            original,
            languageTag,
            translation,
            fuzzyOriginal != null ? fuzzyOriginal : false
        );

        return kvList.stream()
            .map(po -> {
                I18nDictionaryKVDTO dto = new I18nDictionaryKVDTO();
                dto.setId(po.getId());
                dto.setDictionaryType(po.getDictionaryType());
                dto.setOriginal(po.getOriginal());
                dto.setTranslation(po.getTranslation());
                dto.setLanguageTag(po.getLanguageTag());
                dto.setRemark(po.getRemark());
                dto.setCreatedTime(po.getCreatedTime());
                return dto;
            })
            .collect(Collectors.toList());
    }

    private I18nDictionaryDTO convertToDTO(I18nDictionaryPO po) {
        I18nDictionaryDTO dto = new I18nDictionaryDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    @Override
    public void refreshCache(String dictionaryType) {
        if (dictionaryType != null) {
            List<I18nDictionaryValuePO> poList = i18KVMapper.selectByDictionaryType(dictionaryType);
            flightBasicService.refreshI18nCache(dictionaryType, convertValueToDictMap(poList));
        }
    }

    @Override
    @DSTransactional
    public void update(UpdateI18nDictionaryRequestDTO request) {
        // 查询原记录
        I18nDictionaryPO oldDictionary = i18nDictMapper.selectById(request.getId());
        if (oldDictionary == null) {
            throw new RuntimeException("字典不存在");
        }

        // 如果字典类型发生变化，需要同步更新子表
        boolean needUpdateKV = !oldDictionary.getDictionaryType().equals(request.getDictionaryType());
        
        // 更新主表
        I18nDictionaryPO dictionary = I18nDictionaryPO.builder()
                .id(request.getId())
                .dictionaryName(request.getDictionaryName())
                .dictionaryType(request.getDictionaryType())
                .remark(request.getRemark())
                .createdTime(oldDictionary.getCreatedTime())
                .build();
        i18nDictMapper.updateById(dictionary);

        // 如果字典类型变化，同步更新子表
        if (needUpdateKV) {
            LambdaQueryWrapper<I18nDictionaryValuePO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(I18nDictionaryValuePO::getDictionaryType, oldDictionary.getDictionaryType());
            
            List<I18nDictionaryValuePO> kvList = i18KVMapper.selectList(wrapper);
            if (!kvList.isEmpty()) {
                // 更新所有关联的KV记录的字典类型
                kvList.forEach(kv -> {
                    kv.setDictionaryType(request.getDictionaryType());
                    i18KVMapper.updateById(kv);
                });
            }
            
            // 刷新新旧两个字典的缓存
            refreshCache(oldDictionary.getDictionaryType());
            refreshCache(request.getDictionaryType());
        }
    }

    @Override
    @DSTransactional
    public void deleteKV(String id) {
        I18nDictionaryValuePO value = i18KVMapper.selectById(id);
        if (value != null) {
            // 删除记录
            i18KVMapper.deleteById(id);
            
            // 刷新缓存
            refreshCache(value.getDictionaryType());
        }
    }

    @Override
    @DSTransactional
    public void addOrUpdateKVByType(String dictionaryType, String original, String translation, String languageTag) {
        if (StringUtils.isBlank(dictionaryType) || StringUtils.isBlank(original)
            || StringUtils.isBlank(translation) || StringUtils.isBlank(languageTag)) {
            return;
        }

        // 查询字典ID
        LambdaQueryWrapper<I18nDictionaryPO> dictWrapper = new LambdaQueryWrapper<>();
        dictWrapper.eq(I18nDictionaryPO::getDictionaryType, dictionaryType);
        I18nDictionaryPO dictionary = i18nDictMapper.selectOne(dictWrapper);

        if (dictionary == null) {
            throw new RuntimeException("字典类型不存在：" + dictionaryType);
        }

        // 查询是否已存在该语言的翻译
        List<I18nDictionaryKVDTO> existingTranslations = queryKV(
            dictionary.getId(),
            null,
            original,
            languageTag,
            null,
            false  // 精确匹配
        );

        if (!existingTranslations.isEmpty()) {
            // 如果已存在翻译，更新它
            I18nDictionaryKVDTO existingTranslation = existingTranslations.get(0);
            updateKV(UpdateI18nDictionaryKVRequestDTO.builder()
                    .id(existingTranslation.getId())
                    .key(original)
                    .translation(translation)
                    .languageTag(languageTag)
                    .remark(existingTranslation.getRemark())
                    .build());
        } else {
            // 如果不存在，则新增翻译
            addKV(AddI18nDictionaryKVRequestDTO.builder()
                    .dictionaryId(dictionary.getId())
                    .key(original)
                    .translation(translation)
                    .languageTag(languageTag)
                    .build());
        }

        // 刷新缓存
        refreshCache(dictionaryType);
    }

    @Override
    public Map<String, Map<String, String>> fetchDictData(String dictionaryType) {
        List<I18nDictionaryValuePO> poList = i18KVMapper.selectByDictionaryType(dictionaryType);
        if (CollectionUtils.isEmpty(poList)) {
            return Maps.newHashMap();
        }
        return convertValueToDictMap(poList);
    }

    @Override
    @DSTransactional
    public void importData(InputStream inputStream) throws Exception {
        List<I18nDictionaryImportDTO> list = new ArrayList<>();
        
        // 先查询所有已存在的字典类型
        List<I18nDictionaryPO> existingDictionaries = i18nDictMapper.selectList(null);
        Set<String> existingDictionaryTypes = existingDictionaries.stream()
                .map(I18nDictionaryPO::getDictionaryType)
                .collect(Collectors.toSet());
        
        EasyExcel.read(inputStream, I18nDictionaryImportDTO.class, new ReadListener<I18nDictionaryImportDTO>() {
            @Override
            public void invoke(I18nDictionaryImportDTO data, AnalysisContext context) {
                // 数据校验
                if (StringUtils.isBlank(data.getDictionaryType())) {
                    throw new RuntimeException(String.format("第%d行数据错误：字典类型不能为空", context.readRowHolder().getRowIndex() + 1));
                }
                if (StringUtils.isBlank(data.getOriginal())) {
                    throw new RuntimeException(String.format("第%d行数据错误：原文不能为空", context.readRowHolder().getRowIndex() + 1));
                }
                if (StringUtils.isBlank(data.getTranslation())) {
                    throw new RuntimeException(String.format("第%d行数据错误：译文不能为空", context.readRowHolder().getRowIndex() + 1));
                }
                if (StringUtils.isBlank(data.getLanguageTag())) {
                    throw new RuntimeException(String.format("第%d行数据错误：语言标签不能为空", context.readRowHolder().getRowIndex() + 1));
                }
                
                // 验证字典类型是否存在
                validateDictionaryType(data.getDictionaryType(), context.readRowHolder().getRowIndex() + 1, existingDictionaryTypes);
                
                // 验证语言标签是否在系统支持范围内
                validateLanguageTag(data.getLanguageTag(), context.readRowHolder().getRowIndex() + 1);
                
                list.add(data);
            }
            
            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                // 批量处理数据
                for (I18nDictionaryImportDTO item : list) {
                    // 查询字典
                    LambdaQueryWrapper<I18nDictionaryPO> dictWrapper = new LambdaQueryWrapper<>();
                    dictWrapper.eq(I18nDictionaryPO::getDictionaryType, item.getDictionaryType());
                    I18nDictionaryPO dictionary = i18nDictMapper.selectOne(dictWrapper);
                    
                    // 查询是否已存在该翻译
                    LambdaQueryWrapper<I18nDictionaryValuePO> valueWrapper = new LambdaQueryWrapper<>();
                    valueWrapper.eq(I18nDictionaryValuePO::getDictionaryType, item.getDictionaryType())
                            .eq(I18nDictionaryValuePO::getOriginal, item.getOriginal())
                            .eq(I18nDictionaryValuePO::getLanguageTag, item.getLanguageTag());
                    
                    I18nDictionaryValuePO value = i18KVMapper.selectOne(valueWrapper);
                    
                    if (value != null) {
                        // 更新已存在的翻译
                        value.setTranslation(item.getTranslation());
                        i18KVMapper.updateByUnicode(value);
                    } else {
                        // 插入新翻译
                        value = I18nDictionaryValuePO.builder()
                                .id(IdUtil.fastUUID())
                                .dictionaryType(item.getDictionaryType())
                                .original(item.getOriginal())
                                .translation(item.getTranslation())
                                .languageTag(item.getLanguageTag())
                                .createdTime(new Date())
                                .build();
                        i18KVMapper.insertByUnicode(value);
                    }
                }
                
                // 刷新所有导入的字典类型的缓存
                Set<String> dictionaryTypes = list.stream()
                        .map(I18nDictionaryImportDTO::getDictionaryType)
                        .collect(Collectors.toSet());
                        
                // 遍历刷新每个字典类型的缓存
                for (String dictionaryType : dictionaryTypes) {
                    List<I18nDictionaryValuePO> poList = i18KVMapper.selectByDictionaryType(dictionaryType);
                    flightBasicService.refreshI18nCache(dictionaryType, convertValueToDictMap(poList));
                }
            }
        }).headRowNumber(0)  // 设置没有标题行
          .sheet()
          .doRead();
    }
}
