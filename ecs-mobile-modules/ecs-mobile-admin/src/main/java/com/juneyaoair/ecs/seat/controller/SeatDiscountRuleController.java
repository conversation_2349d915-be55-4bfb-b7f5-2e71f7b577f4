package com.juneyaoair.ecs.seat.controller;

import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.valid.InsertValid;
import com.juneyaoair.ecs.manage.dto.valid.UpdateValid;
import com.juneyaoair.ecs.seat.param.SeatDiscountRuleParam;
import com.juneyaoair.ecs.seat.result.SeatDiscountRuleInfo;
import com.juneyaoair.ecs.seat.result.SeatDiscountRuleResult;
import com.juneyaoair.ecs.seat.service.SeatDiscountRuleService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 值机选座-常规价格折扣设置
 * @created 2024/3/27 14:20
 */
@Slf4j
@Api(value = "SeatDiscountRuleController", tags = "值机选座-常规价格折扣设置")
@RestController
public class SeatDiscountRuleController {

    @Autowired
    private SeatDiscountRuleService seatDiscountRuleService;

    @ApiOperation(value = "值机选座-常规价格折扣查询", httpMethod = "POST")
    @PostMapping("/seatDiscountRule/getSeatDiscountRule")
    public R<PageResult<SeatDiscountRuleResult>> getSeatDiscountRule(@RequestBody @Validated SeatDiscountRuleParam seatDiscountRuleParam) {
        PageResult<SeatDiscountRuleResult> pageInfo = seatDiscountRuleService.getSeatDiscountRule(seatDiscountRuleParam);
        return R.ok(pageInfo);
    }

    @ApiOperation(value = "值机选座-新增常规价格折扣", httpMethod = "POST")
    @PostMapping("/seatDiscountRule/saveSeatDiscountRule")
    public R<String> saveSeatDiscountRule(@RequestBody @Validated(value = {InsertValid.class}) SeatDiscountRuleInfo seatDiscountRuleInfo) {
        seatDiscountRuleService.saveSeatDiscountRule(seatDiscountRuleInfo);
        return R.ok("OK");
    }

    @ApiOperation(value = "值机选座-更新常规价格折扣", httpMethod = "POST")
    @PostMapping("/seatDiscountRule/updateSeatDiscountRule")
    public R<String> updateSeatDiscountRule(@RequestBody @Validated(value = {UpdateValid.class}) SeatDiscountRuleInfo seatDiscountRuleInfo) {
        seatDiscountRuleService.updateSeatDiscountRule(seatDiscountRuleInfo);
        return R.ok("OK");
    }

}
