package com.juneyaoair.ecs.manage.service.syncfile.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.juneyaoair.ecs.manage.enums.HK_MO_TWCityEnum;
import com.juneyaoair.ecs.manage.service.syncfile.ICitySyncJsFileService;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.manage.b2c.entity.CityInfoPO;
import com.juneyaoair.manage.b2c.entity.CountryPO;
import com.juneyaoair.manage.b2c.service.ICityInfoService;
import com.juneyaoair.manage.b2c.service.ICountryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CitySyncJsFileServiceImpl extends SyncJsFileService implements ICitySyncJsFileService {

    private final static String contentName = "city";
    @Resource
    private ICityInfoService ICityInfoService;
    @Resource
    private ICountryService ICountryService;

    @Override
    protected String getContentName() {
        return contentName;
    }

    @Override
    public String prepareData() {
        List<CityInfoPO> allCityInfoPO = ICityInfoService.list();
        changeHKMOTWCountry(allCityInfoPO);
        List<CountryPO> countryPOS = ICountryService.list();
        Map<String, String> countryCodeMap = countryPOS.stream().collect(Collectors.toMap(CountryPO::getCountryCode, CountryPO::getCountryName));
        //只保留启用状态的
        allCityInfoPO = allCityInfoPO.stream().filter(cityInfoPO -> "1".equals(cityInfoPO.getStatus())).collect(Collectors.toList());
        //再筛选热门
        List<CityInfoPO> listHotCityInfoPO = allCityInfoPO.stream()
                .filter(cityInfoPO ->
                        StringUtils.equals(cityInfoPO.getIsHotCity(), "Y")
                                && StringUtils.equals("1", cityInfoPO.getStatus()))
                .collect(Collectors.toList());
        //生成热门城市&所有启用城市js数组
        return generateCity(allCityInfoPO, countryCodeMap, listHotCityInfoPO).toString();
    }

    private void changeHKMOTWCountry(List<CityInfoPO> allCityInfoPO) {
        if (CollUtil.isEmpty(allCityInfoPO)) {
            return;
        }
        for (CityInfoPO po : allCityInfoPO) {
            if (Arrays.stream(HK_MO_TWCityEnum.values()).anyMatch(i -> i == HK_MO_TWCityEnum.getEnumByCode(po.cityCode))) {
                po.countryCode = "CN";
            }
        }
    }

    private StringBuffer generateCity(List<CityInfoPO> allCityInfoPO, Map<String, String> countryCodeMap, List<CityInfoPO> listHotCityInfoPO) {
        //拼装热门城市 只保留启用状态的城市
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("var allCommoncitys=new Array();\r\n");
        for (int i = 0; i < listHotCityInfoPO.size(); i++) {
            CityInfoPO cityInfoPO = listHotCityInfoPO.get(i);
            String cityCode = cityInfoPO.getCityCode();
            String cityName = cityInfoPO.getCityName();
            String cityEName = cityInfoPO.getCityEName();
            String cityPinYin = HOStringUtil.capitalizeFirstLetter(cityInfoPO.getCityPinYin());
            String cityPinYinAbb = HOStringUtil.capitalizeFirstLetter(cityInfoPO.getCityPinYinAbb());
            String countryName = countryCodeMap.get(cityInfoPO.getCountryCode());
            String isInternational = cityInfoPO.getIsInternational();
            String cityTimeZone = cityInfoPO.getCityTimeZone() == null ? "" : StringUtils.trim(cityInfoPO.getCityTimeZone());
            String cityPicturePcUrl = cityInfoPO.cityPicturePcUrl;
//            /**
//             * pc端城市图片
//             */
//            public String cityPicturePcUrl;
            if (NumberUtil.isNumber(cityTimeZone)) {
                if (Float.parseFloat(cityTimeZone) > 0) {
                    cityTimeZone = "+" + cityTimeZone;
                }
            }
            stringBuffer.append("allCommoncitys[" + i + "]=new Array('" + cityCode + "','" + cityName + "','" + cityEName + "','" + cityPinYin + "','" + cityPinYinAbb + "','" + isInternational + "','" + countryName + "','" + cityTimeZone
                    + "','" + cityPicturePcUrl + "');\r\n");
        }

        //拼装所有城市 只保留启用状态的城市
        stringBuffer.append("var allCitys=new Array();\r\n");
        for (int i = 0; i < allCityInfoPO.size(); i++) {
            CityInfoPO cityInfoPO = allCityInfoPO.get(i);
            String cityCode = cityInfoPO.getCityCode();
            String cityName = cityInfoPO.getCityName();
            String cityEName = cityInfoPO.getCityEName();
            String cityPinYin = cityInfoPO.getCityPinYin();
            String cityPinYinAbb = cityInfoPO.getCityPinYinAbb();
            String cityPinYinFirst = StringUtils.substring(cityPinYin, 0, 1);
            String countryName = countryCodeMap.get(cityInfoPO.getCountryCode());
            String isInternational = cityInfoPO.getIsInternational();
            String cityTimeZone = cityInfoPO.getCityTimeZone() == null ? "" : StringUtils.trim(cityInfoPO.getCityTimeZone());
            if (NumberUtil.isNumber(cityTimeZone)) {
                if (Float.parseFloat(cityTimeZone) > 0) {
                    cityTimeZone = "+" + cityTimeZone;
                }
            }
            String cityPicturePcUrl = cityInfoPO.cityPicturePcUrl;
            stringBuffer.append("allCitys[" + i + "]=new Array('" + cityCode + "','" + cityName + "','" + cityEName + "','" + cityPinYin + "','" + cityPinYinAbb + "','" + cityPinYinFirst + "','" + isInternational + "','" + countryName + "','" + cityTimeZone
                    + "','" + cityPicturePcUrl + "');\r\n");
        }
        return stringBuffer;
    }

    @Override
    public boolean process() {
        mainProcess();
        return true;
    }
}
