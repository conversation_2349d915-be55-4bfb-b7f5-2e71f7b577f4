package com.juneyaoair.ecs.manage.external.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.github.pagehelper.PageInfo;
import com.juneyaoair.ecs.http.bean.HttpResult;
import com.juneyaoair.ecs.http.service.HttpClientService;
import com.juneyaoair.ecs.manage.constant.ManageConstant;
import com.juneyaoair.ecs.manage.dto.base.BaseRequest;
import com.juneyaoair.ecs.manage.dto.base.BaseRequestUtil;
import com.juneyaoair.ecs.manage.dto.base.BaseResult;
import com.juneyaoair.ecs.manage.dto.base.BaseResultDTO;
import com.juneyaoair.ecs.manage.dto.base.PageDataResponse;
import com.juneyaoair.ecs.manage.enums.ResultEnum;
import com.juneyaoair.ecs.manage.external.CussService;
import com.juneyaoair.ecs.manage.properties.ThirdAppUrlSet;
import com.juneyaoair.ecs.seat.param.CheckinSegmentTypeTimeParam;
import com.juneyaoair.ecs.seat.param.CheckinTimeStatusParam;
import com.juneyaoair.ecs.seat.param.Parameter;
import com.juneyaoair.ecs.seat.param.SafeSeatCancelParam;
import com.juneyaoair.ecs.seat.param.SafeSeatParam;
import com.juneyaoair.ecs.seat.param.SafeSeatQueryParam;
import com.juneyaoair.ecs.seat.param.SafeSeatUpdateParam;
import com.juneyaoair.ecs.seat.param.SeatAirportParam;
import com.juneyaoair.ecs.seat.param.SeatBaseOpenQuery;
import com.juneyaoair.ecs.seat.param.SeatBaseOpenSave;
import com.juneyaoair.ecs.seat.param.SeatBaseOpenStatus;
import com.juneyaoair.ecs.seat.param.SeatDiscountRuleParam;
import com.juneyaoair.ecs.seat.param.SeatFlightTypeAreaQuery;
import com.juneyaoair.ecs.seat.param.SeatFlightTypeQuery;
import com.juneyaoair.ecs.seat.param.SeatListQuery;
import com.juneyaoair.ecs.seat.param.SeatChannelParam;
import com.juneyaoair.ecs.seat.param.SeatLockQuery;
import com.juneyaoair.ecs.seat.result.CheckinSegmentTypeTimeResult;
import com.juneyaoair.ecs.seat.result.CussPageResult;
import com.juneyaoair.ecs.seat.result.CussPageResult2;
import com.juneyaoair.ecs.seat.result.SafeSeatInfo;
import com.juneyaoair.ecs.seat.result.SeatAirportResult;
import com.juneyaoair.ecs.seat.result.SeatBaseOpen;
import com.juneyaoair.ecs.seat.result.SeatChannelResult;
import com.juneyaoair.ecs.seat.result.SeatDiscountRuleInfo;
import com.juneyaoair.ecs.seat.result.SeatDiscountRuleResult;
import com.juneyaoair.ecs.seat.result.SeatFlightType;
import com.juneyaoair.ecs.seat.result.SeatFlightTypeArea;
import com.juneyaoair.ecs.seat.result.SeatFlightTypeSeat;
import com.juneyaoair.ecs.seat.result.SeatLockInfo;
import com.juneyaoair.ecs.seat.result.SegmentTypeInfo;
import com.juneyaoair.ecs.upg.param.QueryUpgPriceParam;
import com.juneyaoair.ecs.upg.result.UpgPriceInfo;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 值机选座、升舱服务
 * @created 2023/9/11 15:46
 */
@Service
public class CussServiceImpl implements CussService {

    @Autowired
    private HttpClientService httpClientService;
    @Autowired
    private ThirdAppUrlSet thirdAppUrlSet;

    @Override
    public List<UpgPriceInfo> queryUpgPrice(QueryUpgPriceParam queryUpgPriceParam) {
        BaseRequest<QueryUpgPriceParam> request = BaseRequestUtil.createRequest(queryUpgPriceParam);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.QUERY_UPG_PRICE,null);
        if(!httpResult.isResult()) {
            throw new HoServiceException("升舱价格查询失败，请稍后再试");
        }
        TypeReference<BaseResult<List<UpgPriceInfo>>> typeReference = new TypeReference<BaseResult<List<UpgPriceInfo>>>() {
        };
        BaseResult<List<UpgPriceInfo>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public void refreshAirlineBaseSetRedis() {
        BaseRequest<String> request = BaseRequestUtil.createRequest(null);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.REFRESH_AIRLINE_BASE_SET_REDIS,null);
        if(!httpResult.isResult()) {
            throw new HoServiceException("更新升舱系统航线缓存数据失败，请稍后再试");
        }
        TypeReference<BaseResult<String>> typeReference = new TypeReference<BaseResult<String>>() {
        };
        BaseResult<String> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public CussPageResult<SeatChannelResult> getChannelList(SeatListQuery seatListQuery) {
        BaseRequest<SeatListQuery> request = BaseRequestUtil.createRequest(seatListQuery);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_CHANNEL_SEARCH,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询选座渠道失败，请稍后再试");
        }
        TypeReference<BaseResult<CussPageResult<SeatChannelResult>>> typeReference = new TypeReference<BaseResult<CussPageResult<SeatChannelResult>>>() {
        };
        BaseResult<CussPageResult<SeatChannelResult>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public void createChannel(SeatChannelParam seatChannelParam) {
        BaseRequest<SeatChannelParam> request = BaseRequestUtil.createRequest(seatChannelParam);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_CHANNEL_CREATE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("新增选座渠道失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public void updateChannel(SeatChannelParam seatChannelParam) {
        BaseRequest<SeatChannelParam> request = BaseRequestUtil.createRequest(seatChannelParam);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_CHANNEL_UPDATE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("修改选座渠道失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public CussPageResult<SeatAirportResult> getAirportList(SeatListQuery seatListQuery) {
        BaseRequest<SeatListQuery> request = BaseRequestUtil.createRequest(seatListQuery);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_AIRPORT_SEARCH,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询选座机场失败，请稍后再试");
        }
        TypeReference<BaseResult<CussPageResult<SeatAirportResult>>> typeReference = new TypeReference<BaseResult<CussPageResult<SeatAirportResult>>>() {
        };
        BaseResult<CussPageResult<SeatAirportResult>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public void createAirport(SeatAirportParam seatAirportParam) {
        BaseRequest<SeatAirportParam> request = BaseRequestUtil.createRequest(seatAirportParam);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_AIRPORT_CREATE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("新增选座机场设置失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public void updateAirport(SeatAirportParam seatAirportParam) {
        BaseRequest<SeatAirportParam> request = BaseRequestUtil.createRequest(seatAirportParam);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_AIRPORT_UPDATE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("修改选座机场设置失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public List<SegmentTypeInfo> getSegmentType() {
        BaseRequest<Object> baseRequest = BaseRequestUtil.createRequest(null);
        HttpResult httpResult = httpClientService.doPost(baseRequest,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_CHECK_IN_TIME_SEGMENT_TYPE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询值机航线类型，请稍后再试");
        }
        TypeReference<BaseResult<List<SegmentTypeInfo>>> typeReference = new TypeReference<BaseResult<List<SegmentTypeInfo>>>() {
        };
        BaseResult<List<SegmentTypeInfo>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public List<CheckinSegmentTypeTimeResult> checkinSegmentTypeTimeSearch(String airportCode) {
        CheckinSegmentTypeTimeParam checkinSegmentTypeTimeParam = new CheckinSegmentTypeTimeParam();
        checkinSegmentTypeTimeParam.setAirportCode(airportCode);
        checkinSegmentTypeTimeParam.setPageNum(1);
        checkinSegmentTypeTimeParam.setPageSize(200);
        BaseRequest<CheckinSegmentTypeTimeParam> baseRequest = BaseRequestUtil.createRequest(checkinSegmentTypeTimeParam);
        HttpResult httpResult = httpClientService.doPost(baseRequest,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_AIRPORT_CHECK_IN_TIME_SEGMENT_TYPE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询当前机场值机航线类型配置信息失败，请稍后再试");
        }
        BaseResultDTO<PageInfo<CheckinSegmentTypeTimeResult>> baseResult = JSON.parseObject(httpResult.getResponse(), new TypeReference<BaseResultDTO<PageInfo<CheckinSegmentTypeTimeResult>>>() {
        });
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult().getList();
    }

    @Override
    public void checkinSegmentTypeTimeUpdate(List<CheckinSegmentTypeTimeResult> checkinSegmentTypeTimeList) {
        BaseRequest<List<CheckinSegmentTypeTimeResult>> request = BaseRequestUtil.createRequest(checkinSegmentTypeTimeList);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_AIRPORT_CHECK_IN_TIME_SEGMENT_TYPE_UPDATE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("修改机场值机时间失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public void changeCheckInTimeStatus(CheckinTimeStatusParam checkinTimeStatusParam) {
        BaseRequest<CheckinTimeStatusParam> request = BaseRequestUtil.createRequest(checkinTimeStatusParam);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_AIRPORT_CHECK_IN_TIME_SEGMENT_TYPE_STATUS,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("更新机场值机开放时间状态失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public CussPageResult<SeatFlightType> getFlightTypeList(SeatFlightTypeQuery seatFlightTypeQuery) {
        BaseRequest<SeatFlightTypeQuery> request = BaseRequestUtil.createRequest(seatFlightTypeQuery);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_FLIGHT_TYPE_SEARCH,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询选座机型失败，请稍后再试");
        }
        TypeReference<BaseResult<CussPageResult<SeatFlightType>>> typeReference = new TypeReference<BaseResult<CussPageResult<SeatFlightType>>>() {
        };
        BaseResult<CussPageResult<SeatFlightType>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public Boolean updateFlightType(SeatFlightType seatFlightType) {
        BaseRequest<SeatFlightType> request = BaseRequestUtil.createRequest(seatFlightType);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_FLIGHT_TYPE_UPDATE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("更新机型信息失败，请稍后再试");
        }
        TypeReference<BaseResult<Boolean>> typeReference = new TypeReference<BaseResult<Boolean>>() {
        };
        BaseResult<Boolean> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public SeatFlightType getFlightType(Long flightTypeId) {
        BaseRequest<Long> request = BaseRequestUtil.createRequest(flightTypeId);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_FLIGHT_TYPE_DETAIL,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询指定ID选座机型信息失败，请稍后再试");
        }
        TypeReference<BaseResult<SeatFlightType>> typeReference = new TypeReference<BaseResult<SeatFlightType>>() {
        };
        BaseResult<SeatFlightType> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public void createFlightTypeSeat(SeatFlightTypeSeat seatFlightTypeSeat) {
        BaseRequest<SeatFlightTypeSeat> request = BaseRequestUtil.createRequest(seatFlightTypeSeat);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_FLIGHT_TYPE_SEAT_CREATE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("更新机型座位图信息失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public CussPageResult<SeatFlightTypeArea> getFlightTypeAreaList(SeatFlightTypeAreaQuery seatFlightTypeAreaQuery) {
        BaseRequest<SeatFlightTypeAreaQuery> request = BaseRequestUtil.createRequest(seatFlightTypeAreaQuery);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_FLIGHT_TYPE_AREA_SEARCH,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询选座机型区域划分失败，请稍后再试");
        }
        TypeReference<BaseResult<CussPageResult<SeatFlightTypeArea>>> typeReference = new TypeReference<BaseResult<CussPageResult<SeatFlightTypeArea>>>() {
        };
        BaseResult<CussPageResult<SeatFlightTypeArea>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public void saveFlightTypeArea(SeatFlightTypeArea seatFlightTypeArea) {
        BaseRequest<SeatFlightTypeArea> request = BaseRequestUtil.createRequest(seatFlightTypeArea);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_FLIGHT_TYPE_AREA_SAVE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("保存机型区域划分信息失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public CussPageResult<SeatBaseOpen> getBaseOpenList(SeatBaseOpenQuery seatBaseOpenQuery) {
        BaseRequest<SeatBaseOpenQuery> request = BaseRequestUtil.createRequest(seatBaseOpenQuery);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_BASE_OPEN_SEARCH,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询基础开放信息失败，请稍后再试");
        }
        TypeReference<BaseResult<CussPageResult<SeatBaseOpen>>> typeReference = new TypeReference<BaseResult<CussPageResult<SeatBaseOpen>>>() {
        };
        BaseResult<CussPageResult<SeatBaseOpen>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public void saveBaseOpen(SeatBaseOpenSave seatBaseOpenQuery) {
        BaseRequest<SeatBaseOpenSave> request = BaseRequestUtil.createRequest(seatBaseOpenQuery);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_BASE_OPEN_SAVE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("保存基础开放时间信息失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public void changeBaseOpenStatus(SeatBaseOpenStatus seatBaseOpenStatus) {
        BaseRequest<SeatBaseOpenStatus> request = BaseRequestUtil.createRequest(seatBaseOpenStatus);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_BASE_OPEN_STATUS,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("修改基础开放时间状态失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public CussPageResult2<SeatDiscountRuleResult> getSeatDiscountRule(SeatDiscountRuleParam seatDiscountRuleParam) {
        BaseRequest<SeatDiscountRuleParam> request = BaseRequestUtil.createRequest(seatDiscountRuleParam);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_GET_SEAT_DISCOUNT_RULE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询常规价格折扣失败，请稍后再试");
        }
        TypeReference<BaseResult<CussPageResult2<SeatDiscountRuleResult>>> typeReference = new TypeReference<BaseResult<CussPageResult2<SeatDiscountRuleResult>>>() {
        };
        BaseResult<CussPageResult2<SeatDiscountRuleResult>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public void saveSeatDiscountRule(SeatDiscountRuleInfo seatDiscountRuleInfo) {
        BaseRequest<SeatDiscountRuleInfo> request = BaseRequestUtil.createRequest(seatDiscountRuleInfo);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_SAVE_SEAT_DISCOUNT_RULE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("保存常规价格折扣失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public void updateSeatDiscountRule(SeatDiscountRuleInfo seatDiscountRuleInfo) {
        BaseRequest<SeatDiscountRuleInfo> request = BaseRequestUtil.createRequest(seatDiscountRuleInfo);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_UPDATE_SEAT_DISCOUNT_RULE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("更新常规价格折扣失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public Parameter getParameter(String parameterKey) {
        if (StringUtils.isEmpty(parameterKey)) {
            return null;
        }
        Parameter tParameter = new Parameter();
        tParameter.setParameterKey(parameterKey);
        BaseRequest<Parameter> request = BaseRequestUtil.createRequest(tParameter);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.CUSS_GET_PARAMETER,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("获取参数信息接口失败，请稍后再试");
        }
        TypeReference<BaseResult<Parameter>> typeReference = new TypeReference<BaseResult<Parameter>>() {
        };
        BaseResult<Parameter> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public void updateParameter(Parameter parameter) {
        BaseRequest<Parameter> request = BaseRequestUtil.createRequest(parameter);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.CUSS_UPDATE_PARAMETER,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("更新参数信息接口失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public CussPageResult<SeatLockInfo> getSeatLockList(SeatLockQuery seatLockQuery) {
        BaseRequest<SeatLockQuery> request = BaseRequestUtil.createRequest(seatLockQuery);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_GET_SEAT_LOCK_LIST,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询座位关闭清单查询失败，请稍后再试");
        }
        TypeReference<BaseResult<CussPageResult<SeatLockInfo>>> typeReference = new TypeReference<BaseResult<CussPageResult<SeatLockInfo>>>() {
        };
        BaseResult<CussPageResult<SeatLockInfo>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public void saveSeatLock(SeatLockInfo seatLockInfo) {
        BaseRequest<SeatLockInfo> request = BaseRequestUtil.createRequest(seatLockInfo);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.SEAT_SAVE_SEAT_LOCK,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("保存座位关闭信息失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public void createSafeSeat(SafeSeatParam safeSeatParam) {
        BaseRequest<SafeSeatParam> request = BaseRequestUtil.createRequest(safeSeatParam);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.CREATE_SAFE_SEAT,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("保存安全员座位信息失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public void updateSafeSeat(SafeSeatUpdateParam safeSeatUpdateParam) {
        BaseRequest<SafeSeatUpdateParam> request = BaseRequestUtil.createRequest(safeSeatUpdateParam);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.UPDATE_SAFE_SEAT,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("更新安全员座位信息失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public PageDataResponse<SafeSeatInfo> getSafeSeatList(SafeSeatQueryParam safeSeatQueryParam) {
        BaseRequest<SafeSeatQueryParam> request = BaseRequestUtil.createRequest(safeSeatQueryParam);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.GET_SAFE_SEAT_LIST,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询安全员座位信息清单失败，请稍后再试");
        }
        TypeReference<BaseResult<PageDataResponse<SafeSeatInfo>>> typeReference = new TypeReference<BaseResult<PageDataResponse<SafeSeatInfo>>>() {
        };
        BaseResult<PageDataResponse<SafeSeatInfo>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public void cancelSafeSeat(SafeSeatCancelParam safeSeatCancelParam) {
        BaseRequest<SafeSeatCancelParam> request = BaseRequestUtil.createRequest(safeSeatCancelParam);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getCussUrl() + ManageConstant.CANCEL_SAFE_SEAT,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("执行取消安全员座位旅客操作失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }
}
