package com.juneyaoair.ecs.manage.mapstruct;

import com.juneyaoair.ecs.manage.dto.pet.PetCounterDto;
import com.juneyaoair.ecs.manage.dto.pet.PetFlightPlanDto;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.manage.b2c.entity.pet.PetCounterPO;
import com.juneyaoair.manage.b2c.entity.pet.PetFlightPlanPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/12 18:10
 */
@Mapper
public interface PetCounterToPOMapStruct {
    PetCounterToPOMapStruct INSTANCE = Mappers.getMapper(PetCounterToPOMapStruct.class);
    @Mappings({})
    PetCounterPO toPetCounterToPO(PetCounterDto petCounterDto);

    @Mappings({
            @Mapping(source = "flightStart", target = "flightStart", qualifiedByName = "stringToDate"),
            @Mapping(source = "flightEnd", target = "flightEnd", qualifiedByName = "stringToDate")
    })
    PetFlightPlanPO toPetFlightPlanPO(PetFlightPlanDto petFlightPlanDto);

    @Named("stringToDate")
    default Date stringToDate(String dateStr) {
        Date now = new Date();
        if (dateStr == null || dateStr.isEmpty()) {
            return now;
        }
        try {
            return DateUtil.toDate(dateStr, DateUtil.DATE_FORMATE_YYYY_MM_DD);
        } catch (Exception e) {
            return null; // 或抛出异常
        }
    }
}
