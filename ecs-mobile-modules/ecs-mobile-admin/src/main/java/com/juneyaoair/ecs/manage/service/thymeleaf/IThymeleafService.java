package com.juneyaoair.ecs.manage.service.thymeleaf;

import com.juneyaoair.ecs.manage.dto.message.request.MessageDto;
import com.juneyaoair.ecs.manage.enums.NoticeTemplateEnum;

import java.util.Date;

/**
 * <AUTHOR>
 * @description Thymeleaf模板服务
 * @date 2023/5/17 10:11
 */
public interface IThymeleafService {
    /**
     * 生成html静态文件
     * @param messageDto
     */
    String createMessageHtml(MessageDto messageDto, NoticeTemplateEnum noticeTemplateEnum, Date curDate);
}
