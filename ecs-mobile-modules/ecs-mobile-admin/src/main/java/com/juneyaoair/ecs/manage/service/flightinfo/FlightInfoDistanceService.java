package com.juneyaoair.ecs.manage.service.flightinfo;

import com.juneyaoair.ecs.manage.dto.flightdistance.FlightDistancePricedDTO;
import com.juneyaoair.ecs.manage.dto.flightdistance.FlightDistancePricedLogsDTO;

import java.util.List;

public interface FlightInfoDistanceService {
    List<FlightDistancePricedDTO> select(FlightDistancePricedDTO param);

    void add(FlightDistancePricedDTO param);

    void update(FlightDistancePricedDTO param);

    void pause(FlightDistancePricedDTO param);

    List<FlightDistancePricedLogsDTO> queryLog(FlightDistancePricedDTO param);
}
