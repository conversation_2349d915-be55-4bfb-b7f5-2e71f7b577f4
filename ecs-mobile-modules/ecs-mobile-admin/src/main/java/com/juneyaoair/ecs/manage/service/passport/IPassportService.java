package com.juneyaoair.ecs.manage.service.passport;

import com.juneyaoair.ecs.manage.dto.activity.request.passport.PassportRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.passport.PassportInformation;

import java.util.List;

/**
 * @ClassName IPassportService
 * @Description
 * <AUTHOR>
 * @Date 2025/4/2 20:51
 * @Version 1.0
 */
public interface IPassportService {

    /**
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.activity.response.passport.PassportInformation>
     * <AUTHOR>
     * @Description 获取护照补全领取列表
     * @Date 21:53 2025/4/2
     **/

    List<PassportInformation> toCatchPassportList(PassportRequest passportRequest);

}
