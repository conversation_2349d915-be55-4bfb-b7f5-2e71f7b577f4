package com.juneyaoair.ecs.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.juneyaoair.ecs.excel.bean.ThirdPartPrize;
import com.juneyaoair.ecs.manage.config.AdminConfig;
import com.juneyaoair.ecs.manage.dto.config.ThirdPartActivityInfo;
import com.juneyaoair.ecs.manage.dto.config.ThirdPartMerchantInfo;
import com.juneyaoair.ecs.manage.dto.config.ThirdPartPrizeType;
import com.juneyaoair.ecs.manage.dto.config.ThirdPartPrizeTotalConfig;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.ecs.utils.ValidatorUtils;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.activity.ThirdPrizeRecordPO;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * @ClassName ThirdPartPrizesListener
 * @Description
 * <AUTHOR>
 * @Date 2025/3/31 8:38
 * @Version 1.0
 */
@SuppressWarnings("all")
@Scope("prototype")
@Component
public class ThirdPartPrizesListener extends AnalysisEventListener<ThirdPartPrize> {

    private static final String VALID_STATUS = "Y";

    private static final String INIT_STATUS = "INIT";

    @Resource
    private AdminConfig adminConfig;


    @Getter
    private final List<ThirdPrizeRecordPO> thirdPrizeRecordPOList = new ArrayList<>();


    @Override
    public void invoke(ThirdPartPrize thirdPartPrize, AnalysisContext analysisContext) {
        toValidateParams(thirdPartPrize, analysisContext.readRowHolder().getRowIndex() + 1);
        toCheckParams(thirdPartPrize, analysisContext.readRowHolder().getRowIndex() + 1);
        ThirdPrizeRecordPO thirdPrizeRecordPO = new ThirdPrizeRecordPO();
        BeanUtils.copyNotNullProperties(thirdPartPrize, thirdPrizeRecordPO);
        thirdPrizeRecordPO.setId(HOStringUtil.newGUID());
        thirdPrizeRecordPO.setCreateTime(new Date());
        thirdPrizeRecordPOList.add(thirdPrizeRecordPO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }


    /**
     * @param thirdPartPrize 原始数据
     * @param rowIndex     行数
     * @return void
     * <AUTHOR>
     * @Description 校验参数是否非空
     * @Date 21:38 2025/3/31
     **/
    private void toValidateParams(ThirdPartPrize thirdPartPrize, int rowIndex) {
        String errMsg = "";
        try {
            ValidatorUtils.valid(thirdPartPrize);
        } catch (HoServiceException hoServiceException) {
            errMsg = hoServiceException.getMessage();
        }
        if (StringUtils.isNotEmpty(errMsg)) {
            throw new HoServiceException(String.format("第%d行数据错误:%s", rowIndex, errMsg));
        }
    }

    /**
     * @param thirdPartPrize
     * @param rowIndex
     * @return void
     * <AUTHOR>
     * @Description 校验奖品
     * @Date 19:27 2025/4/1
     **/
    private void toCheckParams(ThirdPartPrize thirdPartPrize, int rowIndex) {
        ThirdPartPrizeTotalConfig thirdPartPrizeTotalConfig = adminConfig.toCatchThirdPartPrizeSendConfig();
        if (null == thirdPartPrizeTotalConfig ||
                null == thirdPartPrizeTotalConfig.getThirdPartActivityInfoConfig() || null == thirdPartPrizeTotalConfig.getThirdPartMerchantInfoConfig() ||
                null == thirdPartPrizeTotalConfig.getThirdPartPrizeTypeConfig() || CollectionUtils.isEmpty(thirdPartPrizeTotalConfig.getThirdPartActivityInfoConfig().getThirdPartActivityInfoList()) ||
                CollectionUtils.isEmpty(thirdPartPrizeTotalConfig.getThirdPartMerchantInfoConfig().getThirdPartMerchantInfoList()) || CollectionUtils.isEmpty(thirdPartPrizeTotalConfig.getThirdPartPrizeTypeConfig().getThirdPartPrizeTypeList())) {
            throw new HoServiceException("请检查配置信息");
        }
        Optional<ThirdPartActivityInfo> thirdPartActivityInfoOptional = thirdPartPrizeTotalConfig.getThirdPartActivityInfoConfig().getThirdPartActivityInfoList().stream().filter(el -> StringUtils.isNotEmpty(el.getActivityStatus()) && VALID_STATUS.equals(el.getActivityStatus()) && thirdPartPrize.getActivityCode().equals(el.getActivityCode())).findFirst();
        if (!thirdPartActivityInfoOptional.isPresent()) {
            throw new HoServiceException(String.format("第%d行数据错误:%s", rowIndex, "活动号非法"));
        }
        Optional<ThirdPartMerchantInfo> commonMerchantInfoOptional = thirdPartPrizeTotalConfig.getThirdPartMerchantInfoConfig().getThirdPartMerchantInfoList().stream().filter(el -> StringUtils.isNotEmpty(el.getMerchantStatus()) && VALID_STATUS.equals(el.getMerchantStatus()) && el.getMerchantCode().equals(thirdPartPrize.getMerchantCode())).findFirst();
        if (!commonMerchantInfoOptional.isPresent()) {
            throw new HoServiceException(String.format("第%d行数据错误:%s", rowIndex, "商户号非法"));
        }
        Optional<ThirdPartPrizeType> commonPrizeTypeOptional = thirdPartPrizeTotalConfig.getThirdPartPrizeTypeConfig().getThirdPartPrizeTypeList().stream().filter(el -> el.getPrizeType().equals(thirdPartPrize.getPrizeType())).findFirst();
        if (!commonPrizeTypeOptional.isPresent()) {
            throw new HoServiceException(String.format("第%d行数据错误:%s", rowIndex, "奖品类型非法"));
        }
        if (!INIT_STATUS.equals(thirdPartPrize.getPrizeStatus())) {
            throw new HoServiceException(String.format("第%d行数据错误:%s", rowIndex, "奖品状态值非法"));
        }
    }
}
