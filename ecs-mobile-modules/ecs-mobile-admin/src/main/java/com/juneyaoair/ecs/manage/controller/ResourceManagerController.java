package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.activity.common.ResourceManaDTO;
import com.juneyaoair.ecs.manage.dto.activity.common.ResourcePageReq;
import com.juneyaoair.ecs.manage.dto.base.PageDataResponse;
import com.juneyaoair.ecs.manage.service.resource.ResourceManagerService;
import com.juneyaoair.manage.b2c.entity.ResourcePO;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

@RequestMapping("resourceManager")
@RestController
@Api(value = "resourceManagerController", tags = "资源管理相关API")
@Slf4j
public class ResourceManagerController {

    @Autowired ResourceManagerService resourceManagerService;

    /**
     * 添加资源
     * @param platform
     * @param moduleName
     * @param minVer
     * @param maxVer
     * @param file
     * @return
     * @throws IOException
     * @throws NoSuchAlgorithmException
     */
    @PostMapping(path = "/addResource",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation(value = "添加资源", notes = "", httpMethod = "POST")
    public R<ResourcePO> addResource(
            @RequestParam(value = "platform", required = true) String platform,
            @RequestParam(value = "moduleName", required = true) String moduleName,
            @RequestParam(value = "minVer", required = true) String minVer,
            @RequestParam(value = "maxVer", required = true) String maxVer,
            @RequestParam(value = "file", required = false) MultipartFile file) throws IOException, NoSuchAlgorithmException {
        log.info("添加资源，参数为：{}，{}，{}，{}，{}",platform,moduleName,minVer,maxVer,file);
        ResourcePO resourcePO = resourceManagerService.addResource(platform,moduleName,minVer,maxVer,file);
        return R.ok(resourcePO);
    }

    /**
     * 资源列表分页查询
     * @param resourcePageReq
     * @return
     */
    @PostMapping("/showList")
    @ApiOperation(value = "资源列表分页查询", notes = "", httpMethod = "POST")
    public R<PageDataResponse> getResourceList(@RequestBody ResourcePageReq resourcePageReq){
        log.info("资源分页查询，参数为：{}",resourcePageReq);
        PageDataResponse pageDataResponse = resourceManagerService.resourceListPageQuery(resourcePageReq);
        return R.ok(pageDataResponse);
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @GetMapping("getResource/{id}")
    @ApiOperation(value = "根据id查询资源信息", notes = "", httpMethod = "GET")
    public R<ResourcePO> getResourceById(@PathVariable String id){
        log.info("查询id为：{}的资源信息",id);
        ResourcePO resourcePO = resourceManagerService.getResourceById(id);
        return R.ok(resourcePO);
    }

    /**
     * 根据id修改
     * @param resourceManaDTO
     * @return
     */
    @PostMapping("updateResource")
    @ApiOperation(value = "根据id修改资源信息", notes = "", httpMethod = "POST")
    public R updateResourceById(@RequestBody ResourceManaDTO resourceManaDTO){
        log.info("待修改的资源信息为：{}",resourceManaDTO);
        resourceManagerService.updateResourceById(resourceManaDTO);
        return R.ok();
    }

    /**
     * 根据id修改资源状态
     * @param flag
     * @param id
     * @return
     */
    @PostMapping("/updateFlag/{flag}")
    @ApiOperation(value = "根据id修改资源状态", notes = "", httpMethod = "POST")
    public R updateFlagById(@PathVariable String flag, String id){
        log.info("待修改的资源id为：{},修改资源状态为：{}",id, flag);
        resourceManagerService.updateFlagById(flag, id);
        return R.ok();
    }

}
