package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.base.PageDataResponse;
import com.juneyaoair.ecs.manage.dto.memberRights.MemberLevelDTO;
import com.juneyaoair.ecs.manage.dto.memberRights.MemberRightsDTO;
import com.juneyaoair.ecs.manage.dto.memberRights.MemberRightsPageReq;
import com.juneyaoair.ecs.manage.properties.ThirdAppUrlSet;
import com.juneyaoair.ecs.manage.service.MemberRightsManage.MemberRightsManageService;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.MemberLevelManagePO;
import com.juneyaoair.manage.b2c.entity.MemberRightsPO;
import com.juneyaoair.manage.thirdapi.crm.Header;
import com.juneyaoair.manage.thirdapi.crm.PtApiCRMRequest;
import com.ruoyi.common.core.domain.R;
import com.juneyaoair.ecs.manage.constant.ManageConstant;
import com.ruoyi.common.core.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.utils.ip.IpUtils;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequestMapping("memberRights")
@RestController
@Api(value = "memberRightsController", tags = "权益管理相关API")
@Slf4j
public class MemberRightsController{

    @Autowired
    MemberRightsManageService memberRightsService;

    @Autowired
    private ThirdAppUrlSet thirdAppUrlSet;

    @PostMapping("/searchForPage")
    @ApiOperation(value = "权益列表分页查询", notes = "", httpMethod = "POST")
    public R<PageDataResponse> searchForPage(@RequestBody MemberRightsPageReq memberRightsPageReq){
        log.info("权益分页查询，参数为：{}",memberRightsPageReq);
        PageDataResponse pageDataResponse = memberRightsService.memberRightsListPageQuery(memberRightsPageReq);
        return R.ok(pageDataResponse);
    }

    @PostMapping("/upload")
    @ApiOperation(value = "上传图标", notes = "", httpMethod = "POST")
    public R<String> fileUpload(@RequestParam("file") MultipartFile file){
        log.info("上传的图片为：{}",file);
        String url = memberRightsService.fileUpload(file);
        return R.ok(url);
    }

    @PostMapping("/addRights")
    @ApiOperation(value = "添加权益", notes = "", httpMethod = "POST")
    public R addRights(@RequestBody MemberRightsDTO memberRightsDTO){
        log.info("新增权益，参数为：{}",memberRightsDTO);
        memberRightsService.addRights(memberRightsDTO);
        return R.ok();
    }

    @PostMapping("/deleteRights/{id}")
    @ApiOperation(value = "根据id删除权益", notes = "", httpMethod = "POST")
    public R deleteRights(@PathVariable String id){
        log.info("待删除的权益id为：{}",id);
        memberRightsService.deleteRightsById(id);
        return R.ok();
    }

    @PostMapping("/updateRights")
    @ApiOperation(value = "根据id修改权益", notes = "", httpMethod = "POST")
    public R updateRights(@RequestBody MemberRightsDTO memberRightsDTO){
        log.info("待修改的权益为：{}",memberRightsDTO);
        memberRightsService.updateRightsById(memberRightsDTO);
        return R.ok();
    }

    @GetMapping("getRights/{id}")
    @ApiOperation(value = "根据id查询权益", notes = "", httpMethod = "GET")
    public R<MemberRightsPO> getRights(@PathVariable String id){
        log.info("查询id为：{}的权益信息",id);
        MemberRightsPO memberRightsPO = memberRightsService.getRightsById(id);
        return R.ok(memberRightsPO);
    }

    @GetMapping("searchAllMemberLevel")
    @ApiOperation(value = "查询所有权益规则", notes = "", httpMethod = "GET")
    public R<List<MemberLevelManagePO>> searchAllMemberLevel(){
        List<MemberLevelManagePO> memberLevelPOS = memberRightsService.searchAllMemberLevel();
        return R.ok(memberLevelPOS);
    }

    @GetMapping("/syncCRM/{ruleId}")
    @ApiOperation(value = "同步crm", notes = "", httpMethod = "GET")
    public R<String> syncCRM(@PathVariable("ruleId") String ruleId, HttpServletRequest request){

        log.info("CRM规则ID为:{}",ruleId);

        if(StringUtils.isEmpty(ruleId)){
            throw new HoServiceException("规则id不可为空");
        }

        try{
            Header header = new Header();
            header.setClientIP(IpUtils.getIpAddr(request));
            header.setClientVersion("");
            header.setTimestamp(System.currentTimeMillis());

            PtApiCRMRequest<Map<String, String[]>> ptApiCRMRequest = new PtApiCRMRequest();
            Map<String, String[]> dataMap = new HashMap<>();
            String[] strings = ruleId.split(",");
            dataMap.put("RuleIds", strings);
            ptApiCRMRequest.setData(dataMap);
            ptApiCRMRequest.setChannel("MOBILE");
            ptApiCRMRequest.setChannelPwd("MOBILE2014");
            ptApiCRMRequest.setHeader(header);

            String result = memberRightsService.syncCRM(ptApiCRMRequest, strings, thirdAppUrlSet.getSyncCrmUrl() + ManageConstant.CRM_MEMBER_RIGHTS);
            return R.ok(result);
        }catch (Exception e) {
            log.error("同步CRM会员权益信息错误!" + e.getMessage());
            throw new HoServiceException("同步CRM会员权益信息错误!" + e.getMessage());
        }
    }

    @GetMapping("searchLevelById/{id}")
    @ApiOperation(value = "根据id查询权益级别", notes = "", httpMethod = "GET")
    public R<MemberLevelManagePO> searchLevelById(@PathVariable String id){
        log.info("查询id为：{}的权益信息",id);
        MemberLevelManagePO memberLevelPO = memberRightsService.searchLevelById(id);
        return R.ok(memberLevelPO);
    }

    @PostMapping("/updateMemberLevel")
    @ApiOperation(value = "修改权益级别", notes = "", httpMethod = "POST")
    public R updateMemberLevel(@RequestBody MemberLevelDTO memberLevelDTO){
        log.info("待修改的权益级别为：{}", memberLevelDTO);
        memberRightsService.updateMemberLevel(memberLevelDTO);
        return R.ok();
    }
}
