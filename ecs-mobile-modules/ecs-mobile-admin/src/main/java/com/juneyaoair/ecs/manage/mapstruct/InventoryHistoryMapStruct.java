package com.juneyaoair.ecs.manage.mapstruct;

import com.juneyaoair.ecs.manage.dto.dih.InventoryHistoryExcel;
import com.juneyaoair.ecs.mongo.entity.InventoryHistoryPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/14 15:35
 */
@Mapper
public interface InventoryHistoryMapStruct {
    InventoryHistoryMapStruct INSTANCE = Mappers.getMapper(InventoryHistoryMapStruct.class);
    @Mappings({})
    InventoryHistoryExcel toInventoryHistoryExcel(InventoryHistoryPO inventoryHistoryPO);
}
