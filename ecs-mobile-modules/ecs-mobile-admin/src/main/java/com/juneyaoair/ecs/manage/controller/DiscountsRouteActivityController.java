package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.discount.ro.DiscountsRouteActivityRO;
import com.juneyaoair.ecs.manage.service.baseinfo.IDiscountsRouteService;
import com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteActivityPO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName DiscountsRouteActivityController
 * @Description
 * <AUTHOR>
 * @Date 2023/12/13 15:26
 * @Version 1.0
 */


@RequestMapping("discountsRouteActivity")
@RestController
@RequiredArgsConstructor
@Api(value = "DiscountsRouteActivityController", tags = "特惠航线API")
@Slf4j
public class DiscountsRouteActivityController extends HoBaseController{


    @Autowired
    private IDiscountsRouteService discountsRouteService;


    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ApiOperation(value = "新增", httpMethod = "POST")
    public R toAdd(@RequestBody DiscountsRouteActivityRO discountsRouteActivityRO) {
        return R.ok(discountsRouteService.save(discountsRouteActivityRO));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ApiOperation(value = "查询列表", httpMethod = "POST")
    public R toCatchList(@RequestBody DiscountsRouteActivityRO discountsRouteActivityRO) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        List<DiscountsRouteActivityPO> discountsRouteActivityPOS = discountsRouteService.toCatchList(discountsRouteActivityRO);
        return getPageData(discountsRouteActivityPOS, pageDomain);
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "/getDetails/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "获取详情", httpMethod = "GET")
    public R toCatchDetail(@PathVariable("id") String id) {
        return R.ok(discountsRouteService.getDetails(id));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ApiOperation(value = "更新", httpMethod = "POST")
    public R toUpdate(@RequestBody DiscountsRouteActivityRO discountsRouteActivityRO) {
        return R.ok(discountsRouteService.toUpdate(discountsRouteActivityRO));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "/updateValid/{id}/{valid}", method = RequestMethod.GET)
    @ApiOperation(value = "活动有效状态修改", httpMethod = "GET")
    public R toUpdateValid(@PathVariable("id") String id,@PathVariable("valid") String valid) {
        return R.ok(discountsRouteService.toUpdateValid(id,valid));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "/updateStatus/{id}/{status}", method = RequestMethod.GET)
    @ApiOperation(value = "活动发布状态修改", httpMethod = "GET")
    public R toUpdateStatus(@PathVariable("id") String id,@PathVariable("status") String status) {
        return R.ok(discountsRouteService.toUpdateStatus(id,status));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "/getSelectList", method = RequestMethod.GET)
    @ApiOperation(value = "下拉框选择", httpMethod = "GET")
    public R toCatchSelectList() {
        return R.ok(discountsRouteService.getSelectList());
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "getChildNamesById/{activityId}", method = RequestMethod.GET)
    @ApiOperation(value = "获取子名称", httpMethod = "GET")
    public R toCatchChildNameList(@PathVariable("activityId") String activityId, @Param("dataId") String  dataId) {
        return R.ok(discountsRouteService.getChildNameList(activityId,dataId));
    }

}
