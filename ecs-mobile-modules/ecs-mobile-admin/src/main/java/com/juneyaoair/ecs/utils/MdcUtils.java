package com.juneyaoair.ecs.utils;

import org.slf4j.MDC;

/**
 * @Author: caolei
 * @Description: MdcUtils
 * @Date: 2021/7/30 10:03
 * @Modified by:
 */
public class MdcUtils {

    /** 请求数据处理ID */
    public static final String REQUEST_ID = "requestId";


    /**
     * 请求处理ID
     * @param requestId
     */
    public static void setRequestId(String requestId) {
        MDC.put(REQUEST_ID, requestId);
    }

    /**
     * 获取处理ID
     * @return
     */
    public static String getRequestId() {
        return MDC.get(REQUEST_ID);
    }

    /**
     * 清理MDC
     */
    public static void clear() {
        MDC.clear();
    }
}
