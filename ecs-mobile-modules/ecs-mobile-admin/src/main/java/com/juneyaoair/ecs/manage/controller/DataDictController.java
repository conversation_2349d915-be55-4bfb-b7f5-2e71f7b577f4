package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.datadict.DICTType;
import com.juneyaoair.ecs.manage.dto.notice.DICTValue;
import com.juneyaoair.ecs.manage.service.datadict.IDataDictService;
import com.juneyaoair.manage.b2c.service.IDictService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName DataDictController
 * @Description 数据字典管理后台
 * <AUTHOR>
 * @Date 2024/1/23 14:34
 * @Version 1.0
 */

@RequestMapping("admin/datadict")
@RestController
@RequiredArgsConstructor
@Api(value = "DataDictController", tags = "数据字典API")
@Slf4j
public class DataDictController extends HoBaseController{

    @Autowired
    IDataDictService dataDictService;

    @Autowired
    private IDictService dictService;

    @PostMapping(value = "getDICTType")
    @ApiOperation(value = "列表获取", notes = "", httpMethod = "POST")
    public R<PageResult<DICTType>> getDICTType(@RequestBody DICTType dictType) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        List<DICTType> dictTypes = dataDictService.toCatchDICTTypeList(dictType);
        return getPageData(dictTypes, pageDomain);
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "addDICTType", method = RequestMethod.POST)
    @ApiOperation(value = "增加数据字典类型", httpMethod = "POST")
    public R addDICTType(@RequestBody DICTType dICTType) {
        dataDictService.toAddDICTType(dICTType);
        return R.ok();
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "updateDICTType", method = RequestMethod.GET)
    @ApiOperation(value = "修改数据字典类型", httpMethod = "GET")
    public R updateDICTType(DICTType dICTType) {
        dataDictService.toUpdateDICTType(dICTType);
        return R.ok();
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "deleteDICTType", method = RequestMethod.GET)
    @ApiOperation(value = "删除数据字典类型", httpMethod = "GET")
    public R deleteDICTType(String ids) {
        dataDictService.toDeleteDICTType(ids);
        return R.ok();
    }

    @PostMapping(value = "getDICTValue")
    @ApiOperation(value = "获取数据字典数据项列表", notes = "", httpMethod = "POST")
    public R<List<DICTValue>> getDICTValue(@RequestBody DICTValue dictValue) {
        return R.ok(dataDictService.toCatchDICTValueList(dictValue));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "addDICTValue", method = RequestMethod.POST)
    @ApiOperation(value = "增加数据字典数据项", httpMethod = "POST")
    public R addDICTValue(@RequestBody DICTValue dICTValue) {
        dataDictService.toAddDICTValue(dICTValue);
        return R.ok();
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "updateDICTValue", method = RequestMethod.POST)
    @ApiOperation(value = "更新数据字典数据项", httpMethod = "POST")
    public R updateDICTValue(@RequestBody DICTValue dICTValue) {
        dataDictService.toUpdateDICTValue(dICTValue);
        return R.ok();
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "deleteDICTValue", method = RequestMethod.GET)
    @ApiOperation(value = "删除数据字典数据项", httpMethod = "GET")
    public R deleteDICTValue(String ids) {
        dataDictService.toDeleteDICTValue(ids);
        return R.ok();
    }

    @PostMapping(value = "queryDTValue")
    @ApiOperation(value = "通用根据DTTYPE获取具体的DTVALUE", notes = "", httpMethod = "POST")
    public R<List<DICTValue>> queryDTValue(@RequestBody DICTValue dictValue) {
        DICTValue value = new DICTValue();
        value.setDtCode(dictValue.getDtCode());
        return R.ok(dictService.findDICTValueList(value));
    }

}
