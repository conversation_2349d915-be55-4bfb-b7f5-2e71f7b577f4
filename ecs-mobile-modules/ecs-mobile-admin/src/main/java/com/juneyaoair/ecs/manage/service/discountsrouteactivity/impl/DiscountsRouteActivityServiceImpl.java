package com.juneyaoair.ecs.manage.service.discountsrouteactivity.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.juneyaoair.ecs.manage.dto.discount.ro.DiscountsRouteActivityRO;
import com.juneyaoair.ecs.manage.dto.discount.ro.ThreeTitleDataRo;
import com.juneyaoair.ecs.manage.dto.discount.ro.TwoTitleDataRo;
import com.juneyaoair.ecs.manage.dto.discount.vo.DiscountsRouteActivityVo;
import com.juneyaoair.ecs.manage.service.discountsrouteactivity.IDiscountsRouteActivityService;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteActivityPO;
import com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteTitlePO;
import com.juneyaoair.manage.b2c.mapper.DiscountsRouteActivityMapper;
import com.juneyaoair.manage.b2c.service.IDiscountRouteActivityServices;
import com.juneyaoair.manage.b2c.service.IDiscountsRouteTitleService;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName DiscountsRouteActivityServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/11/17 15:40
 * @Version 1.0
 */
@Service
@RefreshScope
@Slf4j
public class DiscountsRouteActivityServiceImpl implements IDiscountsRouteActivityService {

    @Autowired
    private IDiscountRouteActivityServices discountRouteActivityService;


    @Autowired
    private IDiscountsRouteTitleService discountsRouteTitleService;

    @Resource
    private DiscountsRouteActivityMapper discountsRouteActivityMapper;


    @Override
    public DiscountsRouteActivityVo getDetails(String id) {
        DiscountsRouteActivityVo discountsRouteActivityVo = new DiscountsRouteActivityVo();
        DiscountsRouteActivityPO selectOne = discountRouteActivityService.getById(id);
        if (selectOne != null) {
            BeanUtils.copyNotNullProperties(selectOne, discountsRouteActivityVo);
            List<TwoTitleDataRo> twoTitleDataRoList = new ArrayList<>();
            DiscountsRouteTitlePO discountsRouteTitlePO = new DiscountsRouteTitlePO();
            discountsRouteTitlePO.setRouteActivityId(selectOne.getId());
            discountsRouteTitlePO.setTitleLevel("2");
            //获取二级标题
            List<DiscountsRouteTitlePO> twoTitleList = discountsRouteTitleService.toGainAllRecords(discountsRouteTitlePO);
            if (CollectionUtil.isNotEmpty(twoTitleList)) {
                twoTitleList.forEach(twoTitlePO -> {
                    TwoTitleDataRo twoTitleDataRo = new TwoTitleDataRo();
                    twoTitleDataRo.setId(twoTitlePO.getId());
                    twoTitleDataRo.setName(twoTitlePO.getTitle());

                    DiscountsRouteTitlePO treeTitlePO = new DiscountsRouteTitlePO();
                    treeTitlePO.setRouteActivityId(selectOne.getId());
                    treeTitlePO.setTitleLevel("3");
                    treeTitlePO.setParentTitleId(twoTitleDataRo.getId());
                    //获取三级标题
                    List<DiscountsRouteTitlePO> treeTitleList = discountsRouteTitleService.toGainAllRecords(treeTitlePO);
                    if (CollectionUtil.isNotEmpty(treeTitleList)) {
                        List<ThreeTitleDataRo> threeTitleData = new ArrayList<>();
                        treeTitleList.forEach(treeTitle -> {
                            ThreeTitleDataRo threeTitleDataRo = new ThreeTitleDataRo();
                            threeTitleDataRo.setId(treeTitle.getId());
                            threeTitleDataRo.setParentTitleId(treeTitle.getParentTitleId());
                            threeTitleDataRo.setName(treeTitle.getTitle());
                            threeTitleDataRo.setDataSetName(treeTitle.getChildName());
                            threeTitleData.add(threeTitleDataRo);
                        });
                        twoTitleDataRo.setThreeTitleData(threeTitleData);
                    }

                    twoTitleDataRoList.add(twoTitleDataRo);
                });
                discountsRouteActivityVo.setTwoTitleDataRoList(twoTitleDataRoList);
            }
        }
        return discountsRouteActivityVo;
    }

    @Override
    public List<DiscountsRouteActivityPO> getList(String isValid, String status, String newDate) {
        LambdaQueryWrapper<DiscountsRouteActivityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(isValid), DiscountsRouteActivityPO::getIsValid, isValid);
        queryWrapper.eq(StringUtils.isNotEmpty(status), DiscountsRouteActivityPO::getIsValid, status);
        queryWrapper.le(StringUtils.isNotEmpty(newDate), DiscountsRouteActivityPO::getStartDate, newDate);
        queryWrapper.ge(StringUtils.isNotEmpty(newDate), DiscountsRouteActivityPO::getEndDate, newDate);
        return discountsRouteActivityMapper.selectList(queryWrapper);
    }

    @Override
    public void update(DiscountsRouteActivityRO discountsRouteActivityRO) {
        DiscountsRouteActivityPO discountsRouteActivityPO = new DiscountsRouteActivityPO();
        discountsRouteActivityPO.setUpdateTime(DateUtil.getDateStringAllDate(new Date()));
        discountsRouteActivityPO.setUpdateUser(SecurityUtils.getUsername());
        BeanUtils.copyProperties(discountsRouteActivityRO, discountsRouteActivityPO);
        discountsRouteActivityMapper.updateById(discountsRouteActivityPO);

        LambdaQueryWrapper<DiscountsRouteTitlePO> deleteMapper = new LambdaQueryWrapper<>();
        deleteMapper.eq(StringUtils.isNotEmpty(discountsRouteActivityRO.getId()), DiscountsRouteTitlePO::getRouteActivityId, discountsRouteActivityRO.getId());
        discountsRouteTitleService.remove(deleteMapper);

        if (CollectionUtil.isNotEmpty(discountsRouteActivityRO.getTwoTitleDataRoList())) {
            //二级标题
            discountsRouteActivityRO.getTwoTitleDataRoList().forEach(TwoTitleDataRo -> {
                        DiscountsRouteTitlePO discountsRouteTitlePO = new DiscountsRouteTitlePO();
                        discountsRouteTitlePO.setId(IdUtil.simpleUUID());
                        discountsRouteTitlePO.setTitleLevel("2");
                        discountsRouteTitlePO.setTitle(TwoTitleDataRo.getName());
                        discountsRouteTitlePO.setRouteActivityId(discountsRouteActivityPO.getId());
                        discountsRouteTitleService.save(discountsRouteTitlePO);
                        TwoTitleDataRo.getThreeTitleData().forEach(threeTitleDataRo -> {
                            if (StringUtils.isBlank(threeTitleDataRo.getName()) || StringUtils.isBlank(threeTitleDataRo.getDataSetName())) {
                                throw new HoServiceException("三级标题,数据组名称,都不能为空！");
                            }
                            DiscountsRouteTitlePO threeTitlePO = new DiscountsRouteTitlePO();
                            threeTitlePO.setId(IdUtil.simpleUUID());
                            threeTitlePO.setTitleLevel("3");
                            threeTitlePO.setChildName(threeTitleDataRo.getName());
                            threeTitlePO.setTitle(threeTitleDataRo.getName());
                            threeTitlePO.setChildName(threeTitleDataRo.getDataSetName());
                            threeTitlePO.setParentTitleId(discountsRouteTitlePO.getId());
                            threeTitlePO.setRouteActivityId(discountsRouteActivityPO.getId());
                            discountsRouteTitleService.save(threeTitlePO);
                        });
                    }
            );
        }
    }

    @Override
    public void updateValid(String id, String valid) {
        LambdaUpdateWrapper<DiscountsRouteActivityPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(DiscountsRouteActivityPO::getIsValid,valid).eq(DiscountsRouteActivityPO::getId,id);
        discountRouteActivityService.update(updateWrapper);
    }

    @Override
    public void updateStatus(String id, String status) {
        LambdaUpdateWrapper<DiscountsRouteActivityPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(DiscountsRouteActivityPO::getStatus,status).eq(DiscountsRouteActivityPO::getId,id);
        discountRouteActivityService.update(updateWrapper);
    }
}
