package com.juneyaoair.ecs.manage.controller.pet;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.juneyaoair.ecs.manage.controller.HoBaseController;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.dih.InventoryHistoryParam;
import com.juneyaoair.ecs.manage.dto.pet.PetCounterDto;
import com.juneyaoair.ecs.manage.dto.pet.PetFlightPlanDto;
import com.juneyaoair.ecs.manage.dto.pet.PetFlightPlanParam;
import com.juneyaoair.ecs.manage.enums.MessageTypeEnum;
import com.juneyaoair.ecs.manage.service.pet.PetBizService;
import com.juneyaoair.manage.b2c.entity.MessagePO;
import com.juneyaoair.manage.b2c.entity.pet.PetCounterPO;
import com.juneyaoair.manage.b2c.entity.pet.PetFlightPlanPO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/11 16:14
 */
@Api("宠物航班管理")
@RequestMapping(value = "/pet")
@RestController
public class PetController extends HoBaseController {
    @Autowired
    private PetBizService petBizService;

    @ApiOperation(value = "分页查询宠物柜台")
    @PostMapping(value = "pageList")
    public R<PageResult<PetCounterPO>> pageList(@RequestBody @Validated PetCounterDto petCounterDto){
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        List<PetCounterPO> list = petBizService.queryList(petCounterDto);
        return getPageData(list, pageDomain);
    }

    @ApiOperation(value = "新增宠物柜台")
    @PostMapping(value = "addPetCounter")
    public R addPetCounter(@RequestBody @Validated PetCounterDto petCounterDto){
        petBizService.addPetCounter(petCounterDto);
        return R.ok();
    }

    @ApiOperation(value = "查看宠物柜台")
    @PostMapping(value = "findPetCounter")
    public R<PetCounterPO> findPetCounter(@RequestBody @Validated PetCounterDto petCounterDto){
        return R.ok(petBizService.findPetCounter(petCounterDto));
    }

    @ApiOperation(value = "禁用/启用宠物柜台")
    @PostMapping(value = "disablePetCounter")
    public R disablePetCounter(@RequestBody @Validated PetCounterDto petCounterDto){
        petBizService.disablePetCounter(petCounterDto);
        return R.ok();
    }

    @ApiOperation(value = "编辑宠物柜台")
    @PostMapping(value = "editPetCounter")
    public R editPetCounter(@RequestBody @Validated PetCounterDto petCounterDto){
        petBizService.editPetCounter(petCounterDto);
        return R.ok();
    }

    @ApiOperation(value = "新增宠物航班计划")
    @PostMapping(value = "addPetFlightPlan")
    public R addPetFlightPlan(@RequestBody @Validated PetFlightPlanDto  petFlightPlanDto){
        petBizService.addPetFlightPlan(petFlightPlanDto);
        return R.ok();
    }

    @ApiOperation(value = "分页查询宠物航班计划")
    @PostMapping(value = "petFlightPlanPageList")
    public R<PageResult<PetFlightPlanPO>> petFlightPlanPageList(@RequestBody @Validated PetFlightPlanParam petFlightPlanParam){
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        List<PetFlightPlanPO> list = petBizService.queryPetFlightPlanList(petFlightPlanParam);
        return getPageData(list, pageDomain);
    }

    @ApiOperation(value = "查看航班计划")
    @PostMapping(value = "findPetFlightPlan")
    public R<PetFlightPlanPO> findPetFlightPlan(@RequestBody @Validated PetFlightPlanParam petFlightPlanParam){
        return R.ok(petBizService.findPetFlightPlan(petFlightPlanParam));
    }

    @ApiOperation(value = "禁用/启用宠物航班计划")
    @PostMapping(value = "switchPetFlightPlanStatus")
    public R switchPetFlightPlanStatus(@RequestBody @Validated PetFlightPlanParam petFlightPlanParam){
        petBizService.switchPetFlightPlanStatus(petFlightPlanParam);
        return R.ok();
    }

    @ApiOperation(value = "编辑宠物航班计划")
    @PostMapping(value = "editPetFlightPlan")
    public R editPetFlightPlan(@RequestBody @Validated PetFlightPlanDto petFlightPlanDto){
        petBizService.editPetFlightPlan(petFlightPlanDto);
        return R.ok();
    }
}
