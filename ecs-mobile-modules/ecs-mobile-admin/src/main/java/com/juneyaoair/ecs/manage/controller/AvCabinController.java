package com.juneyaoair.ecs.manage.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.ecs.http.bean.HttpResult;
import com.juneyaoair.ecs.http.service.HttpClientService;
import com.juneyaoair.ecs.manage.constant.ManageConstant;
import com.juneyaoair.ecs.manage.dto.avcabin.*;
import com.juneyaoair.ecs.manage.dto.avcabin.request.AvCabinParam;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.enums.ResultEnum;
import com.juneyaoair.ecs.manage.properties.ThirdAppUrlSet;
import com.juneyaoair.ecs.redis.service.ZeroRedisService;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/8 10:16
 */
@RequestMapping("/avcabin")
@RestController
@RequiredArgsConstructor
@Api(value = "AvCabinController", tags = "航班舱位AV缓存数据")
@Slf4j
public class AvCabinController extends HoBaseController {
    @Autowired
    private ZeroRedisService zeroRedisService;
    @Autowired
    private HttpClientService httpClientService;
    @Autowired
    private ThirdAppUrlSet thirdAppUrlSet;
    private final String[] CABIN_ARRAYS = {"J", "C", "D", "A", "R", "I", "Y", "B", "M", "U", "H", "Q", "V", "W", "S", "T", "Z"
            , "E", "K", "L", "N", "X", "G"};

    @PostMapping("/pageList")
    @ApiOperation(value = "航班舱位分页查询", notes = "航班舱位分页查询notes")
    public R<PageResult<AvCabinDto>> pageList(@RequestBody @Validated AvCabinParam avCabinParam) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        List<String> dateList = DateUtil.getDateByTimeRange(avCabinParam.getStartTime(), avCabinParam.getEndTime());
        String depCity = avCabinParam.getDepCity();
        String arrCity = avCabinParam.getArrCity();
        List<AvCabinDto> avCabinDtoList = new ArrayList<>();
        dateList.forEach(dateStr -> {
            String k = "TASK:IBE:" + depCity + arrCity + dateStr + ":AV";
            Map<String, String> map = zeroRedisService.getMapData(k);
            if (CollectionUtil.isNotEmpty(map)) {
                String listStr = map.get("AV");
                if (StringUtils.isNotEmpty(listStr)) {
                    AvIBEData avIBEData = (AvIBEData) JsonUtil.jsonToBean(listStr, new TypeToken<AvIBEData>() {
                    }.getType());
                    if (ResultEnum.S1001.getResultCode().equals(avIBEData.getResultCode())) {
                        if (CollectionUtils.isNotEmpty(avIBEData.getIBESegmentList())) {
                            avIBEData.getIBESegmentList().stream().forEach(ibeSegment -> {
                                if (ibeSegment == null || ibeSegment != null && !ibeSegment.getCarrier().matches("HO.*")) {
                                    return;
                                }
                                AvCabinDto avCabinDto = new AvCabinDto();
                                String[] cabinList = ibeSegment.getCabinList();
                                List<CabinData> cabinDataList = new ArrayList<>();
                                for (int i = 0; i < CABIN_ARRAYS.length; i++) {
                                    CabinData cabinData = new CabinData();
                                    cabinData.setCabinCode(CABIN_ARRAYS[i]);
                                    cabinData.setNum(cabinList);
                                    cabinDataList.add(cabinData);
                                }
                                avCabinDto.setCarrier(ibeSegment != null ? ibeSegment.getCarrier() : null);
                                avCabinDto.setFlightDate(dateStr);
                                avCabinDto.setCabinDateList(cabinDataList);
                                avCabinDtoList.add(avCabinDto);
                            });
                        }
                    }
                }
            }
        });
        //分页处理
        PageInfo<AvCabinDto> pageInfo = dealPageInfo(pageDomain.getPageNum(), pageDomain.getPageSize(), avCabinDtoList);
        return getPageData(pageInfo, pageDomain);
    }

    @PostMapping("/refresh")
    @ApiOperation(value = "刷新航班舱位数量", notes = "刷新航班舱位数量notes")
    public R refresh(@RequestBody @Validated AvCabinParam avCabinParam) {
        List<String> dateList = DateUtil.getDateByTimeRange(avCabinParam.getStartTime(), avCabinParam.getEndTime());
        if (CollectionUtil.isNotEmpty(dateList)) {
            AvInfoParam avInfoParam = new AvInfoParam();
            dateList.forEach(dateStr -> {
                avInfoParam.setFlightDate(dateStr);
                avInfoParam.setOrgCityCode(avCabinParam.getDepCity());
                avInfoParam.setDestCityCode(avCabinParam.getArrCity());
                HttpResult httpResult = httpClientService.doPostJson(avInfoParam, thirdAppUrlSet.getB2cJobUrl() + ManageConstant.GET_AV_INFO, null);
                if (httpResult.isResult()) {
                    AvInfoResult avInfoResult = JsonUtil.fromJson(httpResult.getResponse(), AvInfoResult.class);
                    log.info("AV执行结果:{}",avInfoResult);
                    /*if (!ResultEnum.S1001.getResultCode().equals(String.valueOf(avInfoResult.getStatus()))) {
                        throw new ServiceException(avInfoResult.getMessage());
                    }*/
                } else {
                    throw new ServiceException(httpResult.getResponse());
                }
            });
        }
        return R.ok();
    }
}
