package com.juneyaoair.ecs.manage.service.baseinfo.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.juneyaoair.ecs.manage.config.AdminConfig;
import com.juneyaoair.ecs.manage.dto.discount.ro.DiscountsRouteActivityRO;
import com.juneyaoair.ecs.manage.dto.discount.vo.ChildNameVo;
import com.juneyaoair.ecs.manage.dto.discount.vo.DiscountsRouteActivityVo;
import com.juneyaoair.ecs.manage.dto.discount.vo.DiscountsSelectVo;
import com.juneyaoair.ecs.manage.external.FlightBasicService;
import com.juneyaoair.ecs.manage.service.baseinfo.IDiscountsRouteService;
import com.juneyaoair.ecs.manage.service.discountsRoute.DiscountsRouteData;
import com.juneyaoair.ecs.manage.service.discountsrouteactivity.IDiscountsRouteActivityService;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.activity.ActivityChildInfoPO;
import com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteActivityPO;
import com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteTitlePO;
import com.juneyaoair.manage.b2c.mapper.DiscountsRouteActivityMapper;
import com.juneyaoair.manage.b2c.service.IActivityChildInfoService;
import com.juneyaoair.manage.b2c.service.IDiscountsRouteTitleService;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName DiscountsRouteServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/12/13 15:29
 * @Version 1.0
 */

@Service
@RefreshScope
@Slf4j
public class DiscountsRouteServiceImpl implements IDiscountsRouteService {
    @Autowired
    private IDiscountsRouteActivityService discountsRouteActivityService;

    @Autowired
    private DiscountsRouteData discountsRouteData;

    @Autowired
    private IDiscountsRouteTitleService discountsRouteTitleService;

    @Resource
    private DiscountsRouteActivityMapper discountsRouteActivityMapper;

    @Autowired
    private FlightBasicService flightBasicService;
    @Autowired
    private IActivityChildInfoService activityChildInfoService;



    @Resource
    private AdminConfig config;

    private final static String YES = "Y";


    @Override
    public boolean save(DiscountsRouteActivityRO discountsRouteActivityRO) {
        try {
            DiscountsRouteActivityPO discountsRouteActivityPO = new DiscountsRouteActivityPO();
            BeanUtils.copyProperties(discountsRouteActivityRO, discountsRouteActivityPO);
            discountsRouteActivityPO.setIsValid("Y");
            discountsRouteActivityPO.setStatus("N");
            discountsRouteActivityPO.setActivityUrl(config.getActivityUrl() + "?type=" + discountsRouteActivityPO.getActivityParam()
                    + "&titlestyle=4&title=" + discountsRouteActivityPO.getActivityName() + "&navRightItem=1");
            discountsRouteActivityPO.setCreateTime(DateUtil.getDateStringAllDate(new Date()));
            discountsRouteActivityPO.setCreateUser(SecurityUtils.getUsername());

            discountsRouteActivityPO.setId(IdUtil.simpleUUID());
            discountsRouteActivityMapper.insert(discountsRouteActivityPO);
            if (CollectionUtil.isNotEmpty(discountsRouteActivityRO.getTwoTitleDataRoList())) {
                //二级标题
                discountsRouteActivityRO.getTwoTitleDataRoList().forEach(TwoTitleDataRo -> {
                            DiscountsRouteTitlePO discountsRouteTitlePO = new DiscountsRouteTitlePO();
                            discountsRouteTitlePO.setId(IdUtil.simpleUUID());
                            discountsRouteTitlePO.setTitleLevel("2");
                            discountsRouteTitlePO.setTitle(TwoTitleDataRo.getName());
                            discountsRouteTitlePO.setRouteActivityId(discountsRouteActivityPO.getId());
                            discountsRouteTitleService.save(discountsRouteTitlePO);
                            TwoTitleDataRo.getThreeTitleData().forEach(threeTitleDataRo -> {
                                if (StringUtils.isAnyEmpty(threeTitleDataRo.getName(), threeTitleDataRo.getDataSetName())) {
                                    throw new HoServiceException("三级标题,数据组名称,都不能为空！");
                                }
                                DiscountsRouteTitlePO threeTitlePO = new DiscountsRouteTitlePO();
                                threeTitlePO.setId(IdUtil.simpleUUID());
                                threeTitlePO.setTitleLevel("3");
                                threeTitlePO.setChildName(threeTitleDataRo.getName());
                                threeTitlePO.setTitle(threeTitleDataRo.getName());
                                threeTitlePO.setChildName(threeTitleDataRo.getDataSetName());
                                threeTitlePO.setParentTitleId(discountsRouteTitlePO.getId());
                                threeTitlePO.setRouteActivityId(discountsRouteActivityPO.getId());
                                discountsRouteTitleService.save(threeTitlePO);
                            });
                        }
                );
            }
        } catch (Exception e) {
            log.error("记录新增出错，错误信息为:", e);
            throw new HoServiceException("新增出错");
        }
        return true;
    }

    @Override
    public List<DiscountsRouteActivityPO> toCatchList(DiscountsRouteActivityRO discountsRouteActivityRO) {
        LambdaQueryWrapper<DiscountsRouteActivityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(discountsRouteActivityRO.getActivityName()), DiscountsRouteActivityPO::getActivityName, discountsRouteActivityRO.getActivityName());
        queryWrapper.orderByDesc(DiscountsRouteActivityPO::getCreateTime);
        return discountsRouteActivityMapper.selectList(queryWrapper);
    }

    @Override
    public DiscountsRouteActivityVo getDetails(String id) {
        return discountsRouteActivityService.getDetails(id);
    }

    @Override
    public boolean toUpdate(DiscountsRouteActivityRO discountsRouteActivityRO) {
        try {
            discountsRouteActivityService.update(discountsRouteActivityRO);
            discountsRouteData.replaceRedis();
            discountsRouteData.removeCacheDiscountsRoute(discountsRouteActivityRO.getActivityParam());
        } catch (Exception e) {
            log.error("记录修改出错，错误信息为:", e);
            throw new HoServiceException("修改出错");
        }
        return true;
    }

    @Override
    public boolean toUpdateValid(String id, String valid) {
        try {
            discountsRouteActivityService.updateValid(id, valid);
            DiscountsRouteActivityVo discountsRouteActivityVo = discountsRouteActivityService.getDetails(id);
            discountsRouteData.replaceRedis();
            discountsRouteData.removeCacheDiscountsRoute(discountsRouteActivityVo.getActivityParam());
        } catch (Exception e) {
            log.error("活动有效状态修改失败，错误信息为:", e);
            throw new HoServiceException("活动有效状态修改失败");
        }
        return true;
    }

    @Override
    public boolean toUpdateStatus(String id, String status) {
        try {
            discountsRouteActivityService.updateStatus(id, status);
            DiscountsRouteActivityVo discountsRouteActivityVo = discountsRouteActivityService.getDetails(id);
            discountsRouteData.replaceRedis();
            discountsRouteData.removeCacheDiscountsRoute(discountsRouteActivityVo.getActivityParam());
            if (YES.equalsIgnoreCase(status)) {
                flightBasicService.refreshPriceCache();
            }
        } catch (Exception e) {
            log.error("活动发布状态修改失败，错误信息为:", e);
            throw new HoServiceException("活动发布状态修改失败");
        }
        return true;
    }

    @Override
    public List<DiscountsSelectVo> getSelectList() {
        List<DiscountsSelectVo> discountsSelectVos = new ArrayList<>();
        QueryWrapper<DiscountsRouteActivityPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("IS_VALID", YES);
        List<DiscountsRouteActivityPO> activityPOS = discountsRouteActivityMapper.selectList(queryWrapper);
        activityPOS.forEach(activity -> {
            DiscountsSelectVo discountsSelectVo = new DiscountsSelectVo();
            discountsSelectVo.setActivityName(activity.getActivityName());
            discountsSelectVo.setId(activity.getId());
            discountsSelectVos.add(discountsSelectVo);
        });
        return discountsSelectVos;
    }

    @Override
    public List<ChildNameVo> getChildNameList(String activityId, String dataId) {
        List<ChildNameVo> childNameVos = new ArrayList<>();
        LambdaQueryWrapper<DiscountsRouteTitlePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(activityId), DiscountsRouteTitlePO::getRouteActivityId, activityId);
        queryWrapper.eq(DiscountsRouteTitlePO::getTitleLevel, "3");
        List<DiscountsRouteTitlePO> discountsRouteTitlePOList = discountsRouteTitleService.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(discountsRouteTitlePOList)) {
            if (StringUtils.isNotEmpty(dataId)) {
                ActivityChildInfoPO activityChildInfo = new ActivityChildInfoPO();
                activityChildInfo.setId(dataId);
                activityChildInfo.setDataType("5");
                List<ActivityChildInfoPO> allChildRecords = activityChildInfoService.toGainAllChildRecords(activityChildInfo);
                if (CollectionUtils.isNotEmpty(allChildRecords)) {
                    ActivityChildInfoPO childInfoPO = allChildRecords.get(0);
                    ChildNameVo childNameVo = new ChildNameVo();
                    childNameVo.setChildName(childInfoPO.getDataName());
                    childNameVos.add(childNameVo);
                }
            } else {
                discountsRouteTitlePOList.forEach(discountsRouteTitlePO -> {
                    ActivityChildInfoPO childInfo = new ActivityChildInfoPO();
                    childInfo.setDataName(discountsRouteTitlePO.getChildName());
                    childInfo.setBaseinfoId(discountsRouteTitlePO.getRouteActivityId());
                    childInfo.setDataType("5");
                    List<ActivityChildInfoPO> childInfos = activityChildInfoService.toGainAllRecordsWithNonIds(childInfo);
                    if (CollectionUtil.isEmpty(childInfos)) {
                        ChildNameVo childNameVo = new ChildNameVo();
                        childNameVo.setChildName(discountsRouteTitlePO.getChildName());
                        childNameVos.add(childNameVo);
                    }
                });
            }
        }
        return childNameVos;
    }
}
