package com.juneyaoair.ecs.manage.service.news.impl;

import com.juneyaoair.ecs.manage.dto.news.request.NewsCategoryRequest;
import com.juneyaoair.ecs.manage.dto.news.request.NewsChannelRequest;
import com.juneyaoair.ecs.manage.dto.news.request.NewsRequest;
import com.juneyaoair.ecs.manage.dto.news.response.News;
import com.juneyaoair.ecs.manage.dto.news.response.NewsCategory;
import com.juneyaoair.ecs.manage.dto.news.response.NewsChannel;
import com.juneyaoair.ecs.manage.dto.news.response.NewsDetail;
import com.juneyaoair.ecs.manage.service.news.INewsService;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c2014.entity.Channel2014PO;
import com.juneyaoair.manage.b2c2014.entity.NewsCategoryPO;
import com.juneyaoair.manage.b2c2014.entity.NewsContextPO;
import com.juneyaoair.manage.b2c2014.entity.NewsPO;
import com.juneyaoair.manage.b2c2014.service.ChannelService2014;
import com.juneyaoair.manage.b2c2014.service.NewService;
import com.juneyaoair.manage.b2c2014.service.NewsCategoryService;
import com.juneyaoair.manage.b2c2014.service.NewsContextService;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName NewsServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/31 8:39
 * @Version 1.0
 */

@Service
@RefreshScope
@Slf4j
public class NewsServiceImpl implements INewsService {

    @Resource
    private NewService newService;

    @Resource
    private NewsCategoryService newsCategoryService;

    @Resource
    private NewsContextService newsContextService;

    @Resource
    private ChannelService2014 channelService;

    private static final String UN_DELETE_FLAG = "N";

    private static final String DELETED_FLAG = "Y";

    private static final String RELEASED_FLAG = "Y";

    private static final String UN_RELEASE_FLAG = "N";

    private static final String CHANNEL_CODE_B2C = "B2C";


    @Override
    public List<News> toCatchNewsList(NewsRequest newsRequest) {
        NewsPO newsPO = new NewsPO();
        BeanUtils.copyNotNullProperties(newsRequest, newsPO);
        newsPO.setDelflag(UN_DELETE_FLAG);
        List<NewsPO> allNews = newService.toCatchAllNews(newsPO);
        if (CollectionUtils.isEmpty(allNews)) {
            return null;
        }
        List<News> newsList = new ArrayList<>();
        allNews.forEach(el -> {
            if (StringUtils.isEmpty(el.getChannelCode())) {
                el.setChannelCode(CHANNEL_CODE_B2C);
            }
            News news = News.builder()
                    .newsId(el.getNewsId())
                    .newTitle(el.getNewTitle())
                    .newAuthor(el.getNewAuthor())
                    .releaseFlag(el.getReleaseFlag())
                    .releaseDate(el.getReleaseDate()).channelCode(el.getChannelCode())
                    .categoryName(el.getCategoryName()).build();
            newsList.add(news);

        });
        return newsList;
    }

    @Override
    public boolean toAddNewCategory(NewsCategoryRequest newsCategoryRequest) {
        //如果父栏目ID为0 默认是插入主栏目记录
        if (newsCategoryRequest.getParentCategory() == 0) {
            //需要检查子目录ID是否已经存在
            toCheckIfChildCategoryExist(newsCategoryRequest.getCategoryId());
        } else {
            //插入的是子栏目 1. 需要判断父栏目是否存在(欲插入的栏目其父ID应当是已经存在的某个栏目的子ID) 2. 需要检查子栏目是否已存在
            toCheckIfParentCategoryExist(newsCategoryRequest.getParentCategory());
            toCheckIfChildCategoryExist(newsCategoryRequest.getCategoryId());
        }
        return toSaveCategoryRecord(newsCategoryRequest);
    }

    @Override
    public boolean toUpdateNewCategory(NewsCategoryRequest newsCategoryRequest) {
        toCheckIfCategoryExist(newsCategoryRequest.getParentCategory(), newsCategoryRequest.getCategoryId());
        NewsCategoryPO newsCategoryPO = new NewsCategoryPO();
        BeanUtils.copyNotNullProperties(newsCategoryRequest, newsCategoryPO);
        newsCategoryPO.setEditDate(new Date());
        newsCategoryPO.setEditName(SecurityUtils.getUserId());
        return newsCategoryService.toUpdateCategory(newsCategoryPO) > 0;
    }

    @Override
    public boolean toDeleteNewCategory(NewsCategoryRequest newsCategoryRequest) {
        //1. 检查待删除的栏目是否存在
        toCheckIfCategoryExist(newsCategoryRequest.getParentCategory(), newsCategoryRequest.getCategoryId());
        //2. 检查名下是否有子级目录
        toCheckIfUnDeleteCategoryExist(newsCategoryRequest.getCategoryId());
        NewsCategoryPO delNewsCategoryPO = new NewsCategoryPO();
        delNewsCategoryPO.setDelflag(DELETED_FLAG);
        delNewsCategoryPO.setParentCategory(newsCategoryRequest.getParentCategory());
        delNewsCategoryPO.setCategoryId(newsCategoryRequest.getCategoryId());
        delNewsCategoryPO.setEditName(SecurityUtils.getUserId());
        delNewsCategoryPO.setEditDate(new Date());
        return newsCategoryService.toUpdateCategory(delNewsCategoryPO) > 0;
    }

    @Override
    public List<NewsCategory> toCatchNewsCategoryList(NewsCategory newsCategory) {
        NewsCategoryPO newsCategoryPO = new NewsCategoryPO();
        newsCategoryPO.setChannelCode(newsCategory.getChannelCode());
        newsCategoryPO.setDelflag(UN_DELETE_FLAG);
        List<NewsCategoryPO> allRecords = newsCategoryService.toSearchAllRecords(newsCategoryPO);
        if (CollectionUtils.isEmpty(allRecords)) {
            return null;
        }
        return toConvertNewsCategoryList(allRecords);
    }

    @Override
    public boolean toAddNewChannel(NewsChannelRequest newsChannelRequest) {
        List<Channel2014PO> channelPOS = channelService.list();
        if (CollectionUtils.isNotEmpty(channelPOS)) {
            Optional<Channel2014PO> channelPO = channelPOS.stream().filter(el -> StringUtils.isNotEmpty(el.getChannelCode()) && el.getChannelCode().equalsIgnoreCase(newsChannelRequest.getChannelCode())).findFirst();
            if (channelPO.isPresent()) {
                throw new HoServiceException("已存在此渠道号，无法添加");
            }
        }
        Channel2014PO channelPO = new Channel2014PO();
        BeanUtils.copyNotNullProperties(newsChannelRequest, channelPO);
        return channelService.save(channelPO);
    }

    @Override
    public boolean toUpdateNewChannel(NewsChannelRequest newsChannelRequest) {
        List<Channel2014PO> channelPOS = channelService.list();
        if (CollectionUtils.isEmpty(channelPOS)) {
            throw new HoServiceException("未找到可供修改的记录");
        }
        Optional<Channel2014PO> channelPO = channelPOS.stream().filter(el -> StringUtils.isNotEmpty(el.getChannelCode()) && el.getChannelCode().equalsIgnoreCase(newsChannelRequest.getChannelCode())).findFirst();
        if (!channelPO.isPresent()) {
            throw new HoServiceException("未找到可供修改的记录");
        }
        Channel2014PO updateChannelPO = new Channel2014PO();
        BeanUtils.copyNotNullProperties(newsChannelRequest, updateChannelPO);
        return channelService.toUpdateChannel(updateChannelPO) > 0;
    }

    @Override
    public boolean toDeleteNewChannel(NewsChannelRequest newsChannelRequest) {
        List<Channel2014PO> channelPOS = channelService.list();
        if (CollectionUtils.isEmpty(channelPOS)) {
            throw new HoServiceException("未找到可供删除的记录");
        }
        Optional<Channel2014PO> channelPO = channelPOS.stream().filter(el -> StringUtils.isNotEmpty(el.getChannelCode()) && el.getChannelCode().equalsIgnoreCase(newsChannelRequest.getChannelCode())).findFirst();
        if (!channelPO.isPresent()) {
            throw new HoServiceException("未找到可供删除的记录");
        }
        Channel2014PO updateChannelPO = new Channel2014PO();
        updateChannelPO.setChannelCode(newsChannelRequest.getChannelCode());
        updateChannelPO.setDelflag(DELETED_FLAG);
        return channelService.toUpdateChannel(updateChannelPO) > 0;
    }

    @Override
    public List<NewsChannel> toCatchNewChannelList(NewsChannelRequest newsChannelRequest) {
        List<Channel2014PO> channelPOS = channelService.list();
        if (CollectionUtils.isEmpty(channelPOS)) {
            return null;
        }
        List<Channel2014PO> undeletedChannels = channelPOS.stream().filter(el -> StringUtils.isEmpty(el.getDelflag()) || (StringUtils.isNotEmpty(el.getDelflag()) && el.getDelflag().equals(UN_DELETE_FLAG))).collect(Collectors.toList());
        List<NewsChannel> response = new ArrayList<>();
        for (Channel2014PO channelPO : undeletedChannels
        ) {
            NewsChannel newsChannel = new NewsChannel();
            BeanUtils.copyNotNullProperties(channelPO, newsChannel);
            response.add(newsChannel);
        }
        return response;
    }

    @Override
    public NewsDetail toCatchNewsDetail(NewsRequest newsRequest) {
        NewsPO newsPO = new NewsPO();
        newsPO.setNewsId(newsRequest.getNewsId());
        return newService.toCatchNewsDetail(newsPO);
    }

    @Override
    public Boolean toDeleteNews(NewsRequest newsRequest) {
        if (newsRequest.getNewsId() <= 0) {
            throw new HoServiceException("新闻ID不合法");
        }
        NewsPO newsPO = new NewsPO();
        newsPO.setNewsId(newsRequest.getNewsId());
        newsPO.setDelflag(DELETED_FLAG);
        if (newService.toUpdateNews(newsPO) <= 0) {
            throw new HoServiceException("删除失败");
        }
        NewsContextPO newsContextPO = new NewsContextPO();
        newsContextPO.setNewsId(newsRequest.getNewsId());
        newsContextPO.setDelflag(DELETED_FLAG);
        return newsContextService.toUpdateNewsContext(newsContextPO) > 0;
    }

    @Override
    public Boolean toUpdateNewsReleaseStatus(NewsRequest newsRequest) {
        if (newsRequest.getNewsId() <= 0) {
            throw new HoServiceException("新闻ID不合法");
        }
        NewsPO newsPO = new NewsPO();
        newsPO.setNewsId(newsRequest.getNewsId());
        newsPO.setReleaseFlag(newsRequest.getReleaseFlag());
        newsPO.setEditId(SecurityUtils.getUserId());
        newsPO.setEditDate(new Date());
        //如果发布新闻 需要修改发布时间为当前时间
        if (StringUtils.isNotEmpty(newsRequest.getReleaseFlag()) && newsRequest.getReleaseFlag().equals(RELEASED_FLAG)) {
            newsPO.setReleaseDate(new Date());
        }
        return newService.toUpdateNews(newsPO) > 0;
    }

    @Override
    public Boolean toAddNews(NewsRequest newsRequest) {
        //检查新闻ID是否存在
        NewsPO newsPO = new NewsPO();
        newsPO.setNewsId(newsRequest.getNewsId());
        List<NewsPO> allNews = newService.toCatchAllNews(newsPO);
        if (CollectionUtils.isNotEmpty(allNews)) {
            throw new HoServiceException("ID已存在，请更换");
        }

        NewsContextPO newsContextPO = new NewsContextPO();
        newsContextPO.setNewsId(newsRequest.getNewsId());
        List<NewsContextPO> allRecords = newsContextService.toCatchAllRecords(newsContextPO);
        if (CollectionUtils.isNotEmpty(allRecords)) {
            throw new HoServiceException("ID已存在，请更换");
        }

        NewsPO insertPO = new NewsPO();
        BeanUtils.copyNotNullProperties(newsRequest, insertPO);
        insertPO.setNewAuthor(SecurityUtils.getUsername());
        insertPO.setReleaseDate(new Date());
        insertPO.setInputId(SecurityUtils.getUserId());
        insertPO.setInputDate(new Date());
        insertPO.setEditDate(new Date());
        insertPO.setDelflag(UN_DELETE_FLAG);
        if (!newService.save(insertPO)) {
            throw new HoServiceException("新增失败");
        }
        //向context表中插入正文记录
        NewsContextPO insertNewsContextPO = new NewsContextPO();
        insertNewsContextPO.setNewsId(newsRequest.getNewsId());
        insertNewsContextPO.setNewsContext(newsRequest.getNewsContext());
        insertNewsContextPO.setDelflag(UN_DELETE_FLAG);
        return newsContextService.save(insertNewsContextPO);
    }

    @Override
    public Boolean toUpdateNews(NewsRequest newsRequest) {
        if (newsRequest.getNewsId() <= 0) {
            throw new HoServiceException("ID不合法");
        }
        NewsPO newsPO = new NewsPO();
        newsPO.setNewsId(newsRequest.getNewsId());
        List<NewsPO> allNews = newService.toCatchAllNews(newsPO);
        NewsContextPO newsContextPO = new NewsContextPO();
        newsContextPO.setNewsId(newsRequest.getNewsId());
        List<NewsContextPO> allRecords = newsContextService.toCatchAllRecords(newsContextPO);
        if (CollectionUtils.isEmpty(allNews) || CollectionUtils.isEmpty(allRecords)) {
            throw new HoServiceException("未找到可供更新的记录");
        }
        //1. 更新主新闻表
        NewsPO updateNewsPO = new NewsPO();
        BeanUtils.copyNotNullProperties(newsRequest, updateNewsPO);
        updateNewsPO.setNewsId(newsRequest.getNewsId());
        updateNewsPO.setEditId(SecurityUtils.getUserId());
        updateNewsPO.setEditDate(new Date());
        updateNewsPO.setReleaseFlag(UN_RELEASE_FLAG);
        if (newService.toUpdateNews(updateNewsPO) <= 0) {
            throw new HoServiceException("更新失败");
        }

        //2. 更新新闻内容表
        NewsContextPO updateNewsContextPO = new NewsContextPO();
        updateNewsContextPO.setNewsId(newsRequest.getNewsId());
        updateNewsContextPO.setNewsContext(newsRequest.getNewsContext());
        updateNewsContextPO.setDelflag(UN_DELETE_FLAG);
        return newsContextService.toUpdateNewsContext(updateNewsContextPO) > 0;
    }

    /**
     * @param allRecords
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.news.response.NewsCategory>
     * <AUTHOR>
     * @Description 转换返回体 以树形结构返回
     * @Date 14:03 2024/3/12
     **/

    private List<NewsCategory> toConvertNewsCategoryList(List<NewsCategoryPO> allRecords) {
        if (CollectionUtils.isEmpty(allRecords)) {
            return null;
        }
        return toCatchAllNewsCategory(allRecords);
    }

    /**
     * @param allRecords
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.news.response.NewsCategory>
     * <AUTHOR>
     * @Description 递归获取全部栏目列表
     * @Date 14:31 2024/3/12
     **/
    private List<NewsCategory> toCatchAllNewsCategory(List<NewsCategoryPO> allRecords) {
        List<NewsCategory> newsCategoryList = new ArrayList<>();
        for (NewsCategoryPO categoryPO : allRecords
        ) {
            NewsCategory newsCategory = new NewsCategory();
            BeanUtils.copyNotNullProperties(categoryPO, newsCategory);
            newsCategoryList.add(newsCategory);
        }
        // 1、返回的结果集
        List<NewsCategory> tree = new ArrayList<>();
        // 2、获取到最外层的栏目信息
        List<NewsCategory> parentNewsCategoryList = newsCategoryList.stream().filter(newsCategory -> Objects.equals(newsCategory.getParentCategory(), 0)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(parentNewsCategoryList)) {
            parentNewsCategoryList.sort(Comparator.comparing(NewsCategory::getCategoryId));
            // 3、取出栏目信息
            parentNewsCategoryList.forEach(el -> {
                // 4、放入集合中
                tree.add(el);
                // 5、添加子节点
                addChildCategory(el, newsCategoryList);
            });

        }
        return tree;
    }

    /**
     * @param sysNewCategory
     * @param all
     * @return void
     * <AUTHOR>
     * @Description 递归处理子栏目信息
     * @Date 15:27 2024/3/19
     **/
    private void addChildCategory(NewsCategory sysNewCategory, List<NewsCategory> all) {
        // 1、拿到子栏目列表
        List<NewsCategory> tempList = all.stream()
                .filter(dept -> Objects.equals(sysNewCategory.getCategoryId(), dept.getParentCategory()))
                .collect(Collectors.toList());
        sysNewCategory.setChildren(tempList);
        if (CollectionUtils.isNotEmpty(tempList)) {
            tempList.sort(Comparator.comparing(NewsCategory::getCategoryId));
            tempList.forEach(category -> {
                //2、添加子节点
                addChildCategory(category, all);
            });
        }

    }

    /**
     * @param categoryId
     * @return void
     * <AUTHOR>
     * @Description 检查某栏目下是否还有子栏目
     * @Date 13:07 2024/3/12
     **/
    private void toCheckIfUnDeleteCategoryExist(Integer categoryId) {
        NewsCategoryPO newsInsertCategoryPO = new NewsCategoryPO();
        newsInsertCategoryPO.setParentCategory(categoryId);
        newsInsertCategoryPO.setDelflag(UN_DELETE_FLAG);
        if (CollectionUtils.isNotEmpty(newsCategoryService.toSearchAllRecords(newsInsertCategoryPO))) {
            throw new HoServiceException("存在下级栏目，无法删除");
        }
    }

    /**
     * @param newsCategoryRequest
     * @return boolean
     * <AUTHOR>
     * @Description 新增栏目
     * @Date 12:24 2024/3/12
     **/
    private boolean toSaveCategoryRecord(NewsCategoryRequest newsCategoryRequest) {
        NewsCategoryPO newsInsertCategoryPO = new NewsCategoryPO();
        BeanUtils.copyNotNullProperties(newsCategoryRequest, newsInsertCategoryPO);
        newsInsertCategoryPO.setEditDate(new Date());
        newsInsertCategoryPO.setEditName(SecurityUtils.getUserId());
        return newsCategoryService.save(newsInsertCategoryPO);
    }

    /**
     * @param categoryId
     * @return void
     * <AUTHOR>
     * @Description 检查子栏目是否存在
     * @Date 12:18 2024/3/12
     **/
    private void toCheckIfChildCategoryExist(Integer categoryId) {
        NewsCategoryPO newsCategoryPO = new NewsCategoryPO();
        newsCategoryPO.setCategoryId(categoryId);
        List<NewsCategoryPO> allRecords = newsCategoryService.toSearchAllRecords(newsCategoryPO);
        if (CollectionUtils.isNotEmpty(allRecords)) {
            throw new HoServiceException("子栏目ID已存在，请重新输入");
        }
    }

    /**
     * @param parentCategoryId
     * @return void
     * <AUTHOR>
     * @Description 检查父栏目是否存在
     * @Date 12:18 2024/3/12
     **/
    private void toCheckIfParentCategoryExist(Integer parentCategoryId) {
        NewsCategoryPO newsCategoryPO = new NewsCategoryPO();
        newsCategoryPO.setCategoryId(parentCategoryId);
        List<NewsCategoryPO> allRecords = newsCategoryService.toSearchAllRecords(newsCategoryPO);
        if (CollectionUtils.isEmpty(allRecords)) {
            throw new HoServiceException("主栏目不存在，请检查");
        }
    }

    /**
     * @param parentCategoryId
     * @param categoryId
     * @return void
     * <AUTHOR>
     * @Description 根据子栏目ID及父栏目ID判断记录是否存在
     * @Date 12:55 2024/3/12
     **/
    private void toCheckIfCategoryExist(Integer parentCategoryId, Integer categoryId) {
        NewsCategoryPO newsCategoryPO = new NewsCategoryPO();
        newsCategoryPO.setCategoryId(categoryId);
        newsCategoryPO.setParentCategory(parentCategoryId);
        List<NewsCategoryPO> allRecords = newsCategoryService.toSearchAllRecords(newsCategoryPO);
        if (CollectionUtils.isEmpty(allRecords)) {
            throw new HoServiceException("记录不存在，请检查");
        }
    }
}
