package com.juneyaoair.ecs.manage.service.discountsrouteactivity;

import com.juneyaoair.ecs.manage.dto.discount.ro.DiscountsRouteActivityRO;
import com.juneyaoair.ecs.manage.dto.discount.vo.DiscountsRouteActivityVo;
import com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteActivityPO;

import java.util.List;

/**
 * @ClassName IDiscountsRouteActivityService
 * @Description
 * <AUTHOR>
 * @Date 2023/11/17 15:40
 * @Version 1.0
 */
public interface IDiscountsRouteActivityService {

    DiscountsRouteActivityVo getDetails(String id);

    List<DiscountsRouteActivityPO> getList(String isValid, String status, String newDate);

    void update(DiscountsRouteActivityRO discountsRouteActivityRO);

    void  updateValid(String id,String valid);

    void  updateStatus(String id,String status);

}
