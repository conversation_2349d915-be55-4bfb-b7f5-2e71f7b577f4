package com.juneyaoair.ecs.manage.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.news.request.NewsCategoryRequest;
import com.juneyaoair.ecs.manage.dto.news.request.NewsChannelRequest;
import com.juneyaoair.ecs.manage.dto.news.request.NewsRequest;
import com.juneyaoair.ecs.manage.dto.news.response.News;
import com.juneyaoair.ecs.manage.dto.news.response.NewsCategory;
import com.juneyaoair.ecs.manage.dto.news.response.NewsChannel;
import com.juneyaoair.ecs.manage.dto.news.response.NewsDetail;
import com.juneyaoair.ecs.manage.service.news.INewsService;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName NewsController
 * @Description 新闻相关服务
 * <AUTHOR>
 * @Date 2024/1/31 8:36
 * @Version 1.0
 */

@RequestMapping("news")
@RestController
@RequiredArgsConstructor
@Api(value = "NewsController", tags = "新闻配置API")
@Slf4j
public class NewsController extends HoBaseController{

    @Autowired
    private INewsService newsService;

    //渠道号配置相关

    @PostMapping(value = "toCatchNewChannelList")
    @ApiOperation(value = "获取渠道号列表信息", httpMethod = "POST")
    public R<List<NewsChannel>> toCatchNewChannelList(@RequestBody NewsChannelRequest newsChannelRequest) {
        return R.ok(newsService.toCatchNewChannelList(newsChannelRequest));
    }

    @PostMapping(value = "toAddNewChannel")
    @ApiOperation(value = "新增渠道号", httpMethod = "POST")
    public R<Boolean> toAddNewChannel(@RequestBody NewsChannelRequest newsChannelRequest) {
        return R.ok(newsService.toAddNewChannel(newsChannelRequest));
    }

    @PostMapping(value = "toUpdateNewChannel")
    @ApiOperation(value = "修改渠道号", httpMethod = "POST")
    public R<Boolean> toUpdateNewChannel(@RequestBody NewsChannelRequest newsChannelRequest) {
        return R.ok(newsService.toUpdateNewChannel(newsChannelRequest));
    }

    @PostMapping(value = "toDeleteNewChannel")
    @ApiOperation(value = "删除渠道号", httpMethod = "POST")
    public R<Boolean> toDeleteNewChannel(@RequestBody NewsChannelRequest newsChannelRequest) {
        return R.ok(newsService.toDeleteNewChannel(newsChannelRequest));
    }

    //2. 栏目配置相关

    @PostMapping(value = "toCatchNewCategoryList")
    @ApiOperation(value = "查询栏目列表", notes = "树形结构返回", httpMethod = "POST")
    public R<List<NewsCategory>> toDeleteNewCategory(@RequestBody NewsCategory newsCategory) {
        return R.ok(newsService.toCatchNewsCategoryList(newsCategory));
    }

    @PostMapping(value = "toAddNewCategory")
    @ApiOperation(value = "新增栏目", httpMethod = "POST")
    public R<Boolean> toAddNewCategory(@RequestBody @Validated NewsCategoryRequest newsCategoryRequest) {
        return R.ok(newsService.toAddNewCategory(newsCategoryRequest));
    }

    @PostMapping(value = "toUpdateNewCategory")
    @ApiOperation(value = "修改栏目", httpMethod = "POST")
    public R<Boolean> toUpdateNewCategory(@RequestBody @Validated NewsCategoryRequest newsCategoryRequest) {
        return R.ok(newsService.toUpdateNewCategory(newsCategoryRequest));
    }

    @PostMapping(value = "toDeleteNewCategory")
    @ApiOperation(value = "删除栏目", httpMethod = "POST")
    public R<Boolean> toDeleteNewCategory(@RequestBody @Validated NewsCategoryRequest newsCategoryRequest) {
        return R.ok(newsService.toDeleteNewCategory(newsCategoryRequest));
    }

    //3. 新闻页相关

    @PostMapping(value = "toCatchNewsList")
    @ApiOperation(value = "新闻列表页", httpMethod = "POST")
    public R<PageResult<News>> toCatchNewsList(@RequestBody NewsRequest newsRequest) {
        try {
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(newsRequest));
            PageDomain pageDomain = TableSupport.buildPageRequest();
            startPage(pageDomain);
            Page<News> localPage = PageHelper.getLocalPage();
            List<News> newsList = newsService.toCatchNewsList(newsRequest);
            R<PageResult<News>> pageData = getPageData(newsList, localPage);
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(pageData));
            return pageData;
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            Context.setContext(null);
        }
        return R.fail();
    }

    @PostMapping(value = "toCatchNewsDetail")
    @ApiOperation(value = "获取新闻详情", httpMethod = "POST")
    public R<NewsDetail> toCatchNewsDetail(@RequestBody NewsRequest newsRequest) {
        return R.ok(newsService.toCatchNewsDetail(newsRequest));
    }

    @PostMapping(value = "toAddNews")
    @ApiOperation(value = "新增新闻", httpMethod = "POST")
    public R<Boolean> toAddNews(@RequestBody NewsRequest newsRequest) {
        return R.ok(newsService.toAddNews(newsRequest));
    }

    @PostMapping(value = "toUpdateNews")
    @ApiOperation(value = "修改新闻", httpMethod = "POST")
    public R<Boolean> toUpdateNews(@RequestBody NewsRequest newsRequest) {
        return R.ok(newsService.toUpdateNews(newsRequest));
    }

    @PostMapping(value = "toDeleteNews")
    @ApiOperation(value = "删除新闻", notes = "同步删除新闻正文", httpMethod = "POST")
    public R<Boolean> toDeleteNews(@RequestBody NewsRequest newsRequest) {
        return R.ok(newsService.toDeleteNews(newsRequest));
    }

    @PostMapping(value = "toUpdateNewsReleaseStatus")
    @ApiOperation(value = "修改新闻发布状态", httpMethod = "POST")
    public R<Boolean> toReleaseNews(@RequestBody @Validated NewsRequest newsRequest) {
        return R.ok(newsService.toUpdateNewsReleaseStatus(newsRequest));
    }

}
