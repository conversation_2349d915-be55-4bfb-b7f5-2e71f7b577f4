package com.juneyaoair.ecs.manage.service.flightinfo;

import com.juneyaoair.ecs.manage.dto.cfc.dto.AddOrUpdateCommonFlyCityRequestDTO;
import com.juneyaoair.ecs.manage.dto.cfc.dto.CommonFlyCityDTO;
import com.juneyaoair.ecs.manage.dto.cfc.dto.CommonFlyCityRequestDTO;

import java.util.List;

public interface CommonFlyCityService {
    List<CommonFlyCityDTO> list(CommonFlyCityRequestDTO request);
    
    void add(AddOrUpdateCommonFlyCityRequestDTO request);
    
    void update(AddOrUpdateCommonFlyCityRequestDTO request);
    
    void updateStatus(String cfcId, String status);

    void delete(String cfcId);
}
