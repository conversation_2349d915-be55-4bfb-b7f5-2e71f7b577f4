package com.juneyaoair.ecs;

import com.juneyaoair.common.security.annotation.EnableManageCustomConfig;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;
import com.ruoyi.common.swagger.annotation.EnableCustomSwagger2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 *
 * <AUTHOR>
 * @date 2023/4/10  16:39
 */
@EnableManageCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@EnableDiscoveryClient
@EnableScheduling
@EnableTransactionManagement
@EnableFeignClients(basePackages = "com.juneyaoair")
@SpringBootApplication(scanBasePackages = {"com.juneyaoair","com.ruoyi"})
@Slf4j
public class ManageAdminApplication {
    public static void main(String[] args) {
        SpringApplication.run(ManageAdminApplication.class, args);
        log.info("(♥◠‿◠)ﾉﾞ  ManageAdminApplication模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                " .-------.       ____     __        \n" +
                " |  _ _   \\      \\   \\   /  /    \n" +
                " | ( ' )  |       \\  _. /  '       \n" +
                " |(_ o _) /        _( )_ .'         \n" +
                " | (_,_).' __  ___(_ o _)'          \n" +
                " |  |\\ \\  |  ||   |(_,_)'         \n" +
                " |  | \\ `'   /|   `-'  /           \n" +
                " |  |  \\    /  \\      /           \n" +
                " ''-'   `'-'    `-..-'              ");
    }
}
