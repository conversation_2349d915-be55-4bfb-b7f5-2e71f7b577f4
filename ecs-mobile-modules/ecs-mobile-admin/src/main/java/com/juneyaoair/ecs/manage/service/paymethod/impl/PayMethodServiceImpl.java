package com.juneyaoair.ecs.manage.service.paymethod.impl;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.juneyaoair.ecs.http.bean.HttpResult;
import com.juneyaoair.ecs.http.service.HttpClientService;
import com.juneyaoair.ecs.manage.constant.ManageConstant;
import com.juneyaoair.ecs.manage.dto.activity.request.paymethod.PayMethodActivityDTO;
import com.juneyaoair.ecs.manage.properties.ThirdAppUrlSet;
import com.juneyaoair.ecs.utils.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.juneyaoair.ecs.manage.dto.activity.request.paymethod.PayMethodDTO;
import com.juneyaoair.ecs.manage.dto.activity.request.paymethod.PayMethodPageReq;
import com.juneyaoair.ecs.manage.dto.base.PageDataResponse;
import com.juneyaoair.ecs.manage.service.paymethod.PayMethodService;

import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.PayMethodPO;
import com.juneyaoair.manage.b2c.entity.PayMethodRoutePO;
import com.juneyaoair.manage.b2c.entity.PayMethodSearchPO;
import com.juneyaoair.manage.b2c.entity.activity.PayMethodActivityPO;
import com.juneyaoair.manage.b2c.mapper.PayMethodActivityMapper;
import com.juneyaoair.manage.b2c.mapper.PayMethodMapper;

import com.juneyaoair.manage.b2c.mapper.PayMethodRouteMapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName PaymethodServiceImpl
 * @Description 支付方式配置相关API
 * <AUTHOR>
 * @Date 2025/7/31 15:47
 * @Version 1.0
 */
@Service
@Slf4j
public class PayMethodServiceImpl implements PayMethodService {

    @Autowired
    PayMethodMapper payMethodMapper;

    @Autowired
    PayMethodActivityMapper payMethodActivityMapper;

    @Autowired
    PayMethodRouteMapper payMethodRouteMapper;

    @Autowired
    private HttpClientService httpClientService;

    @Autowired
    private ThirdAppUrlSet thirdAppUrlSet;

    /**
     * 支付方式配置列表分页查询
     * @param param
     * @return
     */
    @Override
    public PageDataResponse payMethodListPageQuery(PayMethodPageReq param) {

        PageDataResponse pageDataResponse = new PageDataResponse();

        List<PayMethodSearchPO> payMethodSearchPOS = payMethodMapper.payMethodListPageQuery();
        List<PayMethodActivityPO> payMethodActivityPOS = payMethodActivityMapper.payMethodActivityQuery();
        for (PayMethodSearchPO payMethodSearchPO : payMethodSearchPOS) {
            if (payMethodActivityPOS!=null) {
                List<Object> collect = payMethodActivityPOS.stream()
                        .filter(o -> StrUtil.equals(o.getPayMethodId(), payMethodSearchPO.getId()))
                        .collect(Collectors.toList());
                String jsonString = JSONObject.toJSONString(collect);
                List<PayMethodActivityPO> PayMethodActivityPOS2 = JSONObject.parseArray(jsonString, PayMethodActivityPO.class);
                payMethodSearchPO.setPayMethodActivityPOS(PayMethodActivityPOS2);
            }
        }

        if (payMethodSearchPOS != null) {

            Stream<PayMethodSearchPO> stream = payMethodSearchPOS.stream();

            if (StringUtils.isNotBlank(param.getChannel())) {
                stream = stream.filter(p -> StringUtils.equals(p.getChannel(), param.getChannel()));
            }
            if (StringUtils.isNotBlank(param.getPayMethodCode())) {
                stream=stream.filter(p -> StringUtils.equals(p.getPayMethodCode(), param.getPayMethodCode()));
            }
            if (StringUtils.isNotBlank(param.getPayProductType())) {
                stream=stream.filter(p -> StringUtils.equals(p.getPayProductType(), param.getPayProductType()));
            }
            if (StringUtils.isNotBlank(param.getPosition())) {
                stream=stream.filter(p -> StringUtils.equals(p.getPosition(), param.getPosition()));
            }

            if (StringUtils.isNotBlank(param.getMerchantPayment())) {
                stream=stream.filter(p -> StringUtils.equals(p.getMerchantPayment(), param.getMerchantPayment()));
            }
            if (StringUtils.isNotBlank(param.getActivityId())) {
                stream=stream.filter(p -> {
                    List<PayMethodActivityPO> payMethodActivity = p.getPayMethodActivityPOS();
                    if (payMethodActivity==null){
                        return false;
                    }
                    for (PayMethodActivityPO o : payMethodActivity) {
                        if (StringUtils.contains(param.getActivityId(), o.getId())) {
                            return true;
                        }
                    }
                    return false;
                });
            }

            if (StringUtils.isNotBlank(param.getPromotionDescription())) {
                stream = stream.filter(o -> {
                    List<PayMethodActivityPO> payMethodActivity = o.getPayMethodActivityPOS();
                    for (PayMethodActivityPO payMethodActivityPO : payMethodActivity) {
                        if (StringUtils.contains(payMethodActivityPO.getPromotionDescription(),param.getPromotionDescription())) {
                            return true;
                        }
                    }
                    return false;
                });
            }

            if (StringUtils.isNotBlank(param.getStartTime())&&StringUtils.isNotBlank(param.getEndTime())) {
                stream = stream.filter(o -> {
                    List<PayMethodActivityPO> payMethodActivity = o.getPayMethodActivityPOS();
                    for (PayMethodActivityPO payMethodActivityPO : payMethodActivity) {
                        Date paramEndDate = cn.hutool.core.date.DateUtil.parse(param.getEndTime(), DatePattern.NORM_DATETIME_PATTERN);
                        Date paramStartDate = cn.hutool.core.date.DateUtil.parse(param.getStartTime(), DatePattern.NORM_DATETIME_PATTERN);
                        if (paramStartDate.before(payMethodActivityPO.getStartTime())&&
                                paramEndDate.after(payMethodActivityPO.getStartTime())){
                            return true;
                        }
                    }
                    return false;
                });
            }else if (StringUtils.isNotBlank(param.getStartTime())){
                stream = stream.filter(o -> {
                    List<PayMethodActivityPO> payMethodActivity = o.getPayMethodActivityPOS();
                    for (PayMethodActivityPO tPayMethodActivityDTO : payMethodActivity) {
                        Date paramStartDate = cn.hutool.core.date.DateUtil.parse(param.getStartTime(), DatePattern.NORM_DATETIME_PATTERN);
                        if (paramStartDate.before(tPayMethodActivityDTO.getStartTime()) ){
                            return true;
                        }
                    }
                    return false;
                });
            }else if (StringUtils.isNotBlank(param.getEndTime())){
                stream = stream.filter(o -> {
                    List<PayMethodActivityPO> payMethodActivity = o.getPayMethodActivityPOS();
                    for (PayMethodActivityPO payMethodActivityPO : payMethodActivity) {
                        Date paramStartDate = cn.hutool.core.date.DateUtil.parse(param.getEndTime(), DatePattern.NORM_DATETIME_PATTERN);
                        if (paramStartDate.after(payMethodActivityPO.getEndTime()) ){
                            return true;
                        }
                    }
                    return false;
                });
            }
            payMethodSearchPOS = stream.collect(Collectors.toList());
        }

        long total = payMethodSearchPOS.size();
        List<PayMethodSearchPO> result = manualPagination(payMethodSearchPOS, param.getPageNum(), param.getPageSize());
        pageDataResponse.setTotal(total);
        pageDataResponse.setData(result);
        return pageDataResponse;
    }

    @Override
    public PayMethodPO getPayMethodById(String id) {
        if(StringUtils.isEmpty(id)) {
            throw new HoServiceException("缺少指定的目标ID");
        }
        try{
            PayMethodPO payMethodPO = payMethodMapper.getpayMethodById(id);
            return payMethodPO;
        }catch (Exception e){
            log.error("查询资源信息发生错误:{}", e.getMessage());
            throw new HoServiceException("查询资源信息发生错误",e.getMessage());
        }
    }

    @Override
    public PayMethodActivityDTO getPayMethodActivityById(String id) {
        if(StringUtils.isEmpty(id)) {
            throw new HoServiceException("缺少指定的目标ID");
        }

        PayMethodActivityPO payMethodActivityPO = payMethodActivityMapper.getpayMethodActivityById(id);

        PayMethodPO payMethodPO = payMethodMapper.getpayMethodById(payMethodActivityPO.getPayMethodId());

        PayMethodActivityDTO  payMethodActivityDTO =new PayMethodActivityDTO();
        BeanUtils.copyProperties(payMethodActivityPO,payMethodActivityDTO);
        payMethodActivityDTO.setChannel(payMethodPO.getChannel());
        payMethodActivityDTO.setPayProductType(payMethodPO.getPayProductType());
        payMethodActivityDTO.setPayMethodCode(payMethodPO.getPayMethodCode());
        //国内航线
        List<PayMethodRoutePO> DRoute = payMethodRouteMapper.selectByRouteType(payMethodActivityPO.getId(),"D");
        List<String> DCollect = DRoute.stream()
                .map(item -> item.getDepAirport()+"-"+item.getArrAirport())
                .collect(Collectors.toList());
        payMethodActivityDTO.setDsList(DCollect);
        //国际航线
        List<PayMethodRoutePO> IRoute = payMethodRouteMapper.selectByRouteType(payMethodActivityPO.getId(),"I");

        List<String> ICollect = IRoute.stream()
                .map(item -> item.getDepAirport()+"-"+item.getArrAirport())
                .collect(Collectors.toList());
        payMethodActivityDTO.setIsList(ICollect);
        return payMethodActivityDTO;

    }

    @Override
    @DSTransactional
    public void updatePayMethodById(PayMethodDTO payMethodDTO) {
        if(StringUtils.isEmpty(payMethodDTO.getId())) {
            throw new HoServiceException("缺少指定的目标ID");
        }

        PayMethodPO payMethodPO = new PayMethodPO();
        BeanUtils.copyProperties(payMethodDTO,payMethodPO);

        payMethodPO.setUpdater(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(payMethodPO.getUpdater())) {
            throw new HoServiceException("更新人不可为空");
        }
        payMethodPO.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(payMethodPO.getUpdateTime().toString())) {
            throw new HoServiceException("更新时间不可为空");
        }
        try{
            int effectLine =  payMethodMapper.updatePayMethodById(payMethodPO);
            if(effectLine>0) {
                //取消同渠道 产品类型的其他默认选项
                if(StringUtils.equals(payMethodDTO.getIsDefault(),"Y")){
                    PayMethodPO payMethodPO2 = new PayMethodPO();
                    payMethodPO2.setIsDefault("N");
                    payMethodPO2.setUpdater(SecurityUtils.getUsername());
                    if(StringUtils.isEmpty(payMethodPO.getUpdater())) {
                        throw new HoServiceException("更新人不可为空");
                    }
                    payMethodPO2.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
                    if(StringUtils.isEmpty(payMethodPO.getUpdateTime().toString())) {
                        throw new HoServiceException("更新时间不可为空");
                    }
                    payMethodPO2.setId(payMethodPO.getId());
                    payMethodPO2.setChannel(payMethodPO.getChannel());
                    payMethodPO2.setPayProductType(payMethodPO.getPayProductType());
                    payMethodMapper.updateByConditionSelective(payMethodPO2);
                }
                return;
            }else if(effectLine==0){
                throw new HoServiceException("该支付方式不存在，请检查id是否正确");
            } else {
                throw new HoServiceException("更新支付方式失败");
            }
        }catch (Exception e){
            log.error("更新失败:{}", e.getMessage());
            throw new HoServiceException("更新支付方式失败",e.getMessage());
        }
    }

    @Override
    @DSTransactional
    public void updatePayMethodActivityById(PayMethodActivityDTO payMethodActivityDTO) {
        if(StringUtils.isEmpty(payMethodActivityDTO.getId())) {
            throw new HoServiceException("缺少指定的目标ID");
        }

        PayMethodActivityPO payMethodActivityPO = new PayMethodActivityPO();
        BeanUtils.copyProperties(payMethodActivityDTO,payMethodActivityPO);

        payMethodActivityPO.setUpdater(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(payMethodActivityPO.getUpdater())) {
            throw new HoServiceException("更新人不可为空");
        }
        payMethodActivityPO.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(payMethodActivityPO.getUpdateTime().toString())) {
            throw new HoServiceException("更新时间不可为空");
        }

        int effectLine =  payMethodActivityMapper.updatePayMethodActivityById(payMethodActivityPO);
        if(effectLine>0) {
            //区分是否区分航线
            if ("Y".equals(payMethodActivityDTO.getDistinguishRoute())){
                payMethodRouteMapper.deleteByPayActivityId(payMethodActivityPO.getId());

                if (payMethodActivityDTO.getDsList()!=null && !payMethodActivityDTO.getDsList().isEmpty()){
                    for (String item:payMethodActivityDTO.getDsList()) {
                        PayMethodRoutePO  payMethodRoutePO = new PayMethodRoutePO();
                        payMethodRoutePO.setId(HOStringUtil.newGUID());
                        payMethodRoutePO.setRouteType("D");
                        payMethodRoutePO.setDepAirport(item.substring(0,item.indexOf("-")));
                        payMethodRoutePO.setArrAirport(item.substring(item.indexOf("-")+1));
                        payMethodRoutePO.setPayActivityId(payMethodActivityPO.getId());
                        payMethodRoutePO.setCreateUser(SecurityUtils.getUsername());
                        payMethodRoutePO.setCreateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
                        payMethodRoutePO.setUpdateUser(SecurityUtils.getUsername());
                        payMethodRoutePO.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
                        payMethodRouteMapper.add(payMethodRoutePO);
                    }
                }
                if (payMethodActivityDTO.getIsList()!=null && !payMethodActivityDTO.getIsList().isEmpty()){
                    for (String item:payMethodActivityDTO.getIsList()) {
                        PayMethodRoutePO payMethodRoutePO = new PayMethodRoutePO();
                        payMethodRoutePO.setId(HOStringUtil.newGUID());
                        payMethodRoutePO.setRouteType("I");
                        payMethodRoutePO.setDepAirport(item.substring(0,item.indexOf("-")));
                        payMethodRoutePO.setArrAirport(item.substring(item.indexOf("-")+1));
                        payMethodRoutePO.setPayActivityId(payMethodActivityPO.getId());
                        payMethodRoutePO.setCreateUser(SecurityUtils.getUsername());
                        payMethodRoutePO.setCreateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
                        payMethodRoutePO.setUpdateUser(SecurityUtils.getUsername());
                        payMethodRoutePO.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
                        payMethodRouteMapper.add(payMethodRoutePO);
                    }
                }
            }
            return;
        }else if(effectLine==0){
            throw new HoServiceException("该活动不存在，请检查id是否正确");
        } else {
            throw new HoServiceException("更新活动失败");
        }
    }

    @Override
    public String checkChannelAndProduct(PayMethodDTO payMethodDTO) {
        if(StringUtils.isEmpty(payMethodDTO.getChannel())) {
            throw new HoServiceException("渠道不可为空");
        }
        if(StringUtils.isEmpty(payMethodDTO.getPayProductType())) {
            throw new HoServiceException("产品类型不可为空");
        }
        if(StringUtils.isEmpty(payMethodDTO.getIsDefault())) {
            throw new HoServiceException("IsDefault不可为空");
        }
        if(StringUtils.equals(payMethodDTO.getIsDefault(),"Y")){
            try{
                List<PayMethodPO> payMethodPOS = payMethodMapper.selectByCondition(payMethodDTO);
                log.info("已存在的默认选中的支付方式为：{}",payMethodPOS);
                if (payMethodPOS != null && payMethodPOS.size() > 0) {
                    String msg = "该产品类型在同一渠道中已存在默认选中的支付方式，是否替换？";
                    return msg;
                }
            }catch (Exception e){
                throw new HoServiceException("checkChannelAndProduct失败");
            }
        }
        return null;
    }

    @Override
    public String parseImage(MultipartFile file) {
        if (file.isEmpty()) {
            throw new HoServiceException("上传的图片不能为空");
        }
        try {
            byte[] bytes = file.getBytes();
            String encode = "data:image/jpeg;base64,"+Base64Encoder.encode(bytes);
            return encode;
        } catch (IOException e) {
            log.error("解析图片错误！{}",e);
            throw new HoServiceException("解析图片错误！");
        }
    }

    @Override
    public void changePayMethodStatus(String status, String id) {
        if(StringUtils.isEmpty(id)) {
            throw new HoServiceException("缺少指定的目标ID");
        }
        if(StringUtils.isEmpty(status)) {
            throw new HoServiceException("缺少指定的目标状态");
        }
        PayMethodPO payMethodPO = new PayMethodPO();
        payMethodPO.setStatus(status);
        payMethodPO.setId(id);

        payMethodPO.setUpdater(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(payMethodPO.getUpdater())) {
        throw new HoServiceException("更新人不可为空");
        }
        payMethodPO.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(payMethodPO.getUpdateTime().toString())) {
            throw new HoServiceException("更新时间不可为空");
        }

        try{
            int effectLine =  payMethodMapper.updatePayMethodById(payMethodPO);
            if(effectLine>0) {
                return;
            }else if(effectLine==0){
                throw new HoServiceException("该支付方式不存在，请检查id是否正确");
            } else {
                throw new HoServiceException("更新支付方式状态失败");
            }
        }catch (Exception e){
            log.error("更新失败:{}", e.getMessage());
            throw new HoServiceException("更新支付方式状态失败",e.getMessage());
        }


    }

    @Override
    public void changePayMethodActivityStatus(String status, String id) {
        if(StringUtils.isEmpty(id)) {
            throw new HoServiceException("缺少指定的目标ID");
        }
        if(StringUtils.isEmpty(status)) {
            throw new HoServiceException("缺少指定的目标状态");
        }

        if ("Y".equals(status)){
            //获取该支付活动的支付方式，产品类型，支付渠道
            PayMethodActivityPO payMethod = payMethodActivityMapper.getpayMethodActivityById(id);
            PayMethodActivityPO select = new PayMethodActivityPO();
            select.setPayMethodId(payMethod.getPayMethodId());
            List<PayMethodActivityPO>  payMethodActivityPOS  =payMethodActivityMapper.getpayMethodActivityByPayMethodId(select.getPayMethodId());
            List<PayMethodActivityPO>  filterPayMethod = payMethodActivityPOS.stream().filter(payMethodActivityPO -> (!payMethodActivityPO.getId().equals(payMethod.getId()))&&"Y".equals(payMethodActivityPO.getStatus())).collect(Collectors.toList());

            //与当前日期存在交际支付活动
            List<PayMethodActivityPO> payMethodList=filterPayMethod.stream().
                    filter(payMethodItem->
                            DateUtil.isOverlap(payMethodItem.getStartTime(), payMethodItem.getEndTime(),payMethod.getStartTime(),payMethod.getEndTime()))
                    .collect(Collectors.toList());



            if (CollectionUtils.isNotEmpty(payMethodList)) {
                //判断是否区分航线
                boolean isRoute = payMethodList.stream().anyMatch(payParam -> "N".equals(payParam.getDistinguishRoute()));

                if (isRoute||"N".equals(payMethod.getDistinguishRoute())) {
                    throw new HoServiceException("该时间周期内存在已发布活动，请不要重复发布活动");
                } else {

                    PayMethodActivityDTO payMethodActivityDTO = getPayMethodActivityById(id);

                    if (ObjectUtil.isNotEmpty(payMethodActivityDTO)) {
                        //判断区分航线时，是否存在重复航线
                        Boolean payRoute =payMethodList.stream().anyMatch(payParam -> {
                            PayMethodActivityDTO payMethodActivityDTO2 = getPayMethodActivityById(payParam.getId());
                            if (payMethodActivityDTO2 != null) {
                                if (CollectionUtils.isNotEmpty(payMethodActivityDTO2.getIsList())||CollectionUtils.isNotEmpty(payMethodActivityDTO2.getDsList())){
                                    if (!Collections.disjoint(payMethodActivityDTO2.getDsList(),payMethodActivityDTO.getDsList())||
                                            !Collections.disjoint(payMethodActivityDTO2.getIsList(),payMethodActivityDTO.getIsList())) {
                                        return true;
                                    }
                                }
                            }
                            return false;
                        });

                        if (payRoute){
                            throw new HoServiceException("该时间周期内存在已发布活动，请不要重复发布活动");
                        }
                    }


                }
            }
        }

        PayMethodActivityPO payMethodActivityPO = new PayMethodActivityPO();
        payMethodActivityPO.setStatus(status);
        payMethodActivityPO.setId(id);

        payMethodActivityPO.setUpdater(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(payMethodActivityPO.getUpdater())) {
        throw new HoServiceException("更新人不可为空");
        }
        payMethodActivityPO.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(payMethodActivityPO.getUpdateTime().toString())) {
            throw new HoServiceException("更新时间不可为空");
        }

        int effectLine =  payMethodActivityMapper.updatePayMethodActivityById(payMethodActivityPO);
        if(effectLine>0) {
            return;
        }else if(effectLine==0){
            throw new HoServiceException("该活动不存在，请检查id是否正确");
        } else {
            throw new HoServiceException("更新活动失败");
        }
    }

    @Override
    public void deletePayMethodActivityById(String id) {
        if(StringUtils.isEmpty(id)) {
            throw new HoServiceException("缺少指定的目标ID");
        }
        int effectLine =  payMethodActivityMapper.deletePayMethodActivityById(id);
        if(effectLine>0) {
            return;
        } else {
            throw new HoServiceException("删除活动失败");
        }
    }

    @Override
    @DSTransactional
    public PayMethodPO addPayMethod(PayMethodDTO payMethodDTO) {
        PayMethodPO payMethodPO = new PayMethodPO();
        BeanUtils.copyProperties(payMethodDTO,payMethodPO);
        payMethodPO.setId(HOStringUtil.newGUID());
        payMethodPO.setStatus("Y");
        payMethodPO.setCreater(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(payMethodPO.getCreater())) {
            throw new HoServiceException("创建人不可为空");
        }
        payMethodPO.setCreateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(payMethodPO.getCreateTime().toString())){
            throw new HoServiceException("创建时间不可为空");
        }
        payMethodPO.setUpdater(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(payMethodPO.getUpdater())) {
        throw new HoServiceException("更新人不可为空");
        }
        payMethodPO.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(payMethodPO.getUpdateTime().toString())) {
            throw new HoServiceException("更新时间不可为空");
        }
        int effectLine = payMethodMapper.addPayMethod(payMethodPO);
        if(effectLine>0){
            //取消同渠道 产品类型的其他默认选项
            if(StringUtils.equals(payMethodDTO.getIsDefault(),"Y")){
                PayMethodPO payMethodPO2 = new PayMethodPO();
                payMethodPO2.setIsDefault("N");
                payMethodPO2.setUpdater(SecurityUtils.getUsername());
                if(StringUtils.isEmpty(payMethodPO.getUpdater())) {
                throw new HoServiceException("更新人不可为空");
                }
                payMethodPO2.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
                if(StringUtils.isEmpty(payMethodPO.getUpdateTime().toString())) {
                    throw new HoServiceException("更新时间不可为空");
                }
                payMethodPO2.setId(payMethodPO.getId());
                payMethodPO2.setChannel(payMethodPO.getChannel());
                payMethodPO2.setPayProductType(payMethodPO.getPayProductType());
                payMethodMapper.updateByConditionSelective(payMethodPO2);
            }
            return payMethodPO;
        }else {
            throw new HoServiceException("新增支付方式失败");
        }

    }

    @Override
    @DSTransactional
    public PayMethodActivityPO addPayMethodActivity(PayMethodActivityDTO payMethodActivityDTO) {
        PayMethodActivityPO payMethodActivityPO = new PayMethodActivityPO();
        BeanUtils.copyProperties(payMethodActivityDTO,payMethodActivityPO);
        String id = IdUtil.simpleUUID();
        id = StrUtil.subSuf(id, id.length() - 6);
        payMethodActivityPO.setId(id);
        payMethodActivityPO.setStatus("N");
        payMethodActivityPO.setCreater(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(payMethodActivityPO.getCreater())) {
            throw new HoServiceException("创建人不可为空");
        }
        payMethodActivityPO.setCreateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(payMethodActivityPO.getCreateTime().toString())){
            throw new HoServiceException("创建时间不可为空");
        }
        payMethodActivityPO.setUpdater(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(payMethodActivityPO.getUpdater())) {
        throw new HoServiceException("更新人不可为空");
        }
        payMethodActivityPO.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(payMethodActivityPO.getUpdateTime().toString())) {
            throw new HoServiceException("更新时间不可为空");
        }
        int effectLine = payMethodActivityMapper.addPayMethodActivity(payMethodActivityPO);
        if(effectLine>0){
            //区分是否区分航线
            if ("Y".equals(payMethodActivityDTO.getDistinguishRoute())){

                if (payMethodActivityDTO.getDsList()!=null && !payMethodActivityDTO.getDsList().isEmpty()){
                    for (String item:payMethodActivityDTO.getDsList()) {
                        PayMethodRoutePO  payMethodRoutePO = new PayMethodRoutePO();
                        payMethodRoutePO.setId(HOStringUtil.newGUID());
                        payMethodRoutePO.setRouteType("D");
                        payMethodRoutePO.setDepAirport(item.substring(0,item.indexOf("-")));
                        payMethodRoutePO.setArrAirport(item.substring(item.indexOf("-")+1));
                        payMethodRoutePO.setPayActivityId(payMethodActivityPO.getId());
                        payMethodRoutePO.setCreateUser(SecurityUtils.getUsername());
                        payMethodRoutePO.setCreateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
                        payMethodRoutePO.setUpdateUser(SecurityUtils.getUsername());
                        payMethodRoutePO.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
                        payMethodRouteMapper.add(payMethodRoutePO);
                    }
                }
                if (payMethodActivityDTO.getIsList()!=null && !payMethodActivityDTO.getIsList().isEmpty()){
                    for (String item:payMethodActivityDTO.getIsList()) {
                        PayMethodRoutePO payMethodRoutePO = new PayMethodRoutePO();
                        payMethodRoutePO.setId(HOStringUtil.newGUID());
                        payMethodRoutePO.setRouteType("I");
                        payMethodRoutePO.setDepAirport(item.substring(0,item.indexOf("-")));
                        payMethodRoutePO.setArrAirport(item.substring(item.indexOf("-")+1));
                        payMethodRoutePO.setPayActivityId(payMethodActivityPO.getId());
                        payMethodRoutePO.setCreateUser(SecurityUtils.getUsername());
                        payMethodRoutePO.setCreateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
                        payMethodRoutePO.setUpdateUser(SecurityUtils.getUsername());
                        payMethodRoutePO.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
                        payMethodRouteMapper.add(payMethodRoutePO);
                    }

                }
            }
            return payMethodActivityPO;
        }else {
            throw new HoServiceException("新增支付方式失败");
        }
    }

    @Override
    public List<PayMethodActivityPO> getActivityByPayMethodId(String payMethodId) {
        if(StringUtils.isEmpty(payMethodId)){
            throw new HoServiceException("支付方式id不可为空");
        }
        List<PayMethodActivityPO>  payMethodActivityPOS  =payMethodActivityMapper.getpayMethodActivityByPayMethodId(payMethodId);
        return payMethodActivityPOS;
    }

    @Override
    public void refreshRedis(String merchantPayment, String channel, String payProductType) {
        if(StringUtils.isEmpty(merchantPayment) || StringUtils.isEmpty(channel) || StringUtils.isEmpty(payProductType)){
            throw new HoServiceException("刷新缓存时，所选的商户类型、渠道和产品类型不可为空！");
        }

        String key = channel + ":" + merchantPayment + ":" + payProductType;
        HttpResult httpResult = httpClientService.doPost(key,thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.REFRESH_PAYMETHOD_CACHE, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("刷新支付方式Redis缓存失败，请稍后重试");
        }
    }

    @Override
    public Boolean checkAuth() {
        try {
            return SecurityUtils.getLoginUser().getSysUser().isAdmin();
        }catch (Exception e) {
            throw new HoServiceException("用户获取失败");
        }
    }

    /**
     * 手动分页实现
     * @param dataList
     * @param pageNum
     * @param pageSize
     * @return
     */
    private List<PayMethodSearchPO> manualPagination(List<PayMethodSearchPO> dataList, int pageNum, int pageSize) {
        // 处理空集合或无效输入
        if (dataList == null || dataList.isEmpty() || pageSize <= 0 || pageNum <= 0) {
            return Collections.emptyList();
        }

        List<PayMethodSearchPO> sortedList = new ArrayList<>(dataList);

        // 排序：处理null值，将null视为最大值（排在最后）
        sortedList.sort(Comparator.comparing(
                po -> po.getOrderNo() != null ? po.getOrderNo() : Integer.MAX_VALUE,
                Comparator.naturalOrder()
        ));

        // 计算总记录数和总页数
        int totalRecords = sortedList.size();
        int totalPages = (totalRecords + pageSize - 1) / pageSize;

        // 请求页码超过总页数时返回空列表
        if (pageNum > totalPages) {
            return Collections.emptyList();
        }

        // 计算分页起始索引
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, totalRecords);

        return new ArrayList<>(sortedList.subList(startIndex, endIndex));
    }
}
