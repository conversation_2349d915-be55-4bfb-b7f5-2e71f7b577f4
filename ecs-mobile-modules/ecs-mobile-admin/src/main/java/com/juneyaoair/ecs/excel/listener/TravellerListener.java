package com.juneyaoair.ecs.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellExtra;
import com.juneyaoair.ecs.excel.bean.Traveller;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/3 12:59
 */
public class TravellerListener extends AnalysisEventListener<Traveller> {
    /**
     * 缓存的数据列表
     */
    @Getter
    private List<Traveller> cachedDataList = new ArrayList<>();
    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        super.onException(exception, context);
    }

    @Override
    public void invoke(Traveller traveller, AnalysisContext analysisContext) {
        // 将解析到的数据添加到缓存列表
        cachedDataList.add(traveller);
    }

    @Override
    public void extra(CellExtra extra, AnalysisContext context) {
        super.extra(extra, context);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        return super.hasNext(context);
    }
}
