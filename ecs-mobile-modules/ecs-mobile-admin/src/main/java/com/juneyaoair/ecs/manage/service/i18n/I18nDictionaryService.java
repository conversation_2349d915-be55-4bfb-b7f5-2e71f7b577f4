package com.juneyaoair.ecs.manage.service.i18n;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.juneyaoair.ecs.manage.dto.I18n.AddI18nDictionaryKVRequestDTO;
import com.juneyaoair.ecs.manage.dto.I18n.AddI18nDictionaryRequestDTO;
import com.juneyaoair.ecs.manage.dto.I18n.I18nDictionaryDTO;
import com.juneyaoair.ecs.manage.dto.I18n.I18nDictionaryKVDTO;
import com.juneyaoair.ecs.manage.dto.I18n.UpdateI18nDictionaryKVRequestDTO;
import com.juneyaoair.ecs.manage.dto.I18n.UpdateI18nDictionaryRequestDTO;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

public interface I18nDictionaryService {
    void add(AddI18nDictionaryRequestDTO request);

    void addKV(AddI18nDictionaryKVRequestDTO request);

    @DSTransactional
    void updateKV(UpdateI18nDictionaryKVRequestDTO request);

    void delete(String id);

    List<I18nDictionaryDTO> query(String dictionaryName, String dictionaryType);

    List<I18nDictionaryKVDTO> queryKV(String dictionaryId, String dictionaryType, String original, String languageTag, String translation, Boolean fuzzyOriginal);

    @DSTransactional
    void update(UpdateI18nDictionaryRequestDTO request);

    @DSTransactional
    void deleteKV(String id);

    /**
     * 刷新指定字典类型的缓存
     * @param dictionaryType 字典类型
     */
    void refreshCache(String dictionaryType);

    /**
     * 根据字典类型添加或更新翻译
     * @param dictionaryType 字典类型
     * @param original 原文
     * @param translation 翻译
     * @param languageTag 语言标签
     */
    void addOrUpdateKVByType(String dictionaryType, String original, String translation, String languageTag);

    /**
     * 根据字典类型查询翻译
     * @param dictionaryType 字典类型
     * @param original 原文
     * @param languageTag 语言标签
     * @param translation 翻译
     * @return 翻译列表
     */
    List<I18nDictionaryKVDTO> queryKVByType(String dictionaryType, String original, String languageTag, String translation);

    /**
     * 查询指定类型字典信息
     * @param dictionaryType
     * @return
     */
    Map<String, Map<String, String>> fetchDictData(String dictionaryType);

    /**
     * 批量导入字典数据
     * @param inputStream Excel文件输入流
     * @throws Exception 导入异常
     */
    @DSTransactional
    void importData(InputStream inputStream) throws Exception;
}
