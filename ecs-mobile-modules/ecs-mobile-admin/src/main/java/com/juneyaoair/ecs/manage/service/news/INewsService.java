package com.juneyaoair.ecs.manage.service.news;

import com.juneyaoair.ecs.manage.dto.news.request.NewsCategoryRequest;
import com.juneyaoair.ecs.manage.dto.news.request.NewsChannelRequest;
import com.juneyaoair.ecs.manage.dto.news.request.NewsRequest;
import com.juneyaoair.ecs.manage.dto.news.response.News;
import com.juneyaoair.ecs.manage.dto.news.response.NewsCategory;
import com.juneyaoair.ecs.manage.dto.news.response.NewsChannel;
import com.juneyaoair.ecs.manage.dto.news.response.NewsDetail;

import java.util.List;

/**
 * @ClassName INewsService
 * @Description
 * <AUTHOR>
 * @Date 2024/1/31 8:39
 * @Version 1.0
 */
public interface INewsService {
    /**
     * @param newsRequest
     * @return java.util.List<com.juneyaoair.manage.b2c2014.entity.NewsPO>
     * <AUTHOR>
     * @Description 获取新闻列表
     * @Date 16:50 2024/3/12
     **/

    List<News> toCatchNewsList(NewsRequest newsRequest);

    /**
     * @param newsCategoryRequest
     * @return boolean
     * <AUTHOR>
     * @Description 新增栏目
     * @Date 12:34 2024/3/12
     **/
    boolean toAddNewCategory(NewsCategoryRequest newsCategoryRequest);

    /**
     * @param newsCategoryRequest
     * @return boolean
     * <AUTHOR>
     * @Description 修改栏目
     * @Date 12:35 2024/3/12
     **/
    boolean toUpdateNewCategory(NewsCategoryRequest newsCategoryRequest);

    /**
     * @param newsCategoryRequest
     * @return boolean
     * <AUTHOR>
     * @Description 删除栏目
     * @Date 12:57 2024/3/12
     **/
    boolean toDeleteNewCategory(NewsCategoryRequest newsCategoryRequest);

    /**
     * @param newsCategory
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.news.response.NewsCategory>
     * <AUTHOR>
     * @Description 查询栏目列表
     * @Date 13:59 2024/3/12
     **/
    List<NewsCategory> toCatchNewsCategoryList(NewsCategory newsCategory);

    /**
     * @param newsChannelRequest
     * @return boolean
     * <AUTHOR>
     * @Description 新增渠道号
     * @Date 15:20 2024/3/12
     **/
    boolean toAddNewChannel(NewsChannelRequest newsChannelRequest);

    /**
     * @param newsChannelRequest
     * @return boolean
     * <AUTHOR>
     * @Description 修改渠道号
     * @Date 15:27 2024/3/12
     **/
    boolean toUpdateNewChannel(NewsChannelRequest newsChannelRequest);

    /**
     * @param newsChannelRequest
     * @return boolean
     * <AUTHOR>
     * @Description 删除渠道号
     * @Date 15:38 2024/3/12
     **/
    boolean toDeleteNewChannel(NewsChannelRequest newsChannelRequest);

    /**
     * @param newsChannelRequest
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.news.response.NewsChannel>
     * <AUTHOR>
     * @Description 获取渠道号信息列表
     * @Date 15:45 2024/3/12
     **/
    List<NewsChannel> toCatchNewChannelList(NewsChannelRequest newsChannelRequest);

    /**
     * @param newsRequest
     * @return com.juneyaoair.ecs.manage.dto.news.response.NewsDetail
     * <AUTHOR>
     * @Description 获取新闻详情
     * @Date 8:59 2024/3/13
     **/
    NewsDetail toCatchNewsDetail(NewsRequest newsRequest);

    /**
     * @param newsRequest
     * @return java.lang.Boolean
     * <AUTHOR>
     * @Description 删除新闻（同步删除表T_NEWS_CONTEXT中新闻记录）
     * @Date 10:16 2024/3/13
     **/
    Boolean toDeleteNews(NewsRequest newsRequest);

    /**
     * @param newsRequest
     * @return java.lang.Boolean
     * <AUTHOR>
     * @Description 发布新闻
     * @Date 11:08 2024/3/13
     **/
    Boolean toUpdateNewsReleaseStatus(NewsRequest newsRequest);

    /**
     * @param newsRequest
     * @return java.lang.Boolean
     * <AUTHOR>
     * @Description 新增新闻
     * @Date 12:07 2024/3/13
     **/
    Boolean toAddNews(NewsRequest newsRequest);

    /**
     * @param newsRequest
     * @return java.lang.Boolean
     * <AUTHOR>
     * @Description 更新新闻
     * @Date 12:31 2024/3/13
     **/

    Boolean toUpdateNews(NewsRequest newsRequest);
}
