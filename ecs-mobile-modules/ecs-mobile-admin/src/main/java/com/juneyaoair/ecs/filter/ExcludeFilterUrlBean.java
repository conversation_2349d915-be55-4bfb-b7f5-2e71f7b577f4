package com.juneyaoair.ecs.filter;

import lombok.Data;
import org.apache.commons.compress.utils.Sets;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * <AUTHOR>
 * @Description Filter排除的URL清单
 * @created 2024/4/30 14:14
 */
@Data
@Component
@ConfigurationProperties(prefix = "com.juneyaoair")
public class ExcludeFilterUrlBean {

    /** 需要排除的地址清单 */
    private Set<String> excludeFilterUrl;

    public Set<String> getExcludeFilterUrl() {
        return null == excludeFilterUrl ? Sets.newHashSet() : excludeFilterUrl;
    }
}
