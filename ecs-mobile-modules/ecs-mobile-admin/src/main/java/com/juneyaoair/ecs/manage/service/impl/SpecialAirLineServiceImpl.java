package com.juneyaoair.ecs.manage.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.juneyaoair.ecs.http.bean.HttpResult;
import com.juneyaoair.ecs.http.service.HttpClientService;
import com.juneyaoair.ecs.manage.constant.ManageConstant;
import com.juneyaoair.ecs.manage.dto.base.BaseRequest;
import com.juneyaoair.ecs.manage.dto.base.BaseRequestUtil;
import com.juneyaoair.ecs.manage.dto.base.BaseResult;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.specialAirline.SpecialAirlineBase;
import com.juneyaoair.ecs.manage.dto.specialAirline.SpecialAirlineBaseParam;
import com.juneyaoair.ecs.manage.dto.specialAirline.SpecialAirlineInfo;
import com.juneyaoair.ecs.manage.dto.specialAirline.SpecialAirlineQueryParam;
import com.juneyaoair.ecs.manage.enums.ResultEnum;
import com.juneyaoair.ecs.manage.properties.ThirdAppUrlSet;
import com.juneyaoair.ecs.manage.service.SpecialAirLineService;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: caolei
 * @Description: 特价航线
 * @Date: 2025/02/07 16:42
 * @Modified by:
 */
@Service
public class SpecialAirLineServiceImpl implements SpecialAirLineService {

    @Autowired
    private HttpClientService httpClientService;
    @Autowired
    private ThirdAppUrlSet thirdAppUrlSet;

    @Override
    public void create(SpecialAirlineBaseParam specialAirlineBaseParam) {
        specialAirlineBaseParam.setOperator(SecurityUtils.getUsername());
        BaseRequest<SpecialAirlineBaseParam> request = BaseRequestUtil.createRequest(specialAirlineBaseParam);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.CREATE_SPECIAL_AIRLINE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("创建特价航线失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public void updateSpecialAirline(SpecialAirlineBaseParam specialAirlineBaseParam) {
        specialAirlineBaseParam.setOperator(SecurityUtils.getUsername());
        BaseRequest<SpecialAirlineBaseParam> request = BaseRequestUtil.createRequest(specialAirlineBaseParam);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.UPDATE_SPECIAL_AIRLINE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("更新特价航线失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public void deleteSpecialAirline(SpecialAirlineBase specialAirlineBase) {
        specialAirlineBase.setOperator(SecurityUtils.getUsername());
        BaseRequest<SpecialAirlineBase> request = BaseRequestUtil.createRequest(specialAirlineBase);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.DELETE_SPECIAL_AIRLINE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("删除特价航线失败，请稍后再试");
        }
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        BaseResult<Object> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public PageResult<SpecialAirlineInfo> getList(SpecialAirlineQueryParam specialAirlineQueryParam) {
        BaseRequest<SpecialAirlineQueryParam> request = BaseRequestUtil.createRequest(specialAirlineQueryParam);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.GET_SPECIAL_AIRLINE,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询特价航线清单失败，请稍后再试");
        }
        TypeReference<BaseResult<PageResult<SpecialAirlineInfo>>> typeReference = new TypeReference<BaseResult<PageResult<SpecialAirlineInfo>>>() {
        };
        BaseResult<PageResult<SpecialAirlineInfo>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public SpecialAirlineInfo getSpecialAirlineInfo(SpecialAirlineBase specialAirlineBase) {
        BaseRequest<SpecialAirlineBase> request = BaseRequestUtil.createRequest(specialAirlineBase);
        HttpResult httpResult = httpClientService.doPost(request,thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.GET_SPECIAL_AIRLINE_BY_ID,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询指定ID特价航线信息失败，请稍后再试");
        }
        TypeReference<BaseResult<SpecialAirlineInfo>> typeReference = new TypeReference<BaseResult<SpecialAirlineInfo>>() {
        };
        BaseResult<SpecialAirlineInfo> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if(!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }
}
