package com.juneyaoair.ecs.manage.service.holiday.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.juneyaoair.ecs.http.bean.HttpResult;
import com.juneyaoair.ecs.http.service.HttpClientService;
import com.juneyaoair.ecs.manage.constant.ManageConstant;
import com.juneyaoair.ecs.manage.dto.activity.common.HolidayCalendar;
import com.juneyaoair.ecs.manage.dto.base.BaseResult;
import com.juneyaoair.ecs.manage.enums.ResultEnum;
import com.juneyaoair.ecs.manage.properties.ThirdAppUrlSet;
import com.juneyaoair.ecs.manage.service.holiday.IHolidayCalenderService;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.activity.HolidayPO;
import com.juneyaoair.manage.b2c.service.HolidayService;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * @ClassName HolidayServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/22 13:25
 * @Version 1.0
 */

@Service
@RefreshScope
@Slf4j
public class HolidayCalenderServiceImpl implements IHolidayCalenderService {

    @Resource
    private HolidayService holidayService;

    @Autowired
    private HttpClientService httpClientService;

    @Autowired
    private ThirdAppUrlSet thirdAppUrlSet;

    private final static String TYPE = "getType";

    private final static String ACTUAL_TYPE = "type";

    private final static String YEAR = "getYear";

    private final static String YEARLY = "yearly";

    private final static String DAILY = "daily";


    @Override
    public List<HolidayCalendar> toCatchHolidayList(HttpServletRequest request) {
        List<HolidayCalendar> retholidayCalendarList = new ArrayList<>();
        List<HolidayCalendar> waitToHandleList = new ArrayList<>();

        List<HolidayPO> holidayPOS = holidayService.list();
        if (CollectionUtils.isEmpty(holidayPOS)) {
            return retholidayCalendarList;
        }
        toReValue(waitToHandleList, holidayPOS);
        if (CollectionUtils.isEmpty(waitToHandleList)) {
            return retholidayCalendarList;
        }
        String type = request.getParameter(TYPE);
        String year = request.getParameter(YEAR);
        if (StringUtils.isNotEmpty(type)) {
            switch (type) {
                case YEARLY:
                    retholidayCalendarList = yearlyHolidayCalender(waitToHandleList);
                    break;
                case DAILY:
                    retholidayCalendarList = dailyHolidayCalender(waitToHandleList, Integer.parseInt(year));
                    break;
                default:
                    throw new HoServiceException("暂不支持的类型");
            }
            if (CollectionUtils.isNotEmpty(retholidayCalendarList)) {
                retholidayCalendarList.sort(Comparator.nullsLast(
                        Comparator.comparing(
                                HolidayCalendar::getDate
                                , Comparator.nullsLast(Comparable::compareTo)).thenComparing(HolidayCalendar::getExtraField
                                , Comparator.nullsLast(Comparable::compareTo))
                ));
            }
        }
        return retholidayCalendarList;
    }

    /**
     * @param waitToHandleList
     * @param holidayPOS
     * @return void
     * <AUTHOR>
     * @Description 重新赋值
     * @Date 10:11 2024/1/25
     **/
    private void toReValue(List<HolidayCalendar> waitToHandleList, List<HolidayPO> holidayPOS) {
        for (HolidayPO holidayPO : holidayPOS
        ) {
            HolidayCalendar holidayCalendar = new HolidayCalendar();
            holidayCalendar.setId(holidayPO.getId());
            holidayCalendar.setName(deConvertType(holidayPO.getRestOrWork()));
            holidayCalendar.setDate(holidayPO.getHolidayDate());
            holidayCalendar.setHolidayName(holidayPO.getHolidayName());
            holidayCalendar.setDesc(holidayPO.getRemark());

            holidayCalendar.setDisplay(holidayPO.getDisplay());//设置是否显示字段

            waitToHandleList.add(holidayCalendar);
        }
        if (CollectionUtils.isNotEmpty(waitToHandleList)) {
            waitToHandleList.sort(Comparator.comparing(HolidayCalendar::getDate));
        }
    }

    @Override
    public void optHoliday(HolidayCalendar holidayCalendar, HttpServletRequest request) {
        String optType = request.getParameter(ACTUAL_TYPE);
        try {
            if (StringUtils.isEmpty(optType)) {
                throw new HoServiceException("操作类型不可为空");
            }
            List<HolidayPO> holidayPOList = holidayService.list();
            switch (optType) {
                case "add":
                    toAddHoliday(holidayCalendar);
                    break;
                case "modify":
                    toModifyHoliday(holidayPOList, holidayCalendar);
                    break;
                case "delete":
                    toDeleteHoliday(holidayPOList, holidayCalendar);
                    break;
                default:
                    throw new HoServiceException("操作类型错误！");
            }
        } catch (Exception e) {
            log.error("节假日操作类型:{},错误信息:", optType, e);
            throw new HoServiceException(e.getMessage());
        }
    }

    @Override
    @SuppressWarnings("rawtypes")
    public void toClearCache() {
        HttpResult httpResult = httpClientService.doGet(thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.REMOVE_HOLIDAY_CACHE, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("节假日缓存清空失败，请稍后重试");
        }
        TypeReference<BaseResult> typeReference = new TypeReference<BaseResult>() {
        };
        BaseResult baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if (!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())) {
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    /**
     * @param holidayCalendar
     * @return void
     * <AUTHOR>
     * @Description 节假日的新增操作
     * @Date 8:46 2024/1/25
     **/

    private void toAddHoliday(HolidayCalendar holidayCalendar) {
        HolidayPO holidayPO = HolidayPO.builder()
                .id(HOStringUtil.newGUID())
                .holidayDate(holidayCalendar.getDate())
                .restOrWork(toConvertHolidayType(holidayCalendar.getName()))
                .holidayName(holidayCalendar.getHolidayName())
                .remark(holidayCalendar.getDesc())

                .display(holidayCalendar.getDisplay())//新增添加是否在日期框显示字段

                .createdBy(SecurityUtils.getUsername())
                .createdTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS))
                .updatedBy(SecurityUtils.getUsername())
                .updatedTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS)).build();
        boolean save = holidayService.save(holidayPO);
        if (!save) {
            throw new HoServiceException("新增失败");
        }
    }

    private void toDeleteHoliday(List<HolidayPO> holidayPOS, HolidayCalendar holidayCalendar) {
        if (CollectionUtils.isEmpty(holidayPOS)) {
            throw new HoServiceException("未查询到可供删除的列表");
        }
        if (StringUtils.isEmpty(holidayCalendar.getId())) {
            throw new HoServiceException("待删除记录ID不可为空");
        }
        HolidayPO holidayPO = holidayPOS.stream().filter(el -> StringUtils.isNotEmpty(el.getId()) && el.getId().equals(holidayCalendar.getId())).findFirst().orElse(null);
        if (holidayPO == null) {
            throw new HoServiceException("未查询到可供删除的记录");
        }
        boolean remove = holidayService.removeById(holidayCalendar.getId());
        if (!remove) {
            throw new HoServiceException("删除失败");
        }
    }

    private void toModifyHoliday(List<HolidayPO> holidayPOS, HolidayCalendar holidayCalendar) {
        if (CollectionUtils.isEmpty(holidayPOS)) {
            throw new HoServiceException("未查询到可修改的列表");
        }
        if (StringUtils.isEmpty(holidayCalendar.getId())) {
            throw new HoServiceException("待修改记录ID不可为空");
        }
        HolidayPO holidayPO = holidayPOS.stream().filter(el -> StringUtils.isNotEmpty(el.getId()) && el.getId().equals(holidayCalendar.getId())).findFirst().orElse(null);
        if (holidayPO == null) {
            throw new HoServiceException("未查询到可供修改的记录");
        }
        holidayPO.setHolidayDate(holidayCalendar.getDate());
        holidayPO.setRestOrWork(toConvertHolidayType(holidayCalendar.getName()));
        holidayPO.setHolidayName(holidayCalendar.getHolidayName());
        holidayPO.setRemark(holidayCalendar.getDesc());

        holidayPO.setDisplay(holidayCalendar.getDisplay());//新增修改是否显示在日期框字段

        holidayPO.setUpdatedBy(SecurityUtils.getUsername());
        holidayPO.setUpdatedTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        boolean update = holidayService.updateById(holidayPO);
        if (!update) {
            throw new HoServiceException("更新失败");
        }
    }

    private List<HolidayCalendar> yearlyHolidayCalender(List<HolidayCalendar> holidayCalendarList) {
        List<HolidayCalendar> retList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        if (CollectionUtils.isNotEmpty(holidayCalendarList)) {
            //初始化
            HolidayCalendar initCalender = holidayCalendarList.get(0);
            Date initDate = DateUtil.StringToDate(initCalender.getDate(), DateUtil.DATE_FORMATE);
            if (null == initDate) {
                throw new HoServiceException("日期格式有误");
            }
            calendar.setTime(initDate);
            int initYear = calendar.get(Calendar.YEAR);
            for (int i = 0; i < holidayCalendarList.size(); i++) {
                HolidayCalendar holidayCalendar = holidayCalendarList.get(i);
                Date date = DateUtil.StringToDate(holidayCalendar.getDate(), DateUtil.DATE_FORMATE);
                if (null == date) {
                    throw new HoServiceException("日期格式有误");
                }
                calendar.setTime(date);
                int year = calendar.get(Calendar.YEAR);

                if (year != initYear) {
                    HolidayCalendar newHolidayCalender = new HolidayCalendar();
                    newHolidayCalender.setExtraField(String.valueOf(initYear));
                    //获取当年假日天数
                    List<HolidayCalendar> holidayCalendarList1 = dailyHolidayCalender(holidayCalendarList, initYear);
                    List<HolidayCalendar> holidayCalenderList2 = new ArrayList<HolidayCalendar>();
                    for (HolidayCalendar holiday : holidayCalendarList1) {
                        if (holiday.getName().equals("休")) {
                            holidayCalenderList2.add(holiday);
                        }
                    }
                    newHolidayCalender.setDesc(String.valueOf(holidayCalenderList2.size()));
                    retList.add(newHolidayCalender);
                    initYear = year;
                    --i;
                }
                if (i == holidayCalendarList.size() - 1) {
                    HolidayCalendar newHolidayCalender = new HolidayCalendar();
                    newHolidayCalender.setExtraField(String.valueOf(initYear));
                    //获取当年假日天数
                    List<HolidayCalendar> holidayCalendarList1 = dailyHolidayCalender(holidayCalendarList, initYear);
                    newHolidayCalender.setDesc(String.valueOf(holidayCalendarList1.size()));
                    retList.add(newHolidayCalender);
                }
            }
        }
        return retList;
    }

    private List<HolidayCalendar> dailyHolidayCalender(List<HolidayCalendar> holidayCalendarList, int getYear) {
        List<HolidayCalendar> retList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        if (CollectionUtils.isNotEmpty(holidayCalendarList)) {
            for (HolidayCalendar holidayCalendar : holidayCalendarList) {
                Date date = DateUtil.StringToDate(holidayCalendar.getDate(), DateUtil.DATE_FORMATE);
                if (null == date) {
                    throw new HoServiceException("日期格式有误");
                }
                calendar.setTime(date);
                int year = calendar.get(Calendar.YEAR);
                if (getYear == year) {
                    retList.add(holidayCalendar);
                }
            }
        }
        return retList;
    }

    private String toConvertHolidayType(String original) {
        if (StringUtils.isEmpty(original)) {
            return "UNKNOWN";
        }
        if ("休".equals(original)) {
            return "REST";
        }
        if ("班".equals(original)) {
            return "WORK";
        }
        return "UNKNOWN";
    }

    private String deConvertType(String original) {
        if (StringUtils.isEmpty(original)) {
            return "未知";
        }
        if ("REST".equals(original)) {
            return "休";
        }
        if ("WORK".equals(original)) {
            return "班";
        }
        return "未知";
    }
}
