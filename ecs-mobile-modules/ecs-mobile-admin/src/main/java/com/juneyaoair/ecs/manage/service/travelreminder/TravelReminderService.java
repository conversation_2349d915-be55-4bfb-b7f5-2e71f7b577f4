package com.juneyaoair.ecs.manage.service.travelreminder;

import com.juneyaoair.ecs.manage.dto.travelreminder.TravelReminderQueryDTO;
import com.juneyaoair.ecs.manage.dto.travelreminder.TravelReminderSaveDTO;
import com.juneyaoair.ecs.manage.dto.travelreminder.TravelReminderListDTO;
import java.util.List;

public interface TravelReminderService {
    /**
     * 查询出行提醒列表
     *
     * @param queryDTO 查询条件
     * @return 出行提醒列表
     */
    List<TravelReminderListDTO> queryList(TravelReminderQueryDTO queryDTO);

    /**
     * 删除出行提醒
     *
     * @param id 主题ID
     */
    void delete(String id);

    /**
     * 保存或提交出行提醒
     *
     * @param saveDTO 保存参数
     */
    void saveOrSubmit(TravelReminderSaveDTO saveDTO);
} 