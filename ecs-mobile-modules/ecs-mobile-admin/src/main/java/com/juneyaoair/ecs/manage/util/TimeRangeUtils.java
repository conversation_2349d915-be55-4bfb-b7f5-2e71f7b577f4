package com.juneyaoair.ecs.manage.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @ClassName TimeRangeUtils
 * @Description
 * <AUTHOR>
 * @Date 2025/7/15 18:52
 * @Version 1.0
 */
public class TimeRangeUtils {

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final SimpleDateFormat SDF = new SimpleDateFormat(DATE_FORMAT);

    /**
     * 获取两段时间的交集，以主时间范围为主，并校验输入时间是否合法
     *
     * @param mainRangeStart   主时间范围开始时间
     * @param mainRangeEnd     主时间范围结束时间
     * @param subRangeStart    次时间范围开始时间
     * @param subRangeEnd      次时间范围结束时间
     * @return 交集的时间范围字符串数组，格式为 [start, end]；如果没有交集返回 null
     * @throws IllegalArgumentException 如果传入的时间对中结束时间早于开始时间，则抛出异常
     */
    public static String[] getIntersectionTimeRange(
            String mainRangeStart,
            String mainRangeEnd,
            String subRangeStart,
            String subRangeEnd) {

        try {
            // 解析时间
            Date mainStart = SDF.parse(mainRangeStart);
            Date mainEnd = SDF.parse(mainRangeEnd);
            Date subStart = SDF.parse(subRangeStart);
            Date subEnd = SDF.parse(subRangeEnd);

            // 校验主时间范围是否合法
            if (mainEnd.before(mainStart)) {
                throw new IllegalArgumentException("主时间范围非法：结束时间早于开始时间");
            }

            // 校验次时间范围是否合法
            if (subEnd.before(subStart)) {
                throw new IllegalArgumentException("次时间范围非法：结束时间早于开始时间");
            }

            // 判断是否有交集
            if (subEnd.before(mainStart) || subStart.after(mainEnd)) {
                return null; // 完全无交集
            }

            // 取交集的起止时间
            Date intersectStart = subStart.compareTo(mainStart) > 0 ? subStart : mainStart;
            Date intersectEnd = subEnd.compareTo(mainEnd) < 0 ? subEnd : mainEnd;

            return new String[]{SDF.format(intersectStart), SDF.format(intersectEnd)};
        } catch (ParseException e) {
            throw new IllegalArgumentException("时间格式错误，请使用格式：" + DATE_FORMAT, e);
        }
    }
    // 测试方法
    public static void main(String[] args) {
        // 示例1：有交集
        System.out.println("示例1：" + java.util.Arrays.toString(getIntersectionTimeRange(
                "2024-01-01 00:00:00", "2024-05-01 00:00:00",
                "2024-03-01 13:14:15", "2024-06-01 17:25:46")));

        // 示例2：无交集
        System.out.println("示例2：" + java.util.Arrays.toString(getIntersectionTimeRange(
                "2024-01-01 00:00:00", "2024-05-01 00:00:00",
                "2024-06-01 13:14:15", "2024-07-01 17:25:46")));

        // 示例3：次时间范围非法
        System.out.println("示例3：" + java.util.Arrays.toString(getIntersectionTimeRange(
                "2024-01-01 00:00:00", "2024-05-01 00:00:00",
                "2024-07-01 00:00:00", "2024-06-01 00:00:00"))); // 次时间非法
    }
}
