package com.juneyaoair.ecs.manage.service.syncfile.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.juneyaoair.ecs.manage.service.syncfile.IAirlineSyncStaticFileService;
import com.juneyaoair.ecs.manage.util.ListUtil;
import com.juneyaoair.ecs.redis.service.PrimaryRedisService;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.ecs.utils.SystemConstants;
import com.juneyaoair.ecs.utils.SystemUtil;
import com.juneyaoair.manage.b2c.entity.AirlineAPO;
import com.juneyaoair.manage.b2c.mapper.TAirlineAMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class AirlineSyncStaticFileServiceImpl extends SyncStaticFileService<AirlineAPO> implements IAirlineSyncStaticFileService {

    private final static String contentName = "airline";
    @Resource
    TAirlineAMapper airLineMapper;
    @Resource
    private PrimaryRedisService primaryRedisService;


    @Override
    public List<AirlineAPO> prepareData() {
        //主要目的是获取 出发城市->到达城市
        List<AirlineAPO> airlineAPOS = airLineMapper.selectByAll(null);
        if (CollUtil.isEmpty(airlineAPOS)) {
            return null;
        }
        return airlineAPOS.stream().filter(ListUtil.distinctByKey(i -> i.depCity + "|" + i.arrCity))
                .collect(Collectors.toList());
    }

    @Override
    protected void removeCacheInRedis(List<AirlineAPO> airlineAPOS) {
        if (CollectionUtil.isEmpty(airlineAPOS)) {
            return;
        }
        List<String> depArrCityStrList = airlineAPOS.stream()
                .filter(i -> StrUtil.isNotEmpty(i.depCity) && StrUtil.isNotEmpty(i.arrCity))
                .map(i -> i.depCity + i.arrCity).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(depArrCityStrList)) {
            return;
        }
        depArrCityStrList.forEach(code -> {
            String key1 = SystemUtil.envRedisDir() + "common:deparrAirline:" + code;
            primaryRedisService.delete("API:" + key1);
        });
        String key2 = SystemUtil.envRedisDir() + "common:deparrAirline:";
        primaryRedisService.delete("API:" + key2);
        primaryRedisService.delete("API:" + SystemUtil.envRedisDir() + "airLineInfoHash");
        primaryRedisService.delete("FlightBasic:AirLine");
    }

    @Override
    protected void updateVersionNo() {
        updateVersionNo(SystemConstants.REDIS_AIRLINE_INFO);
        updateVersionNo(SystemConstants.REDIS_CITY_INFO);
    }

    @Override
    protected String map2Js(Map<String, Object> resultMap) {
        return "var __airline = " + JsonUtil.objectToJson(resultMap);
    }

    @Override
    protected String getContentName() {
        return contentName;
    }


    @Override
    public Object data2ObjData(List<AirlineAPO> data) {
        return data.stream()
                .collect(Collectors.groupingBy(i -> i.depCity, Collectors.toList()))
                .entrySet().stream().collect(
                        Collectors.toMap(Map.Entry::getKey,
                                k -> k.getValue().stream().map(i -> i.arrCity).collect(Collectors.toList())));
    }

    @Override
    public boolean process() {
        mainProcess();
        return true;
    }
}
