package com.juneyaoair.ecs.manage.service.osaka;

import com.juneyaoair.ecs.manage.dto.activity.request.osaka.OsakaCouponClaimQueryRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.osaka.OsakaCouponClaimResponse;

import java.util.List;

/**
 * @ClassName IOsakaCouponClaimService
 * @Description
 * <AUTHOR>
 * @Date 2025/6/23 14:06
 * @Version 1.0
 */
public interface IOsakaCouponClaimService {

    /**
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.activity.response.osaka.OsakaCouponClaimResponse>
     * <AUTHOR>
     * @Description 大阪接送机券领取列表查询
     * @Date 14:20 2025/6/23
     **/
    List<OsakaCouponClaimResponse> doCouponClaimListQuery(OsakaCouponClaimQueryRequest osakaCouponClaimQueryRequest);

}
