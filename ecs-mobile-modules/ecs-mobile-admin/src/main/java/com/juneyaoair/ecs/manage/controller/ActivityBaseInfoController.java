package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.activity.request.ActivityBaseInfo;
import com.juneyaoair.ecs.manage.dto.activity.request.ActivityChildInfo;
import com.juneyaoair.ecs.manage.dto.activity.request.ActivityCoupon;
import com.juneyaoair.ecs.manage.dto.activity.request.ActivityCouponDetail;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.baseinfo.IBaseInfoService;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * @ClassName ActivityBaseInfoController
 * @Description 活动基础信息配置类
 * <AUTHOR>
 * @Date 2023/11/16 10:52
 * @Version 1.0
 */

@RequestMapping("baseInfo")
@RestController
@RequiredArgsConstructor
@Api(value = "ActivityBaseInfoController", tags = "活动基础配置API")
@Slf4j
public class ActivityBaseInfoController extends HoBaseController{

    @Autowired
    private IBaseInfoService baseInfoService;

    @PostMapping(value = "getList")
    @ApiOperation(value = "信息一览获取", notes = "", httpMethod = "POST")
    public R<PageResult<ActivityBaseInfo>> searchForPage(@RequestBody ActivityBaseInfo activityBaseInfo) {
        try {
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(activityBaseInfo));
            PageDomain pageDomain = TableSupport.buildPageRequest();
            R<PageResult<ActivityBaseInfo>> ret = R.ok(baseInfoService.toCatchList(activityBaseInfo, pageDomain));
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(ret));
            return ret;
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            Context.setContext(null);
        }
        return R.fail();
    }

    @PostMapping(value = "getChildList")
    @ApiOperation(value = "作品信息一览获取", notes = "", httpMethod = "POST")
    public R<PageResult<ActivityChildInfo>> searchChildForPage(@RequestBody ActivityChildInfo activityChildInfo) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        List<ActivityChildInfo> childList = baseInfoService.toCatchChildList(activityChildInfo);
        return getPageData(childList, pageDomain);
    }


    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "addBaseInfo", method = RequestMethod.POST)
    @ApiOperation(value = "新增基础信息配置", httpMethod = "POST")
    public R toAddPrizePoolRecord(@RequestBody ActivityBaseInfo activityBaseInfo) {
        return R.ok(baseInfoService.toAddBaseInfo(activityBaseInfo));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "updateBaseInfo", method = RequestMethod.POST)
    @ApiOperation(value = "更新基础信息配置", httpMethod = "POST")
    public R toUpdatePrizePoolRecord(@RequestBody ActivityBaseInfo activityBaseInfo) {
        return R.ok(baseInfoService.toUpdateBaseInfo(activityBaseInfo));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "addChildInfo", method = RequestMethod.POST)
    @ApiOperation(value = "新增子基础信息配置", httpMethod = "POST")
    public R toAddChildInfo(@RequestBody ActivityChildInfo activityChildInfo) {
        return R.ok(baseInfoService.toAddChildInfo(activityChildInfo));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "updateChildInfo", method = RequestMethod.POST)
    @ApiOperation(value = "更新子基础信息配置", httpMethod = "POST")
    public R toUpdateChildInfo(@RequestBody ActivityChildInfo activityChildInfo) {
        return R.ok(baseInfoService.toUpdateChildInfo(activityChildInfo));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "getChildDetailById", method = RequestMethod.POST)
    @ApiOperation(value = "根据id获取图片信息", httpMethod = "POST")
    public R toCatchChildDetailById(@RequestBody ActivityChildInfo activityChildInfo) {
        return R.ok(baseInfoService.toCatchChildDetailById(activityChildInfo));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "updateChildStatus", method = RequestMethod.POST)
    @ApiOperation(value = "修改状态", httpMethod = "POST")
    public R toUpdateChildStatus(@RequestBody ActivityChildInfo activityChildInfo) {
        return R.ok(baseInfoService.toUpdateChildStatus(activityChildInfo));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "getCouponById", method = RequestMethod.POST)
    @ApiOperation(value = "查询图片信息", httpMethod = "POST")
    public R toCatchCouponById(@RequestBody ActivityBaseInfo activityBaseInfo) {
        return R.ok(baseInfoService.toCatchCouponById(activityBaseInfo));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "importExcel", method = RequestMethod.POST)
    @ApiOperation(value = "导入Excel表格", httpMethod = "POST")
    public R importExcel(@RequestParam MultipartFile file, @RequestParam String dataDesc) throws IOException {
        return R.ok(baseInfoService.importExcel(file,dataDesc));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "addCoupon", method = RequestMethod.POST)
    @ApiOperation(value = "活动添加", httpMethod = "POST")
    public R toAddCoupon(@RequestBody ActivityCoupon activityCoupon) {
        return R.ok(baseInfoService.toAddCoupon(activityCoupon));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "delCoupon", method = RequestMethod.POST)
    @ApiOperation(value = "活动删除", httpMethod = "POST")
    public R toDelCoupon(@RequestBody ActivityCoupon activityCoupon) {
        return R.ok(baseInfoService.toDelCoupon(activityCoupon));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "delCouponDetail", method = RequestMethod.POST)
    @ApiOperation(value = "删除优惠券详情", httpMethod = "POST")
    public R toDelCouponDetail(@RequestBody ActivityCouponDetail activityCouponDetail) {
        return R.ok(baseInfoService.toDelCouponDetail(activityCouponDetail));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "updateCouponDetail", method = RequestMethod.POST)
    @ApiOperation(value = "审核作品", httpMethod = "POST")
    public R toUpdateCouponDetail(@RequestBody ActivityCouponDetail activityCouponDetail) {
        return R.ok(baseInfoService.toUpdateCouponDetail(activityCouponDetail));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "addCouponDetail", method = RequestMethod.POST)
    @ApiOperation(value = "新增券详情", httpMethod = "POST")
    public R toAddCouponDetail(@RequestBody ActivityCouponDetail activityCouponDetail) {
        return R.ok(baseInfoService.toAddCouponDetail(activityCouponDetail));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "getType", method = RequestMethod.GET)
    @ApiOperation(value = "获取类型", httpMethod = "GET")
    public R toCatchType() {
        return R.ok(baseInfoService.toCatchType());
    }
}
