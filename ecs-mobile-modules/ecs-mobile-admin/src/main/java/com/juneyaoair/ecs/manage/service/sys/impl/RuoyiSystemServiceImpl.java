package com.juneyaoair.ecs.manage.service.sys.impl;

import com.google.common.collect.Maps;
import com.juneyaoair.ecs.manage.external.RuoyiSystemBaseService;
import com.juneyaoair.ecs.manage.service.sys.RuoyiSystemService;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.SysDictData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 若依系统服务
 * @created 2024/3/19 10:08
 */
@Service
public class RuoyiSystemServiceImpl implements RuoyiSystemService {

    @Autowired
    private RuoyiSystemBaseService ruoyiSystemBaseService;

    @Override
    public Map<String, SysDictData> dictType(String dictType) {
        R<List<SysDictData>> result = ruoyiSystemBaseService.dictType(dictType);
        if (!Constants.SUCCESS.equals(result.getCode())) {
            throw new HoServiceException(result.getCode(), result.getMsg());
        }
        List<SysDictData> dataList = result.getData();
        Map<String, SysDictData> map = Maps.newHashMap();
        if (CollectionUtils.isEmpty(dataList)) {
            return map;
        }
        for (SysDictData sysDictData : dataList) {
            map.put(sysDictData.getDictValue(), sysDictData);
        }
        return map;
    }

    @Override
    public void checkDictValue(String dictType, String dictValue) {
        if (StringUtils.isAnyBlank(dictType, dictValue)) {
            throw new HoServiceException("字典类型和字典值均不能为空");
        }
        Map<String, SysDictData> sysDictDataMap = dictType(dictType);
        if (null == sysDictDataMap || sysDictDataMap.isEmpty()) {
            throw new HoServiceException("未获取到字段数据");
        }
        SysDictData sysDictData = sysDictDataMap.get(dictValue);
        if (null == sysDictData) {
            throw new HoServiceException("字典值不存在");
        }
    }
}
