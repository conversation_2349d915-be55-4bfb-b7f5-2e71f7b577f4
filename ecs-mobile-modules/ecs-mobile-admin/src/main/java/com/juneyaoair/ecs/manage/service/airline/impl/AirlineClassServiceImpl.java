package com.juneyaoair.ecs.manage.service.airline.impl;


import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.juneyaoair.ecs.manage.dto.airline.airlineclass.AddOrUpDateAirlineClassDTO;
import com.juneyaoair.ecs.manage.dto.airline.airlineclass.AirlineClassDTO;
import com.juneyaoair.ecs.manage.dto.airline.airlineclass.AirlineClassIdRequestDTO;
import com.juneyaoair.ecs.manage.dto.airline.airlineclass.AirlineClassLogDTO;
import com.juneyaoair.ecs.manage.dto.airline.airlineclass.QueryAirlineClassRequestDTO;
import com.juneyaoair.ecs.manage.service.airline.IAirlineClassService;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.AirlineClassLogPO;
import com.juneyaoair.manage.b2c.entity.AirlineClassPO;
import com.juneyaoair.manage.b2c.mapper.AirlineClassLogPOMapper;
import com.juneyaoair.manage.b2c.mapper.AirlineClassPOMapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AirlineClassServiceImpl implements IAirlineClassService {

    @Resource
    private AirlineClassPOMapper airlineClassMapper;

    @Resource
    private AirlineClassLogPOMapper operateLogMapper;

    @Override
    public List<AirlineClassDTO> list(QueryAirlineClassRequestDTO request) {
        // 构建查询条件
        LambdaQueryWrapper<AirlineClassPO> queryWrapper = new LambdaQueryWrapper<>();
        if (request != null && StringUtils.isNotBlank(request.getAirlineCode())) {
            queryWrapper.eq(AirlineClassPO::getAirlineCode, request.getAirlineCode());
        }
        
        // 查询数据
        List<AirlineClassPO> list = airlineClassMapper.selectList(queryWrapper);

        // 转换为DTO
        return list.stream().map(airlineClassPO ->
                AirlineClassDTO.builder()
                        .id(airlineClassPO.getId())
                        .airlineCode(airlineClassPO.getAirlineCode())
                        .businessClass(airlineClassPO.getBusinessClass())
                        .economyClass(airlineClassPO.getEconomyClass())
                        .effectiveDate(airlineClassPO.getEffectiveDate())
                        .expirationDate(airlineClassPO.getExpirationDate())
                        .status(airlineClassPO.getStatus())
                        .createdBy(airlineClassPO.getCreatedBy())
                        .createdTime(airlineClassPO.getCreatedTime())
                        .updatedBy(airlineClassPO.getUpdatedBy())
                        .updatedTime(airlineClassPO.getUpdatedTime())
                        .build()
        ).collect(Collectors.toList());
    }

    @Override
    @DSTransactional
    public void add(AddOrUpDateAirlineClassDTO request) {

        Date date = new Date();
        String user = SecurityUtils.getUsername();
        String airlineClassId = IdUtil.fastUUID();

        airlineClassMapper.insert(AirlineClassPO.builder()
                .id(airlineClassId)
                .airlineCode(request.getAirlineCode())
                .businessClass(request.getBusinessClass())
                .economyClass(request.getEconomyClass())
                .effectiveDate(request.getEffectiveDate())
                .expirationDate(request.getExpirationDate())
                .status(request.getStatus())
                .createdBy(user)
                .createdTime(date)
                .updatedBy(user)
                .updatedTime(date)
                .build());
        operateLogMapper.insert(AirlineClassLogPO.builder()
                .id(IdUtil.fastUUID())
                .airlineClassId(airlineClassId)
                .airlineCode(request.getAirlineCode())
                .businessClass(request.getBusinessClass())
                .economyClass(request.getEconomyClass())
                .effectiveDate(request.getEffectiveDate())
                .expirationDate(request.getExpirationDate())
                .status(request.getStatus())
                .createdBy(user)
                .createdTime(date)
                .build());

    }


    /**
     * 修改
     *
     * @param request
     */
    @Override
    public void update(AddOrUpDateAirlineClassDTO request) {
        // 根据id查询是否存在
        AirlineClassPO airlineClassPO = airlineClassMapper.selectById(request.getId());
        if (airlineClassPO != null) {
            // 更新
            airlineClassMapper.updateById(AirlineClassPO.builder()
                    .id(request.getId())
                    .airlineCode(request.getAirlineCode())
                    .businessClass(request.getBusinessClass())
                    .economyClass(request.getEconomyClass())
                    .effectiveDate(request.getEffectiveDate())
                    .expirationDate(request.getExpirationDate())
                    .status(request.getStatus())
                    .updatedBy(SecurityUtils.getUsername())
                    .updatedTime(new Date())
                    .build());
            // 记录日志
            operateLogMapper.insert(AirlineClassLogPO.builder()
                    .id(IdUtil.fastUUID())
                    .airlineClassId(request.getId())
                    .airlineCode(request.getAirlineCode())
                    .businessClass(request.getBusinessClass())
                    .economyClass(request.getEconomyClass())
                    .effectiveDate(request.getEffectiveDate())
                    .expirationDate(request.getExpirationDate())
                    .status(request.getStatus())
                    .createdBy(SecurityUtils.getUsername())
                    .createdTime(new Date())
                    .build());
        } else {
            throw new HoServiceException("航线舱位不存在");
        }
    }


    /**
     * 删除
     *
     * @param request
     */
    @Override
    public void delete(AirlineClassIdRequestDTO request) {
        // 根据id查询是否存在
        AirlineClassPO airlineClassPO = airlineClassMapper.selectById(request.getId());
        if (airlineClassPO != null) {
            // 删除
            airlineClassMapper.deleteById(request.getId());
        } else {
            throw new HoServiceException("航线舱位不存在");
        }
    }


    /**
     * 查看操作日志
     *
     * @param request
     * @return
     */
    @Override
    public List<AirlineClassLogDTO> logList(AirlineClassIdRequestDTO request) {
        // selelct all by id
        List<AirlineClassLogPO> list = operateLogMapper.selectList(
                new LambdaQueryWrapper<AirlineClassLogPO>().eq(AirlineClassLogPO::getAirlineClassId, request.getId()));
        // convent po to dto,sort by create time desc
        return list.stream().map(po ->
                        AirlineClassLogDTO.builder()
                                .airlineCode(po.getAirlineCode())
                                .businessClass(po.getBusinessClass())
                                .economyClass(po.getEconomyClass())
                                .effectiveDate(po.getEffectiveDate())
                                .expirationDate(po.getExpirationDate())
                                .status(po.getStatus())
                                .createdBy(po.getCreatedBy())
                                .createdTime(po.getCreatedTime())
                                .build()).collect(Collectors.toList())
                .stream().sorted(Comparator.comparing(AirlineClassLogDTO::getCreatedTime).reversed()).collect(Collectors.toList());
    }
}
