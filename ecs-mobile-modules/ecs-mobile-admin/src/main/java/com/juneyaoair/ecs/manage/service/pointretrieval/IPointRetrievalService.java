package com.juneyaoair.ecs.manage.service.pointretrieval;

import com.juneyaoair.ecs.manage.dto.activity.request.pointretrieval.PointRetrievalRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.pointretrieval.PointRetrievalInformation;

import java.util.List;

/**
 * @ClassName IPointRetrievalService
 * @Description 积分找回接口
 * <AUTHOR>
 * @Date 2025/3/17 10:27
 * @Version 1.0
 */
public interface IPointRetrievalService {

    /**
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.activity.response.pointretrieval.PointRetrievalInformation>
     * <AUTHOR>
     * @Description 获取积分找回奖品领取流水表
     * @Date 13:37 2025/3/17
     **/
    List<PointRetrievalInformation> toCatchPointRetrievalList(PointRetrievalRequest pointRetrievalRequest);
}
