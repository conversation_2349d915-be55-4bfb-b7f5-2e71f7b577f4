package com.juneyaoair.ecs.manage.external;

import com.juneyaoair.ecs.manage.dto.base.PageDataResponse;
import com.juneyaoair.ecs.seat.param.CheckinTimeStatusParam;
import com.juneyaoair.ecs.seat.param.Parameter;
import com.juneyaoair.ecs.seat.param.SafeSeatCancelParam;
import com.juneyaoair.ecs.seat.param.SafeSeatParam;
import com.juneyaoair.ecs.seat.param.SafeSeatQueryParam;
import com.juneyaoair.ecs.seat.param.SafeSeatUpdateParam;
import com.juneyaoair.ecs.seat.param.SeatAirportParam;
import com.juneyaoair.ecs.seat.param.SeatBaseOpenQuery;
import com.juneyaoair.ecs.seat.param.SeatBaseOpenSave;
import com.juneyaoair.ecs.seat.param.SeatBaseOpenStatus;
import com.juneyaoair.ecs.seat.param.SeatDiscountRuleParam;
import com.juneyaoair.ecs.seat.param.SeatFlightTypeAreaQuery;
import com.juneyaoair.ecs.seat.param.SeatFlightTypeQuery;
import com.juneyaoair.ecs.seat.param.SeatListQuery;
import com.juneyaoair.ecs.seat.param.SeatChannelParam;
import com.juneyaoair.ecs.seat.param.SeatLockQuery;
import com.juneyaoair.ecs.seat.result.CheckinSegmentTypeTimeResult;
import com.juneyaoair.ecs.seat.result.CussPageResult;
import com.juneyaoair.ecs.seat.result.CussPageResult2;
import com.juneyaoair.ecs.seat.result.SafeSeatInfo;
import com.juneyaoair.ecs.seat.result.SeatAirportResult;
import com.juneyaoair.ecs.seat.result.SeatBaseOpen;
import com.juneyaoair.ecs.seat.result.SeatChannelResult;
import com.juneyaoair.ecs.seat.result.SeatDiscountRuleInfo;
import com.juneyaoair.ecs.seat.result.SeatDiscountRuleResult;
import com.juneyaoair.ecs.seat.result.SeatFlightType;
import com.juneyaoair.ecs.seat.result.SeatFlightTypeArea;
import com.juneyaoair.ecs.seat.result.SeatFlightTypeSeat;
import com.juneyaoair.ecs.seat.result.SeatLockInfo;
import com.juneyaoair.ecs.seat.result.SegmentTypeInfo;
import com.juneyaoair.ecs.upg.param.QueryUpgPriceParam;
import com.juneyaoair.ecs.upg.result.UpgPriceInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 值机选座、升舱服务
 * @created 2023/9/11 15:46
 */
public interface CussService {

    /**
     * 升舱价格查询
     * @param queryUpgPriceParam
     * @return
     */
    List<UpgPriceInfo> queryUpgPrice(QueryUpgPriceParam queryUpgPriceParam);

    /**
     * 更新升舱系统航线缓存数据
     */
    void refreshAirlineBaseSetRedis();

    /**
     * 值机选座-渠道查询
     * @param seatListQuery
     * @return
     */
    CussPageResult<SeatChannelResult> getChannelList(SeatListQuery seatListQuery);

    /**
     * 值机选座-创建渠道
     * @param seatChannelParam
     */
    void createChannel(SeatChannelParam seatChannelParam);

    /**
     * 值机选座-修改渠道
     * @param seatChannelParam
     */
    void updateChannel(SeatChannelParam seatChannelParam);

    /**
     * 值机选座-机场查询
     * @param seatListQuery
     * @return
     */
    CussPageResult<SeatAirportResult> getAirportList(SeatListQuery seatListQuery);

    /**
     * 值机选座-机场创建
     * @param seatAirportParam
     */
    void createAirport(SeatAirportParam seatAirportParam);

    /**
     * 值机选座-机场修改
     * @param seatAirportParam
     */
    void updateAirport(SeatAirportParam seatAirportParam);

    /**
     * 查询全部值机航线类型
     * @return
     */
    List<SegmentTypeInfo> getSegmentType();

    /**
     * 查询当前机场值机开放时间航线类型配置信息
     * @param airportCode
     * @return
     */
    List<CheckinSegmentTypeTimeResult> checkinSegmentTypeTimeSearch(String airportCode);

    /**
     * 值机选座-保存机场值机开放时间
     * @param checkinSegmentTypeTimeList
     */
    void checkinSegmentTypeTimeUpdate(List<CheckinSegmentTypeTimeResult> checkinSegmentTypeTimeList);

    /**
     * 值机选座-更新机场值机开放时间状态
     * @param checkinTimeStatusParam
     */
    void changeCheckInTimeStatus(CheckinTimeStatusParam checkinTimeStatusParam);

    /**
     * 值机选座-机型查询
     * @param seatFlightTypeQuery
     * @return
     */
    CussPageResult<SeatFlightType> getFlightTypeList(SeatFlightTypeQuery seatFlightTypeQuery);

    /**
     * 值机选座-更新机型信息
     * @param seatFlightType
     * @return
     */
    Boolean updateFlightType(SeatFlightType seatFlightType);

    /**
     * 值机选座-指定ID机型查询
     * @param flightTypeId
     * @return
     */
    SeatFlightType getFlightType(Long flightTypeId);

    /**
     * 值机选座-更新机型座位图信息
     * @param seatFlightTypeSeat
     * @return
     */
    void createFlightTypeSeat(SeatFlightTypeSeat seatFlightTypeSeat);

    /**
     * 值机选座-机型区域划分查询
     * @param seatFlightTypeAreaQuery
     * @return
     */
    CussPageResult<SeatFlightTypeArea> getFlightTypeAreaList(SeatFlightTypeAreaQuery seatFlightTypeAreaQuery);

    /**
     * 值机选座-保存机型区域划分信息
     * @param seatFlightTypeArea
     */
    void saveFlightTypeArea(SeatFlightTypeArea seatFlightTypeArea);

    /**
     * 值机选座-基础开放查询
     * @param seatBaseOpenQuery
     * @return
     */
    CussPageResult<SeatBaseOpen> getBaseOpenList(SeatBaseOpenQuery seatBaseOpenQuery);

    /**
     * 值机选座-保存基础开放时间信息
     * @param seatBaseOpenQuery
     */
    void saveBaseOpen(SeatBaseOpenSave seatBaseOpenQuery);

    /**
     * 值机选座-修改基础开放时间状态
     * @param seatBaseOpenStatus
     */
    void changeBaseOpenStatus(SeatBaseOpenStatus seatBaseOpenStatus);

    /**
     * 值机选座-常规价格折扣查询
     * @param seatDiscountRuleParam
     * @return
     */
    CussPageResult2<SeatDiscountRuleResult> getSeatDiscountRule(SeatDiscountRuleParam seatDiscountRuleParam);

    /**
     * 值机选座-新增常规价格折扣
     * @param seatDiscountRuleInfo
     */
    void saveSeatDiscountRule(SeatDiscountRuleInfo seatDiscountRuleInfo);

    /**
     * 值机选座-更新常规价格折扣
     * @param seatDiscountRuleInfo
     */
    void updateSeatDiscountRule(SeatDiscountRuleInfo seatDiscountRuleInfo);

    /**
     * 查询参数信息
     * @param parameterKey
     * @return
     */
    Parameter getParameter(String parameterKey);

    /**
     * 更新参数信息
     * @param parameter
     */
    void updateParameter(Parameter parameter);

    /**
     * 值机选座-座位关闭清单查询
     * @param seatLockQuery
     * @return
     */
    CussPageResult<SeatLockInfo> getSeatLockList(SeatLockQuery seatLockQuery);

    /**
     * 值机选座-保存座位关闭信息
     * @param seatLockInfo
     */
    void saveSeatLock(SeatLockInfo seatLockInfo);

    /**
     * 生成安全员座位信息
     * @param safeSeatParam
     */
    void createSafeSeat(SafeSeatParam safeSeatParam);

    /**
     * 更新安全员座位信息
     * @param safeSeatUpdateParam
     */
    void updateSafeSeat(SafeSeatUpdateParam safeSeatUpdateParam);

    /**
     * 更新安全员座位信息
     * @param safeSeatQueryParam
     * @return
     */
    PageDataResponse<SafeSeatInfo> getSafeSeatList(SafeSeatQueryParam safeSeatQueryParam);

    /**
     * 执行取消安全员座位旅客操作
     * @param safeSeatCancelParam
     */
    void cancelSafeSeat(SafeSeatCancelParam safeSeatCancelParam);

}
