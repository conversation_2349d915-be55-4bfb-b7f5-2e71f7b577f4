package com.juneyaoair.ecs.excel.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description 升舱规则航线
 * @created 2023/8/21 13:19
 */
@Data
public class UpgAirlineRuleSegment {

    @ExcelProperty(index = 0, value = "出发机场")
    @Length(min = 3, max = 3, message = "出发机场格式不正确")
    @NotBlank(message = "出发机场不能为空")
    @ApiModelProperty(value = "出发机场")
    private String depAirportCode;

    @ExcelProperty(index = 1, value = "到达机场")
    @Length(min = 3, max = 3, message = "到达机场格式不正确")
    @NotBlank(message = "到达机场不能为空")
    @ApiModelProperty(value = "到达机场")
    private String arrAirportCode;

}
