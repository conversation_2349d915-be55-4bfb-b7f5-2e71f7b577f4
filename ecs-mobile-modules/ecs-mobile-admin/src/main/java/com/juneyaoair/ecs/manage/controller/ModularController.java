package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.manage.b2c.entity.ModularPO;
import com.juneyaoair.ecs.manage.dto.modular.ModularDTO;
import com.juneyaoair.ecs.manage.dto.modular.ModularIdDTO;
import com.juneyaoair.ecs.manage.dto.modular.ModularSyncDTO;
import com.juneyaoair.ecs.manage.dto.modular.ModularQueryReqDTO;
import com.juneyaoair.manage.b2c.service.IModularService;
import com.juneyaoair.manage.b2c.service.IModularVersionManageService;
import com.juneyaoair.manage.b2c.service.IDictvalueService;
import com.juneyaoair.manage.b2c.entity.dict.DictvaluePO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
@Api(value = "ModularController", tags = "服务配置管理")
@RestController
@RequestMapping("/modular")
public class ModularController extends HoBaseController {
    @Autowired
    private IModularService modularService;
    @Autowired
    private IDictvalueService dictvalueService;
    @Autowired
    private IModularVersionManageService modularVersionManageService;

    @PostMapping("/pageList")
    @ApiOperation(value = "服务配置列表分页查询", notes = "")
    public R<PageResult<ModularDTO>> pageList(@RequestBody ModularQueryReqDTO queryReq) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        
        ModularPO modularPO = new ModularPO();
        modularPO.setName(queryReq.getName());
        modularPO.setModularBm(queryReq.getSuperiorModule());
        modularPO.setChannelCode(queryReq.getChannelCode());
        modularPO.setUrl(queryReq.getUrl());
        
        List<ModularDTO> list = modularService.selectModularList(modularPO);
        return getPageData(list, pageDomain);
    }

    @PostMapping("/listAll")
    @ApiOperation(value = "服务配置列表全量查询", notes = "")
    public R<List<ModularDTO>> listAll(@RequestBody ModularPO modularPO) {
        List<ModularDTO> list = modularService.selectModularList(modularPO);
        return R.ok(list);
    }

    @PostMapping("/queryParentModular")
    @ApiOperation(value = "查询所有上级模块", notes = "")
    public R<List<DictvaluePO>> queryParentModular() {
        DictvaluePO value = new DictvaluePO();
        value.setDtCode("SERVICE");
        List<DictvaluePO> list = dictvalueService.findDICTValueList(value);
        return R.ok(list);
    }

    @PostMapping("/addModular")
    @ApiOperation(value = "新增模块", notes = "")
    public R<Void> addModular(@RequestBody ModularDTO modularDTO) {
        ModularPO modularPO = new ModularPO();
        BeanUtils.copyProperties(modularDTO, modularPO);
        modularPO.setEventmodule(modularDTO.getEventModule());
        modularPO.setEventtype(modularDTO.getEventType());
        modularPO.setWinname(modularDTO.getWinName());
        
        modularPO.setId(UUID.randomUUID().toString());
        modularPO.setCreateTime(new Date());
        modularPO.setUpdateTime(new Date());
        modularPO.setIsUsed("Y");
        switch (modularPO.getModularBm()) {
            // 机场交通
            case "AIRPORT_TRAFFIC":
                if (StringUtils.isBlank(modularPO.getGroupType())) {
                    return R.fail("分组类型不能为空");
                }
                break;
            default:
                break;
        }
        boolean success = modularService.saveModularWithVersion(modularPO);
        return success ? R.ok() : R.fail("新增失败");
    }

    @PostMapping("/updateModular")
    @ApiOperation(value = "更新模块", notes = "")
    public R<Void> updateModular(@RequestBody ModularDTO modularDTO) {
        ModularPO modularPO = new ModularPO();
        BeanUtils.copyProperties(modularDTO, modularPO);
        modularPO.setEventmodule(modularDTO.getEventModule());
        modularPO.setEventtype(modularDTO.getEventType());
        modularPO.setWinname(modularDTO.getWinName());
        
        modularPO.setUpdateTime(new Date());
        boolean success = modularService.updateModularWithVersion(modularPO);
        return success ? R.ok() : R.fail("更新失败");
    }

    @PostMapping("/synchronous")
    @ApiOperation(value = "同步版本号", notes = "")
    public R<Void> synchronous(@RequestBody ModularSyncDTO syncDTO) {
        boolean success = modularService.synchronousWithVersion(syncDTO);
        return success ? R.ok() : R.fail("同步失败：未找到相关服务配置或同步过程出错");
    }

    @PostMapping("/queryModifyModular")
    @ApiOperation(value = "查询单个模块详情", notes = "")
    public R<ModularDTO> queryModifyModular(@RequestBody ModularIdDTO modularIdDTO) {
        ModularDTO result = modularService.queryModifyModular(modularIdDTO.getId());
        return result != null ? R.ok(result) : R.fail("查询无此记录");
    }

    @PostMapping("/deleteModular")
    @ApiOperation(value = "删除模块", notes = "")
    public R<Void> deleteModular(@RequestBody ModularIdDTO modularIdDTO) {
        ModularPO modularPO = new ModularPO();
        modularPO.setId(modularIdDTO.getId());
        modularPO.setIsUsed("N");
        modularPO.setUpdateTime(new Date());
        boolean success = modularService.deleteModularWithVersion(modularPO);
        return success ? R.ok() : R.fail("删除失败");
    }
}
