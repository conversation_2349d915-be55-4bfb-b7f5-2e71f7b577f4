package com.juneyaoair.ecs.manage.service.MemberRightsManage.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.github.pagehelper.Page;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.ecs.http.bean.HttpResult;
import com.juneyaoair.ecs.http.service.HttpClientService;
import com.juneyaoair.ecs.manage.constant.ManageConstant;
import com.juneyaoair.ecs.manage.dto.base.PageDataResponse;
import com.juneyaoair.ecs.manage.dto.file.HoFile;
import com.juneyaoair.ecs.manage.dto.memberRights.MemberLevelDTO;
import com.juneyaoair.ecs.manage.dto.memberRights.MemberRightsDTO;
import com.juneyaoair.ecs.manage.dto.memberRights.MemberRightsPageReq;
import com.juneyaoair.ecs.manage.properties.ThirdAppUrlSet;
import com.juneyaoair.ecs.manage.service.MemberRightsManage.MemberRightsManageService;
import com.juneyaoair.ecs.manage.service.file.IFileUploadService;
import com.juneyaoair.ecs.manage.util.HoPageUtils;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.MemberLevelManagePO;
import com.juneyaoair.manage.b2c.entity.MemberRightsPO;
import com.juneyaoair.manage.b2c.mapper.MemberLevelManageMapper;
import com.juneyaoair.manage.b2c.mapper.MemberRightsManageMapper;
import com.juneyaoair.manage.thirdapi.crm.*;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import java.util.function.BiConsumer;

import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2025/8/11 10:04
 * @Version 1.0
 */
@Slf4j
@Service
public class MemberRightsManageServiceImpl implements MemberRightsManageService {

    @Autowired
    MemberRightsManageMapper memberRightsMapper;

    @Autowired
    MemberLevelManageMapper memberLevelMapper;

    @Autowired
    private IFileUploadService iFileService;

    @Autowired
    private HttpClientService httpClientService;

    @Autowired
    private ThirdAppUrlSet thirdAppUrlSet;

    @Autowired
    SqlSessionFactory sqlSessionFactory;

    @Override
    public PageDataResponse memberRightsListPageQuery(MemberRightsPageReq memberRightsPageReq) {

        PageDataResponse pageDataResponse = new PageDataResponse();
        PageDomain pageDomain = new PageDomain();

        try{
            //设置分页
            pageDomain.setPageNum(memberRightsPageReq.getPageNum());
            pageDomain.setPageSize(memberRightsPageReq.getPageSize());
            HoPageUtils.startPageBy(pageDomain);

            Page<MemberRightsPO> page = memberRightsMapper.memberRightsListPageQuery(memberRightsPageReq);

            long total = page.getTotal();
            List<MemberRightsPO> result = page.getResult();
            pageDataResponse.setTotal(total);
            pageDataResponse.setData(result);
            return pageDataResponse;
        }catch (Exception e){
            log.error("查询权益列表发生错误:{}", e.getMessage());
            throw new HoServiceException("查询权益列表发生错误",e.getMessage());
        }
    }

    @Override
    public String fileUpload(MultipartFile file) {
        HoFile hoFile = new HoFile();
        try{
            hoFile = iFileService.uploadFile(file,null, null, true);
        }catch (Exception e){
            throw new HoServiceException("文件上传失败");
        }
        String url = hoFile.getUrl();
        return url;
    }

    @Override
    public void addRights(MemberRightsDTO memberRightsDTO) {
        HttpResult httpResult = httpClientService.doGet(thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.REMOVE_RIGHTS_CACHE, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("权益缓存清空失败，请稍后重试");
        }
        MemberRightsPO memberRightsPO = new MemberRightsPO();
        BeanUtils.copyProperties(memberRightsDTO, memberRightsPO);
        memberRightsPO.setId(HOStringUtil.newGUID());
        memberRightsPO.setIsUsed("Y");
        memberRightsPO.setCreateUser(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(memberRightsPO.getCreateUser())) {
            throw new HoServiceException("更新人不可为空");
        }
        memberRightsPO.setCreateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(memberRightsPO.getCreateTime().toString())) {
            throw new HoServiceException("更新时间不可为空");
        }
        memberRightsPO.setUpdateUser(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(memberRightsPO.getUpdateUser())) {
            throw new HoServiceException("更新人不可为空");
        }
        memberRightsPO.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(memberRightsPO.getUpdateTime().toString())) {
            throw new HoServiceException("更新时间不可为空");
        }
        int effectLine = memberRightsMapper.addRights(memberRightsPO);
        if(effectLine>0){
            return;
        }else {
            throw new HoServiceException("新增权益失败");
        }
    }

    @Override
    @DSTransactional
    public void deleteRightsById(String id) {
        if(StringUtils.isEmpty(id)) {
            throw new HoServiceException("缺少指定的目标ID");
        }
        HttpResult httpResult = httpClientService.doGet(thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.REMOVE_RIGHTS_CACHE, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("权益缓存清空失败，请稍后重试");
        }
        MemberRightsPO memberRightsPO = new MemberRightsPO();
        memberRightsPO.setId(id);
        memberRightsPO.setIsUsed("N");
        memberRightsPO.setUpdateUser(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(memberRightsPO.getUpdateUser())) {
            throw new HoServiceException("更新人不可为空");
        }
        memberRightsPO.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(memberRightsPO.getUpdateTime().toString())) {
            throw new HoServiceException("更新时间不可为空");
        }
        int effectLine = memberRightsMapper.updateRightsById(memberRightsPO);
        if(effectLine>0){
            return;
        }else {
            throw new HoServiceException("删除权益失败");
        }
    }

    @Override
    public void updateRightsById(MemberRightsDTO memberRightsDTO) {
        HttpResult httpResult = httpClientService.doGet(thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.REMOVE_RIGHTS_CACHE, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("权益缓存清空失败，请稍后重试");
        }
        MemberRightsPO memberRightsPO = new MemberRightsPO();
        BeanUtils.copyProperties(memberRightsDTO, memberRightsPO);
        memberRightsPO.setUpdateUser(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(memberRightsPO.getUpdateUser())) {
            throw new HoServiceException("更新人不可为空");
        }
        memberRightsPO.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(memberRightsPO.getUpdateTime().toString())) {
            throw new HoServiceException("更新时间不可为空");
        }
        int effectLine = memberRightsMapper.updateRightsById(memberRightsPO);
        if(effectLine>0){
            return;
        }else {
            throw new HoServiceException("修改权益失败");
        }
    }

    @Override
    public MemberRightsPO getRightsById(String id) {
        if(StringUtils.isEmpty(id)) {
            throw new HoServiceException("缺少指定的目标ID");
        }
        try{
            MemberRightsPO memberRightsPO = memberRightsMapper.getRightsById(id);
            return memberRightsPO;
        }catch (Exception e){
            log.error("查询权益信息发生错误:{}", e.getMessage());
            throw new HoServiceException("查询权益信息发生错误",e.getMessage());
        }
    }

    @Override
    public List<MemberLevelManagePO> searchAllMemberLevel() {
        try {
            List<MemberLevelManagePO> memberLevelPOS = memberLevelMapper.selectAll();
            return memberLevelPOS;
        }catch (Exception e){
            log.error("查询权益规则发生错误:{}", e.getMessage());
            throw new HoServiceException("查询权益规则发生错误",e.getMessage());
        }

    }

    @Override
    public String syncCRM(PtApiCRMRequest<Map<String, String[]>> ptApiCRMRequest, String[] strings, String crmUrl) throws SQLException {

        HttpResult httpResult0 = httpClientService.doGet(thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.REMOVE_RIGHTS_CACHE, null);
        if (!httpResult0.isResult()) {
            throw new HoServiceException("权益缓存清空失败，请稍后重试");
        }

        HttpResult httpResult = httpClientService.doPost(ptApiCRMRequest, crmUrl, null);
        String crmResponse = httpResult.getResponse();
        //获取crm权益信息
        PtCRMResponse<CrmMemberRightsRuleDetailResponse> crmRightsResponseDTO = (PtCRMResponse<CrmMemberRightsRuleDetailResponse>)
                JsonUtil.jsonToBean(crmResponse, new TypeToken<PtCRMResponse<CrmMemberRightsRuleDetailResponse>>() {}.getType());

        if (crmRightsResponseDTO.getCode() == 0 && crmRightsResponseDTO.getIsSuccess()) {
            String desc = "";
            List<CrmMemberRightsRuleSoaDTO> memberRightsRuleSoaDTOList = crmRightsResponseDTO.getData().getRuleInfos();
            if(!HOStringUtil.isNullOrEmpty(memberRightsRuleSoaDTOList)){
                for (CrmMemberRightsRuleSoaDTO memberRightsRuleSoaDTO : memberRightsRuleSoaDTOList) {
                    String ruleId = memberRightsRuleSoaDTO.getRuleId() + "";
                    //会员级别同步
                    addAndUpdateMemberLevel(ruleId, memberRightsRuleSoaDTO.getRuleName(),memberRightsRuleSoaDTO.getRuleType());
                    //会员系统的权益
                    List<CrmRightInfoDetailDTO> rightInfoDetailDTOList = memberRightsRuleSoaDTO.getDetails();
                    Map<String, CrmRightInfoDetailDTO> crmRightMap = new HashMap<>();
                    if (!HOStringUtil.isNullOrEmpty(rightInfoDetailDTOList)) {
                        for (CrmRightInfoDetailDTO crmRightInfoDetailDTO : rightInfoDetailDTOList) {
                            crmRightMap.put(ruleId + crmRightInfoDetailDTO.getRightsRecordId(), crmRightInfoDetailDTO);
                        }
                    }
                    List<MemberRightsPO> deleteList = new ArrayList<>();
                    List<MemberRightsPO> addList = new ArrayList<>();
                    List<MemberRightsPO> updateList = new ArrayList<>();
                    //数据库会员权益信息进行对比，以CRM会员信息为主
                    MemberRightsPO memberRightsPO = new MemberRightsPO();
                    memberRightsPO.setIsUsed("Y");
                    memberRightsPO.setRuleId(ruleId);
                    List<MemberRightsPO> allEnableMemberRightPOList = memberRightsMapper.selectByRuleId(memberRightsPO);
                    Map<String, Object> localRightMap = new HashMap<>();
                    if (!HOStringUtil.isNullOrEmpty(allEnableMemberRightPOList)) {
                        for (MemberRightsPO memberRights : allEnableMemberRightPOList) {
                            String key = memberRights.getRuleId() + memberRights.getRightsRecordId();
                            localRightMap.put(key, memberRights);
                            //CRM会员系统中存在执行更新操作，不存在执行删除操作
                            if (crmRightMap.containsKey(key)) {
                                CrmRightInfoDetailDTO crmRightInfoDetailDTO = crmRightMap.get(key);
                                MemberRightsPO update = new MemberRightsPO();
                                update.setRuleId(ruleId);
                                update.setId(memberRights.getId());
                                update.setRightsRecordId(crmRightInfoDetailDTO.getRightsRecordId());
                                update.setRightsNameCn(crmRightInfoDetailDTO.getRightsNameCn());
                                update.setRightsDescCn(crmRightInfoDetailDTO.getRightsDescCn());
                                update.setRightsNameEn(crmRightInfoDetailDTO.getRightsNameEn());
                                update.setRightsDescEn(crmRightInfoDetailDTO.getRightsDescEn());
                                update.setReceiveNumber(crmRightInfoDetailDTO.getNumber());
                                update.setOnlyDisplay(crmRightInfoDetailDTO.isOnlyDisplay() ? "Y" : "N");
                                update.setUpdateUser(SecurityUtils.getUsername());
                                update.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
                                updateList.add(update);
                            } else {
                                MemberRightsPO delete = new MemberRightsPO();
                                delete.setId(memberRights.getId());
                                delete.setRuleId(memberRights.getRuleId());
                                delete.setRightsRecordId(memberRights.getRightsRecordId());
                                delete.setIsUsed("N");
                                deleteList.add(delete);
                            }
                        }
                    }
                    //筛选需要新增的权益
                    if (!HOStringUtil.isNullOrEmpty(rightInfoDetailDTOList)) {
                        for (CrmRightInfoDetailDTO crmRightInfoDetailDTO : rightInfoDetailDTOList) {
                            String key = ruleId + crmRightInfoDetailDTO.getRightsRecordId();
                            if (!localRightMap.containsKey(key)) {
                                MemberRightsPO add = new MemberRightsPO();
                                add.setId(HOStringUtil.newGUID());
                                add.setRuleId(ruleId);
                                add.setRightsRecordId(crmRightInfoDetailDTO.getRightsRecordId());
                                add.setReceiveNumber(crmRightInfoDetailDTO.getNumber());
                                add.setRightsNameCn(crmRightInfoDetailDTO.getRightsNameCn());
                                add.setRightsDescCn(crmRightInfoDetailDTO.getRightsDescCn());
                                add.setRightsNameEn(crmRightInfoDetailDTO.getRightsNameEn());
                                add.setRightsDescEn(crmRightInfoDetailDTO.getRightsDescEn());
                                add.setOnlyDisplay(crmRightInfoDetailDTO.isOnlyDisplay() ? "Y" : "N");
                                add.setIsUsed("Y");
                                //默认使用会员系统的名称，可修改
                                add.setName(crmRightInfoDetailDTO.getRightsNameCn());
                                add.setCreateUser(SecurityUtils.getUsername());
                                add.setCreateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
                                add.setUpdateUser(SecurityUtils.getUsername());
                                add.setUpdateTime(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
                                addList.add(add);
                            }
                        }
                    }
                    if (!HOStringUtil.isNullOrEmpty(deleteList)) {
                        deleteByBatch(deleteList);
                        desc+="删除权益" + deleteList.size() + "条;";
                        log.info("【权益服务】删除权益" + deleteList.size() + "条");
                    }
                    if (!HOStringUtil.isNullOrEmpty(updateList)) {
                        updateByBatch(updateList);
                        desc+="更新权益" + updateList.size() + "条;";
                        log.info("【权益服务】更新权益" + updateList.size() + "条");
                    }
                    if (!HOStringUtil.isNullOrEmpty(addList)) {
                        insertByBatch(addList);
                        desc+="新增权益" + addList.size() + "条;";
                        log.info("【权益服务】新增权益" + addList.size() + "条");
                    }
                }
            }else{
                //查询不到相关权益删除原有信息
                List<MemberRightsPO> deleteList = new ArrayList<>();
                for(String string:strings){
                    MemberRightsPO delete = new MemberRightsPO();
                    delete.setRuleId(string);
                    delete.setIsUsed("N");
                    deleteList.add(delete);
                }
                deleteByRuleIdBatch(deleteList);
                desc+="删除权益" + deleteList.size() + "条;";
                log.info("【权益服务】删除权益" + deleteList.size() + "条");
            }
            return desc;
        } else {
            throw new HoServiceException("同步CRM会员权益信息错误!");
        }
    }

    @Override
    public MemberLevelManagePO searchLevelById(String id) {
        if(StringUtils.isEmpty(id)){
            throw new HoServiceException("id不能为空");
        }
        try{
            MemberLevelManagePO memberLevel = memberLevelMapper.selectByPrimaryKey(id);
            return memberLevel;
        }catch (Exception e){
            throw new HoServiceException("会员权益级别查询失败");
        }

    }

    @Override
    public void updateMemberLevel(MemberLevelDTO memberLevelDTO) {
        HttpResult httpResult = httpClientService.doGet(thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.REMOVE_RIGHTS_CACHE, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("权益缓存清空失败，请稍后重试");
        }
        MemberLevelManagePO memberLevelPO = new MemberLevelManagePO();
        BeanUtils.copyProperties(memberLevelDTO,memberLevelPO);
        int effectLine = memberLevelMapper.updateByPrimaryKeySelective(memberLevelPO);
        if(effectLine>0){
            return;
        }else {
            throw new HoServiceException("修改权益级别失败");
        }
    }

    private int addAndUpdateMemberLevel(String ruleId, String ruleName,String ruleType) {

        if(StringUtils.isEmpty(ruleId)){
            throw new HoServiceException("ruleId不能为空");
        }
        MemberLevelManagePO memberLevel = memberLevelMapper.selectByPrimaryKey(ruleId);
        if (memberLevel == null) {
            memberLevel = new MemberLevelManagePO();
            memberLevel.setRuleId(ruleId);
            memberLevel.setSerialNumber(Integer.valueOf(ruleId));
            memberLevel.setRuleName(ruleName);
            memberLevel.setRuleNameRemark(ruleName);
            memberLevel.setType(ruleType);
            return memberLevelMapper.insertSelective(memberLevel);
        } else {
            memberLevel.setRuleId(ruleId);
            memberLevel.setSerialNumber(Integer.valueOf(ruleId));
            memberLevel.setRuleName(ruleName);
            memberLevel.setType(ruleType);
            return memberLevelMapper.updateByPrimaryKeySelective(memberLevel);
        }
    }

    // 更新批处理
    private void updateByBatch(List<MemberRightsPO> list) throws SQLException {
        executeBatchOperation(list, (mapper, item) -> mapper.updateSingle(item));
    }

    // 删除批处理
    private void deleteByBatch(List<MemberRightsPO> list) throws SQLException {
        executeBatchOperation(list, (mapper, item) -> mapper.deleteSingle(item));
    }

    // 插入批处理
    private void insertByBatch(List<MemberRightsPO> addlist) throws SQLException {
        executeBatchOperation(addlist, (mapper, item) -> mapper.insertSingle(item));
    }

    // 按规则ID删除批处理
    private void deleteByRuleIdBatch(List<MemberRightsPO> list) throws SQLException {
        executeBatchOperation(list, (mapper, item) -> mapper.deleteSingleByRuleId(item));
    }

    // 批处理执行方法
    private void executeBatchOperation(
            List<MemberRightsPO> list,
            BiConsumer<MemberRightsManageMapper, MemberRightsPO> operation
    ) throws SQLException {
        if (list == null || list.isEmpty()) {
            log.info("空列表，跳过批处理操作");
            return;
        }

        try (SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH)) {
            MemberRightsManageMapper mapper = sqlSession.getMapper(MemberRightsManageMapper.class);

            // 执行所有操作
            for (MemberRightsPO item : list) {
                operation.accept(mapper, item);
            }
            sqlSession.commit();
            log.info("批处理操作完成，记录数: {}", list.size());
            return;
        } catch (Exception e) {
            throw new SQLException("批量操作失败", e);
        }
    }
}
