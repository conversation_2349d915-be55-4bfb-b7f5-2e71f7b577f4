package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.activity.request.paymethod.PayMethodActivityDTO;
import com.juneyaoair.ecs.manage.dto.activity.request.paymethod.PayMethodDTO;
import com.juneyaoair.ecs.manage.dto.activity.request.paymethod.PayMethodPageReq;
import com.juneyaoair.ecs.manage.dto.base.PageDataResponse;
import com.juneyaoair.ecs.manage.service.paymethod.PayMethodService;
import com.juneyaoair.manage.b2c.entity.PayMethodPO;
import com.juneyaoair.manage.b2c.entity.activity.PayMethodActivityPO;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

@RequestMapping("payMethod")
@RestController
@Api(value = "payMethodController", tags = "支付方式配置相关API")
@Slf4j
public class PayMethodController {

    @Autowired
    PayMethodService payMethodService;

    @PostMapping("/showPayMethodList")
    @ApiOperation(value = "支付方式配置列表分页查询", notes = "", httpMethod = "POST")
    public R<PageDataResponse> getPayMethodList(@RequestBody PayMethodPageReq payMethodPageReq){
        log.info("支付方式配置列表分页查询，参数为：{}",payMethodPageReq);
        PageDataResponse pageDataResponse = payMethodService.payMethodListPageQuery(payMethodPageReq);
        return R.ok(pageDataResponse);
    }

    @GetMapping("getPayMethod/{id}")
    @ApiOperation(value = "根据id查询支付方式信息", notes = "", httpMethod = "GET")
    public R<PayMethodPO> getPayMethodById(@PathVariable String id){
        log.info("查询id为：{}的支付方式信息",id);
        PayMethodPO payMethodPO = payMethodService.getPayMethodById(id);
        return R.ok(payMethodPO);
    }

    @GetMapping("getPayMethodActivity/{id}")
    @ApiOperation(value = "根据id查询活动信息", notes = "", httpMethod = "GET")
    public R<PayMethodActivityDTO> getPayMethodActivityById(@PathVariable String id){
        log.info("查询id为：{}的活动信息",id);
        PayMethodActivityDTO payMethodActivityDTO = payMethodService.getPayMethodActivityById(id);
        return R.ok(payMethodActivityDTO);
    }

    @PostMapping("checkChannelAndProduct")
    @ApiOperation(value = "检查支付方式的渠道和产品类型", notes = "", httpMethod = "POST")
    public R checkChannelAndProduct(@RequestBody PayMethodDTO payMethodDTO){
        log.info("待检查的支付方式信息为：{}",payMethodDTO);
        R r = new R<>();
        String msg = payMethodService.checkChannelAndProduct(payMethodDTO);
        if(msg != null){
            r.setCode(10002);
            r.setMsg(msg);
            return r;
        }
        return R.ok();
    }

    @PostMapping("updatePayMethod")
    @ApiOperation(value = "根据id修改支付方式", notes = "", httpMethod = "POST")
    public R updatePayMethodById(@RequestBody PayMethodDTO payMethodDTO){
        log.info("待修改的支付方式信息为：{}",payMethodDTO);
        payMethodService.updatePayMethodById(payMethodDTO);
        return R.ok();
    }

    @PostMapping("updatePayMethodActivity")
    @ApiOperation(value = "根据id修改活动", notes = "", httpMethod = "POST")
    public R updatePayMethodActivityById(@RequestBody PayMethodActivityDTO payMethodActivityDTO){
        log.info("待修改的支付方式信息为：{}",payMethodActivityDTO);
        payMethodService.updatePayMethodActivityById(payMethodActivityDTO);
        return R.ok();
    }

    @PostMapping("parseImage")
    @ApiOperation(value = "解析图片", notes = "", httpMethod = "POST")
    public R<String> parseImage(@RequestParam("file") MultipartFile file){
        log.info("上传的图片为：{}",file);
        String encode = payMethodService.parseImage(file);
        return R.ok(encode);
    }

    @PostMapping("changePayMethodStatus/{status}")
    @ApiOperation(value = "修改支付方式状态", notes = "", httpMethod = "POST")
    public R changePayMethodStatus(@PathVariable String status, String id){
        log.info("待修改的支付方式id为：{},修改支付方式状态为：{}",id, status);
        payMethodService.changePayMethodStatus(status, id);
        return R.ok();
    }

    @PostMapping("changePayMethodActivityStatus/{status}")
    @ApiOperation(value = "修改活动状态", notes = "", httpMethod = "POST")
    public R changePayMethodActivityStatus(@PathVariable String status, String id){
        log.info("待修改的活动id为：{},修改活动状态为：{}",id, status);
        payMethodService.changePayMethodActivityStatus(status, id);
        return R.ok();
    }

    @PostMapping("deletePayMethodActivity")
    @ApiOperation(value = "删除活动", notes = "", httpMethod = "POST")
    public R deletePayMethodActivity(String id){
        log.info("待删除活动的id为：{}",id);
        payMethodService.deletePayMethodActivityById(id);
        return R.ok();
    }

    @PostMapping("addPayMethod")
    @ApiOperation(value = "添加支付方式", notes = "", httpMethod = "POST")
    public R<PayMethodPO> addPayMethod(@RequestBody PayMethodDTO payMethodDTO){
        log.info("添加支付方式：{}",payMethodDTO);
        PayMethodPO payMethodPO = payMethodService.addPayMethod(payMethodDTO);
        return R.ok(payMethodPO);
    }

    @PostMapping("addPayMethodActivity")
    @ApiOperation(value = "添加活动", notes = "", httpMethod = "POST")
    public R<PayMethodActivityPO> addPayMethodActivity(@RequestBody PayMethodActivityDTO payMethodActivityDTO){
        log.info("添加活动：{}",payMethodActivityDTO);
        PayMethodActivityPO payMethodActivityPO = payMethodService.addPayMethodActivity(payMethodActivityDTO);
        return R.ok(payMethodActivityPO);
    }

    @GetMapping("getActivityByPayMethodId/{payMethodId}")
    @ApiOperation(value = "根据支付方式id查询活动信息", notes = "", httpMethod = "GET")
    public R<List<PayMethodActivityPO>> getActivityByPayMethodId(@PathVariable String payMethodId){
        log.info("查询支付方式id为：{}的活动信息",payMethodId);
        List<PayMethodActivityPO> payMethodActivityPOS = payMethodService.getActivityByPayMethodId(payMethodId);
        return R.ok(payMethodActivityPOS);
    }

    @PostMapping("refreshRedis")
    @ApiOperation(value = "刷新缓存", notes = "", httpMethod = "POST")
    public R refreshRedis(
            @RequestParam(value = "merchantPayment",required = false) String merchantPayment,
            @RequestParam(value = "channel",required = false) String channel,
            @RequestParam(value = "payProductType",required = false) String payProductType){
        log.info("商户类型：{}，渠道：{}，支付产品类型：{}",merchantPayment,channel,payProductType);
        payMethodService.refreshRedis(merchantPayment,channel,payProductType);
        return R.ok();
    }

    @PostMapping("checkAuth")
    @ApiOperation(value = "检查权限", notes = "", httpMethod = "POST")
    public R<Boolean> checkAuth(){
        Boolean isSuperAdmin = payMethodService.checkAuth();
        return R.ok(isSuperAdmin);
    }
}
