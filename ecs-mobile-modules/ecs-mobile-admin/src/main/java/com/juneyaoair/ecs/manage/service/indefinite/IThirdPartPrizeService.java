package com.juneyaoair.ecs.manage.service.indefinite;

import com.juneyaoair.ecs.manage.dto.activity.request.coupon.ThirdPartPrizeRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.commonprizesend.ThirdPartPrizeResponse;
import com.juneyaoair.ecs.manage.dto.activity.response.commonprizesend.ThirdPartPrizeConfigInformationResponse;
import com.juneyaoair.manage.b2c.entity.activity.ThirdPrizeRecordPO;

import java.util.List;

/**
 * @ClassName IIPointRetrievalService
 * @Description
 * <AUTHOR>
 * @Date 2025/4/1 20:40
 * @Version 1.0
 */
@SuppressWarnings("all")
public interface IThirdPartPrizeService {

    /**
     * @return com.juneyaoair.ecs.manage.dto.activity.response.commonprizesend.IndefiniteQuantityResponse
     * <AUTHOR>
     * @Description 获取第三方奖品发送的配置信息 包括:活动信息、商户信息、奖品类型信息等的配置
     * @Date 13:11 2025/4/1
     **/

    ThirdPartPrizeConfigInformationResponse toCatchThirdPartPrizeConfigInformation();

    /**
     * <AUTHOR>
     * @Description 批量导入第三方奖品编码
     * @Date 20:03 2025/4/1
     **/
    void toBatchPreserveThirdPartPrizes(List<ThirdPrizeRecordPO> thirdPrizeRecordPOList);

    /**
     * @param thirdPartPrizeRequest
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.activity.response.commonprizesend.ThirdCouponResponse>
     * <AUTHOR>
     * @Description 查询第三方奖品列表
     * @Date 21:50 2025/4/1
     **/
    List<ThirdPartPrizeResponse> toCatchThirdPartPrizeList(ThirdPartPrizeRequest thirdPartPrizeRequest);

}
