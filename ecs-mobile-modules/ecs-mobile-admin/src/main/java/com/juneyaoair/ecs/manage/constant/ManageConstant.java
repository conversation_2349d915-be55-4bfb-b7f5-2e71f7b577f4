package com.juneyaoair.ecs.manage.constant;

/**
 * <AUTHOR>
 * @description 常用请求路径以及常量
 * @date 2023/5/8 23:36
 */
public interface ManageConstant {
    /**
     * AV缓存刷新
     */
    String GET_AV_INFO = "/flightController/getAvInfo";
    /**
     * 同盾图像检测
     */
    String IMAGE_CONTENT_CHECK = "/TongDunSecurity/imageContent";
    /**
     * 同盾文本检测
     */
    String TEXT_CONTENT_CHECK = "/TongDunSecurity/textContent";
    /**
     * 航距查询
     */
    String AIRLINE_MILEAGE = "/airline/mileage";
    /**
     * 机型信息查询
     */
    String GET_AIRCRAFT_TYPE_INFO = "/aircraftTypeInfo/getAircraftTypeInfo";
    /**
     * 升舱价格查询
     */
    String QUERY_UPG_PRICE = "/upg/upg/queryUpgPrice";
    /**
     * 更新升舱系统航线缓存数据
     */
    String REFRESH_AIRLINE_BASE_SET_REDIS = "/upg/upg/refreshAirlineBaseSetRedis";
    /**
     * 查询权益券订单详情
     */
    String GET_COUPON_ORDER_DETAIL = "/Order/AirplaneUpgrade/GetCouponOrderDetail/v10";


    /**
     * <AUTHOR>
     * @Description 刷新价格缓存
     * @Date 14:48 2023/12/8
     **/
    String REFRESH_PRICE_CACHE = "/discountsRouteCache/refreshPriceCache";

    /** 查询选座渠道 */
    String SEAT_CHANNEL_SEARCH = "/customer/TChannel/search";

    /** 新增选座渠道 */
    String SEAT_CHANNEL_CREATE = "/customer/TChannel/add";

    /** 修改选座渠道 */
    String SEAT_CHANNEL_UPDATE = "/customer/TChannel/update";

    /** 查询选座机场设置 */
    String SEAT_AIRPORT_SEARCH = "/customer/TAirportCheckinSet/search";

    /** 新增选座机场设置 */
    String SEAT_AIRPORT_CREATE = "/customer/TAirportCheckinSet/add";

    /** 修改选座机场设置 */
    String SEAT_AIRPORT_UPDATE = "/customer/TAirportCheckinSet/update";

    /** 修改选座机场设置 */
    String SEAT_CHECK_IN_TIME_SEGMENT_TYPE = "/customer/checkinSegmentTypeTime/getSegmentType";

    /** 查询当前机场值机航线类型配置信息 */
    String SEAT_AIRPORT_CHECK_IN_TIME_SEGMENT_TYPE = "/customer/checkinSegmentTypeTime/search";

    /** 修改当前机场值机航线类型配置信息 */
    String SEAT_AIRPORT_CHECK_IN_TIME_SEGMENT_TYPE_UPDATE = "/customer/checkinSegmentTypeTime/update";

    /** 更新机场值机开放时间状态 */
    String SEAT_AIRPORT_CHECK_IN_TIME_SEGMENT_TYPE_STATUS = "/customer/checkinSegmentTypeTime/updateStatus";

    /** 查询选座机型信息 */
    String SEAT_FLIGHT_TYPE_SEARCH = "/customer/TFlightType/search";

    /** 更新选座机型信息 */
    String SEAT_FLIGHT_TYPE_UPDATE = "/customer/TFlightType/update";

    /** 值机选座-指定ID机型查询 */
    String SEAT_FLIGHT_TYPE_DETAIL = "/customer/TFlightType/detail";

    /** 创建选座机型座位图信息 */
    String SEAT_FLIGHT_TYPE_SEAT_CREATE = "/customer/TFlightTypeSeat/createFlightTypeSeat";

    /** 查询机型区域划分信息 */
    String SEAT_FLIGHT_TYPE_AREA_SEARCH = "/customer/flightType/searchFlightTypeArea";

    /** 保存机型区域划分信息 */
    String SEAT_FLIGHT_TYPE_AREA_SAVE = "/customer/flightType/saveFlightTypeArea";

    /** 查询基础开放信息 */
    String SEAT_BASE_OPEN_SEARCH = "/customer/seatOpentime/searchSeatBaseOpentimeSet";

    /** 保存基础开放时间信息 */
    String SEAT_BASE_OPEN_SAVE = "/customer/seatOpentime/saveSeatBaseOpentimeSet";

    /** 修改基础开放时间状态 */
    String SEAT_BASE_OPEN_STATUS = "/customer/seatOpentime/disableSeatBaseOpentimeSet";

    /** 查询常规价格折扣 */
    String SEAT_GET_SEAT_DISCOUNT_RULE = "/customer/seatDiscount/getSeatDiscountRule";

    /** 保存常规价格折扣 */
    String SEAT_SAVE_SEAT_DISCOUNT_RULE = "/customer/seatDiscount/saveSeatDiscountRule";

    /** 更新常规价格折扣 */
    String SEAT_UPDATE_SEAT_DISCOUNT_RULE = "/customer/seatDiscount/updateSeatDiscountRule";

    /** 查询参数信息 */
    String CUSS_GET_PARAMETER = "/cuss/parameter/getParameter";

    /** 更新参数信息 */
    String CUSS_UPDATE_PARAMETER = "/cuss/parameter/updateParameter";

    /** 值机选座-座位关闭清单查询 */
    String SEAT_GET_SEAT_LOCK_LIST = "/customer/seatLock/searchSeatLock";

    /** 值机选座-保存座位关闭信息 */
    String SEAT_SAVE_SEAT_LOCK = "/customer/seatLock/saveSeatLock";

    /** 值机选座-保存安全员座位信息 */
    String CREATE_SAFE_SEAT = "/upg/safeSeat/createSafeSeat";

    /** 值机选座-更新安全员座位信息 */
    String UPDATE_SAFE_SEAT = "/upg/safeSeat/updateSafeSeat";

    /** 值机选座-查询安全员座位信息清单 */
    String GET_SAFE_SEAT_LIST = "/upg/safeSeat/getSafeSeatList";

    /** 值机选座-执行取消安全员座位旅客操作 */
    String CANCEL_SAFE_SEAT = "/upg/safeSeat/cancelSafeSeat";

    /** 新增奖池 */
    String CREATE_PRIZE_POOL = "/activityPrizePool/createPrizePool";

    /** 修改奖池信息 */
    String UPDATE_PRIZE_POOL = "/activityPrizePool/updatePrizePool";

    /** 变更奖池状态 */
    String CHANGE_PRIZE_POOL_STATUS = "/activityPrizePool/changePrizePoolStatus";

    /** 新增奖池奖品 */
    String CREATE_PRIZE_ENTITY = "/activityPrizePool/createPrizeEntity";

    /** 更新奖池奖品 */
    String UPDATE_PRIZE_ENTITY = "/activityPrizePool/updatePrizeEntity";

    /** 变更奖品状态 */
    String CHANGE_PRIZE_ENTITY_STATUS = "/activityPrizePool/changePrizeEntityStatus";

    /** 查询奖池列表 */
    String GET_PRIZE_POOL_LIST = "/activityPrizePool/getPrizePoolList";

    /** 查询奖品信息 */
    String GET_PRIZE_ENTITY_INFO = "/activityPrizePool/getPrizeEntityInfo";
    /** 创建事件 */
    String CREATE_EVENT_INFO = "/eventInfo/createEventInfo";
    /** 更新事件 */
    String UPDATE_EVENT_INFO = "/eventInfo/updateEventInfo";
    /** 新增事件奖品 */
    String CREATE_EVENT_PRIZE = "/eventInfo/createPrize";
    /** 更新事件奖品 */
    String UPDATE_EVENT_PRIZE = "/eventInfo/updatePrize";
    /** 删除事件奖品 */
    String DELETE_EVENT_PRIZE = "/eventInfo/deletePrize";
    /** 更新事件状态 */
    String UPDATE_EVENT_STATUS = "/eventInfo/updateEventStatus";
    /** 查询事件列表 */
    String GET_EVENT_INFO_LIST = "/eventInfo/getEventInfoList";
    /** 查询事件奖品列表 */
    String GET_EVENT_PRIZE_LIST = "/eventInfo/getEventPrizeList";

    /** 刷新国际化语言字典缓存 */
    String I18N_REFRESH = "/i18nDictionary/refreshCache";

    String CN_DESC = "中国";

    /** 节假日缓存清除 */
    String REMOVE_HOLIDAY_CACHE = "/cityLowPrice/toRemoveHolidayCalenderCache";

    /** 刷新支付方式缓存 */
    String REFRESH_PAYMETHOD_CACHE = "/payMethodCache/refreshRedisCache";

    /** 权益缓存清除 */
    String REMOVE_RIGHTS_CACHE = "/memberRightsCache/toRemoveRightsCache";

    /** crm权益 */
    String CRM_MEMBER_RIGHTS = "/api/MemberRights/RuleDetail";

    /** 基础服务-创建特价航线 */
    String CREATE_SPECIAL_AIRLINE = "/specialAirline/create";
    /** 基础服务-更新特价航线 */
    String UPDATE_SPECIAL_AIRLINE = "/specialAirline/update";
    /** 基础服务-删除特价航线 */
    String DELETE_SPECIAL_AIRLINE = "/specialAirline/delete";
    /** 基础服务-查询特价航线清单 */
    String GET_SPECIAL_AIRLINE = "/specialAirline/getList";
    /** 基础服务-查询指定ID特价航线 */
    String GET_SPECIAL_AIRLINE_BY_ID = "/specialAirline/getSpecialAirlineInfo";

}
