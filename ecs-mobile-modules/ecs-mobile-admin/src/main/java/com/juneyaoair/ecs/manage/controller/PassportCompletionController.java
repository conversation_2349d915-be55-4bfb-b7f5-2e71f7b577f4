package com.juneyaoair.ecs.manage.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.juneyaoair.ecs.manage.dto.activity.request.passport.PassportRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.passport.PassportInformation;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.passport.IPassportService;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * @ClassName PassportCompletionController
 * @Description
 * <AUTHOR>
 * @Date 2025/4/2 20:49
 * @Version 1.0
 */

@RequestMapping("passport")
@RestController
@RequiredArgsConstructor
@Api(value = "PassportCompletionController", tags = "护照补全API")
@Slf4j
public class PassportCompletionController extends HoBaseController {

    @Autowired
    private IPassportService passportService;

    @PostMapping(value = "toCatchPassportList")
    @ApiOperation(value = "护照补全领取流水查询", httpMethod = "POST")
    public R<PageResult<PassportInformation>> toCatchPassportList(@RequestBody @Validated PassportRequest passportRequest, BindingResult bindingResult) {
        try {
            if (bindingResult.hasErrors()) {
                throw new HoServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
            }
            List<PassportInformation> passportInformationList = passportService.toCatchPassportList(passportRequest);
            PageDomain pageDomain = TableSupport.buildPageRequest();
            PageInfo<PassportInformation> pageInfo = dealPageInfo(pageDomain.getPageNum(), pageDomain.getPageSize(), passportInformationList);
            return getPageData(pageInfo, pageDomain);
        } catch (Exception exception) {
            log.error(exception.getMessage());
            return R.fail(exception.getMessage());
        } finally {
            Context.setContext(null);
        }
    }

    @ApiOperation(value = "护照补全流水下载")
    @PostMapping(value = "downloadPassportRecords")
    public void downloadPassportRecords(HttpServletResponse response, @RequestBody @Validated PassportRequest passportRequest, BindingResult bindingResult) throws IOException {
        if (bindingResult.hasErrors()) {
            throw new HoServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        List<PassportInformation> passportInformationList = passportService.toCatchPassportList(passportRequest);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "护照补全流水数据.xlsx";
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
        EasyExcel.write(response.getOutputStream(), PassportInformation.class).sheet("护照补全流水数据").doWrite(passportInformationList);
    }

}
