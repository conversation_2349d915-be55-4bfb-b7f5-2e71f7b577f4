package com.juneyaoair.ecs.manage.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.dto.activity.request.CityInfo;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.activity.ICityInfoServices;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @ClassName CityController
 * @Description 城市查询相关服务
 * <AUTHOR>
 * @Date 2023/12/28 9:55
 * @Version 1.0
 */

@RequestMapping("city")
@RestController
@RequiredArgsConstructor
@Api(value = "CityController", tags = "城市查询相关API")
@Slf4j
public class CityController extends HoBaseController {

    @Autowired
    private ICityInfoServices cityInfoService;

    @ResponseBody
    @RequestMapping(value = "toCatchCityList", method = RequestMethod.POST)
    @ApiOperation(value = "获取城市列表", httpMethod = "POST")
    public R<PageResult<CityInfo>> queryLotteryPoolResult(@RequestBody CityInfo cityInfo, HttpServletRequest request) {
        initContext();
        log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(cityInfo));
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        Page<CityInfo> localPage = PageHelper.getLocalPage();
        List<CityInfo> cityInfoList = cityInfoService.selectCityList(cityInfo, request);
        R<PageResult<CityInfo>> pageData = getPageData(cityInfoList, localPage);
        log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(pageData));
        return pageData;
    }

}
