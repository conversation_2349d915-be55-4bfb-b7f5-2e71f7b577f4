package com.juneyaoair.ecs.manage.aop;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.UtilException;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.juneyaoair.ecs.http.service.HttpClientService;
import com.juneyaoair.ecs.manage.dto.airline.AirLineLabelReqDTO;
import com.juneyaoair.ecs.manage.properties.ThirdAppUrlSet;
import com.juneyaoair.ecs.redis.service.PrimaryRedisService;
import com.juneyaoair.ecs.redis.service.RedisKeyConstants;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.ecs.utils.SystemConstants;
import com.juneyaoair.esc.manage.util.IPUtil;
import com.juneyaoair.manage.b2c.entity.AirlineAPO;
import com.juneyaoair.manage.b2c.mapper.TVersionInfoMapper;
import com.juneyaoair.manage.b2c.service.IAirlineService;
import com.juneyaoair.manage.thirdapi.IFlightBasicCacheService;
import io.seata.common.util.ReflectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 更新城市信息后处理缓存信息
 */
@Component
@Aspect
@Slf4j
public class CItyInfoAspect {

    @Resource
    private TVersionInfoMapper tVersionInfoMapper;
    @Resource
    private PrimaryRedisService primaryRedisService;
    @Autowired
    private IFlightBasicCacheService flightBasicService;
    @Resource
    IAirlineService airLineService;
    @Autowired
    private ThirdAppUrlSet thirdAppUrlSet;
    @Autowired
    private HttpClientService httpClientService;

    //切入城市管理
    @Pointcut(
            "@annotation(CityAopMethodAnno)")
    private void cityPointcut() {
    }

    //切入机场管理
    @Pointcut(
            "@annotation(CityAopMethodAnno)")
    private void airportPointcut() {
    }

    //切入航线管理
    @Pointcut(
            "@annotation(CityAopMethodAnno)")
    private void airlinePointcut() {
    }

    //切入省份管理
    @Pointcut(
            "@annotation(CityAopMethodAnno)")
    private void provincePointcut() {
    }

    //切入国家管理
    @Pointcut(
            "@annotation(CityAopMethodAnno)")
    private void countryPointcut() {
    }

    /**
     * 城市修改的后置通知
     */
    @After("cityPointcut()")
    public void afterCityInfo(JoinPoint joinPoint) {
        //获取方法的参数列表
        Object[] objs = joinPoint.getArgs();
        try {
            if (objs == null || objs.length == 0) {
                return;
            }
            //对改动过的城市更新缓存&版本号
            process(objs, "cityCode", this::updateCityRedisAndVersion);
        } catch (Exception e) {
            log.error("参数：{}，城市切面更新异常：", objs, e);
        }
    }

    private void process(Object[] objs, String fieldName, Consumer<String> consumer) {
        List<String> collect = Arrays.stream(objs).map(
                i -> {
                    if (i instanceof Collection) {
                        return ((Collection<?>) i).stream().map(it -> {
                            if (it instanceof String) {
                                return (String) it;
                            }
                            try {
                                Object code = ReflectionUtil.getFieldValue(it, fieldName);
                                if (code instanceof String) {
                                    return (String) code;
                                }
                            } catch (NoSuchFieldException ignored) {
                            }
                            return "";
                        }).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                    } else if (i instanceof String) {
                        return Collections.singletonList((String) i);
                    } else {
                        try {
                            Object code = ReflectUtil.getFieldValue(i, fieldName);
                            if (code instanceof String) {
                                return Collections.singletonList((String) code);
                            }
                        } catch (UtilException ignored) {
                        }
                        return Collections.singletonList("");
                    }
                }
        ).flatMap(Collection::stream).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) {
            return;
        }
        collect.forEach(i -> {
            CompletableFuture
                    .runAsync(() -> consumer.accept(i))
                    .exceptionally((ex) -> {
                        log.error(ex.getMessage());
                        return null;
                    });
        });
    }

    private void updateCityRedisAndVersion(String fieldName) {
        //清除指定缓存
        primaryRedisService.removeHashData(RedisKeyConstants.REDIS_CITY_INFO_HASH, fieldName);
        primaryRedisService.delete(RedisKeyConstants.REDIS_AIRPORT_INFO_CD);
        //FlightBasic缓存
        flightBasicService.clearCityCache(IPUtil.getLocalIp());
        flightBasicService.clearAirlineCache(IPUtil.getLocalIp());
        //更新版本号
        updateVersionNoByVersionType(SystemConstants.REDIS_CITY_INFO);
    }

    /***
     * 机场修改后的后置通知
     */
    @After("airportPointcut()")
    public void afterAirportInfo(JoinPoint joinPoint) {
        //获取方法的参数列表
        Object[] objs = joinPoint.getArgs();
        try {
            //对改动过的机场更新缓存&版本号
            process(objs, "airportCode", this::updateAirportRedisAndVersion);
        } catch (Exception e) {
            log.error("参数：{}，机场切面更新异常：", objs, e);
        }
    }

    private void updateAirportRedisAndVersion(String fieldName) {
        primaryRedisService.removeHashData(RedisKeyConstants.REDIS_AIRPORT_INFO_HASH, fieldName);
        primaryRedisService.delete(RedisKeyConstants.REDIS_AIRPORT_INFO_CD);
        //FlightBasic缓存
        flightBasicService.clearAirlineCache(IPUtil.getLocalIp());
        //更新版本号
        updateVersionNoByVersionType(SystemConstants.REDIS_AIRPORT_INFO);
    }

    /***
     * 航线修改后的后置通知
     * @param joinPoint
     */
    @After("airlinePointcut()")
    public void afterAirLine(JoinPoint joinPoint) {
        //获取方法的参数列表
        Object[] objs = joinPoint.getArgs();
        String depCityCode = "";
        String arrCityCode = "";
        String depAirportCode = "";
        String arrAirportCode = "";
        try {
            if (objs != null && objs.length > 0) {
                for (Object obj : objs) {
                    if (obj instanceof AirlineAPO) {
                        depCityCode = ((AirlineAPO) obj).getDepCity();
                        arrCityCode = ((AirlineAPO) obj).getArrCity();
                        depAirportCode = ((AirlineAPO) obj).getDepAirport() == null ? "" : ((AirlineAPO) obj).getDepAirport();
                        arrAirportCode = ((AirlineAPO) obj).getArrAirport() == null ? "" : ((AirlineAPO) obj).getArrAirport();
                        break;
                    } else if (obj instanceof AirLineLabelReqDTO) {
                        AirLineLabelReqDTO airlineLabelPO = ((AirLineLabelReqDTO) obj);
                        //根据airlineId
                        AirlineAPO airlineAPO = new AirlineAPO();
                        airlineAPO.setAirlineId(airlineLabelPO.getAirlineId());
                        LambdaQueryWrapper<AirlineAPO> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(AirlineAPO::getAirlineId, airlineLabelPO.getAirlineId());
                        airlineAPO = airLineService.getOne(queryWrapper);
                        if (airlineAPO != null) {
                            depCityCode = airlineAPO.getDepCity();
                            arrCityCode = airlineAPO.getArrCity();
                            depAirportCode = airlineAPO.getDepAirport() == null ? "" : airlineAPO.getDepAirport();
                            arrAirportCode = airlineAPO.getArrAirport() == null ? "" : airlineAPO.getArrAirport();
                        }
                        break;
                    }
                }
            }
            if (StringUtils.isNotBlank(depCityCode) && StringUtils.isNotBlank(arrCityCode)) {
                String key1 = RedisKeyConstants.REDIS_DEP_ARR_AIRLINE + depCityCode + arrCityCode;
                String key2 = RedisKeyConstants.REDIS_DEP_ARR_AIRLINE + depCityCode + arrCityCode + depAirportCode + arrAirportCode;
                CompletableFuture.runAsync(() -> {
                    //API:test:common:deparrAirline:CGQCKG
                    //API:test:common:deparrAirline:DYGNKGDYGNKG
                    primaryRedisService.delete("API:" + key1);
                    primaryRedisService.delete("API:" + key2);
                    primaryRedisService.delete(RedisKeyConstants.REDIS_AIRLINE_INFO_HASH);
                    flightBasicService.clearAirlineCache(IPUtil.getLocalIp());
                    //更新版本号
                    updateVersionNoByVersionType(SystemConstants.REDIS_AIRLINE_INFO);
                }).exceptionally(ex -> {
                    log.error(ex.getMessage());
                    return null;
                });
            }
        } catch (Exception e) {
            log.error("参数：{}，航线切面更新异常：", objs, e);
        }
    }

    @Pointcut("countryPointcut()||provincePointcut()")
    private void orOperation() {
    }

    /**
     * 后置通知：目标方法之后执行（始终执行）
     */
    @After("orOperation()")
    public void doCityInfoAfter(JoinPoint joinPoint) {
        //获取方法的参数列表
        Object[] objs = joinPoint.getArgs();
        String result = "";
        try {
            //清空Redis
            primaryRedisService.delete(RedisKeyConstants.AIRLINE_UPG_RULE_REDIS);
            //清除基础服务省份信息
            flightBasicService.clearProvinceCache(IPUtil.getLocalIp());
            //更新版本号
            updateVersionNoByVersionType(SystemConstants.REDIS_CITY_INFO);
            updateVersionNoByVersionType(SystemConstants.REDIS_AIRLINE_INFO);
            updateVersionNoByVersionType(SystemConstants.REDIS_AIRPORT_INFO);
            log.info("【更新城市信息】结束,请求信息:{},响应数据：{}", JsonUtil.objectToJson(objs[0]), result);
        } catch (Exception e) {
            //记录下错误的信息便于后期排查
            log.error("【更新城市信息】发生错误:{},请求信息:{},响应数据：{}", e, JsonUtil.objectToJson(objs[0]), result);
        }
    }

    private void updateVersionNoByVersionType(String versionType) {
        String currentTimeStr = DateUtil.getCurrentTimeStr();
        tVersionInfoMapper.updateVersionNoByVersionType(currentTimeStr, versionType);
    }
}
