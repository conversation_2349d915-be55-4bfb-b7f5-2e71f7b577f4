package com.juneyaoair.ecs.manage.service.cityinfo;

import com.juneyaoair.ecs.manage.dto.citymanage.CityExportDTO;
import com.juneyaoair.ecs.manage.dto.citymanage.CityInfoReqDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ICityInfoAggrService {
    /**
     * 导出查询
     * @param prizeRecord
     * @return
     */
    List<CityExportDTO> exportList(CityInfoReqDTO prizeRecord);

    /**
     * 资源同步
     * @return
     */
    boolean syncJsonAndJs();
}
