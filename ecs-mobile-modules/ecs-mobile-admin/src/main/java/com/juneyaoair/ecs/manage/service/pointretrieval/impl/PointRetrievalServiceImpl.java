package com.juneyaoair.ecs.manage.service.pointretrieval.impl;

import com.juneyaoair.ecs.manage.dto.activity.request.pointretrieval.PointRetrievalRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.pointretrieval.PointRetrievalInformation;
import com.juneyaoair.ecs.manage.service.pointretrieval.IPointRetrievalService;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.service.RetrievalSignUpPOService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName PointRetrievalServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/3/17 10:28
 * @Version 1.0
 */

@Service
@RefreshScope
@Slf4j
@SuppressWarnings("all")
public class PointRetrievalServiceImpl implements IPointRetrievalService {

    @Autowired
    private RetrievalSignUpPOService retrievalSignUpPOService;


    @Override
    public List<PointRetrievalInformation> toCatchPointRetrievalList(PointRetrievalRequest pointRetrievalRequest) {
        if (StringUtils.isAllEmpty(pointRetrievalRequest.getFfpCardNo(), pointRetrievalRequest.getSignUpStartTime(), pointRetrievalRequest.getSignUpEndTime(), pointRetrievalRequest.getReceiveStartTime(), pointRetrievalRequest.getReceiveEndTime())) {
            throw new HoServiceException("不允许无条件查询.");
        }
        if (StringUtils.isNotEmpty(pointRetrievalRequest.getFfpCardNo()) ||
                StringUtils.isNoneEmpty(pointRetrievalRequest.getSignUpStartTime(), pointRetrievalRequest.getSignUpEndTime()) || StringUtils.isNoneEmpty(pointRetrievalRequest.getReceiveStartTime(), pointRetrievalRequest.getReceiveEndTime())) {
            if (StringUtils.isNotEmpty(pointRetrievalRequest.getFfpCardNo())) {
                return retrievalSignUpPOService.toCatchRetrievalSignUpInformation(pointRetrievalRequest);
            } else if (StringUtils.isNoneEmpty(pointRetrievalRequest.getSignUpStartTime(), pointRetrievalRequest.getSignUpEndTime())) {
                //报名时间间隔是否正确 是否小于0或者大于3个月
                int signUpDiffDays = DateUtil.dateDiff(pointRetrievalRequest.getSignUpStartTime(), pointRetrievalRequest.getSignUpEndTime(), DateUtil.DATE_FORMATE);
                if (signUpDiffDays <= 0) {
                    throw new HoServiceException("报名时间非法");
                }
                if (signUpDiffDays > 90) {
                    throw new HoServiceException("报名时间间隔过大(已超过90个自然日),请缩小查询范围.");
                }
            } else if (StringUtils.isNoneEmpty(pointRetrievalRequest.getReceiveStartTime(), pointRetrievalRequest.getReceiveEndTime())) {
                //领取时间间隔是否正确 是否小于0或者大于3个月
                int receiveDiffDays = DateUtil.dateDiff(pointRetrievalRequest.getReceiveStartTime(), pointRetrievalRequest.getReceiveEndTime(), DateUtil.DATE_FORMATE);
                if (receiveDiffDays <= 0) {
                    throw new HoServiceException("领取时间非法");
                }
                if (receiveDiffDays > 90) {
                    throw new HoServiceException("领取时间间隔过大(已超过90个自然日),请缩小查询范围.");
                }
            }
        } else {
            throw new HoServiceException("报名时间或者领取时间范围不可全部为空.");
        }
        //数据查询
        return retrievalSignUpPOService.toCatchRetrievalSignUpInformation(pointRetrievalRequest);
    }
}
