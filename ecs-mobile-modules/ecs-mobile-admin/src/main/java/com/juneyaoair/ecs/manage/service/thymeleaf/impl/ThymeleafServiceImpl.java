package com.juneyaoair.ecs.manage.service.thymeleaf.impl;

import com.juneyaoair.ecs.manage.dto.file.HoFile;
import com.juneyaoair.ecs.manage.dto.message.request.MessageDto;
import com.juneyaoair.ecs.manage.enums.FileModuleName;
import com.juneyaoair.ecs.manage.enums.LanguageEnum;
import com.juneyaoair.ecs.manage.enums.NoticeTemplateEnum;
import com.juneyaoair.ecs.manage.service.file.IFileUploadService;
import com.juneyaoair.ecs.manage.service.thymeleaf.IThymeleafService;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;
import java.util.Locale;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/17 10:14
 */
@RefreshScope
@Service
@Slf4j
public class ThymeleafServiceImpl implements IThymeleafService {
    @Autowired
    private TemplateEngine templateEngine;
    @Autowired
    private IFileUploadService fileUploadService;
    /**
     * 本地文件临时存放目录
     */
    @Value("${message.tmpPath:}")
    private String tmpPath;

    /**
     * 生成html静态文件
     *
     * @param messageDto
     */
    @Override
    public String createMessageHtml(MessageDto messageDto, NoticeTemplateEnum noticeTemplateEnum, Date curDate) {
        //初始化运行上下文
        Context context = new Context();
        //设置数据模型
        context.setVariable("title", messageDto.getMessageTitle());
        context.setVariable("time", DateUtil.convertDate2Str(curDate,DateUtil.DATE_FORMATE_YYYY_MM_CHINA));
        //不同的语言对于时间格式要求不一致
        if(StringUtils.isNotBlank(messageDto.getLanguage())){
            if(LanguageEnum.EN_US.name().equals(messageDto.getLanguage())){
                context.setVariable("time", DateUtil.convertDate2Str(curDate,DateUtil.DATE_FORMATE_DD_MMM_YYYY, Locale.ENGLISH));
            }
        }
        context.setVariable("body", messageDto.getMessageContent());
        PrintWriter printWriter = null;
        try {
            //把静态文件生成到服务器本地
            String fileName = noticeTemplateEnum.getDir() + "_" + messageDto.getMessageid() + "_" + System.currentTimeMillis();
            File file = new File(tmpPath + fileName + ".html");
            // 判断父级目录是否存在，不存在则创建
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            printWriter = new PrintWriter(file);
            this.templateEngine.process(noticeTemplateEnum.getName(), context, printWriter);
            //同步上传file至sftp服务器，并返回url地址
            HoFile hofile = fileUploadService.uploadLocalFile(file,FileModuleName.MESSAGE.getModuleName(),fileName);
            return hofile.getUrl();
        } catch (FileNotFoundException e) {
            log.error("文件模板生成异常:", e);
            throw new HoServiceException("文件模板生成异常");
        } catch (IOException e) {
            log.error("文件输出异常:", e);
            throw new HoServiceException("文件输出异常");
        } finally {
            if (printWriter != null) {
                printWriter.close();
            }
        }
    }

}
