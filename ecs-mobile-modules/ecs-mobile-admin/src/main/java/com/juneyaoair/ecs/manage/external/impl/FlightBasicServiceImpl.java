package com.juneyaoair.ecs.manage.external.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.juneyaoair.ecs.http.bean.HttpResult;
import com.juneyaoair.ecs.http.service.HttpClientService;
import com.juneyaoair.ecs.manage.constant.ManageConstant;
import com.juneyaoair.ecs.manage.dto.I18n.I18nDictionaryRefreshCacheRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.event.EventInfoPO;
import com.juneyaoair.ecs.manage.dto.activity.request.event.EventInfoQueryParam;
import com.juneyaoair.ecs.manage.dto.activity.response.event.EventPrizePO;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ActivityPrizeEntryParam;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ActivityPrizeEntryQueryParam;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ActivityPrizePoolParam;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ActivityPrizePoolQueryParam;
import com.juneyaoair.ecs.manage.dto.activity.response.prizepool.ActivityPrizeEntryInfo;
import com.juneyaoair.manage.b2c.entity.activity.ActivityPrizePoolPO;
import com.juneyaoair.ecs.manage.dto.base.*;
import com.juneyaoair.ecs.manage.enums.ResultEnum;
import com.juneyaoair.ecs.manage.external.FlightBasicService;
import com.juneyaoair.ecs.manage.external.flightbaisc.AircraftTypeInfo;
import com.juneyaoair.ecs.manage.external.flightbaisc.AircraftTypeInfoQuery;
import com.juneyaoair.ecs.manage.external.flightbaisc.AirlineMileageInfo;
import com.juneyaoair.ecs.manage.external.flightbaisc.AirlineMileageParam;
import com.juneyaoair.ecs.manage.properties.ThirdAppUrlSet;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.thirdapi.IFlightBasicCacheService;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description FlightBasicService
 * @created 2023/8/17 9:23
 */
@Service
@Slf4j
public class FlightBasicServiceImpl implements FlightBasicService {

    @Autowired
    private HttpClientService httpClientService;
    @Autowired
    private ThirdAppUrlSet thirdAppUrlSet;
    @Autowired
    private IFlightBasicCacheService flightBasicService;

    @Override
    public List<AirlineMileageInfo> getAirLineMileage(String minMileage, String maxMileage, String depCity, String arrCity) {
        AirlineMileageParam airlineMileageParam = new AirlineMileageParam();
        airlineMileageParam.setMinMileage(minMileage);
        airlineMileageParam.setMaxMileage(maxMileage);
        airlineMileageParam.setDepCity(depCity);
        airlineMileageParam.setArrCity(arrCity);
        BaseRequest<AirlineMileageParam> request = BaseRequestUtil.createRequest(airlineMileageParam);
        HttpResult httpResult = httpClientService.doPostJson(request, thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.AIRLINE_MILEAGE, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("航距信息查询失败，请稍后再试");
        }
        TypeReference<BaseResult<List<AirlineMileageInfo>>> typeReference = new TypeReference<BaseResult<List<AirlineMileageInfo>>>() {
        };
        BaseResult<List<AirlineMileageInfo>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if (!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())) {
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public List<AircraftTypeInfo> getAircraftTypeInfo() {
        AircraftTypeInfoQuery aircraftTypeInfoQuery = new AircraftTypeInfoQuery();
        aircraftTypeInfoQuery.setRefresh(true);
        BaseRequest<Object> request = BaseRequestUtil.createRequest(aircraftTypeInfoQuery);
        HttpResult httpResult = httpClientService.doPostJson(request, thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.GET_AIRCRAFT_TYPE_INFO, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("航距信息缓存更新失败，请稍后再试");
        }
        TypeReference<BaseResult<List<AircraftTypeInfo>>> typeReference = new TypeReference<BaseResult<List<AircraftTypeInfo>>>() {
        };
        BaseResult<List<AircraftTypeInfo>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if (!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())) {
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    @SuppressWarnings("all")
    public void refreshPriceCache() {
        String currentTimeStr = DateUtil.getDateStringAllDate(new Date());
        try {
            BasicBaseReq basicBaseReq = new BasicBaseReq("1.0", "MOBILE", "0.0.0.0");
            HttpResult httpResult = httpClientService.doPostJson(basicBaseReq, thirdAppUrlSet.getFlightbasicUrl() + ManageConstant.REFRESH_PRICE_CACHE, null);
            if (!httpResult.isResult()) {
                log.info("[{}]同步特惠航线数据失败", currentTimeStr);
            }
            TypeReference<BaseResultDTO> typeReference = new TypeReference<BaseResultDTO>() {
            };
            BaseResultDTO baseResultDTO = JSON.parseObject(httpResult.getResponse(), typeReference);
            if (ResultEnum.S10001.getResultCode().equals(baseResultDTO.getResultCode())) {
                log.info("[{}]同步特惠航线数据成功", currentTimeStr);
            } else {
                log.info("[{}]同步特惠航线数据失败", currentTimeStr);
            }
        } catch (Exception e) {
            log.info("[{}]同步特惠航线数据失败", currentTimeStr, e);
        }
    }

    @Override
    @Async("threadPoolExecutor")
    public void clearNoticeCache() {
        flightBasicService.clearNoticeCache();
    }

    @Override
    public void createPrizePool(ActivityPrizePoolParam activityPrizePoolParam) {
        BaseRequest<ActivityPrizePoolParam> request = BaseRequestUtil.createRequest(activityPrizePoolParam);
        HttpResult httpResult = httpClientService.doPostJson(JSON.toJSONString(request), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.CREATE_PRIZE_POOL, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("新增奖池失败，请稍后再试");
        }
        TypeReference<BaseResult<String>> typeReference = new TypeReference<BaseResult<String>>() {
        };
        BaseResult<String> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if (!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())) {
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public void updatePrizePool(ActivityPrizePoolParam activityPrizePoolParam) {
        BaseRequest<ActivityPrizePoolParam> request = BaseRequestUtil.createRequest(activityPrizePoolParam);
        HttpResult httpResult = httpClientService.doPostJson(JSON.toJSONString(request), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.UPDATE_PRIZE_POOL, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("修改奖池信息失败，请稍后再试");
        }
        TypeReference<BaseResult<String>> typeReference = new TypeReference<BaseResult<String>>() {
        };
        BaseResult<String> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if (!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())) {
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public void changePrizePoolStatus(ActivityPrizePoolParam activityPrizePoolParam) {
        BaseRequest<ActivityPrizePoolParam> request = BaseRequestUtil.createRequest(activityPrizePoolParam);
        HttpResult httpResult = httpClientService.doPostJson(JSON.toJSONString(request), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.CHANGE_PRIZE_POOL_STATUS, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("变更奖池状态失败，请稍后再试");
        }
        TypeReference<BaseResult<String>> typeReference = new TypeReference<BaseResult<String>>() {
        };
        BaseResult<String> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if (!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())) {
            throw new HoServiceException(baseResult.getErrorMsg());
        }

    }

    @Override
    public void createPrizeEntity(ActivityPrizeEntryParam activityPrizeEntryParam) {
        BaseRequest<ActivityPrizeEntryParam> request = BaseRequestUtil.createRequest(activityPrizeEntryParam);
        HttpResult httpResult = httpClientService.doPostJson(JSON.toJSONString(request), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.CREATE_PRIZE_ENTITY, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("新增奖池奖品失败，请稍后再试");
        }
        TypeReference<BaseResult<String>> typeReference = new TypeReference<BaseResult<String>>() {
        };
        BaseResult<String> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if (!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())) {
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public void updatePrizeEntity(ActivityPrizeEntryParam activityPrizeEntryParam) {
        BaseRequest<ActivityPrizeEntryParam> request = BaseRequestUtil.createRequest(activityPrizeEntryParam);
        HttpResult httpResult = httpClientService.doPostJson(JSON.toJSONString(request), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.UPDATE_PRIZE_ENTITY, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("更新奖池奖品失败，请稍后再试");
        }
        TypeReference<BaseResult<String>> typeReference = new TypeReference<BaseResult<String>>() {
        };
        BaseResult<String> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if (!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())) {
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public void changePrizeEntityStatus(ActivityPrizeEntryParam activityPrizeEntryParam) {
        BaseRequest<ActivityPrizeEntryParam> request = BaseRequestUtil.createRequest(activityPrizeEntryParam);
        HttpResult httpResult = httpClientService.doPostJson(JSON.toJSONString(request), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.CHANGE_PRIZE_ENTITY_STATUS, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("变更奖品状态失败，请稍后再试");
        }
        TypeReference<BaseResult<String>> typeReference = new TypeReference<BaseResult<String>>() {
        };
        BaseResult<String> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if (!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())) {
            throw new HoServiceException(baseResult.getErrorMsg());
        }
    }

    @Override
    public PageResult<ActivityPrizePoolPO> getPrizePoolList(ActivityPrizePoolQueryParam activityPrizePoolQueryParam) {
        BaseRequest<ActivityPrizePoolQueryParam> request = BaseRequestUtil.createRequest(activityPrizePoolQueryParam);
        HttpResult httpResult = httpClientService.doPostJson(JSON.toJSONString(request), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.GET_PRIZE_POOL_LIST, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询奖池列表失败，请稍后再试");
        }
        TypeReference<BaseResult<PageResult<ActivityPrizePoolPO>>> typeReference = new TypeReference<BaseResult<PageResult<ActivityPrizePoolPO>>>() {
        };
        BaseResult<PageResult<ActivityPrizePoolPO>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if (!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())) {
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

    @Override
    public List<ActivityPrizeEntryInfo> getPrizeEntityInfo(ActivityPrizeEntryQueryParam activityPrizeEntryQueryParam) {
        BaseRequest<ActivityPrizeEntryQueryParam> request = BaseRequestUtil.createRequest(activityPrizeEntryQueryParam);
        HttpResult httpResult = httpClientService.doPostJson(JSON.toJSONString(request), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.GET_PRIZE_ENTITY_INFO, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询奖品信息失败，请稍后再试");
        }
        TypeReference<BaseResult<List<ActivityPrizeEntryInfo>>> typeReference = new TypeReference<BaseResult<List<ActivityPrizeEntryInfo>>>() {
        };
        BaseResult<List<ActivityPrizeEntryInfo>> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if (!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())) {
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }


    @Override
    public void refreshI18nCache(String dictionaryType, Map<String, Map<String, String>> dictionaryMap) {

        I18nDictionaryRefreshCacheRequest reqDto = new I18nDictionaryRefreshCacheRequest();
        reqDto.setDictionaryType(dictionaryType);
        reqDto.setDictionaryMap(dictionaryMap);
        BaseRequest<I18nDictionaryRefreshCacheRequest> request = BaseRequestUtil.createRequest(reqDto);
        //
        HttpResult httpResult = httpClientService.doPostJson(request, thirdAppUrlSet.getFlightbasicProviderUrl() + ManageConstant.I18N_REFRESH, null);
    }

    @Override
    public void createEventInfo(EventInfoPO eventInfo) {
        eventInfo.setCreateUser(SecurityUtils.getUsername());
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        doPostJson(BaseRequestUtil.createRequest(eventInfo), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.CREATE_EVENT_INFO, typeReference);
    }

    @Override
    public void updateEventInfo(EventInfoPO eventInfo) {
        eventInfo.setUpdateUser(SecurityUtils.getUsername());
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        doPostJson(BaseRequestUtil.createRequest(eventInfo), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.UPDATE_EVENT_INFO, typeReference);
    }

    @Override
    public void createPrize(EventPrizePO eventPrize) {
        eventPrize.setCreateUser(SecurityUtils.getUsername());
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        doPostJson(BaseRequestUtil.createRequest(eventPrize), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.CREATE_EVENT_PRIZE, typeReference);
    }

    @Override
    public void updatePrize(EventPrizePO eventPrize) {
        eventPrize.setUpdateUser(SecurityUtils.getUsername());
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        doPostJson(BaseRequestUtil.createRequest(eventPrize), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.UPDATE_EVENT_PRIZE, typeReference);
    }

    @Override
    public void deletePrize(String eventPrizeId) {
        EventPrizePO eventPrize = new EventPrizePO();
        eventPrize.setEventPrizeId(eventPrizeId);
        eventPrize.setUpdateUser(SecurityUtils.getUsername());
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        doPostJson(BaseRequestUtil.createRequest(eventPrize), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.DELETE_EVENT_PRIZE, typeReference);
    }

    @Override
    public void updateEventStatus(String eventInfoId, String status) {
        EventInfoPO eventInfo = new EventInfoPO();
        eventInfo.setEventInfoId(eventInfoId);
        eventInfo.setStatus(status);
        eventInfo.setUpdateUser(SecurityUtils.getUsername());
        TypeReference<BaseResult<Object>> typeReference = new TypeReference<BaseResult<Object>>() {
        };
        doPostJson(BaseRequestUtil.createRequest(eventInfo), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.UPDATE_EVENT_STATUS, typeReference);
    }

    @Override
    public PageResult<EventInfoPO> getEventInfoList(EventInfoQueryParam eventInfoQueryParam) {
        TypeReference<BaseResult<PageResult<EventInfoPO>>> typeReference = new TypeReference<BaseResult<PageResult<EventInfoPO>>>() {
        };
        return doPostJson(BaseRequestUtil.createRequest(eventInfoQueryParam), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.GET_EVENT_INFO_LIST, typeReference);
    }

    @Override
    public List<EventPrizePO> getEventPrizeList(String eventInfoId) {
        EventInfoPO eventInfo = new EventInfoPO();
        eventInfo.setEventInfoId(eventInfoId);
        TypeReference<BaseResult<List<EventPrizePO>>> typeReference = new TypeReference<BaseResult<List<EventPrizePO>>>() {
        };
        return doPostJson(BaseRequestUtil.createRequest(eventInfo), thirdAppUrlSet.getFlightbasicActivityUrl() + ManageConstant.GET_EVENT_PRIZE_LIST, typeReference);
    }

    /**
     * 执行请求并返回结果
     * @param param
     * @param url
     * @param typeReference
     * @return
     * @param <T>
     */
    private <T> T doPostJson(Object param, String url, TypeReference<BaseResult<T>> typeReference) {
        HttpResult httpResult = httpClientService.doPostJson(JSON.toJSONString(param), url, null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询奖品信息失败，请稍后再试");
        }
        BaseResult<T> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if (!ResultEnum.S10001.getResultCode().equals(baseResult.getResultCode())) {
            throw new HoServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

}
