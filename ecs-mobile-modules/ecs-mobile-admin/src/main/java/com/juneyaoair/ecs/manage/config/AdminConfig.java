package com.juneyaoair.ecs.manage.config;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.ecs.manage.dto.config.*;
import com.juneyaoair.ecs.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.Map;

@RefreshScope
@Component
@Slf4j
public class AdminConfig {
    /**
     * 城市分组管理
     */
    @Value("${cityGroup:}")
    private String cityGroup;

    @Value("${activityUrl:https://t-activity.hoair.cn/events/202303AirlinePromotion/index.html}")
    private String activityUrl;

    @Value("${thirdPartPrizeSendConfig:}")
    private String thirdPartPrizeSendConfig;

    @Value("${countStartTime:2024-01-01 00:00:00}")
    private String countStartTime;

    @Value("${countEndTime:2024-12-31 23:59:59}")
    private String countEndTime;

    public Map<String, CityGroup> getCityGroupMap(){
        try {
            Type type = new TypeToken<Map<String, CityGroup>>() {
            }.getType();
            return JsonUtil.jsonToMap(cityGroup, type);
        } catch (Exception e) {
            return null;
        }
    }

    public ThirdPartPrizeTotalConfig toCatchThirdPartPrizeSendConfig() {
        try {
            TypeReference<ThirdPartPrizeTotalConfig> typeReference = new TypeReference<ThirdPartPrizeTotalConfig>() {
            };
            return JSON.parseObject(thirdPartPrizeSendConfig, typeReference);
        } catch (Exception exception) {
            log.error("获取第三方奖品apollo配置失败:",exception);
            return null;
        }
    }


    public String getActivityUrl() {
        return activityUrl;
    }

    public String getCountStartTime() {
        return countStartTime;
    }

    public String getCountEndTimeTime() {
        return countEndTime;
    }
}
