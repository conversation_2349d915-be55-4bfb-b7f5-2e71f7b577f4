package com.juneyaoair.ecs.manage.service.file;

import com.juneyaoair.ecs.manage.dto.file.HoFile;
import com.juneyaoair.ecs.manage.enums.AdvertisementWaterEnum;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/12 10:41
 */
public interface IFileUploadService {
    /**
     * 上传文件
     * @param file
     * @param moduleName
     * @param fileName 文件名称，无后缀
     * @param advertisementWater 水印名称
     * @param specifyDate 文件路径中是否需要年月日
     * @return specifyDate
     */
    HoFile uploadFile(MultipartFile file, String moduleName, String fileName, AdvertisementWaterEnum advertisementWater, boolean specifyDate) throws IOException;
    /**
     * 上传文件
     * @param file
     * @param moduleName
     * @param fileName 文件名称，无后缀
     * @param specifyDate 文件路径中是否需要年月日
     * @return
     */
    HoFile uploadFile(MultipartFile file,String moduleName,String fileName,boolean specifyDate) throws IOException;

    /**
     * 根据本地文件上传至资源服务器
     * @param file
     * @param moduleName
     * @param fileName 文件名称，无后缀
     * @return
     */
    HoFile uploadLocalFile(File file ,String moduleName ,String fileName);


    /**
     * 把字符串转为文件tmp放到本地服务器，再上传到资源服务器
     * @param file 源文件
     * @param fileFormat 转成的文件格式
     * @param remoteUrl 远程资源服务器相对路径（不包含固定的存放目录）
     * @return
     */
    HoFile uploadFile(File file, String fileFormat/*eg:"airline.json"*/, String remoteUrl);

    /**
     * 字节数组上传到远程服务器
     * @param srcFileByte 源字节数组
     * @param fileFormat 转成的文件格式
     * @param remoteUrl 远程资源服务器相对路径（不包含固定的存放目录）
     * @return
     */
    HoFile uploadByte(byte[] srcFileByte, String fileFormat/*eg:"airline.json"*/, String remoteUrl);

    void upload(String fileName, String remoteUrl, Object obj);

}
