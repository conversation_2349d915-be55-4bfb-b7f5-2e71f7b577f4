package com.juneyaoair.ecs.manage.service.syncfile.impl;

import com.juneyaoair.ecs.manage.service.syncfile.IAirlineSyncJsFileService;
import com.juneyaoair.manage.b2c.entity.AirlineAPO;
import com.juneyaoair.manage.b2c.mapper.TAirlineAMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class AirlineSyncJsFileServiceImpl extends SyncJsFileService implements IAirlineSyncJsFileService {

    @Resource
    TAirlineAMapper airLineMapper;
    private final static String contentName = "airline";

    @Override
    public boolean process() {
        mainProcess();
        return true;
    }
    @Override
    protected String getContentName() {
        return contentName;
    }

    @Override
    public String prepareData() {
        List<AirlineAPO> airlinePOs = airLineMapper.selectByAll(null);
        //过滤重复城市对
        Set<String> airlineSet = new HashSet<>();
        for (AirlineAPO airline_a_po : airlinePOs) {
            String airlineString = airline_a_po.getDepCity() + "|" + airline_a_po.getArrCity() + "|" + airline_a_po.getIsInternationalAirline();
            airlineSet.add(airlineString);
        }
        //生成js
        List<String> airlineList = new ArrayList<>(airlineSet);
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("var allLine=new Array();\r\n");

        for (int i = 0; i < airlineList.size(); i++) {
            String[] stringArray = StringUtils.split(airlineList.get(i), "|");
            stringBuffer.append("allLine[" + i + "]=new Array('" + stringArray[0] + "','" + stringArray[1] + "','" + stringArray[2] + "','N','20140101','');\r\n");
        }
        return stringBuffer.toString();
    }


}
