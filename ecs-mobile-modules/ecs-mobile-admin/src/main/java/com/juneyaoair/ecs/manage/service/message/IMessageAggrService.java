package com.juneyaoair.ecs.manage.service.message;

import com.juneyaoair.ecs.manage.dto.message.request.MessageDto;

/**
 * <AUTHOR>
 * @description 公告消息聚合服务
 * @date 2023/5/10 13:14
 */
public interface IMessageAggrService {
    boolean addMessage(MessageDto message);
    boolean updateMessage(MessageDto message);

    /**
     * 公告详情
     * @param messageId
     * @return
     */
    MessageDto messageDetail(String messageId);

    /**
     * 公告发布与下线
     * @param message
     * @return
     */
    boolean publishOrOffline(MessageDto message);
}
