package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.file.HoFile;
import com.juneyaoair.ecs.manage.enums.AdvertisementWaterEnum;
import com.juneyaoair.ecs.manage.service.file.IFileUploadService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description 文件上传服务
 * @date 2023/5/11 15:32
 */
@Api(value = "FileController" ,tags = "文件上传服务")
@RequestMapping("file")
@RestController
public class FileController extends HoBaseController{
    @Autowired
    private IFileUploadService iFileService;

    /**
     *
     * @param moduleName 模块名称
     * @param advertisementWater 广告水印
     * @param file 文件
     * @return
     */
    @ApiOperation(value = "上传文件")
    @PostMapping("upload")
    public R<HoFile> upload(@RequestParam(value = "moduleName", required = false) String moduleName,
                            @RequestParam(value = "advertisementWater", required = false) String advertisementWater,
                            @RequestParam(value = "file") MultipartFile file) throws IOException {
        HoFile hoFile;
        if("Y".equals(advertisementWater)){
            hoFile =  iFileService.uploadFile(file,moduleName,null, AdvertisementWaterEnum.ADVERTISEMENT,true);
        }else{
            hoFile = iFileService.uploadFile(file,moduleName,null,true);
        }
        return R.ok(hoFile);
    }
}
