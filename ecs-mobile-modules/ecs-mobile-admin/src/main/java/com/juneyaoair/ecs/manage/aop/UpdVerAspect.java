package com.juneyaoair.ecs.manage.aop;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.PicturePO;
import com.juneyaoair.manage.b2c.entity.VersionInfoPO;
import com.juneyaoair.manage.b2c.mapper.PicMapper;
import com.juneyaoair.manage.b2c.mapper.TVersionInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 更新图片时顺便更新版本
 * 要解决的问题：aspect要和声明式事务在同一个事务里问题
 * 1、快照读的原理：https://blog.csdn.net/zxxshaycormac/article/details/115421116
 * 2、同一个事务中自己插入可以查到 https://blog.csdn.net/qq_35368296/article/details/130606573
 * 3、拓展到spring：https://blog.csdn.net/u012060033/article/details/87911330
 * 4、aspect和声明式事务问题：https://blog.csdn.net/zy_js/article/details/80476783
 * 5、todo https://blog.csdn.net/qq_18300037/article/details/121168897?spm=1001.2101.3001.6650.1&utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7ECTRLIST%7ERate-1-121168897-blog-116952839.235%5Ev39%5Epc_relevant_yljh&depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7ECTRLIST%7ERate-1-121168897-blog-116952839.235%5Ev39%5Epc_relevant_yljh&utm_relevant_index=2
 */
@Component
@Aspect
@Slf4j
@Order(1)
public class UpdVerAspect {

    @Resource
    private HttpServletRequest request;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private TVersionInfoMapper tVersionInfoMapper;
    @Resource
    PicMapper picMapper;

    @Pointcut(
            "@annotation(UpdVerAnno)")
    private void updVerAnnoPointcut() {
    }

    /**
     *
     */
    @Before("updVerAnnoPointcut()")
    public void beforePictureAlter(JoinPoint joinPoint) {

        //获取方法的参数列表
        Object[] objs = joinPoint.getArgs();
        if (objs == null || objs.length == 0) {
            return;
        }
        if (request.getServletPath().contains("addPic")) {
            if (!(objs[0] instanceof PicturePO)) {
                return;
            }
            PicturePO po = (PicturePO) objs[0];
            //必须要有picLocation
            if (StrUtil.isBlank(po.getPicLocation())) {
                return;
            }
            if (CollUtil.isEmpty(po.getChannel())) {
                return;
            }
            transactionTemplate.execute((TransactionCallback) status -> {
                if (CollUtil.isEmpty(po.getChannel())) {
                    throw new HoServiceException("Channel is empty");
                }
                if (StrUtil.isBlank(po.picLocation)) {
                    throw new HoServiceException("picLocation is empty");
                }
                for (String channel : po.getChannel()) {
                    LambdaQueryWrapper<VersionInfoPO> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(VersionInfoPO::getChannelCode, channel)
                            .eq(VersionInfoPO::getVersionType, po.picLocation);
                    VersionInfoPO infoPO = tVersionInfoMapper.selectOne(queryWrapper);
                    if (infoPO == null) {
                        VersionInfoPO versionInfoPO = new VersionInfoPO();
                        versionInfoPO.setVersionNo(DateUtil.getCurrentTimeStr());
                        versionInfoPO.setVersionType(po.picLocation);
                        versionInfoPO.setChannelCode(channel);
                        tVersionInfoMapper.insertSelective(versionInfoPO);
                    }
                    //普调
                    LambdaUpdateWrapper<VersionInfoPO> verUpdateWrapper = new LambdaUpdateWrapper<>();
                    verUpdateWrapper.eq(StrUtil.isNotBlank(po.getPicLocation()),//之前是like
                                    VersionInfoPO::getVersionType, po.getPicLocation())
                            .set(VersionInfoPO::getVersionNo, DateUtil.getCurrentTimeStr());
                    tVersionInfoMapper.update(null, verUpdateWrapper);
                }
                return null;
            });
        } else if (request.getServletPath().contains("delPic")) {
            //del picId
            if (objs[0] instanceof String) {
                transactionTemplate.execute(
                        (TransactionCallback) status -> {
                            LambdaQueryWrapper<PicturePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                            lambdaQueryWrapper.eq(PicturePO::getPicId, objs[0]);
                            PicturePO picPO = picMapper.selectOne(lambdaQueryWrapper);
                            if (picPO == null || StrUtil.isBlank(picPO.picLocation)) {
                                return null;
                            }
                            LambdaUpdateWrapper<VersionInfoPO> updateWrapper = new LambdaUpdateWrapper<>();
                            updateWrapper.eq(VersionInfoPO::getVersionType, picPO.picLocation)
                                    .set(VersionInfoPO::getVersionNo, DateUtil.getCurrentTimeStr());
                            tVersionInfoMapper.update(null, updateWrapper);
                            return null;
                        }
                );
                return;
            }
        } else if (request.getServletPath().contains("updatePic")) {
            //包括上下线和编辑
            if (!(objs[0] instanceof PicturePO)) {
                return;
            }
            PicturePO po = (PicturePO) objs[0];
            LambdaQueryWrapper<PicturePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(PicturePO::getPicId, po.picId);
            PicturePO picPO = picMapper.selectOne(lambdaQueryWrapper);
            if (picPO == null || StrUtil.isBlank(picPO.picLocation)) {
                return;
            }
            //必须要有picLocation
            if (StrUtil.isBlank(picPO.getPicLocation())) {
                return;
            }
            //普调
            transactionTemplate.execute((TransactionCallback) status -> {
                LambdaUpdateWrapper<VersionInfoPO> verUpdateWrapper = new LambdaUpdateWrapper<>();
                verUpdateWrapper.eq(StrUtil.isNotBlank(picPO.getPicLocation()),//之前是like
                                VersionInfoPO::getVersionType, picPO.getPicLocation())
                        .set(VersionInfoPO::getVersionNo, DateUtil.getCurrentTimeStr());
                tVersionInfoMapper.update(null, verUpdateWrapper);
                return null;
            });
        }
    }
}
