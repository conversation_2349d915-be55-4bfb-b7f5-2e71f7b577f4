package com.juneyaoair.ecs.manage.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.juneyaoair.ecs.manage.common.AbsOrdinaryProcessor;
import com.juneyaoair.ecs.manage.common.AbsPageProcessor;
import com.juneyaoair.ecs.manage.common.RDto;
import com.juneyaoair.ecs.manage.dto.I18n.DeleteSaleOfficeTranslationDTO;
import com.juneyaoair.ecs.manage.dto.I18n.I18nDictionaryKVDTO;
import com.juneyaoair.ecs.manage.dto.I18n.SaleOfficeTranslationDTO;
import com.juneyaoair.ecs.manage.dto.I18n.SaleOfficeTranslationsDTO;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.i18n.I18nDictionaryService;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.CityInfoPO;
import com.juneyaoair.manage.b2c.entity.saleoffice.SaleOfficePO;
import com.juneyaoair.manage.b2c.mapper.TCityInfoMapper;
import com.juneyaoair.manage.b2c.mapper.TSaleOfficeMapper;
import com.juneyaoair.manage.b2c.service.TSaleOfficeService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 营业部管理
 */
@Api(value = "SaleOfficeController", tags = "营业部管理")
@RequestMapping("saleOffice")
@RestController
@Slf4j
public class SaleOfficeController {
    @Resource
    private TSaleOfficeService saleOfficeService;
    @Resource
    private TCityInfoMapper cityInfoMapper;
    @Resource
    private I18nDictionaryService i18nDictionaryService;

    @ApiOperation(value = "分页查询营业部信息", notes = "")
    @PostMapping("searchByPage")
    public R<PageResult<SaleOfficePO>> searchPage(@RequestBody SaleOfficePO po) {
        return new AbsPageProcessor<SaleOfficePO, SaleOfficePO>(po) {
            @Override
            public List<SaleOfficePO> process() {
                LambdaQueryWrapper<SaleOfficePO> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(StrUtil.isNotEmpty(po.id), SaleOfficePO::getId, po.id)
                        .like(StrUtil.isNotEmpty(po.name), SaleOfficePO::getName, po.name)
                        .eq(StrUtil.isNotEmpty(po.cityCode), SaleOfficePO::getCityCode, po.cityCode)
                        .eq(StrUtil.isNotEmpty(po.address), SaleOfficePO::getAddress, po.address)
                        .eq(StrUtil.isNotEmpty(po.officeTel), SaleOfficePO::getOfficeTel, po.officeTel)
                        .eq(StrUtil.isNotEmpty(po.fax), SaleOfficePO::getFax, po.fax)
                        .eq(StrUtil.isNotEmpty(po.officeTimeDesc), SaleOfficePO::getOfficeTimeDesc, po.officeTimeDesc)
                        .eq(StrUtil.isNotEmpty(po.counterTel), SaleOfficePO::getCounterTel, po.counterTel)
                        .eq(StrUtil.isNotEmpty(po.dutyTel), SaleOfficePO::getDutyTel, po.dutyTel)
                        .eq(StrUtil.isNotEmpty(po.officeEmail), SaleOfficePO::getOfficeEmail, po.officeEmail)
                        .eq(StrUtil.isNotEmpty(po.deleteFlag), SaleOfficePO::getDeleteFlag, po.deleteFlag)
                        .eq(StrUtil.isNotEmpty(po.region), SaleOfficePO::getRegion, po.region)
                        .orderByAsc(SaleOfficePO::getDeleteFlag);

                List<SaleOfficePO> list = saleOfficeService.list(wrapper);
                if (CollUtil.isEmpty(list)) {
                    return null;
                }
                List<List<String>> cityCodePartition = Lists.partition(list.stream()
                        .filter(i -> i != null && StrUtil.isNotBlank(i.cityCode))
                        .map(i -> i.cityCode)
                        .collect(Collectors.toList()), 900);
                LambdaQueryWrapper<CityInfoPO> wrapperFile = new LambdaQueryWrapper<>();

                wrapperFile.and(CollUtil.isNotEmpty(cityCodePartition), i -> {
                    for (List<String> strings : cityCodePartition) {
                        i.or(it ->
                                it.in(CityInfoPO::getCityCode, strings)
                        );
                    }
                });

                Map<String, CityInfoPO> cityMap = cityInfoMapper.selectList(wrapperFile).stream().collect(Collectors.toMap(i -> i.cityCode, j -> j));

                list.forEach(i -> {
                    i.createDatetime = null;
                    i.createMan = null;
                    i.updateDatetime = null;
                    i.updateMan = null;
                    i.cityName = cityMap.getOrDefault(i.cityCode, new CityInfoPO()).cityName;
                });
                return list;
            }
        }.execute();
    }

    @ApiOperation(value = "根据id获取营业部info", notes = "")
    @PostMapping("getById")
    public R<SaleOfficePO> getById(@RequestParam String id) {
        RDto<SaleOfficePO> rDto = new AbsOrdinaryProcessor<String, SaleOfficePO>(id) {
            @Override
            public SaleOfficePO process() {
                LambdaQueryWrapper<SaleOfficePO> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(SaleOfficePO::getId, id);
                return saleOfficeService.getOne(wrapper);
            }
        }.execute();
        if (rDto != null && rDto.getData() != null) {
            rDto.getData().createDatetime = null;
            rDto.getData().createMan = null;
            rDto.getData().updateDatetime = null;
            rDto.getData().updateMan = null;
        }
        return rDto;
    }

    @ApiOperation(value = "创建营业厅info", notes = "")
    @PostMapping("add")
    public R add(@RequestBody SaleOfficePO po) {
        if (StrUtil.isBlank(po.address)) {
            throw new HoServiceException("address can't be null!");
        }
        if (StrUtil.isBlank(po.name)) {
            throw new HoServiceException("name can't be null!");
        }

        po.id = HOStringUtil.newGUID();
        po.deleteFlag = Optional.ofNullable(po.deleteFlag).orElse("0");
        po.createDatetime = new Date();
        po.createMan = SecurityUtils.getUsername();
        po.updateDatetime = new Date();
        po.updateMan = SecurityUtils.getUsername();
        return R.ok(saleOfficeService.save(po));
    }

    /**
     * 删除字段对应的所有语言翻译
     * @param cityCode 城市代码
     * @param fieldType 字段类型
     */
    private void deleteFieldTranslations(String cityCode, String fieldType) {
        if (StrUtil.isNotBlank(cityCode)) {
            String original = String.format("%s.%s", cityCode, fieldType);
            List<I18nDictionaryKVDTO> translations = i18nDictionaryService.queryKVByType(
                    "BUSINESS_DEPARTMENT",
                    original,
                    null,
                    null
            );
            translations.forEach(trans -> i18nDictionaryService.deleteKV(trans.getId()));
            i18nDictionaryService.refreshCache("BUSINESS_DEPARTMENT");
        }
    }

    /**
     * 获取字段的所有语言翻译
     * @param cityCode 城市代码
     * @param fieldType 字段类型
     * @return 翻译Map(key:语言标签, value:翻译)
     */
    private Map<String, String> getFieldTranslations(String cityCode, String fieldType) {
        if (StrUtil.isNotBlank(cityCode)) {
            String original = String.format("%s.%s", cityCode, fieldType);
            List<I18nDictionaryKVDTO> translations = i18nDictionaryService.queryKVByType(
                    "BUSINESS_DEPARTMENT",
                    original,
                    null,
                    null
            );
            return translations.stream().collect(
                    Collectors.toMap(I18nDictionaryKVDTO::getLanguageTag, I18nDictionaryKVDTO::getTranslation)
            );
        }
        return new HashMap<>();
    }

    /**
     * 添加或更新字段的语言翻译，使用cityCode作为key前缀
     * @param cityCode 城市代码
     * @param fieldType 字段类型(NAME/ADDRESS/OFFICE_TIME_DESC)
     * @param fieldValue 字段值
     * @param translation 翻译
     * @param languageTag 语言标签
     */
    private void addFieldTranslation(String cityCode, String fieldType, String fieldValue, String translation, String languageTag) {
        if (StrUtil.isNotBlank(cityCode) && StrUtil.isNotBlank(translation)) {
            String original = String.format("%s.%s", cityCode, fieldType);
            i18nDictionaryService.addOrUpdateKVByType("BUSINESS_DEPARTMENT", original, translation, languageTag);
        }
    }

    @ApiOperation(value = "根据id逻辑删除 ", notes = "")
    @PostMapping("delById")
    public R<Boolean> delById(@RequestParam String id) {
        // 先查询原记录，用于删除翻译
        SaleOfficePO oldOffice = saleOfficeService.getOne(
            new LambdaQueryWrapper<SaleOfficePO>()
                .eq(SaleOfficePO::getId, id)
        );
        
        LambdaUpdateWrapper<SaleOfficePO> updateWrapper = new LambdaUpdateWrapper<>();
        Date now = new Date();

        updateWrapper.eq(SaleOfficePO::getId, id)
                .set(SaleOfficePO::getDeleteFlag, "1")/*1表示删除*/
                .set(SaleOfficePO::getUpdateDatetime, now)
                .set(SaleOfficePO::getUpdateMan, SecurityUtils.getUsername());
        boolean result = saleOfficeService.update(updateWrapper);
        
        // 删除对应的翻译
        if (result && oldOffice != null) {
            deleteFieldTranslations(oldOffice.getCityCode(), "NAME");
            deleteFieldTranslations(oldOffice.getCityCode(), "OFFICE_TIME_DESC");
            deleteFieldTranslations(oldOffice.getCityCode(), "ADDRESS");
        }
        
        return R.ok(result);
    }

    @ApiOperation(value = "根据id更新 selective", notes = "")
    @PostMapping("updateById")
    public R<Boolean> updateById(@RequestBody SaleOfficePO po) {
        // 先查询原记录，用于更新翻译
        SaleOfficePO oldOffice = saleOfficeService.getOne(
            new LambdaQueryWrapper<SaleOfficePO>()
                .eq(SaleOfficePO::getId, po.id)
        );
        
        LambdaUpdateWrapper<SaleOfficePO> updateWrapper = new LambdaUpdateWrapper<>();
        Date now = new Date();

        updateWrapper.eq(SaleOfficePO::getId, po.id)
                .set(po.name != null, SaleOfficePO::getName, po.name)
                .set(po.cityCode != null, SaleOfficePO::getCityCode, po.cityCode)
                .set(po.address != null, SaleOfficePO::getAddress, po.address)
                .set(po.officeTel != null, SaleOfficePO::getOfficeTel, po.officeTel)
                .set(po.fax != null, SaleOfficePO::getFax, po.fax)
                .set(po.officeTimeDesc != null, SaleOfficePO::getOfficeTimeDesc, po.officeTimeDesc)
                .set(po.counterTel != null, SaleOfficePO::getCounterTel, po.counterTel)
                .set(po.dutyTel != null, SaleOfficePO::getDutyTel, po.dutyTel)
                .set(po.officeEmail != null, SaleOfficePO::getOfficeEmail, po.officeEmail)
                .set(po.deleteFlag != null, SaleOfficePO::getDeleteFlag, po.deleteFlag)
                .set(po.region != null, SaleOfficePO::getRegion, po.region)
                .set(po.seq != null, SaleOfficePO::getSeq, po.seq)
                .set(SaleOfficePO::getUpdateDatetime, now)
                .set(SaleOfficePO::getUpdateMan, SecurityUtils.getUsername());
        boolean result = saleOfficeService.update(updateWrapper);
        
        // 更新对应的翻译
        if (result && oldOffice != null) {
            if (po.name != null && !po.name.equals(oldOffice.getName())) {
                deleteFieldTranslations(oldOffice.getCityCode(), "NAME");
            }
            
            if (po.officeTimeDesc != null && !po.officeTimeDesc.equals(oldOffice.getOfficeTimeDesc())) {
                deleteFieldTranslations(oldOffice.getCityCode(), "OFFICE_TIME_DESC");
            }
            
            if (po.address != null && !po.address.equals(oldOffice.getAddress())) {
                deleteFieldTranslations(oldOffice.getCityCode(), "ADDRESS");
            }
        }
        
        return R.ok(result);
    }

    @Resource
    private TSaleOfficeMapper saleOfficeMapper;

    @ApiOperation(value = "营业部初始化", notes = "")
    @PostMapping("initData")
    public R<Boolean> initData() {
        String s = readFile("/saleOffice.json");
        List<TSaleOfficePO> oriList = JSONArray.parseArray(s, TSaleOfficePO.class);
        log.info(oriList.toString());
        List<SaleOfficePO> saleOfficePOS = saleOfficeMapper.selectList(null);
        for (SaleOfficePO officePO : saleOfficePOS) {
            if (officePO == null || StrUtil.isNotEmpty(officePO.name)) {
                continue;
            }
            LambdaQueryWrapper<CityInfoPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CityInfoPO::getCityCode, officePO.cityCode);
            List<CityInfoPO> cityInfoPOS = cityInfoMapper.selectList(queryWrapper);
            if (CollUtil.isNotEmpty(cityInfoPOS) && cityInfoPOS.size() == 1) {
                officePO.name = cityInfoPOS.get(0).cityName + "营业部";
            }
            LambdaQueryWrapper<SaleOfficePO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SaleOfficePO::getId, officePO.getId());
            saleOfficeMapper.update(officePO, wrapper);
            log.info("update " + officePO.getId() + " " + officePO.name);
        }

        for (TSaleOfficePO officeDO : oriList) {
            SaleOfficePO po = new SaleOfficePO();
            po.id = UUID.randomUUID().toString().replaceAll("-", "");

            LambdaQueryWrapper<CityInfoPO> cityQueryWrapper = new LambdaQueryWrapper<>();
            cityQueryWrapper.eq(CityInfoPO::getCityName, officeDO.referName);
            List<CityInfoPO> cityInfoPOS = cityInfoMapper.selectList(cityQueryWrapper);
            if (CollUtil.isNotEmpty(cityInfoPOS) && cityInfoPOS.size() == 1) {
                po.cityCode = cityInfoPOS.stream().findFirst().orElse(new CityInfoPO()).cityCode;
            }
            po.name = officeDO.name;
            po.address = officeDO.address;
            po.officeTel = officeDO.officeTel;
            po.fax = officeDO.fax;
            po.officeTimeDesc = officeDO.officeTimeDesc;
            po.counterTel = officeDO.counterTel;
            po.officeEmail = officeDO.officeEmail;
//          po.dutyTel = officeDO.dutyTel;
            po.deleteFlag = "0";
            po.region = officeDO.region;
            po.createDatetime = new Date();
//          po.createMan = officeDO.createMan;
            po.updateDatetime = new Date();
//          po.updateMan = officeDO.updateMan;

            saleOfficeMapper.insert(po);
            log.info("insert " + po.id);
        }
        return R.ok(true);

    }

    private String readFile(String path) {
        BufferedReader reader = null;
        String laststr = "";
        try {
            InputStream inputStream = this.getClass().getResourceAsStream(path);
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
            reader = new BufferedReader(inputStreamReader);
            String tempString = null;
            while ((tempString = reader.readLine()) != null) {
                laststr += tempString;
            }
            reader.close();
        } catch (IOException e) {
            log.error(e.getMessage());
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    log.error(e.getMessage());
                }
            }
        }
        return laststr;
    }

    @Data
    public static class TSaleOfficePO {
        public String id;
        public String name;
        public String referName;
        public String region;
        public String officeTel;
        public String fax;
        public String address;
        public String officeTimeDesc;
        public String counterTel;
        public String officeEmail;
         public String deleteFlag;
    }

    @ApiOperation(value = "更新营业部多语言翻译", notes = "更新营业部名称、营业时间描述、地址的其他语言翻译")
    @PostMapping("updateTranslation")
    public R<Boolean> updateTranslation(@RequestBody SaleOfficeTranslationDTO request) {
        // 参数校验
        if (StrUtil.isBlank(request.getId()) || StrUtil.isBlank(request.getLanguageTag())) {
            throw new HoServiceException("id和languageTag不能为空");
        }

        // 获取营业部信息
        LambdaQueryWrapper<SaleOfficePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SaleOfficePO::getId, request.getId());
        SaleOfficePO saleOffice = saleOfficeService.getOne(wrapper);
        if (saleOffice == null) {
            throw new HoServiceException("营业部不存在");
        }

        addFieldTranslation(saleOffice.getCityCode(), "NAME", saleOffice.getName(), request.getNameTranslation(), request.getLanguageTag());
        addFieldTranslation(saleOffice.getCityCode(), "OFFICE_TIME_DESC", saleOffice.getOfficeTimeDesc(), request.getOfficeTimeDescTranslation(), request.getLanguageTag());
        addFieldTranslation(saleOffice.getCityCode(), "ADDRESS", saleOffice.getAddress(), request.getAddressTranslation(), request.getLanguageTag());

        return R.ok(true);
    }

    @ApiOperation(value = "查询营业部多语言翻译", notes = "查询营业部名称、营业时间描述、地址的所有语言翻译")
    @PostMapping("getTranslations")
    public R<SaleOfficeTranslationsDTO> getTranslations(@RequestParam String id) {
        // 获取营业部信息
        LambdaQueryWrapper<SaleOfficePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SaleOfficePO::getId, id);
        SaleOfficePO saleOffice = saleOfficeService.getOne(wrapper);
        if (saleOffice == null) {
            throw new HoServiceException("营业部不存在");
        }

        // 构建返回对象
        SaleOfficeTranslationsDTO result = new SaleOfficeTranslationsDTO();
        result.setId(saleOffice.getId());
        result.setName(saleOffice.getName());
        result.setOfficeTimeDesc(saleOffice.getOfficeTimeDesc());
        result.setAddress(saleOffice.getAddress());

        result.setNameTranslations(getFieldTranslations(saleOffice.getCityCode(), "NAME"));
        result.setOfficeTimeDescTranslations(getFieldTranslations(saleOffice.getCityCode(), "OFFICE_TIME_DESC"));
        result.setAddressTranslations(getFieldTranslations(saleOffice.getCityCode(), "ADDRESS"));

        return R.ok(result);
    }
 
    /**
     * 删除指定语言的字段翻译
     * @param cityCode 城市代码
     * @param fieldType 字段类型
     * @param languageTag 语言标签
     */
    private void deleteFieldTranslationByLanguage(String cityCode, String fieldType, String languageTag) {
        if (StrUtil.isNotBlank(cityCode) && StrUtil.isNotBlank(languageTag)) {
            String original = String.format("%s.%s", cityCode, fieldType);
            List<I18nDictionaryKVDTO> translations = i18nDictionaryService.queryKVByType(
                    "BUSINESS_DEPARTMENT",
                    original,
                    languageTag,
                    null
            );
            if (!translations.isEmpty()) {
                translations.forEach(trans -> i18nDictionaryService.deleteKV(trans.getId()));
            }
        }
    }

    @ApiOperation(value = "删除营业部多语言翻译", notes = "删除营业部指定语言的翻译")
    @PostMapping("deleteTranslation")
    public R<Boolean> deleteTranslation(@RequestBody DeleteSaleOfficeTranslationDTO request) {
        // 参数校验
        if (StrUtil.isBlank(request.getId()) || StrUtil.isBlank(request.getLanguageTag())) {
            throw new HoServiceException("id和languageTag不能为空");
        }
        
        // 获取营业部信息
        LambdaQueryWrapper<SaleOfficePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SaleOfficePO::getId, request.getId());
        SaleOfficePO saleOffice = saleOfficeService.getOne(wrapper);
        if (saleOffice == null) {
            throw new HoServiceException("营业部不存在");
        }
        
        boolean needRefreshCache = false;
        
        // 根据请求删除对应的翻译
        if (Boolean.TRUE.equals(request.getDeleteName())) {
            deleteFieldTranslationByLanguage(saleOffice.getCityCode(), "NAME", request.getLanguageTag());
            needRefreshCache = true;
        }
        
        if (Boolean.TRUE.equals(request.getDeleteOfficeTimeDesc())) {
            deleteFieldTranslationByLanguage(saleOffice.getCityCode(), "OFFICE_TIME_DESC", request.getLanguageTag());
            needRefreshCache = true;
        }
        
        if (Boolean.TRUE.equals(request.getDeleteAddress())) {
            deleteFieldTranslationByLanguage(saleOffice.getCityCode(), "ADDRESS", request.getLanguageTag());
            needRefreshCache = true;
        }
        
        // 只在有实际删除操作时刷新缓存
        if (needRefreshCache) {
            i18nDictionaryService.refreshCache("BUSINESS_DEPARTMENT");
        }
        
        return R.ok(true);
    }
}
