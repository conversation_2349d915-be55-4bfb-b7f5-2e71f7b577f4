package com.juneyaoair.ecs.manage.service.syncfile.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.map.MapUtil;
import com.juneyaoair.ecs.manage.enums.AirlineTypeEnum;
import com.juneyaoair.ecs.manage.enums.CountryCodeEnum;
import com.juneyaoair.ecs.manage.enums.ResultEnum;
import com.juneyaoair.ecs.manage.service.file.IFileUploadService;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.manage.b2c.mapper.TVersionInfoMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;
import java.io.File;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 同步静态资源
 */
public abstract class SyncStaticFileService<T> extends AbstractSyncService<List<T>> {
    @Lazy
    @Resource
    TVersionInfoMapper tVersionInfoMapper;
    @Lazy
    @Resource
    IFileUploadService fileUploadService;


    @Override
    public void processFile(List<T> data) {
        Map<String, Object> resultMap = getResultMap(data);
        Map<String, Object> groupMap = getResultMapForGroup(data);
        if (MapUtil.isNotEmpty(resultMap)) {
            processJsonFile(map2Json(resultMap));
            processJsFile(map2Js(resultMap));
        }
        if (MapUtil.isNotEmpty(groupMap)) {
            processGroupJsonFile(groupMap2Json(groupMap));
            processGroupJsFile(groupMap2Js(groupMap));
        }
        updateVersionNo();
        removeCacheInRedis(data);
    }

    private void processGroupJsFile(String jsContent) {
        String fileName = getGroupContentName() + ".js";
        File file = new File(String.format("%s%s", new ClassPathResource("/").getAbsolutePath(), fileName));
        cn.hutool.core.io.FileUtil.writeUtf8String(jsContent, file);
        fileUploadService.uploadFile(file, fileName, "flightBasicStatic");
        cn.hutool.core.io.FileUtil.del(file);
    }

    private void processGroupJsonFile(String jsContent) {
        String fileName = getGroupContentName() + ".json";
        File file = new File(String.format("%s%s", new ClassPathResource("/").getAbsolutePath(), fileName));
        cn.hutool.core.io.FileUtil.writeUtf8String(jsContent, file);
        fileUploadService.uploadFile(file, fileName, "flightBasicStatic");
        cn.hutool.core.io.FileUtil.del(file);
    }

    protected String getGroupContentName() {
        return null;
    }

    protected String groupMap2Js(Map<String, Object> groupMap) {
        return null;
    }

    protected String groupMap2Json(Map<String, Object> groupMap) {
        return null;
    }

    protected Map<String, Object> getResultMapForGroup(List<T> data) {
        return null;
    }

    protected abstract void removeCacheInRedis(List<T> data);

    protected abstract void updateVersionNo();

    private Map<String, Object> getResultMap(List<T> data) {
        Map<String, Object> result = new HashMap<>();
        Timestamp timestamp = DateUtil.getTimestamp();
        result.put("objData", data2ObjData(data));
        result.put("loginFlag", false);
        result.put("timeStamp", timestamp.getTime());
        result.put("resultCode", ResultEnum.S10001.getResultCode());
        result.put("resultInfo", ResultEnum.S10001.getResultInfo());
        return result;
    }

    protected abstract String map2Js(Map<String, Object> resultMap);

    private String map2Json(Map<String, Object> resultMap) {
        return JsonUtil.objectToJson(resultMap);
    }

    /**
     * 创建本地js文件-》上传—》删除本地文件
     */
    public void processJsFile(String jsContent) {
        String fileName = getContentName() + ".js";
        File file = new File(String.format("%s%s", new ClassPathResource("/").getAbsolutePath(), fileName));
        cn.hutool.core.io.FileUtil.writeUtf8String(jsContent, file);
        fileUploadService.uploadFile(file, fileName, "flightBasicStatic");
        cn.hutool.core.io.FileUtil.del(file);
    }

    protected abstract String getContentName();

    /**
     * 创建本地json文件-》上传—》删除本地文件
     */
    public void processJsonFile(String jsonContent) {
        String fileName = getContentName() + ".json";
        File file = new File(String.format("%s%s", new ClassPathResource("/").getAbsolutePath(), fileName));
        cn.hutool.core.io.FileUtil.writeUtf8String(jsonContent, file);
        fileUploadService.uploadFile(file, fileName, "flightBasicStatic");
        cn.hutool.core.io.FileUtil.del(file);
    }

    protected void updateVersionNo(String versionName) {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateString = formatter.format(currentTime);
        tVersionInfoMapper.updateVersionNoByVersionType(dateString, versionName);
    }

    public abstract Object data2ObjData(List<T> data);

    @Override
    public abstract List<T> prepareData();

    //改变区域属性
    public String  changeRegion(String international,String countryCode){
        String region = "";
        if (AirlineTypeEnum.INTERNATIONAL.code.equalsIgnoreCase(international)) {
            if (CollUtil.newArrayList(CountryCodeEnum.TW.getCode(), CountryCodeEnum.MO.getCode(), CountryCodeEnum.HK.getCode()).contains(countryCode)) {
                region = "R"/*港澳台*/;
            } else {
                region = "I"/*国际*/;
            }
        } else {
            region = "D"/*非港澳台的国内*/;
        }
        return region;
    }
}
