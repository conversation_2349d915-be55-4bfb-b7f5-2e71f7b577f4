package com.juneyaoair.ecs.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.google.common.collect.Maps;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 解析值机选座机型座位图
 * @created 2024/03/18 13:18
 */
public class SeatFlightTypeListener extends AnalysisEventListener<Map<Integer, String>> {

    @Getter
    private List<Map<String, String>> list;

    public SeatFlightTypeListener() {
        super();
        list = new LinkedList<>();
    }

    @Override
    public void invoke(Map<Integer, String> integerStringMap, AnalysisContext analysisContext) {
        analysisContext.readWorkbookHolder().setIgnoreEmptyRow(false);
        if (null == integerStringMap || integerStringMap.isEmpty()) {
            return;
        }
        Map<String, String> map = Maps.newHashMap();
        boolean allNull = true;
        for (Map.Entry<Integer, String> entry : integerStringMap.entrySet()) {
            String value = entry.getValue();
            map.put(String.valueOf(entry.getKey()), null == value ? "" : value);
            if (StringUtils.isNotBlank(value)) {
                allNull = false;
            }
        }
        if (allNull) {
            map = Maps.newHashMap();
            map.put("0", "");
            list.add(map);
        } else {
            list.add(map);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
