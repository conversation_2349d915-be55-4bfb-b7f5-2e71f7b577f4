package com.juneyaoair.ecs.excel.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * @ClassName CommonCoupon
 * @Description
 * <AUTHOR>
 * @Date 2025/3/31 8:39
 * @Version 1.0
 */
@Data
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.LEFT, dataFormat = 49)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ThirdPartPrize {

    @ExcelProperty(index = 0, value = "活动号")
    @NotBlank(message = "活动号不可为空")
    @ApiModelProperty(value = "活动号", required = true)
    private String activityCode;

    @ExcelProperty(index = 1, value = "商户号")
    @NotBlank(message = "商户号不可为空")
    @ApiModelProperty(value = "商户号", required = true)
    private String merchantCode;

    @ExcelProperty(index = 2, value = "奖品编码")
    @NotBlank(message = "奖品编码不能为空")
    @ApiModelProperty(value = "奖品编码", required = true)
    private String prizeCode;

    @ExcelProperty(index = 3, value = "奖品名称")
    @NotBlank(message = "奖品名称不能为空")
    @ApiModelProperty(value = "奖品名称", required = true)
    private String prizeName;

    @ExcelProperty(index = 4, value = "奖品类型")
    @NotBlank(message = "奖品类型不能为空")
    @ApiModelProperty(value = "奖品类型")
    private String prizeType;

    @ExcelProperty(index = 5, value = "奖品发放数量")
    @Min(value = 1, message = "奖品发放数量必须大于0")
    @ApiModelProperty(value = "奖品发放数量")
    private int prizeAmount;

    @ExcelProperty(index = 6, value = "奖品发放状态")
    @ApiModelProperty(value = "奖品发放状态", required = true)
    @NotBlank(message = "奖品发放状态不能为空")
    private String prizeStatus;

    @ExcelProperty(index = 7, value = "创建人")
    @ApiModelProperty(value = "创建人")
    @NotBlank(message = "创建人不能为空")
    private String createUser;

}
