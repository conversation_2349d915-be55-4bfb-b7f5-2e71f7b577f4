package com.juneyaoair.ecs.manage.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.dto.*;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.baseinfo.CoBrandCreditCardAdService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("creditCardAD")
@RestController
@RequiredArgsConstructor
@Api(value = "CoBrandCreditCardAdController", tags = "联名卡广告位")
@Slf4j
public class CoBrandCreditCardAdController extends HoBaseController {


    @Resource
    private CoBrandCreditCardAdService coBrandCreditCardAdService;

    @PostMapping(value = "list")
    @ApiOperation(value = "联名卡广告位列表", notes = "联名卡广告位列表", httpMethod = "POST")
    public R<PageResult<CoBrandCreditCardAdDTO>> creditCardADList(@RequestBody ListCoBrandCreditCardAdRequestDTO request) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        List<CoBrandCreditCardAdDTO> list = coBrandCreditCardAdService.list(request);
        return getPageData(list, pageDomain);
    }

    @PostMapping(value = "add")
    @ApiOperation(value = "新增联名卡广告位", notes = "新增联名卡广告位", httpMethod = "POST")
    public R addCreditCardAD(@RequestBody AddOrUpdateAdRequestDTO request) {
        coBrandCreditCardAdService.addCreditCardAD(request);
        return R.ok();
    }

    @PostMapping(value = "update")
    @ApiOperation(value = "修改联名卡广告位", notes = "修改联名卡广告位", httpMethod = "POST")
    public R updateCreditCardAD(@RequestBody AddOrUpdateAdRequestDTO request) {
        coBrandCreditCardAdService.updateCreditCardAD(request);
        return R.ok();
    }

    @PostMapping(value = "publish")
    @ApiOperation(value = "发布/下线联名卡广告位", notes = "发布/下线联名卡广告位", httpMethod = "POST")
    public R publishCreditCardAD(@RequestBody CreditCardAdIdRequestDTO request) {
        coBrandCreditCardAdService.publishCreditCardAD(request);
        return R.ok();
    }

    @PostMapping(value = "delete")
    @ApiOperation(value = "删除联名卡广告位", notes = "删除联名卡广告位", httpMethod = "POST")
    public R deleteCreditCardAD(@RequestBody CreditCardAdIdRequestDTO request) {
        coBrandCreditCardAdService.deleteCreditCardAD(request);
        return R.ok();
    }

    @PostMapping(value = "addDetail")
    @ApiOperation(value = "新增广告配置（APP/官网）", notes = "新增广告配置（APP/官网）", httpMethod = "POST")
    public R addCreditCardAdDetail(@RequestBody AddOrUpdateAdDetailRequestDTO request) {
        coBrandCreditCardAdService.addDetail(request);
        return R.ok();
    }

    @PostMapping(value = "updateDetail")
    @ApiOperation(value = "更新广告配置（APP/官网）", notes = "新增广告配置（APP/官网）", httpMethod = "POST")
    public R updateCreditCardAdDetail(@RequestBody AddOrUpdateAdDetailRequestDTO request) {
        coBrandCreditCardAdService.updateDetail(request);
        return R.ok();
    }


}
