package com.juneyaoair.ecs.manage.external.impl;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.ecs.http.bean.HttpResult;
import com.juneyaoair.ecs.http.service.HttpClientService;
import com.juneyaoair.ecs.manage.constant.ManageConstant;
import com.juneyaoair.ecs.manage.dto.base.*;
import com.juneyaoair.ecs.manage.enums.ResultEnum;
import com.juneyaoair.ecs.manage.external.OrderService;
import com.juneyaoair.ecs.manage.properties.ThirdAppUrlSet;
import com.juneyaoair.ecs.upg.param.CouponOrderDetailParam;
import com.juneyaoair.ecs.upg.result.CouponOrderDetailResult;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @Description 订单服务
 * @created 2023/10/24 8:58
 */
@Service
public class OrderServiceImpl implements OrderService {

    @Autowired
    private HttpClientService httpClientService;
    @Autowired
    private ThirdAppUrlSet thirdAppUrlSet;

    @Override
    public CouponOrderDetailResult getCouponOrderDetail(CouponOrderDetailParam couponOrderDetailParam) {
        OrderRequestBase<CouponOrderDetailParam> orderRequestBase = BaseRequestUtil.createOrderRequest(couponOrderDetailParam);
        HttpResult httpResult = httpClientService.doPostJson(orderRequestBase,thirdAppUrlSet.getOpenApiUrl() + ManageConstant.GET_COUPON_ORDER_DETAIL,null);
        if (!httpResult.isResult()) {
            throw new HoServiceException("查询权益券订单详情失败，请稍后再试");
        }
        Type type = new TypeToken<OrderResultBase<CouponOrderDetailResult>>() {
        }.getType();
        OrderResultBase<CouponOrderDetailResult> baseResult = JsonUtil.fromJson(httpResult.getResponse(), type);
        if (!ResultEnum.S1001.getResultCode().equals(baseResult.getResultCode())){
            throw new HoServiceException(baseResult.getErrorInfo());
        }
        return baseResult.getResult();
    }
}
