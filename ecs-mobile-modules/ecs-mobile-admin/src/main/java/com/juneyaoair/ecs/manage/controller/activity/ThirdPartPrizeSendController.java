package com.juneyaoair.ecs.manage.controller.activity;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.excel.bean.ThirdPartPrize;
import com.juneyaoair.ecs.excel.listener.ThirdPartPrizesListener;
import com.juneyaoair.ecs.manage.controller.HoBaseController;
import com.juneyaoair.ecs.manage.dto.activity.request.coupon.ThirdPartPrizeRequest;
import com.juneyaoair.ecs.manage.dto.activity.request.coupon.ThirdPartTemplateRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.commonprizesend.ThirdPartPrizeConfigInformationResponse;
import com.juneyaoair.ecs.manage.dto.activity.response.commonprizesend.ThirdPartPrizeResponse;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.enums.CommonCouponPrizeStatusEnum;
import com.juneyaoair.ecs.manage.service.indefinite.IThirdPartPrizeService;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @ClassName ThirdPartPrizeSendController
 * @Description
 * <AUTHOR>
 * @Date 2025/3/28 21:23
 * @Version 1.0
 */

@RequestMapping("thirdPart")
@RestController
@RequiredArgsConstructor
@Api(value = "ThirdPartPrizeSendController", tags = "第三方奖品后台操作API")
@Slf4j
@SuppressWarnings("all")
public class ThirdPartPrizeSendController extends HoBaseController {

    @Autowired
    private IThirdPartPrizeService thirdPartPrizeService;

    @Autowired
    private ApplicationContext applicationContext;

    @ApiOperation(value = "第三方奖品EXCEL模板下载")
    @PostMapping(value = "downloadTPrizeTemplate")
    public void downloadTPCouponTemplate(HttpServletResponse response, @RequestBody @Validated ThirdPartTemplateRequest thirdPartTempletRequest, BindingResult bindingResult) throws IOException {
        if (bindingResult.hasErrors()) {
            throw new HoServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "第三方奖品EXCEL模板.xlsx";
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
        List<ThirdPartPrize> thirdPartPrizeList = IntStream.range(0, thirdPartTempletRequest.getCouponCount())
                .mapToObj(elem -> {
                    return ThirdPartPrize.builder().activityCode(thirdPartTempletRequest.getActivityCode()).merchantCode(thirdPartTempletRequest.getMerchantCode()).prizeName(thirdPartTempletRequest.getPrizeName())
                            .prizeType(thirdPartTempletRequest.getPrizeType()).prizeAmount(thirdPartTempletRequest.getPrizeAmount()).prizeStatus(CommonCouponPrizeStatusEnum.INIT.getCode()).createUser(SecurityUtils.getUsername()).build();
                })
                .collect(Collectors.toList());
        EasyExcel.write(response.getOutputStream(), ThirdPartPrize.class)
                .sheet("第三方奖品EXCEL模板")
                .doWrite(thirdPartPrizeList);
    }

    @ApiOperation(value = "第三方奖品配置信息查询")
    @GetMapping(value = "toCatchTPrizeConfigInformation")
    public R<ThirdPartPrizeConfigInformationResponse> toCatchTPrizeConfigInformation() {
        try {
            return R.ok(thirdPartPrizeService.toCatchThirdPartPrizeConfigInformation());
        } catch (Exception exception) {
            log.error("第三方奖品配置信息查询查询失败:", exception);
            return R.fail("奖品配置信息查询失败");
        }
    }

    @ApiOperation(value = "导入第三方奖品信息")
    @PostMapping(value = "importThirdPartPrizes")
    public R<String> importThirdPartPrizes(@RequestPart(value = "file") MultipartFile file) {
        ThirdPartPrizesListener thirdPartPrizesListener = applicationContext.getBean(ThirdPartPrizesListener.class);
        try {
            EasyExcel.read(file.getInputStream(), ThirdPartPrize.class, thirdPartPrizesListener).sheet().doRead();
            if (CollectionUtils.isEmpty(thirdPartPrizesListener.getThirdPrizeRecordPOList())) {
                throw new HoServiceException("解析后文件不能为空");
            }
            thirdPartPrizeService.toBatchPreserveThirdPartPrizes(thirdPartPrizesListener.getThirdPrizeRecordPOList());
        } catch (HoServiceException hoServiceException) {
            log.error("导入第三方奖品编码,解析异常,异常信息:", hoServiceException);
            return R.fail(hoServiceException.getMessage());
        } catch (Exception exception) {
            log.error("导入第三方奖品编码,解析异常,异常信息:", exception);
            return R.fail("解析上传文件出现异常");
        }
        return R.ok("导入成功");
    }

    @PostMapping(value = "toCatchThirdPartPrizeList")
    @ApiOperation(value = "第三方奖品列表查询", httpMethod = "POST")
    public R<PageResult<ThirdPartPrizeResponse>> toCatchThirdPartPrizeList(@RequestBody @Validated ThirdPartPrizeRequest thirdPartPrizeRequest, BindingResult bindingResult) {
        try {
            if (bindingResult.hasErrors()) {
                throw new HoServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
            }
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(thirdPartPrizeRequest));
            PageDomain pageDomain = TableSupport.buildPageRequest();
            startPage(pageDomain);
            Page<ThirdPartPrizeResponse> localPage = PageHelper.getLocalPage();
            List<ThirdPartPrizeResponse> thirdCouponResponseList = thirdPartPrizeService.toCatchThirdPartPrizeList(thirdPartPrizeRequest);
            R<PageResult<ThirdPartPrizeResponse>> pageData = getPageData(thirdCouponResponseList, localPage);
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(pageData));
            return pageData;
        } catch (Exception exception) {
            log.error("入参:[{}]第三方奖品列表查询失败:", JSON.toJSONString(thirdPartPrizeRequest), exception);
            return R.fail(exception.getMessage());
        } finally {
            Context.setContext(null);
        }
    }

    @ApiOperation(value = "第三方奖品流水下载")
    @PostMapping(value = "downloadThirdPartPrizeRecords")
    public void downloadThirdPartPrizeRecords(HttpServletResponse response, @RequestBody @Validated ThirdPartPrizeRequest thirdPartPrizeRequest, BindingResult bindingResult) throws IOException {
        if (bindingResult.hasErrors()) {
            throw new HoServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "第三方奖品发放流水.xlsx";
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
        List<ThirdPartPrizeResponse> thirdCouponResponseList = thirdPartPrizeService.toCatchThirdPartPrizeList(thirdPartPrizeRequest);
        EasyExcel.write(response.getOutputStream(), ThirdPartPrizeResponse.class).sheet("第三方奖品发放流水").doWrite(thirdCouponResponseList);
    }
}
