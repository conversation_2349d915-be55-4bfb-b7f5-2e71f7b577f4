package com.juneyaoair.ecs.manage.service.travelreminder.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.juneyaoair.ecs.manage.dto.travelreminder.TravelReminderQueryDTO;
import com.juneyaoair.ecs.manage.dto.travelreminder.TravelReminderSaveDTO;
import com.juneyaoair.ecs.manage.dto.travelreminder.TravelReminderListDTO;
import com.juneyaoair.ecs.manage.dto.travelreminder.TravelReminderContentDTO;
import com.juneyaoair.ecs.manage.service.travelreminder.TravelReminderService;
import com.juneyaoair.manage.b2c.entity.TravelReminderDisplayPO;
import com.juneyaoair.manage.b2c.entity.TravelReminderContentPO;
import com.juneyaoair.manage.b2c.mapper.TravelReminderDisplayPOMapper;
import com.juneyaoair.manage.b2c.mapper.TravelReminderContentPOMapper;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TravelReminderServiceImpl implements TravelReminderService {
    @Resource
    private TravelReminderDisplayPOMapper displayMapper;
    
    @Resource
    private TravelReminderContentPOMapper contentMapper;

    @Override
    public List<TravelReminderListDTO> queryList(TravelReminderQueryDTO queryDTO) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTime = queryDTO.getMaintainStartTime() != null ? sdf.format(queryDTO.getMaintainStartTime()) : null;
        String endTime = queryDTO.getMaintainEndTime() != null ? sdf.format(queryDTO.getMaintainEndTime()) : null;
        
        List<TravelReminderDisplayPO> displayList = displayMapper.selectDisplayList(
            queryDTO.getTheme(),
            queryDTO.getChannel(),
            queryDTO.getMaintainer(),
            startTime,
            endTime
        );
        
        return displayList.stream().map(display -> {
            TravelReminderListDTO dto = new TravelReminderListDTO();
            BeanUtils.copyProperties(display, dto);
            dto.setMaintainer(display.getUpdatedBy());
            dto.setOperationType(display.getThemeStatus());
            
            // 设置sort值
            dto.setSort(Integer.valueOf(display.getDisplayType()));
            
            // 查询文案内容列表
            LambdaQueryWrapper<TravelReminderContentPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TravelReminderContentPO::getThemeId, display.getId());
            List<TravelReminderContentPO> contentList = contentMapper.selectList(wrapper);
            
            // 转换文案内容
            List<TravelReminderContentDTO> contentDTOList = contentList.stream().map(content -> {
                TravelReminderContentDTO contentDTO = new TravelReminderContentDTO();
                contentDTO.setLanguageTag(content.getLanguageTag());
                contentDTO.setTitle(content.getTitle());
                contentDTO.setContent(content.getContent());
                return contentDTO;
            }).collect(Collectors.toList());
            
            dto.setContents(contentDTOList);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        // 删除主题
        displayMapper.deleteById(id);
        
        // 删除相关内容
        LambdaQueryWrapper<TravelReminderContentPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TravelReminderContentPO::getThemeId, id);
        contentMapper.delete(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrSubmit(TravelReminderSaveDTO saveDTO) {
        // 校验sort必填
        if (saveDTO.getSort() == null) {
            throw new HoServiceException("顺序不能为空");
        }
        
        // 校验国际网站渠道的语言内容
        if ("SUBMIT".equals(saveDTO.getOperationType()) 
            && saveDTO.getChannel().contains("G_B2C") 
            && (saveDTO.getContents() == null || saveDTO.getContents().isEmpty())) {
            throw new RuntimeException("国际网站渠道必须维护至少一个语种内容");
        }

        // 保存主题信息
        TravelReminderDisplayPO displayPO = new TravelReminderDisplayPO();
        BeanUtils.copyProperties(saveDTO, displayPO);
        
        // 直接使用sort值作为displayType
        displayPO.setDisplayType(String.valueOf(saveDTO.getSort()));
        // 设置操作类型
        displayPO.setThemeStatus(saveDTO.getOperationType());
        
        Date now = new Date();
        String username = SecurityUtils.getUsername();
        
        if (StringUtils.isBlank(displayPO.getId())) {
            // 新增
            displayPO.setCreatedBy(username);
            displayPO.setCreatedTime(now);
            displayPO.setUpdatedBy(username);
            displayPO.setUpdatedTime(now);
            displayMapper.insert(displayPO);
        } else {
            // 更新
            displayPO.setUpdatedBy(username);
            displayPO.setUpdatedTime(now);
            displayMapper.updateById(displayPO);
            // 删除原有内容
            LambdaQueryWrapper<TravelReminderContentPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TravelReminderContentPO::getThemeId, displayPO.getId());
            contentMapper.delete(wrapper);
        }

        // 保存内容信息
        if (saveDTO.getContents() != null && !saveDTO.getContents().isEmpty()) {
            saveDTO.getContents().forEach(content -> {
                TravelReminderContentPO contentPO = new TravelReminderContentPO();
                contentPO.setThemeId(displayPO.getId());
                contentPO.setLanguageTag(content.getLanguageTag());
                contentPO.setTitle(content.getTitle());
                contentPO.setContent(content.getContent());
                contentPO.setCreatedBy(username);
                contentPO.setCreatedTime(now);
                contentPO.setUpdatedBy(username);
                contentPO.setUpdatedTime(now);
                contentMapper.insert(contentPO);
            });
        }
    }
} 