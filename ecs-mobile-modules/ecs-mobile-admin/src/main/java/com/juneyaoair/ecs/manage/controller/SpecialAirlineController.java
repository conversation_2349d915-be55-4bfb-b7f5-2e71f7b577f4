package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.specialAirline.SpecialAirlineBase;
import com.juneyaoair.ecs.manage.dto.specialAirline.SpecialAirlineBaseParam;
import com.juneyaoair.ecs.manage.dto.specialAirline.SpecialAirlineInfo;
import com.juneyaoair.ecs.manage.dto.specialAirline.SpecialAirlineQueryParam;
import com.juneyaoair.ecs.manage.service.SpecialAirLineService;
import com.ruoyi.common.core.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 特价航线配置
 * @created 2025/01/22 10:31
 */
@Slf4j
@RestController
public class SpecialAirlineController {

    @Autowired
    private SpecialAirLineService specialAirLineService;

    /**
     * 创建特价航线
     * @param specialAirlineBaseParam
     * @return
     */
    @RequestMapping(value = "/specialAirline/create", method = RequestMethod.POST)
    public R<Object> createSpecialAirline(@RequestBody SpecialAirlineBaseParam specialAirlineBaseParam) {
        specialAirLineService.create(specialAirlineBaseParam);
        return R.ok();
    }

    /**
     * 更新特价航线
     * @param specialAirlineBaseParam
     * @return
     */
    @RequestMapping(value = "/specialAirline/update", method = RequestMethod.POST)
    public R<Object> updateSpecialAirline(@RequestBody SpecialAirlineBaseParam specialAirlineBaseParam) {
        specialAirLineService.updateSpecialAirline(specialAirlineBaseParam);
        return R.ok();
    }

    /**
     * 删除特价航线
     * @param specialAirlineBase
     * @return
     */
    @RequestMapping(value = "/specialAirline/delete", method = RequestMethod.POST)
    public R<Object> deleteSpecialAirline(@RequestBody @Validated SpecialAirlineBase specialAirlineBase) {
        specialAirLineService.deleteSpecialAirline(specialAirlineBase);
        return R.ok();
    }

    /**
     * 查询特价航线清单
     * @param specialAirlineQueryParam
     * @return
     */
    @RequestMapping(value = "/specialAirline/getList", method = RequestMethod.POST)
    public R<PageResult<SpecialAirlineInfo>> getList(@RequestBody SpecialAirlineQueryParam specialAirlineQueryParam) {
        PageResult<SpecialAirlineInfo> pageResult = specialAirLineService.getList(specialAirlineQueryParam);
        return R.ok(pageResult);
    }

    /**
     * 查询指定ID特价航线
     * @param specialAirlineBase
     * @return
     */
    @RequestMapping(value = "/specialAirline/getSpecialAirlineInfo", method = RequestMethod.POST)
    public R<SpecialAirlineInfo> getSpecialAirlineInfo(@RequestBody @Validated SpecialAirlineBase specialAirlineBase) {
        return R.ok(specialAirLineService.getSpecialAirlineInfo(specialAirlineBase));
    }

}
