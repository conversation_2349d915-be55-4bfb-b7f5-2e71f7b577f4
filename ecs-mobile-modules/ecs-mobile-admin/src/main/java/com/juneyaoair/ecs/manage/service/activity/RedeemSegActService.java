package com.juneyaoair.ecs.manage.service.activity;

import com.juneyaoair.ecs.manage.dto.activity.redeem.segact.*;
import com.juneyaoair.ecs.manage.dto.activity.redeem.signact.SignActListRequest;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.manage.b2c.entity.RedeemActSignPO;

import java.util.List;

public interface RedeemSegActService {
    PageResult addSegAct(SegAddActRequest o);

    void updateSegAct(SegUpdateActRequest requestDto);

    void commonDelAct(DeleteActRequest requestDto);

    /**
     * 2023 - 兑换活动 - 越飞越省  - 活动参与详情
     * @param requestDto
     * @return
     */
    List<SegActUser> segActUserList(SegActUserListRequest requestDto);

    List<SegRedeemDTO> segRedeemRecord(SegRedeemRecordRequest requestDto);

    void commonAuditAct(ActAuditRequest request);

    List<SegExcelBO> exportSegActExcel(SegExportRequest requestDto);

    List<SegActBO> segActList(SignActListRequest request);
}
