package com.juneyaoair.ecs.manage.service.airline;

import com.juneyaoair.ecs.manage.dto.airline.routelabel.*;

import java.util.List;

public interface IRouteLabelService {
    List<RouteLabelDTO> list(ListRouteLabelRequestDTO request);

    void addRouteLabel(AddOrUpdateRouteLabelRequestDTO request);

    void updateRouteLabel(AddOrUpdateRouteLabelRequestDTO request);

    void deleteRouteLabel(RouteLabelIdRequestDTO request);

    void publishRouteLabel(RouteLabelIdRequestDTO request);

    void addRule(AddOrUpdateRuleRequestDTO request);

    void updateRule(AddOrUpdateRuleRequestDTO request);

    void deleteRule(RouteLabelIdRequestDTO request);

    void publishRule(RouteLabelIdRequestDTO request);
}
