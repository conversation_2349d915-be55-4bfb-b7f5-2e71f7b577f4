package com.juneyaoair.ecs.manage.service.resource;

import com.juneyaoair.ecs.manage.dto.activity.common.ResourceManaDTO;
import com.juneyaoair.ecs.manage.dto.activity.common.ResourcePageReq;
import com.juneyaoair.ecs.manage.dto.base.PageDataResponse;
import com.juneyaoair.manage.b2c.entity.ResourcePO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;

/**
 * @ClassName ResourceManagerService
 * @Description
 * <AUTHOR>
 * @Date 2025/7/23 10:04
 * @Version 1.0
 */
public interface ResourceManagerService {


    /**
     * <AUTHOR>
     * @Description 获取资源列表
     * @Date 2025/7/23 10:04
     * @param resourceReq
     * @return PageDataResponse
     **/
    PageDataResponse resourceListPageQuery(ResourcePageReq resourceReq);

    /**
     * <AUTHOR>
     * @Date 2025/7/24 10:49
     * @param id
     * @return
     */
    ResourcePO getResourceById(String id);

    /**
     * <AUTHOR>
     * @Date 2025/7/24 10:49
     * @param resourceManaDTO
     */
    void updateResourceById(ResourceManaDTO resourceManaDTO);

    /**
     * <AUTHOR>
     * @Date 2025/7/25 10:16
     * @param flag
     * @param id
     */
    void updateFlagById(String flag, String id);

    /**
     * 新增资源
     * <AUTHOR>
     * @Date 2025/7/28 10:16
     * @param platform
     * @param moduleName
     * @param minVer
     * @param maxVer
     * @param file
     * @return
     * @throws IOException
     * @throws NoSuchAlgorithmException
     */
    ResourcePO addResource(String platform,String moduleName,String minVer,String maxVer, MultipartFile file) throws IOException, NoSuchAlgorithmException;
}
