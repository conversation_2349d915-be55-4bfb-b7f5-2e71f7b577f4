package com.juneyaoair.ecs.manage.service.airline.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.juneyaoair.ecs.manage.dto.airline.AirLineExportDTO;
import com.juneyaoair.ecs.manage.dto.airline.AirLineRequestDTO;
import com.juneyaoair.ecs.manage.enums.YorNEnum;
import com.juneyaoair.ecs.manage.service.airline.IAirlineAggrService;
import com.juneyaoair.ecs.manage.service.syncfile.IAirlineSyncJsFileService;
import com.juneyaoair.ecs.manage.service.syncfile.IAirlineSyncStaticFileService;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.AirlineAPO;
import com.juneyaoair.manage.b2c.entity.AirlineLabelPO;
import com.juneyaoair.manage.b2c.entity.AirportInfoPO;
import com.juneyaoair.manage.b2c.entity.CityInfoPO;
import com.juneyaoair.manage.b2c.mapper.TAirlineAMapper;
import com.juneyaoair.manage.b2c.mapper.TAirlineLabelMapper;
import com.juneyaoair.manage.b2c.mapper.TAirportInfoMapper;
import com.juneyaoair.manage.b2c.mapper.TCityInfoMapper;
import com.juneyaoair.manage.b2c.service.IAirlineService;
import com.juneyaoair.manage.thirdapi.IAvCacheService;
import com.juneyaoair.manage.thirdapi.dto.FareFreshNotice;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class AirlineAggrServiceImpl implements IAirlineAggrService {

    @Resource
    TAirlineAMapper airLineMapper;
    @Resource
    private TAirlineLabelMapper airLineLabelMapper;
    @Resource
    IAirlineSyncJsFileService airlineSyncJsFileService;
    @Resource
    private IAirlineSyncStaticFileService airLineSyncstaticFileService;
    @Resource
    private TAirportInfoMapper airportInfoMapper;
    @Resource
    private TCityInfoMapper cityInfoMapper;
    @Autowired
    private IAirlineService airLineService;
    @Autowired
    private IAvCacheService avCacheService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void parseExcel(MultipartFile excelFIle) {
        ExcelReader reader = null;
        try {
            reader = ExcelUtil.getReader(excelFIle.getInputStream(), true);
        } catch (IOException e) {
            throw new HoServiceException(e.getMessage());
        }
        List<AirlineAPO> airlineAPOS = reader.readAll().stream().map(map -> {
            AirlineAPO po = new AirlineAPO();
            String depAirportCode = MapUtils.getString(map, "depAirport");
            String arrAirportCode = MapUtils.getString(map, "arrAirport");
            String tranAirportCode = MapUtils.getString(map, "transitAirport");
            tranAirportCode = tranAirportCode == null ? "" : tranAirportCode;
            String addonRemark = MapUtils.getString(map, "addonRemark");
            LambdaQueryWrapper<AirportInfoPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(AirportInfoPO::getAirportCode, depAirportCode, arrAirportCode, tranAirportCode);
            List<AirportInfoPO> airportInfoPOS = airportInfoMapper.selectList(queryWrapper);
            if (CollUtil.isEmpty(airportInfoPOS)) {
                return null;
            }
            LambdaQueryWrapper<AirlineAPO> queryAirlineWrapper = new LambdaQueryWrapper<>();
            queryAirlineWrapper.eq(AirlineAPO::getDepAirport, depAirportCode)
                    .eq(AirlineAPO::getArrAirport, arrAirportCode)
                    .like(AirlineAPO::getTransitAirport, tranAirportCode);
            List<AirlineAPO> airlineAPOSExist = airLineMapper.selectList(queryAirlineWrapper);
            if (CollUtil.isNotEmpty(airlineAPOSExist)) {
                //有重复
                return null;
            }
            Map<String, AirportInfoPO> airportCodeMap = airportInfoPOS.stream().collect(Collectors.toMap(i -> i.airportCode, j -> j));
            po.setDepAirport(depAirportCode);
            po.setDepCity(airportCodeMap.getOrDefault(depAirportCode, new AirportInfoPO()).cityCode);
            po.setArrAirport(arrAirportCode);
            po.setArrCity(airportCodeMap.getOrDefault(arrAirportCode, new AirportInfoPO()).cityCode);
            po.setTransitAirport(tranAirportCode);
            po.setTransitCity(airportCodeMap.getOrDefault(tranAirportCode, new AirportInfoPO()).cityCode);
            po.setAddonRemark(addonRemark);
            po.airlineId = HOStringUtil.newGUID();
            LambdaQueryWrapper<CityInfoPO> queryCityWrapper = new LambdaQueryWrapper<>();
            queryCityWrapper.in(CityInfoPO::getCityCode, airportInfoPOS.stream().map(i -> i.cityCode).collect(Collectors.toList()));
            List<CityInfoPO> cityInfoPOS = cityInfoMapper.selectList(queryCityWrapper);
            po.isInternationalAirline = cityInfoPOS.stream().anyMatch(i -> "I".equalsIgnoreCase(i.isInternational)) ? "I" : "D";
            po.isTransit = StrUtil.isNotBlank(tranAirportCode) ? "Y" : "N";
            po.isHoLine = "Y";
            po.createMan = SecurityUtils.getUsername();
            po.createTime = new Date();
            po.delflag = "N";
            po.airlineBegindate = new Date();
            po.airlineEnddate = DateUtil.StringToDate("2099-1-1", "yyyy-MM-dd");
            po.isBaggageDirect = "N";
            return po;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(airlineAPOS)) {
            return ;
        }
        //统计spa以及addon数量 国际线
        List<AirlineAPO> addOnAndSpaList = airlineAPOS.stream().filter(airlineAPO -> "I".equals(airlineAPO.getIsInternationalAirline()) && ("Y".equalsIgnoreCase(airlineAPO.getAddonRemark()) || "S".equals(airlineAPO.getAddonRemark()))).collect(Collectors.toList());
        if (addOnAndSpaList.size() > 500) {
            throw new HoServiceException("国际SPA以及ADDON线单次上传请勿超过500条");
        }
        for (AirlineAPO airlineAPO : airlineAPOS) {
            airLineMapper.insert(airlineAPO);
        }
        //异步刷新
        if (CollectionUtil.isNotEmpty(addOnAndSpaList)) {
            List<String> strList = addOnAndSpaList.stream().map(airlineAPO -> airlineAPO.getDepCity() + "-" + airlineAPO.getArrCity()).collect(Collectors.toList());
            String routeComb = strList.stream().collect(Collectors.joining(","));
            FareFreshNotice fareFreshNotice = new FareFreshNotice();
            fareFreshNotice.setNoticeType("1");
            fareFreshNotice.setRouteComb(routeComb);
            avCacheService.refreshAvCache(fareFreshNotice);
        }
    }

    @Override
    public List<AirLineExportDTO> exportList(AirLineRequestDTO requestDTO) {
        List<AirlineAPO> airlineAPOS = airLineMapper.selectByAll(airLineRequestDTO2AirlineAPO(requestDTO));
        if (CollectionUtil.isEmpty(airlineAPOS)) {
            return null;
        }
        List<List<String>> partition = Lists.partition(airlineAPOS.stream().map(i -> i.airlineId).collect(Collectors.toList()), 900);
        LambdaQueryWrapper<AirlineLabelPO> wrapper = new LambdaQueryWrapper<>();

        wrapper.and(i -> {
            for (List<String> strings : partition) {
                i.or(it ->
                        it.in(AirlineLabelPO::getAirlineId, strings)
                );
            }
        });
        List<AirlineLabelPO> airlineLabelPOS = airLineLabelMapper.selectList(wrapper);
        return airlineAPOS.stream().map(i -> {
            AirLineExportDTO exportDTO = new AirLineExportDTO();
            BeanUtils.copyProperties(i, exportDTO);
            exportDTO.addonRemark = ((Function<String, String>) s -> {
                switch (s) {
                    case "N":
                        return "普通";
                    case "Y":
                        return "Addon";
                    case "S":
                        return "SPA";
                    default:
                        return null;
                }
            }).apply(i.addonRemark);
            if (CollectionUtil.isNotEmpty(airlineLabelPOS)) {
                exportDTO.listLabel = StringUtils
                        .join(airlineLabelPOS.stream().filter(it -> StringUtils.equals(it.airlineId, i.airlineId)).collect(Collectors.toList()), ",");
            }
            return exportDTO;
        }).collect(Collectors.toList());
    }


    @Override
    public boolean syncJsonAndJs() {
        airLineSyncstaticFileService.process();
        return true;
    }

    @Override
    public boolean syncJs() {
        airlineSyncJsFileService.process();
        return true;
    }

    @Override
    public boolean add(AirlineAPO airLine, boolean needBack) {
        boolean result = airLineService.add(airLine, needBack);
        //如果添加的为国际航线，调用缓存刷新接口
        if (result &&
                "I".equalsIgnoreCase(airLine.getIsInternationalAirline()) &&
                ("S".equalsIgnoreCase(airLine.getAddonRemark()) || "Y".equalsIgnoreCase(airLine.getAddonRemark()))) {
            FareFreshNotice fareFreshNotice = new FareFreshNotice();
            fareFreshNotice.setRouteComb(airLine.getDepCity() + "-" + airLine.getArrCity());
            fareFreshNotice.setNoticeType("1");
            avCacheService.refreshAvCache(fareFreshNotice);
            if (needBack) {
                FareFreshNotice fareFreshNoticeBack = new FareFreshNotice();
                fareFreshNoticeBack.setRouteComb(airLine.getArrCity() + "-" + airLine.getDepCity());
                fareFreshNoticeBack.setNoticeType("1");
                avCacheService.refreshAvCache(fareFreshNotice);
            }
        }
        return result;
    }

    private AirlineAPO airLineRequestDTO2AirlineAPO(AirLineRequestDTO requestDTO) {
        AirlineAPO ret = new AirlineAPO();
        ret.delflag = YorNEnum.N.getStr();
        ret.depCity = requestDTO.getDepCity();
        ret.arrCity = requestDTO.getArrCity();
        ret.addonRemark = requestDTO.getAddonRemark();
        ret.depAirport = requestDTO.getDepAirport();
        ret.arrAirport = requestDTO.getArrAirport();
        ret.isTransit = requestDTO.getIsTransit();
        ret.isHoLine = requestDTO.getIsHoLine();
        return ret;
    }
}
