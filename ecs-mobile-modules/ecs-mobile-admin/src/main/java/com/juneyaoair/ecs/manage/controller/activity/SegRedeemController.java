package com.juneyaoair.ecs.manage.controller.activity;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.controller.HoBaseController;
import com.juneyaoair.ecs.manage.dto.activity.redeem.segact.*;
import com.juneyaoair.ecs.manage.dto.activity.redeem.signact.SignActListRequest;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.activity.RedeemSegActService;
import com.juneyaoair.ecs.utils.ExcelUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.RedeemActRedeemRecordPO;
import com.juneyaoair.manage.b2c.entity.RedeemActSignPO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 2023 - 积分兑换活动 - 飞行航段兑换 (越飞越省)
 */
@RequestMapping("segRedeem")
@RestController
@RequiredArgsConstructor
@Api(value = "SegRedeemController", tags = "2023-积分兑换活动-飞行航段兑换(越飞越省)")
@Slf4j
public class SegRedeemController extends HoBaseController {

    @Autowired
    RedeemSegActService redeemSegActService;

    @ApiOperation(value = "会员资产查询(越飞越省)", notes = "", httpMethod = "POST")
    @PostMapping(value = "userList")
    public R<PageResult<SegActUser>> userList(@RequestBody SegActUserListRequest requestDto) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        Page<SegActUser> pageInfo = PageHelper.getLocalPage();
        List<SegActUser> list = redeemSegActService.segActUserList(requestDto);
        return getPageData(list, pageInfo);
    }

    @ApiOperation(value = "导出用户活动明细(越飞越省)", notes = "", httpMethod = "POST")
    @PostMapping(value = "exportActExcel")
    public void exportExcel(@RequestBody SegExportRequest requestDto, HttpServletResponse response) {
        try {
            List<SegExcelBO> sheetList = redeemSegActService.exportSegActExcel(requestDto);
            LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
            fieldMap.put("ffpNo", "会员卡号");
            fieldMap.put("ticketNumber", "票号");
            fieldMap.put("flightDate", "航班日期");
            fieldMap.put("airlineCode", "航司号");
            fieldMap.put("flightNumber", "航班号");
            fieldMap.put("bookingClass", "舱位");
            fieldMap.put("isPro", "PRO航段");
            String fileName = requestDto.getFfpNo() + "_" +"越飞越省用户活动明细";
            ExcelUtil.listToExcel(sheetList, fieldMap, fileName, response);
        } catch (Exception e) {
            throw new HoServiceException("导出越飞越省用户活动明细明细异常");
        }
    }

    @ApiOperation(value = "查询活动配置(越飞越省)", notes = "", httpMethod = "POST")
    @PostMapping(value = "actList")
    public R<PageResult<SegActBO>> querySegAct(@RequestBody SignActListRequest request) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        List<SegActBO> list = redeemSegActService.segActList(request);
        return getPageData(list, pageDomain);
    }

    @ApiOperation(value = "新增活动配置(越飞越省)", notes = "", httpMethod = "POST")
    @PostMapping(value = "addAct")
    public R addAct(@RequestBody SegAddActRequest requestDto) {
        redeemSegActService.addSegAct(requestDto);
        return R.ok();
    }

    @ApiOperation(value = "编辑活动配置(越飞越省)", notes = "", httpMethod = "POST")
    @PostMapping(value = "updateAct")
    public R updateAct(@RequestBody SegUpdateActRequest requestDto) {
        redeemSegActService.updateSegAct(requestDto);
        return R.ok();
    }

    @ApiOperation(value = "删除活动配置(越飞越省)", notes = "", httpMethod = "POST")
    @PostMapping(value = "delAct")
    public R delAct(@RequestBody DeleteActRequest requestDto) {
        redeemSegActService.commonDelAct(requestDto);
        return R.ok();
    }

    @ApiOperation(value = "审核活动配置(越飞越省)", notes = "另一用户点击确认活动", httpMethod = "POST")
    @PostMapping(value = "auditAct")
    public R auditAct(@RequestBody ActAuditRequest requestDto) {
        redeemSegActService.commonAuditAct(requestDto);
        return R.ok();
    }

    @ApiOperation(value = "兑换记录(越飞越省)", notes = "", httpMethod = "POST")
    @PostMapping(value = "redeemRecord")
    public R<PageResult<SegRedeemDTO>> redeemRecord(@RequestBody SegRedeemRecordRequest requestDto) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        Page<RedeemActRedeemRecordPO> pageInfo = PageHelper.getLocalPage();
        List<SegRedeemDTO> list = redeemSegActService.segRedeemRecord(requestDto);
        return getPageData(list, pageInfo);
    }

    @ApiOperation(value = "导出兑换记录(越飞越省)", notes = "", httpMethod = "POST")
    @PostMapping(value = "exportRedeemRecordExcel")
    public void exportRedeemRecordExcel(@RequestBody SegRedeemRecordRequest requestDto, HttpServletResponse response) {
        try {
            List<SegRedeemDTO> sheetList = redeemSegActService.segRedeemRecord(requestDto);
            //
            LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
            fieldMap.put("redeemTime", "兑换时间");
            fieldMap.put("awardName", "奖品名称");
            fieldMap.put("awardDes", "奖品描述");
            fieldMap.put("ffpNo", "会员卡号");
            fieldMap.put("pointCount", "消耗积分");
            fieldMap.put("ticketNo", "票号");
            fieldMap.put("redeemState", "兑换状态");
            //
            String fileName = DateUtils.getTime() + "_" + "兑换记录";
            ExcelUtil.listToExcel(sheetList, fieldMap, fileName, response);
        } catch (Exception e) {
            throw new HoServiceException("导出越飞越省兑换记录异常");
        }
    }

}
