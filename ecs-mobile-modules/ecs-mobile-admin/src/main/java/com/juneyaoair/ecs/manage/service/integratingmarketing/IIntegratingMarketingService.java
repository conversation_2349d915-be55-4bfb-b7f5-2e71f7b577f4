package com.juneyaoair.ecs.manage.service.integratingmarketing;

import com.juneyaoair.ecs.manage.dto.activity.request.integratingmarketingController.IntegratingMarketingDetailQueryRequest;
import com.juneyaoair.ecs.manage.dto.activity.request.integratingmarketingController.IntegratingMarketingRewardsRequest;
import com.juneyaoair.ecs.manage.dto.activity.request.integratingmarketingController.IntegratingMarketingTotalQueryRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingDetailQueryResponse;
import com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingRewardResponse;
import com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingTotalQueryResponse;
import com.juneyaoair.ecs.manage.dto.base.PageResult;

import java.util.List;

/**
 * @ClassName IIntegratingMarketingService
 * @Description
 * <AUTHOR>
 * @Date 2025/6/29 11:01
 * @Version 1.0
 */
public interface IIntegratingMarketingService {

    /**
     * 分页查询全员营销汇总信息
     *
     * @param request 查询请求参数
     * @return 分页结果
     */
    PageResult<IntegratingMarketingTotalQueryResponse> doTripSummaryQueryWithPage(IntegratingMarketingTotalQueryRequest request);

    List<IntegratingMarketingTotalQueryResponse> queryMarketingDataForExport(IntegratingMarketingTotalQueryRequest integratingMarketingTotalQueryRequest);

    /**
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingDetailQueryResponse>
     * <AUTHOR>
     * @Description 全员营销明细数据查询
     * @Date 13:34 2025/6/29
     **/
    List<IntegratingMarketingDetailQueryResponse> doTripDetailQuery(IntegratingMarketingDetailQueryRequest integratingMarketingDetailQueryRequest);

    /**
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingRewardResponse>
     * <AUTHOR>
     * @Description 月度奖励金额查询
     * @Date 14:49 2025/6/29
     **/
    List<IntegratingMarketingRewardResponse> downloadTripRewardList(IntegratingMarketingRewardsRequest integratingMarketingRewardsRequest);

}
