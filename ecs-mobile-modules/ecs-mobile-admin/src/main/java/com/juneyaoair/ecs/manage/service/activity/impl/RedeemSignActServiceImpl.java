package com.juneyaoair.ecs.manage.service.activity.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.juneyaoair.ecs.manage.dto.activity.redeem.signact.*;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.enums.activity.redeem.RedeemActEnum;
import com.juneyaoair.ecs.manage.enums.activity.redeem.RedeemActPointEnum;
import com.juneyaoair.ecs.manage.enums.activity.redeem.RedeemActStateEnum;
import com.juneyaoair.ecs.manage.service.activity.RedeemSignActService;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.RedeemActAwardPO;
import com.juneyaoair.manage.b2c.entity.RedeemActPO;
import com.juneyaoair.manage.b2c.entity.RedeemActRedeemRecordPO;
import com.juneyaoair.manage.b2c.entity.RedeemActSignPO;
import com.juneyaoair.manage.b2c.mapper.RedeemActAwardPOMapper;
import com.juneyaoair.manage.b2c.mapper.RedeemActPOMapper;
import com.juneyaoair.manage.b2c.mapper.RedeemActRedeemRecordPOMapper;
import com.juneyaoair.manage.b2c.mapper.RedeemActSignPOMapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RedeemSignActServiceImpl implements RedeemSignActService {

    @Resource
    RedeemActPOMapper actPOMapper;
    @Resource
    RedeemActAwardPOMapper awardPOMapper;
    @Resource
    RedeemActSignPOMapper signPOMapper;
    @Resource
    RedeemActRedeemRecordPOMapper recordPOMapper;

    @Override
    @Transactional
    public PageResult addSignAct(SignAddActRequest requestDto) {

        // 活动Id
        String actId = IdUtil.simpleUUID();
        // 奖品Id
        String awardId = IdUtil.simpleUUID();

        // 配置活动
        RedeemActPO redeemActPO = RedeemActPO.builder()
                .id(actId)
                .actName(DateUtil.formatDateTime(requestDto.getActStartTime()) + "-" + DateUtil.formatDateTime(requestDto.getActEndTime()))
                .actStartTime(requestDto.getActStartTime())
                .actEndTime(requestDto.getActEndTime())
                .actType(RedeemActEnum.SIGN_2023.getActType())
                .actState(RedeemActStateEnum.NEW.getActState())
                .createdBy(SecurityUtils.getUsername())
                .updatedBy(SecurityUtils.getUsername())
                .createdTime(new Date())
                .updatedTime(new Date())
                .build();
        actPOMapper.insert(redeemActPO);

        // 配置活动奖品
        List<SignActAwardDTO> awardDTOS = requestDto.getAwardList();

        for (int i = 0; i < awardDTOS.size(); i++) {
            RedeemActAwardPO redeemActAwardPO = RedeemActAwardPO.builder()
                    // 主键
                    .id(IdUtil.simpleUUID())
                    .actId(actId)
                    .awardId(awardId)
                    .awardName(awardDTOS.get(i).getAwardName())
                    .awardDesc(awardDTOS.get(i).getAwardDesc())
                    .monthlyLimit(awardDTOS.get(i).getMonthlyLimit())
                    .consumedPoint(String.valueOf(awardDTOS.get(i).getNeedPoint()))
                    .consumedPointType(RedeemActPointEnum.SIGN_POINT.getPointType())
                    .awardEnumType(RedeemActEnum.SIGN_2023.getActType())
                    .awardEnumTypeAttr(awardDTOS.get(i).getPrizePoolCode())
                    .createdBy(SecurityUtils.getUsername())
                    .updatedBy(SecurityUtils.getUsername())
                    .createdTime(new Date())
                    .updatedTime(new Date())
                    .build();

            awardPOMapper.insert(redeemActAwardPO);
        }

        return null;
    }

    @Override
    @DSTransactional
    public void updateSignAct(SignUpdateActRequest request) {

        RedeemActPO actPO = actPOMapper.selectById(request.getId());

        // 活动Id
        String actId = actPO.getId();
        // 奖品Id
        String awardId = IdUtil.simpleUUID();

        // 校验 活动状态
        if (actPO.getActState().equals(RedeemActStateEnum.AUDITED.getActState())
                || actPO.getActState().equals(RedeemActStateEnum.DELETE.getActState())) {
            throw new HoServiceException("已审核活动不可更新");
        }


        // Update ACT
        actPO.setActStartTime(request.getActStartTime());
        actPO.setActEndTime(request.getActEndTime());
        actPO.setUpdatedBy(SecurityUtils.getUsername());
        actPO.setUpdatedTime(new Date());
        actPOMapper.updateById(actPO);

        // Update ACT AWARD

        // DELETE OLD
        List<RedeemActAwardPO> awardPOS = awardPOMapper.selectByActId(actPO.getId());
        awardPOMapper.deleteBatchIds(awardPOS.stream().map(RedeemActAwardPO::getId).collect(Collectors.toList()));

        // INSERT NEW
        List<SignActAwardDTO> awardDTOS = request.getAwardList();

        for (int i = 0; i < awardDTOS.size(); i++) {
            RedeemActAwardPO redeemActAwardPO = RedeemActAwardPO.builder()
                    // 主键
                    .id(IdUtil.simpleUUID())
                    .actId(actId)
                    .awardId(awardId)
                    .awardName(awardDTOS.get(i).getAwardName())
                    .awardDesc(awardDTOS.get(i).getAwardDesc())
                    .monthlyLimit(awardDTOS.get(i).getMonthlyLimit())
                    .consumedPoint(String.valueOf(awardDTOS.get(i).getNeedPoint()))
                    .consumedPointType(RedeemActPointEnum.SIGN_POINT.getPointType())
                    .awardEnumType(RedeemActEnum.SIGN_2023.getActType())
                    .awardEnumTypeAttr(awardDTOS.get(i).getPrizePoolCode())
                    .createdBy(SecurityUtils.getUsername())
                    .updatedBy(SecurityUtils.getUsername())
                    .createdTime(new Date())
                    .updatedTime(new Date())
                    .build();

            awardPOMapper.insert(redeemActAwardPO);
        }
    }

    @Override
    public List<SignActUser> signActUserList(SignActUserListRequest request) {
        List<SignActUser> signActUserList = signPOMapper.selectSignUserList(request);
        signActUserList.forEach(v -> v.setMonth(request.getMonth()));
        return signActUserList;
    }

    @Override
    public List<RedeemActSignPO> exportSignActExcel(SignExportRequest request) {
        return signPOMapper.selectExportExcel(request);
    }

    @Override
    public List<SignActBO> signActList(SignActListRequest request) {

        List<SignActBO> resData = new ArrayList<>();

        List<RedeemActPO> actPOS = actPOMapper.selectActByTime(request,RedeemActEnum.SIGN_2023.getActType());

        for (RedeemActPO actPO : actPOS) {
            List<RedeemActAwardPO> awardPOS = awardPOMapper.selectByActId(actPO.getId());

            List<SignAwardBO> awardList = awardPOS.stream().map(v -> SignAwardBO.builder()
                    .id(v.getId())
                    .awardName(v.getAwardName())
                    .awardDesc(StringEscapeUtils.unescapeHtml(v.getAwardDesc()))
                    .monthlyLimit(v.getMonthlyLimit())
                    .needPoint(v.getConsumedPoint())
                    .prizePoolCode(v.getAwardEnumTypeAttr())
                    .build()).collect(Collectors.toList());

            resData.add(SignActBO.builder()
                    .id(actPO.getId())
                    .actStartTime(actPO.getActStartTime())
                    .actEndTime(actPO.getActEndTime())
                    .auditedBy(actPO.getAuditedBy())
                    .audited(actPO.getActState().equals(RedeemActStateEnum.AUDITED.getActState()))
                    .createBy(actPO.getCreatedBy())
                    .updateBy(actPO.getUpdatedBy())
                    .awardList(awardList)
                    .build());
        }

        return resData;
    }

    @Override
    public List<SignRedeemRecordBO> redeemRecord(SignRedeemRecordRequest request) {

        List<RedeemActRedeemRecordPO> redeemRecordPOS = recordPOMapper.selectSignRedeemRecord(request);

        return redeemRecordPOS.stream().map(v -> SignRedeemRecordBO.builder()
                .redeemTime(v.getCreateTime())
                .awardName(v.getAwardName())
                .awardDes(v.getAwardDesc())
                .ffpNo(v.getFfpNo())
                .pointCount(v.getPoint())
                .redeemState(v.getIssuanceState())
                .build()).collect(Collectors.toList());
    }

}
