package com.juneyaoair.ecs.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.google.common.collect.Sets;
import com.juneyaoair.ecs.excel.bean.UpgAirlineRuleSegment;
import com.juneyaoair.ecs.utils.ValidatorUtils;
import lombok.Getter;

import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @created 2023/8/21 13:18
 */
public class UpgAirlineRuleListener extends AnalysisEventListener<UpgAirlineRuleSegment> {

    @Getter
    private Set<String> segmentSet = Sets.newHashSet();

    public UpgAirlineRuleListener() {
        super();
        segmentSet.clear();
    }

    @Override
    public void invoke(UpgAirlineRuleSegment upgAirlineRuleSegment, AnalysisContext analysisContext) {
        ValidatorUtils.valid(upgAirlineRuleSegment);
        segmentSet.add(upgAirlineRuleSegment.getDepAirportCode() + "-" + upgAirlineRuleSegment.getArrAirportCode());
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
