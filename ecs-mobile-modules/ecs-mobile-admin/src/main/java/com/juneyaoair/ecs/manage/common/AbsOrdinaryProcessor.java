package com.juneyaoair.ecs.manage.common;

import com.juneyaoair.ecs.manage.controller.HoBaseController;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.esc.manage.exception.HoServiceException;

public abstract class AbsOrdinaryProcessor<REQ, RES> extends HoBaseController {
    private final REQ _req;

    public AbsOrdinaryProcessor(REQ req) {
        this._req = req;
    }

    public abstract RES process();

    public RDto<RES> execute() {
        RDto<RES> ret = null;
        try {
            initContext();
            ret = RDto.ok(process());
            ret.setMsg(ret.getMsg());
            ret.traceId = Context.getContext().getId();
        } catch (Exception e) {
            throw new HoServiceException(e.getMessage());
        } finally {
            Context.remove();
        }
        return ret;
    }
}
