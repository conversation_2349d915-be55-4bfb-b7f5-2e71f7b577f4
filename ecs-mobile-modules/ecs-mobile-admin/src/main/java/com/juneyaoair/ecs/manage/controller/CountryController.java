package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.country.CountryDTO;
import com.juneyaoair.ecs.manage.service.syncfile.CountrySyncFileService;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.manage.b2c.model.RegionCountryJoin;
import com.juneyaoair.manage.b2c.service.ICountryService;
import com.juneyaoair.manage.b2c.entity.CountryJoinRegionPO;
import com.juneyaoair.manage.b2c.entity.CountryPO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RequestMapping("country")
@RestController
@RequiredArgsConstructor
@Api(value = "CountryController", tags = "国家管理")
@Slf4j
public class CountryController extends HoBaseController {

    @Autowired
    private ICountryService ICountryService;
    @Autowired
    private CountrySyncFileService countrySyncFileService;

    @PostMapping(value = "searchForPage")
    @ApiOperation(value = "分页查询国家信息", notes = "", httpMethod = "POST")
    public R<PageResult<CountryDTO>> searchForPage(@RequestBody CountryPO countryPO) {
        try {
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(countryPO));
            PageDomain pageDomain = TableSupport.buildPageRequest();
            R<PageResult<CountryDTO>> ret = R.ok(ICountryService.searchByAll(countryPO, pageDomain));
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(ret));
            return ret;
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            Context.setContext(null);
        }
        return R.fail();
    }

    @PostMapping(value = "searchList")
    @ApiOperation(value = "查询国家及洲信息", notes = "", httpMethod = "POST")
    public R<List<CountryJoinRegionPO>> searchList(@RequestBody CountryPO countryPO) {
        try {
            return R.ok(ICountryService.selectJoinRegoinByAll(countryPO));
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }

    @GetMapping(value = "searchCountryList")
    @ApiOperation(value = "查询所有国外国家信息（to confirm）", notes = "", httpMethod = "GET")
    public R<List<CountryJoinRegionPO>> searchCountryList() {
        try {
            return R.ok(ICountryService.selectByRegionCodeOrderBySequence());
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }

    @GetMapping(value = "searchRegionCountry")
    @ApiOperation(value = "查询所有国家信息(按洲分组)", notes = "", httpMethod = "GET")
    public R<List<RegionCountryJoin>> searchRegionCountry() {
        return R.ok(ICountryService.searchRegionCountry());
    }

    @PostMapping(value = "delete")
    public R delete(@RequestBody CountryPO countryPO) {
        try {
            if (ICountryService.deleteCountry(countryPO)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }

    @PostMapping(value = "add")
    public R add(@RequestBody CountryPO countryPO) {
        try {
            countryPO.setCreateDatetime(new Date());
            countryPO.setCreatorId(SecurityUtils.getUserId());
            if (ICountryService.add(countryPO)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }

    @PostMapping(value = "update")
    @ApiOperation(value = "更新国家信息", notes = "", httpMethod = "POST")
    public R update(@RequestBody CountryPO countryPO) {
        try {
            return R.ok(ICountryService.update(countryPO));
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }

    /**
     * 更新国家静态文件
     */
    @ResponseBody
    @RequestMapping(value = "updateStaticFile", method = RequestMethod.GET)
    public R<Boolean> updateCountryStaticFile() {
        countrySyncFileService.syncJson();
        return R.ok();
    }
}
