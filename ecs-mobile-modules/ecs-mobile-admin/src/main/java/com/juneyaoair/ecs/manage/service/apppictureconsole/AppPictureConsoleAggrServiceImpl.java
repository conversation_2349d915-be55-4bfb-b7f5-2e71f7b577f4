package com.juneyaoair.ecs.manage.service.apppictureconsole;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.juneyaoair.ecs.manage.dto.activity.response.AppPicture;
import com.juneyaoair.ecs.manage.dto.activity.response.FileInfo;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.mapper.AppPicMapper;
import com.juneyaoair.manage.b2c.mapper.FileInfoMapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AppPictureConsoleAggrServiceImpl implements IAppPictureConsoleAggrService {
    @Resource
    AppPicMapper appPicMapper;
    @Resource
    FileInfoMapper fileInfoMapper;

    @Override
    public AppPicture getPicture(AppPicture req, HttpServletRequest request) {
        AppPicture appPicture = appPicMapper.searchOneByPicId(req);
        if (null == appPicture) {
            throw new HoServiceException("查询无数据");
        }
        List<FileInfo> fileInfoList = new ArrayList<>();
        List<FileInfo> fileInfos = fileInfoMapper.searchAllByCondition(new FileInfo());
        String fileIds = appPicture.getFileIds();
        if (CollectionUtil.isNotEmpty(fileInfos) && StringUtils.isNotEmpty(fileIds)) {
            //处理文件信息
            JSONArray fileIdArray = JSON.parseArray(fileIds);
            for (Object fileId : fileIdArray) {
                List<FileInfo> fileInformation = fileInfos.stream().filter(el -> StringUtils.isNotEmpty(el.getFileId())
                        && StringUtils.isNotEmpty(el.getLinkId()) && el.getFileId().equals(fileId.toString()) && el.getLinkId().equals(appPicture.getPicId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(fileInformation)) {
                    fileInfoList.addAll(fileInformation);
                }
            }
            //处理优惠券码
            String shareCouponsStr = appPicture.getShareCouponsStr();
            List<String> shareCouponList;
            if (StringUtils.isNotEmpty(shareCouponsStr)) {
                shareCouponList = JsonUtil.jsonToList(shareCouponsStr, new com.google.gson.reflect.TypeToken<List<String>>() {
                }.getType());
            } else {
                shareCouponList = new ArrayList<>();
            }
            appPicture.setShareCouponList(shareCouponList);
            String path = request.getContextPath();
            if (appPicture.getDescription2() != null && appPicture.getDescription2().length > 0) {
                try {
                    appPicture.setDescription(new String(appPicture.getDescription2(), "GBK"));
                } catch (Exception e) {
                    log.error("获取图片出错:", e);
                }
            }
            if (CollectionUtils.isNotEmpty(fileInfoList)) {
                for (FileInfo fileInfo : fileInfoList) {
                    fileInfo.setServerPath(path);
                }
            }
            appPicture.setFileInfos(fileInfoList);
        }
        return appPicture;
    }

    @Override
    @DSTransactional
    public boolean addPicture(AppPicture appPicture) {
        if (null == appPicture) {
            throw new HoServiceException("输入参数不可为空");
        }
        int affectLine;
        appPicture.setStatus("N");
        appPicture.setPicId(UUID.randomUUID().toString());
        appPicture.setCreateMan(SecurityUtils.getUsername());
        appPicture.setCreateTime(DateUtil.getCurrentDateStr());
        appPicture.setUpdateTime(DateUtil.getDateStringAllDate(new Date()));
        appPicture.setStartTime(DateUtil.convertDate2Str(DateUtil.StringToDate(appPicture.getStartTime(), DateUtil.DATE_FORMATE), DateUtil.DATE_FORMATE));
        appPicture.setEndTime(DateUtil.convertDate2Str(DateUtil.StringToDate(appPicture.getEndTime(), DateUtil.DATE_FORMATE), DateUtil.DATE_FORMATE));
        //将string转换为byte[]
        if (StringUtils.isNotEmpty(appPicture.getDescription())) {
            try {
                appPicture.setDescription2(appPicture.getDescription().getBytes("GBK"));
            } catch (Exception e) {
                log.error("转换出错:", e);
            }
            appPicture.setDescription("");
        }
        String[] fileIds = new String[appPicture.getFileInfos().size()];
        for (int i = 0; i < appPicture.getFileInfos().size(); i++) {
            FileInfo file = appPicture.getFileInfos().get(i);
            fileIds[i] = file.getFileId();
        }
        appPicture.setFileIds(JSON.toJSON(fileIds).toString());
        //分享优惠券活动处理
        if (CollectionUtils.isNotEmpty(appPicture.getShareCouponList())) {
            appPicture.setShareCouponsStr(appPicture.getShareCouponList().toString());
        }
        affectLine = appPicMapper.save(appPicture);
        for (FileInfo file : appPicture.getFileInfos()) {
            file.setLinkId(appPicture.getPicId());
            fileInfoMapper.insertFileInfoV2(file);
        }
        return affectLine > 0;
    }

    @Override
    public boolean delPicture(AppPicture appPicture) {
        return appPicMapper.deleteById(appPicture.getPicId()) > 0;
    }

    @Override
    @DSTransactional
    public boolean releaseAndOfflinePicture(AppPicture picture) {
        int affectLine;
        if (null == picture) {
            throw new HoServiceException("请求参数不可为空");
        }
        picture.setUpdateMan(SecurityUtils.getUsername());
        picture.setUpdateTime(DateUtil.getDateStringAllDate(new Date()));
        if (StringUtils.isNotEmpty(picture.getStartTime())) {
            picture.setStartTime(DateUtil.convertDate2Str(DateUtil.StringToDate(picture.getStartTime(), DateUtil.DATE_FORMATE), DateUtil.DATE_FORMATE));
        }
        if (StringUtils.isNotEmpty(picture.getEndTime())) {
            picture.setEndTime(DateUtil.convertDate2Str(DateUtil.StringToDate(picture.getEndTime(), DateUtil.DATE_FORMATE), DateUtil.DATE_FORMATE));
        }
        if (StringUtils.isNotEmpty(picture.getDescription())) {
            //存储移动端的访问内容
            try {
                picture.setDescription2(picture.getDescription().getBytes("GBK"));
            } catch (Exception e) {
                log.error("转换出错:", e);
            }
            picture.setDescription("");
        }
        String[] fileIds = new String[picture.getFileInfos().size()];
        for (int i = 0; i < picture.getFileInfos().size(); i++) {
            FileInfo file = picture.getFileInfos().get(i);
            fileIds[i] = file.getFileId();
        }
        picture.setFileIds(JSON.toJSON(fileIds).toString());
        //分享优惠券活动处理
        if (CollectionUtils.isNotEmpty(picture.getShareCouponList())) {
            picture.setShareCouponsStr(picture.getShareCouponList().toString());
        }
        affectLine = appPicMapper.updatePictureV2(picture);
        for (FileInfo file : picture.getFileInfos()) {
            int i = fileInfoMapper.updateFileInfoV2(file);
            if (i == 0) {
                file.setLinkId(picture.getPicId());
                fileInfoMapper.insertFileInfoV2(file);
            }
        }
        return affectLine > 0;
    }

    @Override
    @DSTransactional
    public boolean updatePicture(AppPicture picture) {
        if (null == picture) {
            throw new HoServiceException("请求参数不可为空");
        }
        picture.setUpdateMan(SecurityUtils.getUsername());
        picture.setUpdateTime(DateUtil.getDateStringAllDate(new Date()));
        picture.setStartTime(picture.getStartTime());
        picture.setEndTime(picture.getEndTime());
        int affectLine;
        if (picture.getDescription() != null) {
            try {
                picture.setDescription2(picture.getDescription().getBytes("GBK"));
            } catch (Exception e) {
                log.error("转换出错:", e);
            }
            picture.setDescription("");
        }
        String[] fileIds = new String[picture.getFileInfos().size()];
        for (int i = 0; i < picture.getFileInfos().size(); i++) {
            FileInfo file = picture.getFileInfos().get(i);
            fileIds[i] = file.getFileId();
        }
        picture.setFileIds(JSON.toJSON(fileIds).toString());
        //分享优惠券活动处理
        if (CollectionUtils.isNotEmpty(picture.getShareCouponList())) {
            picture.setShareCouponsStr(picture.getShareCouponList().toString());
        }
        affectLine = appPicMapper.updatePictureV2(picture);
        for (FileInfo file : picture.getFileInfos()) {
            int i = fileInfoMapper.updateFileInfoV2(file);
            if (i == 0) {
                file.setLinkId(picture.getPicId());
                fileInfoMapper.insertFileInfoV2(file);
            }
        }
        return affectLine > 0;
    }

    /**
     * 重新拼接url
     *
     * @param tilte
     * @param tilteStyle
     * @param url
     * @return
     */

    private String reWriteUrl(String tilte, String tilteStyle, String url) {
        String newUrl = url;
        if (StrUtil.isNotBlank(tilteStyle) && StrUtil.isNotBlank(url)) {//重新拼接URL
            if (url.indexOf("?") > -1) {
                url += "&title=" + tilte + "&" + tilteStyle;
            } else {
                url += "?title=" + tilte + "&" + tilteStyle;
            }
            newUrl = url;
        }
        return newUrl;
    }

    /**
     * 比较版本号的大小,前者大则返回一个正数,后者大返回一个负数,相等则返回0
     *
     * @param version1 当前设备版本
     * @param version2 需要比较的版本
     * @return
     */
    private static int compareVersion(String version1, String version2) throws Exception {
        if (version1 == null || version2 == null) {
            throw new Exception("compareVersion error:illegal params.");
        }
        //注意此处为正则匹配，不能用"."；
        String[] versionArray1 = version1.split("\\.");
        String[] versionArray2 = version2.split("\\.");
        int idx = 0;
        //取最小长度值
        int minLength = Math.min(versionArray1.length, versionArray2.length);
        int diff = 0;
        while (idx < minLength
                //先比较长度
                && (diff = versionArray1[idx].length() - versionArray2[idx].length()) == 0
                //再比较字符
                && (diff = versionArray1[idx].compareTo(versionArray2[idx])) == 0) {
            ++idx;
        }
        //如果已经分出大小，则直接返回，如果未分出大小，则再比较位数，有子版本的为大；
        diff = (diff != 0) ? diff : versionArray1.length - versionArray2.length;
        return diff;
    }
}
