package com.juneyaoair.ecs.manage.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.dto.activity.request.pointretrieval.PointRetrievalRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.pointretrieval.PointRetrievalInformation;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.news.response.News;
import com.juneyaoair.ecs.manage.service.pointretrieval.IPointRetrievalService;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * @ClassName PointRetrievalController
 * @Description
 * <AUTHOR>
 * @Date 2025/3/17 10:06
 * @Version 1.0
 */

@RequestMapping("pointRetrieval")
@RestController
@RequiredArgsConstructor
@Api(value = "PointRetrievalController", tags = "积分找回API")
@Slf4j
public class PointRetrievalController extends HoBaseController {

    @Autowired
    private IPointRetrievalService pointRetrievalService;


    @PostMapping(value = "toCatchPointRetrievalList")
    @ApiOperation(value = "积分找回领取详情页", httpMethod = "POST")
    public R<PageResult<PointRetrievalInformation>> toCatchPointRetrievalList(@RequestBody PointRetrievalRequest pointRetrievalRequest) {
        try {
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(pointRetrievalRequest));
            PageDomain pageDomain = TableSupport.buildPageRequest();
            startPage(pageDomain);
            Page<News> localPage = PageHelper.getLocalPage();
            List<PointRetrievalInformation> pointRetrievalList = pointRetrievalService.toCatchPointRetrievalList(pointRetrievalRequest);
            R<PageResult<PointRetrievalInformation>> pageData = getPageData(pointRetrievalList, localPage);
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(pageData));
            return pageData;
        } catch (Exception exception) {
            log.error(exception.getMessage());
            return R.fail(exception.getMessage());
        } finally {
            Context.setContext(null);
        }
    }

    @ApiOperation(value = "积分找回流水下载")
    @PostMapping(value = "downloadPointRetrievalRecords")
    public void downloadPointRetrievalRecord(HttpServletResponse response, @RequestBody PointRetrievalRequest pointRetrievalRequest) throws IOException {
        List<PointRetrievalInformation> pointRetrievalList = pointRetrievalService.toCatchPointRetrievalList(pointRetrievalRequest);
        // 这里的 ContentType 要和前端请求携带的 ContentType 相对应
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 防止中文乱码
        String fileName = URLEncoder.encode("积分找回流水数据", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), PointRetrievalInformation.class).sheet("积分找回流水数据").doWrite(pointRetrievalList);
    }

}
