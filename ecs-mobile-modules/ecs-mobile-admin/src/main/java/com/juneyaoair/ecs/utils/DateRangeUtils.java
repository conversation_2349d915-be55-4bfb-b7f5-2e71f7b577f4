package com.juneyaoair.ecs.utils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
/**
 * @ClassName DateRangeUtils
 * @Description
 * <AUTHOR>
 * @Date 2025/7/8 23:40
 * @Version 1.0
 */
public class DateRangeUtils {

    public static Map<String, Date> getMonthRange(int year, int month) {
        LocalDate firstDayOfMonth = LocalDate.of(year, month, 1);
        LocalDate firstDayOfNextMonth = firstDayOfMonth.plusMonths(1);

        // 使用 ZoneId.systemDefault() 将 LocalDate 转换为 Date
        Date dateStart = Date.from(firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date dateEnd = Date.from(firstDayOfNextMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());

        Map<String, Date> range = new HashMap<>();
        range.put("firstDayOfMonth", dateStart);
        range.put("firstDayOfNextMonth", dateEnd);

        return range;
    }

    // 示例主函数
    public static void main(String[] args) {
        int year = 2025;
        int month = 8;

        Map<String, Date> dateRange = getMonthRange(year, month);
        System.out.println("本月第一天: " + dateRange.get("firstDayOfMonth"));
        System.out.println("下个月第一天: " + dateRange.get("firstDayOfNextMonth"));
    }

}
