package com.juneyaoair.ecs.manage.service.tongdun.impl;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.ecs.http.bean.HttpResult;
import com.juneyaoair.ecs.http.service.HttpClientService;
import com.juneyaoair.ecs.manage.constant.ManageConstant;
import com.juneyaoair.ecs.manage.dto.base.BasicBaseResp;
import com.juneyaoair.ecs.manage.dto.tongdun.FraudApiResponse;
import com.juneyaoair.ecs.manage.dto.tongdun.TongDunRequestDto;
import com.juneyaoair.ecs.manage.enums.FinalDecisionEnum;
import com.juneyaoair.ecs.manage.enums.ResultEnum;
import com.juneyaoair.ecs.manage.properties.ThirdAppUrlSet;
import com.juneyaoair.ecs.manage.service.tongdun.ITongdunService;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/11 11:29
 */
@Service
public class TongdunServiceImpl implements ITongdunService {
    @Autowired
    private HttpClientService httpClientService;
    @Autowired
    private ThirdAppUrlSet thirdAppUrlSet;
    @Override
    public boolean textContent(TongDunRequestDto tongDunRequestDto) {
        HttpResult httpResult = httpClientService.doPostJson(tongDunRequestDto,thirdAppUrlSet.getFlightbasicUrl() + ManageConstant.TEXT_CONTENT_CHECK,null);
        if(httpResult.isResult()){
            Type type = new TypeToken<BasicBaseResp<FraudApiResponse>>() {
            }.getType();
            BasicBaseResp<FraudApiResponse> basicBaseResp = JsonUtil.fromJson(httpResult.getResponse(),type);
            if(!ResultEnum.S10001.getResultCode().equals(basicBaseResp.getResultCode())){
                throw new HoServiceException(basicBaseResp.getResultInfo());
            }
            if (!FinalDecisionEnum.ACCEPT.getCode().equals(basicBaseResp.getResult().getFinal_decision())) {
                throw new HoServiceException("输入的值存在风险！",basicBaseResp.getResult().getSeq_id());
            }
        }
        return true;
    }
}
