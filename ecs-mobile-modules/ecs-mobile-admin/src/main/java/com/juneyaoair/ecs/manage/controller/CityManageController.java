package com.juneyaoair.ecs.manage.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.reflect.TypeToken;
import com.juneyaoair.ecs.manage.aop.CityAopMethodAnno;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.citymanage.*;
import com.juneyaoair.ecs.manage.enums.StatusEnum;
import com.juneyaoair.ecs.manage.service.cityinfo.ICityInfoAggrService;
import com.juneyaoair.ecs.manage.service.syncfile.ICitySyncJsFileService;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.service.ICityInfoService;
import com.juneyaoair.ecs.utils.ExcelUtil;
import com.juneyaoair.manage.b2c.entity.AirlineAPO;
import com.juneyaoair.manage.b2c.entity.CityInfoPO;
import com.juneyaoair.manage.b2c.entity.DstWtInfoPO;
import com.juneyaoair.manage.b2c.entity.CityLabelInfoPO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;


/**
 * 城市管理
 */
@RequestMapping("cityManage")
@RestController
@RequiredArgsConstructor
@Api(value = "CityManageController", tags = "城市管理")
@Slf4j
public class CityManageController extends HoBaseController {

    @Autowired
    private ICityInfoService ICityInfoService;

    @Autowired
    ICityInfoAggrService cityInfoAggrService;

    @ApiOperation(value = "分页查询城市管理信息(包含基础信息、城市标签、夏令时)", notes = "", httpMethod = "POST")
    @PostMapping(value = "searchPage")
    public R<PageResult<CityInfoRespDTO>> searchPage(@RequestBody CityInfoReqDTO param) {
        startPage(TableSupport.buildPageRequest());
        Page<AirlineAPO> pageInfo = PageHelper.getLocalPage();
        List<CityInfoRespDTO> cityInfoRespDTOs = ICityInfoService.searchList(param);
        return getPageData(cityInfoRespDTOs, pageInfo);
    }

    @ApiOperation(value = "添加城市信息", notes = "", httpMethod = "POST")
    @PostMapping(value = "add")
    @CityAopMethodAnno
    public R add(@RequestBody CityManageDTO param) {
        try {
            if (ICityInfoService.add(param) > 0) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail(e.getMessage());
        }
        return R.fail();
    }


    @ApiOperation(value = "更新城市信息", notes = "", httpMethod = "POST")
    @PostMapping(value = "update")
    @CityAopMethodAnno
    public R update(@RequestBody CityManageDTO param) {
        try {
            if (ICityInfoService.update(param)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail(e.getMessage());
        }
        return R.fail();
    }


    @ApiOperation(value = "更新城市禁用or启用", notes = "", httpMethod = "POST")
    @PostMapping(value = "setStatus")
    @CityAopMethodAnno
    public R setStatus(@RequestBody CityManageDTO param) {
        try {
            if (ICityInfoService.setStatus(param)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail(e.getMessage());
        }
        return R.fail();
    }

    @ApiOperation(value = "根据cityCode删除城市信息", notes = "", httpMethod = "GET")
    @GetMapping(value = "delete/{cityCode}")
    @CityAopMethodAnno
    public R delete(@PathVariable("cityCode") String cityCode) {
        try {
            if (ICityInfoService.deleteByCityCode(cityCode)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail(e.getMessage());
        }
        return R.fail();
    }

    @ApiOperation(value = "根据cityCode查询城市信息(包含基础信息、城市标签、夏令时、疫情、重要提示)", notes = "", httpMethod = "GET")
    @GetMapping(value = "searchByCityCode/{cityCode}")
    public R<List<CityInfoRespDTO>> searchByCityCode(@PathVariable String cityCode) {
        try {
            if (StringUtils.isNotEmpty(cityCode)) {
                return R.ok(ICityInfoService.searchByCityCode(cityCode));
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail(e.getMessage());
        }
        return R.fail();
    }

    @ApiOperation(value = "根据id查询夏令时", notes = "", httpMethod = "GET")
    @GetMapping(value = "searchDstById/{id}")
    public R<DstWtInfoPO> searchDstById(@PathVariable("id") String id) {
        try {
            if (StringUtils.isNotEmpty(id)) {
                DstWtInfoPO dstWtInfoPO = ICityInfoService.searchDstById(id);
                dstWtInfoPO.dstDateInfos = JsonUtil.jsonToList(dstWtInfoPO.dstMsg, new TypeToken<List<DstDateInfo>>() {
                        }.getType()
                );
                return R.ok(dstWtInfoPO);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail(e.getMessage());
        }
        return R.fail();
    }

//    @ApiOperation(value = "增加夏令时", notes = "", httpMethod = "POST")
//    @PostMapping(value = "addDst/{cityCode}")
//    @CityAopMethodAnno
//    public R addDst(@RequestBody DstWtInfoPO param, @PathVariable("cityCode") String cityCode) {
//        try {
//            if (ICityInfoService.addDst(param, cityCode)) {
//                return R.ok();
//            }
//        } catch (Exception e) {
//            log.error(e.getMessage());
//        }
//        return R.fail();
//    }

    @ApiOperation(value = "更新城市夏令时", notes = "", httpMethod = "POST")
    @PostMapping(value = "updateDst/{cityCode}")
    @CityAopMethodAnno
    public R updateDst(@RequestBody DstWtInfoPO param, @PathVariable("cityCode") String cityCode) {
        try {
            if (ICityInfoService.updateDst(param, cityCode)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail(e.getMessage());
        }
        return R.fail();
    }


    @ApiOperation(value = "对cityCode搜到的城市标签进行更新 1旧标签->更新或者删除 2新标签->新增", notes = "", httpMethod = "POST")
    @PostMapping(value = "updateCityLabel/{cityCode}")
    @CityAopMethodAnno
    public R<Boolean> updateCityLabel(@RequestBody List<CityLabelInfoPO> param, @PathVariable("cityCode") String cityCode) {
        try {
            if (ICityInfoService.updateCityLabel(param, cityCode)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail(e.getMessage());
        }
        return R.fail();
    }

    @ApiOperation(value = "对cityCode搜到的城市更新重要提示 1旧提示->更新或者删除 2新提示->新增", notes = "", httpMethod = "POST")
    @PostMapping(value = "updateCityWarn/{cityCode}")
    @CityAopMethodAnno
    public R<Boolean> updateCityWarn(@RequestBody List<CityWarnReqDTO> params, @PathVariable("cityCode") String cityCode) {
        try {
            if (CollectionUtils.isNotEmpty(params)) {
                for (CityWarnReqDTO param : params) {
                    if ((StringUtils.isNotBlank(param.arrShowDate) && param.arrShowDate.length() < 13) ||
                            (StringUtils.isNotBlank(param.depShowDate) && param.depShowDate.length() < 13)) {
                        throw new HoServiceException("arrShowDate||depShowDate格式需为yyyy-mm-dd - yyyy-mm-dd");
                    }
                }
            }
            if (ICityInfoService.updateCityWarn(params, cityCode)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail(e.getMessage());
        }
        return R.fail();
    }

    /**
     * 与updateCityWarn重复
     */
    @ApiOperation(value = "与updateCityWarn冲突请用updateCityWarn,对cityCode搜到的城市重要进行新增 1旧提示->更新 2新提示->新增", notes = "", httpMethod = "POST")
    @PostMapping(value = "addCityWarn/{cityCode}")
    @CityAopMethodAnno
    public R addCityWarn(@RequestBody CityWarnReqDTO param, @PathVariable("cityCode") String cityCode) {
        return R.ok();
    }

    @ApiOperation(value = "删除城市重要提示", notes = "", httpMethod = "POST")
    @PostMapping(value = "deleteCityWarn/{cityCode}")
    @CityAopMethodAnno
    public R deleteCityWarn(@RequestBody CityWarnReqDTO param, @PathVariable("cityCode") String cityCode) {
        try {
            if (ICityInfoService.deleteCityWarn(param, cityCode)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail(e.getMessage());
        }
        return R.fail();
    }


    @ApiOperation(value = "更新城市热门状态", notes = "", httpMethod = "GET")
    @GetMapping(value = "switchHotStatus/{cityCode}")
    @CityAopMethodAnno
    public R switchHotStatus(@PathVariable("cityCode") String cityCode) {
        try {
            if (ICityInfoService.switchHotStatus(cityCode)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail(e.getMessage());
        }
        return R.fail();
    }


    /**
     * 更新常用城市状态
     *
     * @param cityCode
     * @return
     */
    @ApiOperation(value = "更新常用城市状态", notes = "", httpMethod = "GET")
    @GetMapping(value = "switchOftenStatus/{cityCode}")
    @CityAopMethodAnno
    public R switchOftenStatus(@PathVariable("cityCode") String cityCode) {
        try {
            if (ICityInfoService.switchOftenStatus(cityCode)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail(e.getMessage());
        }
        return R.fail();
    }


    @ApiOperation(value = "更新常用城市热门区域", notes = "", httpMethod = "GET")
    @GetMapping(value = "switchHotRegionStatus/{cityCode}")
    @CityAopMethodAnno
    public R switchHotRegionStatus(@PathVariable("cityCode") String cityCode) {
        try {
            if (ICityInfoService.switchHotRegionStatus(cityCode)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail(e.getMessage());
        }
        return R.fail();
    }

    @ApiOperation(value = "获取所有城市（启用状态的）", notes = "", httpMethod = "GET")
    @GetMapping(value = "searchAll")
    public R<List<CityInfoPO>> searchAll() {
        try {
            return R.ok(ICityInfoService.selectAll(StatusEnum.ENABLE.getCode()));
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "导出excel", notes = "", httpMethod = "POST")
    @PostMapping(value = "exportExcel")
    public void exportExcel(@RequestBody CityExportReqDTO reqDTO, HttpServletRequest request, HttpServletResponse response) {
        try {
            CityInfoReqDTO prizeRecord = new CityInfoReqDTO();
            prizeRecord.setCityCode(reqDTO.cityCode);
            prizeRecord.setIsInternational(reqDTO.isInternational);
            prizeRecord.setIsHotCity(reqDTO.isHotCity);
            prizeRecord.setIsOftenCity(reqDTO.isOftenCity);
            prizeRecord.setIsHotRegion(reqDTO.isHotRegion);
            List<CityExportDTO> sheetList = cityInfoAggrService.exportList(prizeRecord);
            LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
            fieldMap.put("cityName", "城市名");
            fieldMap.put("cityEName", "城市英文名");
            fieldMap.put("cityCode", "城市三字码");
            fieldMap.put("countryCode", "国家代码");
            fieldMap.put("provinceName", "所属省份");
            fieldMap.put("isOftenCity", "常用城市");
            fieldMap.put("isHotCity", "热门城市");
            fieldMap.put("isHotRegion", "热门地区");
            fieldMap.put("createId", "热门排序");
            fieldMap.put("cityLabelName", "城市标签");
            fieldMap.put("cityTimeZone", "时区");
            fieldMap.put("dstWtInfo", "夏令时");
            fieldMap.put("isInternational", "城市类型");
            fieldMap.put("status", "状态");
            String fileName = "城市管理导出表格";
            ExcelUtil.listToExcel(sheetList, fieldMap, fileName, response);
        } catch (Exception e) {
            log.error("导出城市信息异常" + e.getMessage(), e);
        }
    }

    @Resource
    ICitySyncJsFileService citySyncJsFileService;

    @GetMapping(value = "syncJs")
    public R<Boolean> SyncJs() {
        citySyncJsFileService.process();
        return R.ok();
    }

    @ApiOperation(value = "功能同旧的cityManage/citySyncResources 用于m站同步js、json文件" +
            "1 city.json和city.js存到/路径下" +
            "2 上传到服务器/upload/flightBasicStatic路径下" +
            "3 删除源文件", notes = "", httpMethod = "GET")
    @GetMapping(value = "uploadStaticFile")
    public R uploadStaticFile() {
        return R.ok(cityInfoAggrService.syncJsonAndJs());
    }
}
