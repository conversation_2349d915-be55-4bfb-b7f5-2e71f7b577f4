package com.juneyaoair.ecs.manage.external.flightbaisc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2020-3-12 12:16
 * @description： 航距数据查询请求参数
 * @modified By：
 * @version: $
 */
@Data
public class AirlineMileageParam {

    @ApiModelProperty("最大航距")
    private String maxMileage;

    @ApiModelProperty("最小航距")
    private String minMileage;

    @ApiModelProperty("出发城市")
    private String depCity;

    @ApiModelProperty("到达城市")
    private String arrCity;
}
