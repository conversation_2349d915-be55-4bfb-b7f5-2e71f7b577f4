package com.juneyaoair.ecs.manage.service.dih;

import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.mongo.entity.InventoryHistoryPO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/8 10:56
 */
public interface IDIHDataService {
    PageResult<InventoryHistoryPO> queryInventoryHistoryByPage(String flightDate, int pageNo, int pageSize);
    /**
     * @description 航班操作库存历史数据下载
     * <AUTHOR>
     * @date 2025/4/14 13:32
     * @param flightDateStart
     * @param flightDateEnd
     * @return PageResult<InventoryHistoryPO>
     **/
     void downloadInventoryHistory(String flightDateStart, String flightDateEnd, HttpServletResponse response) throws IOException;
    /**
     * @description 取消航班操作库存历史数据下载
     * <AUTHOR>
     * @date 2025/4/14 13:32
     * @param flightDateStart
     * @param flightDateEnd
     * @return PageResult<InventoryHistoryPO>
     **/
     void cancelInventoryHistory(String flightDateStart, String flightDateEnd);
}
