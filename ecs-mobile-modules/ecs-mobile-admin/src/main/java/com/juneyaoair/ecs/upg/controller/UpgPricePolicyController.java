package com.juneyaoair.ecs.upg.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.google.common.collect.Lists;
import com.juneyaoair.ecs.excel.bean.UpgPricePolicyFlightUpload;
import com.juneyaoair.ecs.excel.bean.UpgPricePolicyUpload;
import com.juneyaoair.ecs.excel.listener.UpgPricePolicyListener;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.upg.param.*;
import com.juneyaoair.ecs.upg.result.UpgPriceInfo;
import com.juneyaoair.ecs.upg.result.UpgPricePolicyInfo;
import com.juneyaoair.ecs.upg.service.UpgPricePolicyService;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 升舱价格政策管理
 * @created 2023/8/22 9:06
 */
@Slf4j
@Api(tags = {"升舱价格政策管理"})
@RestController
public class UpgPricePolicyController {

    @Autowired
    private UpgPricePolicyService upgPricePolicyService;

    @ApiOperation(value = "创建升舱价格政策")
    @PostMapping(value = "/upg/createPricePolicy")
    public R<String> createPricePolicy(@RequestBody @Validated CreateUpgPricePolicyParam upgPricePolicyParam) {
        upgPricePolicyService.createPricePolicy(upgPricePolicyParam, "system");
        return R.ok("创建成功");
    }

    @ApiOperation(value = "查询升舱价格政策清单")
    @PostMapping(value = "/upg/queryPricePolicyList")
    public R<PageResult<UpgPricePolicyInfo>> queryPricePolicyList(@RequestBody @Validated QueryUpgPricePolicyListParam queryUpgPricePolicyListParam) {
        PageResult<UpgPricePolicyInfo> pricePolicyInfoPage = upgPricePolicyService.queryPricePolicyList(queryUpgPricePolicyListParam);
        return R.ok(pricePolicyInfoPage);
    }

    @ApiOperation(value = "升舱价格政策导入模板下载")
    @PostMapping(value = "/upg/pricePolicy/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        InputStream is = getClass().getClassLoader().getResourceAsStream("excel/升舱价格政策导入模板.xlsx");
        //获得响应流
        OutputStream os = response.getOutputStream();
        //设置响应头信息
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("升舱价格政策导入模板", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        //通过响应流将文件输入流读取到的文件写出
        IOUtils.copy(is, os);
    }

    @ResponseBody
    @ApiOperation(value = "航班号手动上传模板下载", notes = "")
    @RequestMapping(value = "/upg/pricePolicy/downloadFlightTemplate", method = RequestMethod.POST)
    public void downloadFlightTemplate(HttpServletResponse response) throws IOException {
        // 这里的 ContentType 要和前端请求携带的 ContentType 相对应
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 防止中文乱码
        String fileName = URLEncoder.encode("航班号手动上传模板", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), UpgPricePolicyFlightUpload.class).autoCloseStream(Boolean.FALSE).sheet("航班号手动上传模板").doWrite(Lists.newArrayList());
    }

    @ApiOperation(value = "导入升舱价格政策")
    @PostMapping(value = "/upg/importPricePolicy")
    public R<String> importPricePolicy(@RequestPart(value = "file") MultipartFile file) {
        UpgPricePolicyListener upgPricePolicyListener = new UpgPricePolicyListener();
        try {
            EasyExcel.read(file.getInputStream(), UpgPricePolicyUpload.class, upgPricePolicyListener).sheet().doRead();
        } catch (ExcelDataConvertException dce) {
            log.error("解析异常，数据转换失败，异常信息：", dce);
            throw new HoServiceException(dce.getMessage());
        } catch (Exception e) {
            log.error("导入升舱价格政策，解析异常，异常信息：", e);
            throw new HoServiceException("解析上传文件出现异常");
        }
        if (CollectionUtils.isEmpty(upgPricePolicyListener.getCreateUpgPricePolicyParamList())) {
            throw new HoServiceException("解析后价格政策不能为空");
        }
        upgPricePolicyService.importPricePolicy(upgPricePolicyListener.getCreateUpgPricePolicyParamList());
        return R.ok("导入成功");
    }

    @ApiOperation(value = "更新数据状态")
    @PostMapping(value = "/upg/updatePricePolicyStatus")
    public R<String> updatePricePolicyStatus(@RequestBody @Validated UpdatePricePolicyStatus updatePricePolicyStatus) {
        upgPricePolicyService.updatePricePolicyStatus(updatePricePolicyStatus);
        return R.ok("操作成功");
    }

    @ApiOperation(value = "更新升舱价格政策")
    @PostMapping(value = "/upg/updatePricePolicy")
    public R<String> updatePricePolicy(@RequestBody @Validated UpdateUpgPricePolicyParam updateUpgPricePolicyParam) {
        upgPricePolicyService.updatePricePolicy(updateUpgPricePolicyParam);
        return R.ok("更新成功");
    }

    @ApiOperation(value = "升舱价格查询")
    @PostMapping(value = "/upg/queryUpgPrice")
    public R<List<UpgPriceInfo>> queryUpgPrice(@RequestBody @Validated QueryUpgPriceParam queryUpgPriceParam) {
        return R.ok( upgPricePolicyService.queryUpgPrice(queryUpgPriceParam));
    }
}
