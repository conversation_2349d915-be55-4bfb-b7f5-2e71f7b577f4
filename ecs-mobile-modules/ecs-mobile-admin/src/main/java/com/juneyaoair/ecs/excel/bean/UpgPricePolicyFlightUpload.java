package com.juneyaoair.ecs.excel.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.juneyaoair.ecs.valid.EnumValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 航班号手动上传模板
 * @created 2023/9/21 13:19
 */
@Data
public class UpgPricePolicyFlightUpload {

    @ExcelProperty(index = 0, value = "航线")
    @NotBlank(message = "航线不能为空")
    @ApiModelProperty(value = "航线", required = true)
    private String segment;

    @ExcelProperty(index = 1, value = "航班号")
    @NotNull(message = "航班号不能为空")
    @ApiModelProperty(value = "航班号", required = true)
    private Integer flightNo;

}
