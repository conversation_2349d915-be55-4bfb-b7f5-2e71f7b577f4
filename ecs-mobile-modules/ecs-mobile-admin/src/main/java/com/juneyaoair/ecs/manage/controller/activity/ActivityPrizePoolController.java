package com.juneyaoair.ecs.manage.controller.activity;

import com.alibaba.excel.EasyExcel;
import com.juneyaoair.ecs.excel.listener.ImportPrizePoolListener;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ActivityPrizeEntryParam;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ActivityPrizeEntryQueryParam;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ActivityPrizePoolParam;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ActivityPrizePoolQueryParam;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ImportPrizePoolInfo;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ImportPrizePoolParam;
import com.juneyaoair.ecs.manage.dto.activity.response.prizepool.ActivityPrizeEntryInfo;
import com.juneyaoair.manage.b2c.entity.activity.ActivityPrizePoolPO;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.external.FlightBasicService;
import com.juneyaoair.ecs.manage.service.activity.ActivityPrizePoolService;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @Author: caolei
 * @Description: 通用抽奖奖池配置--奖池奖品随机发放
 * @Date: 2024/07/09 9:15
 * @Modified by:
 */
@Slf4j
@Api(value = "通用抽奖奖池配置--奖池奖品随机发放")
@RestController
public class ActivityPrizePoolController {

    @Autowired
    private FlightBasicService flightBasicService;

    @Autowired
    private ActivityPrizePoolService activityPrizePoolService;

    @ApiOperation(value = "新增奖池")
    @PostMapping("/activityPrizePool/createPrizePool")
    public R<String> createPrizePool(@RequestBody ActivityPrizePoolParam activityPrizePoolParam) {
        activityPrizePoolParam.setOperator(SecurityUtils.getUsername());
        flightBasicService.createPrizePool(activityPrizePoolParam);
        return R.ok(null);
    }

    @ApiOperation(value = "修改奖池信息")
    @PostMapping("/activityPrizePool/updatePrizePool")
    public R<String> updatePrizePool(@RequestBody ActivityPrizePoolParam activityPrizePoolParam) {
        activityPrizePoolParam.setOperator(SecurityUtils.getUsername());
        flightBasicService.updatePrizePool(activityPrizePoolParam);
        return R.ok(null);
    }

    @ApiOperation(value = "变更奖池状态")
    @PostMapping("/activityPrizePool/changePrizePoolStatus")
    public R<String> changePrizePoolStatus(@RequestBody ActivityPrizePoolParam activityPrizePoolParam) {
        activityPrizePoolParam.setOperator(SecurityUtils.getUsername());
        flightBasicService.changePrizePoolStatus(activityPrizePoolParam);
        return R.ok(null);
    }

    @ApiOperation(value = "新增奖池奖品")
    @PostMapping("/activityPrizePool/createPrizeEntity")
    public R<String> createPrizeEntity(@RequestBody ActivityPrizeEntryParam activityPrizeEntryParam) {
        activityPrizeEntryParam.setOperator(SecurityUtils.getUsername());
        flightBasicService.createPrizeEntity(activityPrizeEntryParam);
        return R.ok(null);
    }

    @ApiOperation(value = "更新奖池奖品")
    @PostMapping("/activityPrizePool/updatePrizeEntity")
    public R<String> updatePrizeEntity(@RequestBody ActivityPrizeEntryParam activityPrizeEntryParam) {
        activityPrizeEntryParam.setOperator(SecurityUtils.getUsername());
        flightBasicService.updatePrizeEntity(activityPrizeEntryParam);
        return R.ok(null);
    }

    @ApiOperation(value = "变更奖品状态")
    @PostMapping("/activityPrizePool/changePrizeEntityStatus")
    public R<String> changePrizeEntityStatus(@RequestBody ActivityPrizeEntryParam activityPrizeEntryParam) {
        activityPrizeEntryParam.setOperator(SecurityUtils.getUsername());
        flightBasicService.changePrizeEntityStatus(activityPrizeEntryParam);
        return R.ok(null);
    }

    @ApiOperation(value = "查询奖池列表")
    @PostMapping("/activityPrizePool/getPrizePoolList")
    public R<PageResult<ActivityPrizePoolPO>> getPrizePoolList(@RequestBody ActivityPrizePoolQueryParam activityPrizePoolQueryParam) {
        PageResult<ActivityPrizePoolPO> pageResult = flightBasicService.getPrizePoolList(activityPrizePoolQueryParam);
        return R.ok(pageResult);
    }

    @ApiOperation(value = "查询奖品信息")
    @PostMapping("/activityPrizePool/getPrizeEntityInfo")
    public R<List<ActivityPrizeEntryInfo>> getPrizeEntityInfo(@RequestBody ActivityPrizeEntryQueryParam activityPrizeEntryQueryParam) {
        List<ActivityPrizeEntryInfo> list = flightBasicService.getPrizeEntityInfo(activityPrizeEntryQueryParam);
        return R.ok(list);
    }

    @ApiOperation(value = "导入活动奖池")
    @PostMapping(value = "/activityPrizePool/importPrizePool")
    public R<String> importPrizePool(@RequestPart(value = "file") MultipartFile file) {
        ImportPrizePoolListener importPrizePoolListener = new ImportPrizePoolListener();
        try {
            EasyExcel.read(file.getInputStream(), ImportPrizePoolParam.class, importPrizePoolListener).sheet().doRead();
        } catch (HoServiceException hse) {
            log.error("导入活动奖池，解析异常，异常信息：", hse);
            throw new HoServiceException(hse.getMessage());
        } catch (Exception e) {
            log.error("导入活动奖池，解析异常，异常信息：", e);
            throw new HoServiceException("解析上传文件出现异常");
        }
        Map<String, ImportPrizePoolInfo> prizePoolInfoMap = importPrizePoolListener.getPrizePoolInfoMap();
        if (null == prizePoolInfoMap || prizePoolInfoMap.isEmpty()) {
            throw new HoServiceException("解析后活动奖池信息不能为空");
        }
        activityPrizePoolService.importPrizePool(prizePoolInfoMap);
        return R.ok("导入成功");
    }

}
