package com.juneyaoair.ecs.manage.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.file.HoFile;
import com.juneyaoair.ecs.manage.dto.notice.DICTValue;
import com.juneyaoair.ecs.manage.dto.notice.MaintainInfo;
import com.juneyaoair.ecs.manage.dto.notice.NoticeInfo;
import com.juneyaoair.ecs.manage.dto.notice.NoticeSort;
import com.juneyaoair.ecs.manage.service.file.IFileUploadService;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.service.IDictService;
import com.juneyaoair.manage.b2c.service.INoticeInfoService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName NoticeController
 * @Description 协议条款相关接口
 * <AUTHOR>
 * @Date 2023/12/20 14:17
 * @Version 1.0
 */

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("notice")
@Api(value = "NoticeController", tags = "协议条款相关接口")
public class NoticeController extends HoBaseController {

    @Autowired
    private INoticeInfoService noticeInfoService;

    @Autowired
    private IDictService dictService;

    @Autowired
    private IFileUploadService iFileService;

    private static final String NOTICE_DICT_TYPE = "c6cc5fd9-abf0-43cf-9f6c-44fa38b12349";

    @PostMapping("selectNoticeInfo")
    public R<PageResult<NoticeInfo>> selectNoticeInfo(@RequestBody @Validated NoticeInfo noticeInfo) {
        try {
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(noticeInfo));
            PageDomain pageDomain = TableSupport.buildPageRequest();
            startPage(pageDomain);
            Page<NoticeInfo> localPage = PageHelper.getLocalPage();
            List<NoticeInfo> airlineDTOS = noticeInfoService.selectNoticeInfo(noticeInfo);
            R<PageResult<NoticeInfo>> pageData = getPageData(airlineDTOS, localPage);
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(pageData));
            return pageData;
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            Context.setContext(null);
        }
        return R.fail();
    }

    @PostMapping("selectNoticeSort")
    public R<PageResult<NoticeSort>> selectNoticeSort(@RequestBody @Validated NoticeSort noticeSort) {
        try {
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(noticeSort));
            PageDomain pageDomain = TableSupport.buildPageRequest();
            startPage(pageDomain);
            Page<NoticeSort> localPage = PageHelper.getLocalPage();
            List<NoticeSort> airlineDTOS = noticeInfoService.selectNoticeSortById(noticeSort);
            R<PageResult<NoticeSort>> pageData = getPageData(airlineDTOS, localPage);
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(pageData));
            return pageData;
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            Context.setContext(null);
        }
        return R.fail();
    }

    @GetMapping("selectFatherModule")
    @SuppressWarnings("rawtypes")
    public R selectFatherModule() {
        Map<String, Object> resultMap = new HashMap<>();
        DICTValue dictValue = new DICTValue();
        NoticeSort noticeSort = new NoticeSort();
        dictValue.setDtid(NOTICE_DICT_TYPE);
        List<DICTValue> dictValueList = dictService.findDICTValueList(dictValue);
        List<NoticeSort> noticeSorts = noticeInfoService.selectFatherModule(noticeSort);
        List<Map<String, Object>> listJson = new ArrayList<>();
        List<Map<String, Object>> listFirst = new ArrayList<>();
        if (noticeSorts != null && noticeSorts.size() > 0) {
            for (NoticeSort string : noticeSorts) {
                Map<String, Object> map = new HashMap<>();
                map.put("noticeId", string.getNtId());
                map.put("modularName", string.getNtName());
                listJson.add(map);
            }
        }
        if (dictValueList != null && dictValueList.size() > 0) {
            for (DICTValue string : dictValueList) {
                Map<String, Object> map = new HashMap<>();
                map.put("firstId", string.getDvid());
                map.put("firstName", string.getDvName());
                listFirst.add(map);
            }
        }
        resultMap.put("noticeList", listJson);
        resultMap.put("fatherList", listFirst);
        return R.ok(resultMap);
    }

    @PostMapping("selectNoticeByDVId")
    @SuppressWarnings("rawtypes")
    public R selectNoticeByDVId(@RequestBody NoticeSort noticeSort) {
        Map<String, Object> resultMap = new HashMap<>();
        List<NoticeSort> noticeSorts = noticeInfoService.selectNoticeSortById(noticeSort);
        List<Map<String, Object>> listJson = new ArrayList<>();
        if (noticeSorts != null && noticeSorts.size() > 0) {
            for (NoticeSort string : noticeSorts) {
                Map<String, Object> map = new HashMap<>();
                map.put("modularName", string.getNtName());
                map.put("noticeId", string.getNtId());
                map.put("language", string.getLanguage());
                listJson.add(map);
            }
        }
        resultMap.put("noticeList", listJson);
        return R.ok(resultMap);
    }

    @PostMapping("addNotice")
    @SuppressWarnings("rawtypes")
    public R addNotice(@RequestBody NoticeSort noticeSort) {
        if (null == noticeSort) {
            return R.fail("数据不可为空！");
        }
        noticeSort.setNtId(HOStringUtil.newGUID());
        int result = noticeInfoService.addNoticeSort(noticeSort);
        if (result > 0) {
            return R.ok();
        } else {
            return R.fail("新增失败！");
        }
    }

    @PostMapping("updateNotice")
    @SuppressWarnings("rawtypes")
    public R updateNotice(@RequestBody NoticeSort noticeSort) {
        if (null == noticeSort) {
            return R.fail("数据不可为空！");
        }
        int result = noticeInfoService.updateNoticeSort(noticeSort);
        if (result > 0) {
            return R.ok();
        } else {
            return R.fail("修改失败！");
        }
    }

    @PostMapping("addNoticeInfo")
    @SuppressWarnings("rawtypes")
    public R addNoticeInfo(@RequestBody NoticeInfo notice) {
        if (null == notice) {
            return R.fail("数据不可为空！");
        }
        int result = noticeInfoService.addNoticeInfo(notice);
        if (result > 0) {
            return R.ok();
        } else {
            return R.fail("新增失败！");
        }
    }

    @PostMapping("updateNoticeInfo")
    @SuppressWarnings("rawtypes")
    public R updateNoticeInfo(@RequestBody NoticeInfo notice) {
        if (null == notice) {
            return R.fail("数据不可为空！");
        }
        int result = noticeInfoService.updateNoticeInfo(notice);
        if (result > 0) {
            return R.ok();
        } else {
            return R.fail("修改失败！");
        }
    }

    @PostMapping("delete")
    @SuppressWarnings("rawtypes")
    public R delete(@RequestBody NoticeInfo notice) {
        if (null == notice) {
            return R.fail("数据不可为空！");
        }
        int result = noticeInfoService.deleteNoticeInfo(notice);
        if (result > 0) {
            return R.ok();
        } else {
            return R.fail("删除失败！");
        }
    }

    @PostMapping("deleteSort")
    @SuppressWarnings("rawtypes")
    public R deleteSort(@RequestBody NoticeSort noticeSort) {
        if (null == noticeSort) {
            return R.fail("数据不可为空！");
        }
        int result = noticeInfoService.deleteNoticeSort(noticeSort);
        if (result > 0) {
            return R.ok();
        } else {
            return R.fail("删除失败！");
        }
    }

    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    @ResponseBody
    @SuppressWarnings("rawtypes")
    public R fileUpload(@RequestParam("file") MultipartFile file, String fileNames, String language) {
        if (StringUtils.isBlank(language)) {
            throw new HoServiceException("语言不能为空");
        }
        try {
            String fileName = fileNames.endsWith(language) ? fileNames : (fileNames + (fileNames.endsWith("_") ? "" : "_") + language);
            HoFile hoFile = iFileService.uploadFile(file, "", fileName,false);
            return R.ok(hoFile);
        } catch (Exception e) {
            return R.fail("文件上传出错，请联系管理员！");
        }
    }

    /**
     * @param maintainInfo
     * @return com.ruoyi.common.core.domain.R
     * <AUTHOR>
     * @Description 新增（选中）模块信息
     * @Date 10:57 2023/12/25
     **/

    @PostMapping("addMaintainInfo")
    @SuppressWarnings("rawtypes")
    public R addMaintainInfo(@RequestBody MaintainInfo maintainInfo) {
        if (null == maintainInfo) {
            return R.fail("数据不可为空！");
        }
        int result = noticeInfoService.addMaintainInfo(maintainInfo);
        if (result > 0) {
            return R.ok();
        } else {
            return R.fail("新增失败！");
        }
    }

    /**
     * @param maintainInfo
     * @return com.ruoyi.common.core.domain.R
     * <AUTHOR>
     * @Description 删除模块信息
     * @Date 11:02 2023/12/25
     **/
    @PostMapping("deleteMaintainInfo")
    @SuppressWarnings("rawtypes")
    public R deleteMaintainInfo(@RequestBody MaintainInfo maintainInfo) {
        if (null == maintainInfo) {
            return R.fail("数据不可为空！");
        }
        int result = noticeInfoService.deleteMaintainInfo(maintainInfo);
        if (result > 0) {
            return R.ok();
        } else {
            return R.fail("删除失败！");
        }
    }

    /**
     * @param maintainInfo
     * @return com.ruoyi.common.core.domain.R
     * <AUTHOR>
     * @Description 更新条款维护信息
     * @Date 11:02 2023/12/25
     **/
    @PostMapping("updateMaintainInfo")
    @SuppressWarnings("rawtypes")
    public R updateMaintainInfo(@RequestBody MaintainInfo maintainInfo) {
        if (null == maintainInfo) {
            return R.fail("数据不可为空！");
        }
        int result = noticeInfoService.updateMaintainInfo(maintainInfo);
        if (result > 0) {
            return R.ok();
        } else {
            return R.fail("删除失败！");
        }
    }

    @PostMapping("fetchMaintainInfo")
    public R<PageResult<MaintainInfo>> fetchMaintainInfo(@RequestBody @Validated MaintainInfo maintainInfo) {
        try {
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(maintainInfo));
            String pageNum = ServletUtils.getParameter("pageNum");
            String pageSize = ServletUtils.getParameter("pageSize");
            PageDomain pageDomain = TableSupport.buildPageRequest();
            if (StringUtils.isAnyEmpty(pageNum, pageSize) || 0 == Convert.toInt(pageNum) || 0 == Convert.toInt(pageSize)) {
                pageDomain.setPageNum(1);
                pageDomain.setPageSize(1000);
            }
            startPage(pageDomain);
            Page<MaintainInfo> localPage = PageHelper.getLocalPage();
            List<MaintainInfo> maintainInfos = noticeInfoService.fetchMaintainInfo(maintainInfo);
            R<PageResult<MaintainInfo>> pageData = getPageData(maintainInfos, localPage);
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(pageData));
            return pageData;
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            Context.setContext(null);
        }
        return R.fail();
    }

    /**
     * @param noticeInfo
     * @return com.ruoyi.common.core.domain.R
     * <AUTHOR>
     * @Description 新增条款详情
     * @Date 13:05 2023/12/25
     **/
    @PostMapping("addNoticeMessage")
    @SuppressWarnings("rawtypes")
    public R addNoticeMessage(@RequestBody NoticeInfo noticeInfo) {
        if (null == noticeInfo) {
            return R.fail("数据不可为空！");
        }
        int result = noticeInfoService.addNoticeMessage(noticeInfo);
        if (result > 0) {
            return R.ok();
        } else {
            return R.fail("新增失败！");
        }
    }

    /**
     * @param noticeInfo
     * @return com.ruoyi.common.core.domain.R
     * <AUTHOR>
     * @Description 删除条款详情
     * @Date 13:05 2023/12/25
     **/
    @PostMapping("deleteNoticeMessage")
    @SuppressWarnings("rawtypes")
    public R deleteNoticeMessage(@RequestBody NoticeInfo noticeInfo) {
        if (null == noticeInfo) {
            return R.fail("数据不可为空！");
        }
        int result = noticeInfoService.deleteNoticeMessage(noticeInfo);
        if (result > 0) {
            return R.ok();
        } else {
            return R.fail("删除失败！");
        }
    }

    /**
     * @param noticeInfo
     * @return com.ruoyi.common.core.domain.R
     * <AUTHOR>
     * @Description 更新条款详情
     * @Date 13:05 2023/12/25
     **/
    @PostMapping("updateNoticeMessage")
    @SuppressWarnings("rawtypes")
    public R updateNoticeMessage(@RequestBody NoticeInfo noticeInfo) {
        if (null == noticeInfo) {
            return R.fail("数据不可为空！");
        }
        int result = noticeInfoService.updateNoticeMessage(noticeInfo);
        if (result > 0) {
            return R.ok();
        } else {
            return R.fail("更新失败！");
        }
    }

    @PostMapping("fetchNoticeMessage")
    public R<PageResult<NoticeInfo>> fetchNoticeMessage(@RequestBody @Validated NoticeInfo noticeInfo) {
        try {
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(noticeInfo));
            PageDomain pageDomain = TableSupport.buildPageRequest();
            startPage(pageDomain);
            Page<NoticeInfo> localPage = PageHelper.getLocalPage();
            List<NoticeInfo> maintainInfos = noticeInfoService.fetchNoticeMessage(noticeInfo);
            R<PageResult<NoticeInfo>> pageData = getPageData(maintainInfos, localPage);
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(pageData));
            return pageData;
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            Context.setContext(null);
        }
        return R.fail();
    }
}
