package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.esc.manage.util.IPUtil;
import com.juneyaoair.manage.thirdapi.IFlightBasicCacheService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.RequiresRoles;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/15 13:06
 */
@Api(value = "RedisCacheController", tags = "缓存清理控制")
@RequestMapping(value = "/cache")
@RestController
public class RedisCacheController {
    @Autowired
    private IFlightBasicCacheService flightBasicService;

    @ApiOperation(value = "清理城市缓存", notes = "", httpMethod = "POST")
    @PostMapping(value = "/clearCityCache")
    @RequiresRoles(value = {"admin","manageadmin"}, logical = Logical.OR)
    public R clearCityCache(HttpServletRequest request) {
        return R.ok(flightBasicService.clearCityCache(IPUtil.getIpAddr(request)));
    }

    @ApiOperation(value = "清理机场缓存", notes = "", httpMethod = "POST")
    @PostMapping(value = "/clearAirportCache")
    @RequiresRoles(value = {"admin","manageadmin"}, logical = Logical.OR)
    public R clearAirportCache(HttpServletRequest request) {
        return R.ok(flightBasicService.clearAirportCache(IPUtil.getIpAddr(request)));
    }

    @ApiOperation(value = "清理航线缓存", notes = "", httpMethod = "POST")
    @PostMapping(value = "/clearAirlineCache")
    @RequiresRoles(value = {"admin","manageadmin"}, logical = Logical.OR)
    public R clearAirlineCache(HttpServletRequest request) {
        return R.ok(flightBasicService.clearAirlineCache(IPUtil.getIpAddr(request)));
    }
}
