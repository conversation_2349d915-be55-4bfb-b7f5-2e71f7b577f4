package com.juneyaoair.ecs.manage.service.baseinfo.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.ecs.manage.dto.activity.common.*;
import com.juneyaoair.ecs.manage.dto.activity.request.ActivityBaseInfo;
import com.juneyaoair.ecs.manage.dto.activity.request.ActivityChildInfo;
import com.juneyaoair.ecs.manage.dto.activity.request.ActivityCoupon;
import com.juneyaoair.ecs.manage.dto.activity.request.ActivityCouponDetail;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.discount.vo.DiscountsRouteActivityVo;
import com.juneyaoair.ecs.manage.external.FlightBasicService;
import com.juneyaoair.ecs.manage.service.baseinfo.IBaseInfoService;
import com.juneyaoair.ecs.manage.service.discountsRoute.DiscountsRouteData;
import com.juneyaoair.ecs.manage.service.discountsrouteactivity.IDiscountsRouteActivityService;
import com.juneyaoair.ecs.redis.service.PrimaryRedisService;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.ExcelUtil;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.activity.*;
import com.juneyaoair.manage.b2c.mapper.ActivityBaseInfoMapper;
import com.juneyaoair.manage.b2c.mapper.ActivityChildInfoMapper;
import com.juneyaoair.manage.b2c.service.*;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName BaseInfoServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/11/16 11:04
 * @Version 1.0
 */
@Service
@RefreshScope
@Slf4j
public class BaseInfoServiceImpl implements IBaseInfoService {
    @Autowired
    private IActivityBaseInfoService activityBaseInfoService;

    @Autowired
    private IActivityChildInfoService activityChildInfoService;

    @Autowired
    private IActivityAirlineService activityAirlineService;

    @Autowired
    private IDiscountsRouteActivityService discountsRouteActivityService;

    @Autowired
    private IActivityCommodityService activityCommodityService;

    @Autowired
    private IActivityInnerPageInfoService innerPageInfoService;

    @Autowired
    private IActivityCouponsService activityCouponsService;

    @Autowired
    private IActivityCouponDetailService activityCouponDetailService;

    @Resource
    private ActivityBaseInfoMapper activityBaseInfoMapper;

    @Resource
    private ActivityChildInfoMapper activityChildInfoMapper;

    @Resource
    private PrimaryRedisService primaryRedisService;

    @Autowired
    private DiscountsRouteData discountsRouteData;

    @Autowired
    private FlightBasicService flightBasicService;

    private final static String NEW_ACTIVITY_SEGMENTS_INFO = "Activity:NewSegmentsInfo";

    @Override
    public PageResult<ActivityBaseInfo> toCatchList(ActivityBaseInfo activityBaseInfo, PageDomain pageDomain) {
        LambdaQueryWrapper<ActivityBaseInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(activityBaseInfo.getArea()), ActivityBaseInfoPO::getArea, activityBaseInfo.getArea());
        queryWrapper.like(StringUtils.isNotEmpty(activityBaseInfo.getActivityParam()), ActivityBaseInfoPO::getActivityParam, activityBaseInfo.getActivityParam());
        queryWrapper.ge(StringUtils.isNotEmpty(activityBaseInfo.getJobStartDate()), ActivityBaseInfoPO::getJobStartDate, activityBaseInfo.getJobStartDate());
        queryWrapper.le(StringUtils.isNotEmpty(activityBaseInfo.getJobEndDate()), ActivityBaseInfoPO::getJobEndDate, activityBaseInfo.getJobEndDate());
        queryWrapper.select(ActivityBaseInfoPO.class, i -> !i.getProperty().equals("nowTime"));
        queryWrapper.orderByDesc(ActivityBaseInfoPO::getUpdateTime);
        Page<ActivityBaseInfoPO> page = new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize());
        Page<ActivityBaseInfoPO> baseInfoPOPage = activityBaseInfoMapper.selectPage(page, queryWrapper);
        if (CollectionUtil.isEmpty(baseInfoPOPage.getRecords())) {
            return new PageResult<>(null, 0L, pageDomain.getPageNum(), pageDomain.getPageSize());
        }
        LambdaQueryWrapper<ActivityChildInfoPO> childInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        childInfoLambdaQueryWrapper.in(ActivityChildInfoPO::getBaseinfoId, baseInfoPOPage.getRecords().stream().filter(Objects::nonNull).map(ActivityBaseInfoPO::getId).filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
        List<ActivityChildInfoPO> activityChildInfoPOS = activityChildInfoMapper.selectList(childInfoLambdaQueryWrapper);
        List<ActivityBaseInfo> activityBaseInfoList = baseInfoPOPage.getRecords().stream().map(i -> {
            ActivityBaseInfo activity = new ActivityBaseInfo();
            BeanUtils.copyProperties(i, activity);
            List<String> dataNameList = activityChildInfoPOS.stream().filter(it -> i.getId().equalsIgnoreCase(it.getBaseinfoId())).map(ActivityChildInfoPO::getDataName).collect(Collectors.toList());
            activity.setDataNames(dataNameList);
            return activity;
        }).collect(Collectors.toList());
        return new PageResult<>(activityBaseInfoList, page.getTotal(), (int) page.getCurrent(), (int) page.getSize());
    }

    @Override
    public List<ActivityChildInfo> toCatchChildList(ActivityChildInfo activityChildInfo) {
        return activityChildInfoService.toGainAllChildRecords(activityChildInfo);
    }

    @Override
    public boolean toAddBaseInfo(ActivityBaseInfo activityBaseInfo) {
        //1. 校验活动参数的唯一性
        ActivityBaseInfoPO activityBaseInfoPO = new ActivityBaseInfoPO();
        BeanUtils.copyProperties(activityBaseInfo, activityBaseInfoPO);
        List<ActivityBaseInfoPO> allStandardRecords = activityBaseInfoService.toGainAllRecords(activityBaseInfoPO);
        if (CollectionUtils.isNotEmpty(allStandardRecords)) {
            throw new HoServiceException("请确保活动参数的唯一性");
        }

        //2. 新增记录
        activityBaseInfoPO.setCreateMan(SecurityUtils.getUsername());
        activityBaseInfoPO.setUpdateMan(SecurityUtils.getUsername());
        activityBaseInfoPO.setCreateTime(DateUtil.getDateStringAllDate(new Date()));
        activityBaseInfoPO.setUpdateTime(DateUtil.getDateStringAllDate(new Date()));
        boolean saveResult = activityBaseInfoService.save(activityBaseInfoPO);
        replaceRedis();
        if (!saveResult) {
            throw new HoServiceException("新增出错！");
        }
        return true;
    }

    @Override
    public boolean toUpdateBaseInfo(ActivityBaseInfo activityBaseInfo) {
        //1. 校验活动参数的唯一性
        ActivityBaseInfoPO activityBaseInfoPO = new ActivityBaseInfoPO();
        BeanUtils.copyProperties(activityBaseInfo, activityBaseInfoPO);
        List<ActivityBaseInfoPO> allStandardRecords = activityBaseInfoService.toGainAllRecords(activityBaseInfoPO);
        if (CollectionUtils.isNotEmpty(allStandardRecords)) {
            throw new HoServiceException("请确保活动参数的唯一性");
        }

        //2. 更新记录
        activityBaseInfoPO.setUpdateMan(SecurityUtils.getUsername());
        activityBaseInfoPO.setUpdateTime(DateUtil.getDateStringAllDate(new Date()));
        activityBaseInfoService.updateById(activityBaseInfoPO);
        return true;
    }

    @Override
    public boolean toAddChildInfo(ActivityChildInfo childInfo) {
        ActivityChildInfoPO activityChildInfoPO = new ActivityChildInfoPO();
        BeanUtils.copyProperties(childInfo, activityChildInfoPO);
        List<ActivityChildInfoPO> allRecords = activityChildInfoService.toGainAllChildRecords(activityChildInfoPO);
        if (CollectionUtils.isNotEmpty(allRecords)) {
            throw new HoServiceException("请确保同一数据类型的关联字段不能重复");
        }
        activityChildInfoPO.setId(HOStringUtil.newGUID());
        activityChildInfoPO.setCreateMan(SecurityUtils.getUsername());
        activityChildInfoPO.setCreateTime(DateUtil.getDateStringAllDate(new Date()));
        activityChildInfoPO.setUpdateMan(SecurityUtils.getUsername());
        activityChildInfoPO.setUpdateTime(DateUtil.getDateStringAllDate(new Date()));
        boolean saveResult = activityChildInfoService.save(activityChildInfoPO);
        if (!saveResult) {
            throw new HoServiceException("新增出错！");
        }
        if ("0".equals(childInfo.getDataType()) || "1".equals(childInfo.getDataType()) || "5".equals(childInfo.getDataType())) {
            List<ActivityAirLine> airlineList = childInfo.getAirlineList();
            for (int i = 0; i < airlineList.size(); i++) {
                ActivityAirlinePO activityAirlinePO = new ActivityAirlinePO();
                BeanUtils.copyProperties(airlineList.get(i), activityAirlinePO);
                activityAirlinePO.setId(HOStringUtil.newGUID());
                activityAirlinePO.setChildInfoId(activityChildInfoPO.getId());
                activityAirlinePO.setSeqNo(i + 1);
                boolean save = activityAirlineService.save(activityAirlinePO);
                if (!save) {
                    throw new HoServiceException("新增出错！");
                }
                if ("5".equals(childInfo.getDataType())) {
                    DiscountsRouteActivityVo discountsRouteActivityVo = discountsRouteActivityService.getDetails(activityChildInfoPO.getId());
                    flightBasicService.refreshPriceCache();
                    discountsRouteData.removeCacheDiscountsRoute(discountsRouteActivityVo.getActivityParam());
                } else {
                    replaceRedis();
                }
            }
        } else if (childInfo.getDataType().equals("2") || childInfo.getDataType().equals("3")) {
            List<ActivityCommodity> activityCommodity = childInfo.getCommodities();
            for (int i = 0; i < activityCommodity.size(); i++) {
                ActivityCommodity activityCommo = activityCommodity.get(i);
                ActivityCommodityPO activityCommodityPO = new ActivityCommodityPO();
                BeanUtils.copyProperties(activityCommo, activityCommodityPO);
                activityCommodityPO.setId(HOStringUtil.newGUID());
                activityCommodityPO.setChildInfoId(activityChildInfoPO.getId());
                activityCommodityPO.setSeqNo(i + 1);
                boolean save = activityCommodityService.save(activityCommodityPO);
                if (!save) {
                    throw new HoServiceException("新增出错！");
                }
            }
        } else if ("4".equals(childInfo.getDataType())) {
            List<ActivityInnerPage> innerPages = childInfo.getInnerPages();
            for (int i = 0; i < innerPages.size(); i++) {
                ActivityInnerPage innerPage = innerPages.get(i);
                ActivityInnerPageInfoPO activityInnerPageInfoPO = new ActivityInnerPageInfoPO();
                BeanUtils.copyProperties(innerPage, activityInnerPageInfoPO);
                activityInnerPageInfoPO.setId(HOStringUtil.newGUID());
                activityInnerPageInfoPO.setChildInfoId(activityChildInfoPO.getId());
                activityInnerPageInfoPO.setSeqNo(i + 1);
                boolean save = innerPageInfoService.save(activityInnerPageInfoPO);
                if (!save) {
                    throw new HoServiceException("新增出错！");
                }
            }
        }
        return true;
    }

    @Override
    public boolean toUpdateChildInfo(ActivityChildInfo childInfo) {
        ActivityChildInfoPO activityChildInfoPO = new ActivityChildInfoPO();
        BeanUtils.copyProperties(childInfo, activityChildInfoPO);
        List<ActivityChildInfoPO> allRecords = activityChildInfoService.toGainAllChildRecords(activityChildInfoPO);
        if (CollectionUtils.isNotEmpty(allRecords)) {
            throw new HoServiceException("请确保同一数据类型的关联字段不能重复");
        }
        activityChildInfoPO.setUpdateMan(SecurityUtils.getUsername());
        activityChildInfoPO.setUpdateTime(DateUtil.getDateStringAllDate(new Date()));
        int updateActivityChildInfo = activityChildInfoService.updateActivityChildInfo(activityChildInfoPO);
        if (updateActivityChildInfo <= 0) {
            throw new HoServiceException("更新失败");
        }
        if ("0".equals(childInfo.getDataType()) || "1".equals(childInfo.getDataType()) || "5".equals(childInfo.getDataType())) {
            ActivityAirLine tempAirLine = new ActivityAirLine();
            tempAirLine.setChildInfoId(childInfo.getId());
            boolean result = activityAirlineService.removeById(tempAirLine);
            if (!result) {
                throw new HoServiceException("删除出错！");
            }
            List<ActivityAirLine> airLines = childInfo.getAirlineList();
            for (int i = 0; i < airLines.size(); i++) {
                ActivityAirLine activityAirLine = airLines.get(i);
                ActivityAirlinePO activityAirLinePO = new ActivityAirlinePO();
                BeanUtils.copyProperties(activityAirLine, activityAirLinePO);
                activityAirLinePO.setId(HOStringUtil.newGUID());
                activityAirLinePO.setChildInfoId(childInfo.getId());
                activityAirLinePO.setSeqNo(i + 1);
                boolean save = activityAirlineService.save(activityAirLinePO);
                if (!save) {
                    throw new HoServiceException("新增出错！");
                }
            }
            if ("5".equals(childInfo.getDataType())) {
                DiscountsRouteActivityVo discountsRouteActivityVo = discountsRouteActivityService.getDetails(childInfo.getBaseinfoId());
                discountsRouteData.replaceRedis();
                discountsRouteData.removeCacheDiscountsRoute(discountsRouteActivityVo.getActivityParam());
            } else {
                replaceRedis();
            }
        } else if (childInfo.getDataType().equals("2") || childInfo.getDataType().equals("3")) {
            List<ActivityCommodity> activityCommodity = childInfo.getCommodities();
            ActivityCommodityPO commodityTemp = new ActivityCommodityPO();
            commodityTemp.setChildInfoId(childInfo.getId());
            activityCommodityService.removeById(commodityTemp);
            for (int i = 0; i < activityCommodity.size(); i++) {
                ActivityCommodity activityCommo = activityCommodity.get(i);
                ActivityCommodityPO activityCommodityPO = new ActivityCommodityPO();
                BeanUtils.copyProperties(activityCommo, activityCommodityPO);
                activityCommodityPO.setId(HOStringUtil.newGUID());
                activityCommodityPO.setChildInfoId(childInfo.getId());
                activityCommodityPO.setSeqNo(i + 1);
                boolean save = activityCommodityService.save(activityCommodityPO);
                if (!save) {
                    throw new HoServiceException("新增出错！");
                }
            }
        } else if ("4".equals(childInfo.getDataType())) {
            List<ActivityInnerPage> innerPages = childInfo.getInnerPages();
            ActivityInnerPageInfoPO activityInnerPageInfoPO = new ActivityInnerPageInfoPO();
            activityInnerPageInfoPO.setChildInfoId(childInfo.getId());
            innerPageInfoService.removeById(activityInnerPageInfoPO);
            for (int i = 0; i < innerPages.size(); i++) {
                ActivityInnerPage innerPage = innerPages.get(i);
                ActivityInnerPageInfoPO activityInnerPageInfo = new ActivityInnerPageInfoPO();
                BeanUtils.copyProperties(innerPage, activityInnerPageInfo);
                activityInnerPageInfo.setSeqNo(i + 1);
                activityInnerPageInfo.setId(HOStringUtil.newGUID());
                activityInnerPageInfo.setChildInfoId(childInfo.getId());
                boolean save = innerPageInfoService.save(activityInnerPageInfo);
                if (!save) {
                    throw new HoServiceException("新增出错！");
                }
            }
        }
        return true;
    }

    @Override
    public ActivityChildInfo toCatchChildDetailById(ActivityChildInfo activityChildInfo) {
        ActivityChildInfoPO activityChildInfoPO = new ActivityChildInfoPO();
        activityChildInfoPO.setId(activityChildInfo.getId());
        ActivityChildInfoPO childInfoPO = activityChildInfoService.toGainChildInfoById(activityChildInfoPO);
        if (null == childInfoPO) {
            throw new HoServiceException("查询无数据！");
        }
        ActivityChildInfo childInfo = new ActivityChildInfo();
        BeanUtils.copyProperties(childInfoPO, childInfo);
        ActivityAirlinePO activityAirLine = new ActivityAirlinePO();
        activityAirLine.setChildInfoId(childInfo.getId());
        ActivityCommodityPO activityCommodityPO = new ActivityCommodityPO();
        activityCommodityPO.setChildInfoId(childInfo.getId());
        ActivityInnerPageInfoPO innerPage = new ActivityInnerPageInfoPO();
        innerPage.setChildInfoId(childInfo.getId());
        List<ActivityAirlinePO> airlinePOS = activityAirlineService.toGainAllById(activityAirLine);
        List<ActivityCommodityPO> commodityPOS = activityCommodityService.toGainAllById(activityCommodityPO);
        List<ActivityInnerPageInfoPO> innerPageInfoPOS = innerPageInfoService.toGainAllById(innerPage);
        if (CollectionUtils.isNotEmpty(airlinePOS)) {
            List<ActivityAirLine> activityAirLines = new ArrayList<>();
            for (ActivityAirlinePO airLinePO : airlinePOS
            ) {
                ActivityAirLine airLine = new ActivityAirLine();
                BeanUtils.copyProperties(airLinePO, airLine);
                activityAirLines.add(airLine);
            }
            childInfo.setAirlineList(activityAirLines);
        }
        if (CollectionUtils.isNotEmpty(commodityPOS)) {
            List<ActivityCommodity> activityCommodities = new ArrayList<>();
            for (ActivityCommodityPO commodityPO : commodityPOS
            ) {
                ActivityCommodity commodity = new ActivityCommodity();
                BeanUtils.copyProperties(commodityPO, commodity);
                activityCommodities.add(commodity);
            }
            childInfo.setCommodities(activityCommodities);
        }

        if (CollectionUtils.isNotEmpty(innerPageInfoPOS)) {
            List<ActivityInnerPage> innerPages = new ArrayList<>();
            for (ActivityInnerPageInfoPO innerPageInfoPO : innerPageInfoPOS
            ) {
                ActivityInnerPage activityInnerPage = new ActivityInnerPage();
                BeanUtils.copyProperties(innerPageInfoPO, activityInnerPage);
                innerPages.add(activityInnerPage);
            }
            childInfo.setInnerPages(innerPages);
        }
        return childInfo;
    }

    @Override
    public boolean toUpdateChildStatus(ActivityChildInfo activityChildInfo) {
        ActivityChildInfoPO childInfoPO = new ActivityChildInfoPO();
        childInfoPO.setIsValid(activityChildInfo.getIsValid());
        childInfoPO.setUpdateMan(SecurityUtils.getUsername());
        childInfoPO.setUpdateTime(DateUtil.getDateStringAllDate(new Date()));
        UpdateWrapper<ActivityChildInfoPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(ActivityChildInfoPO::getId, activityChildInfo.getId());
        boolean updateResult = activityChildInfoService.update(childInfoPO, updateWrapper);
        if (updateResult) {
            replaceRedis();
        }
        return true;
    }

    @Override
    public ActivityBaseInfo toCatchCouponById(ActivityBaseInfo activityBaseInfo) {
        ActivityBaseInfoPO activityBaseInfoPO = new ActivityBaseInfoPO();
        activityBaseInfoPO.setId(activityBaseInfo.getId());
        ActivityBaseInfoPO result = activityBaseInfoService.selectOneByCondition(activityBaseInfoPO);
        if (null == result) {
            throw new HoServiceException("查询无数据！");
        }
        ActivityBaseInfo baseInfo = new ActivityBaseInfo();
        BeanUtils.copyProperties(result, baseInfo);
        return baseInfo;
    }

    @Override
    public List<ActivityAirLine> importExcel(MultipartFile file, String dataDesc) throws IOException {
        CommonsMultipartFile cmf = (CommonsMultipartFile) file;
        DiskFileItem dfi = (DiskFileItem) cmf.getFileItem();
        File fo = dfi.getStoreLocation();
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(FileUtils.openInputStream(fo));
        } catch (IOException e) {
            log.error(e.getMessage());
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
        //把MultipartFile转化为File
        List<ActivityAirLine> activityAirLines = new ArrayList<>();
        //创建Excel，读取文件内容
        try {
            //获取第一个工作表
            if (null == workbook) {
                return null;
            }
            int numberOfSheets = workbook.getNumberOfSheets();
            try {
                for (int j = 0; j < numberOfSheets; j++) {
                    int firstRowNum = workbook.getSheetAt(j).getFirstRowNum();
                    //获取sheet中最后一行行号
                    int lastRowNum = workbook.getSheetAt(j).getLastRowNum();
                    int i1 = firstRowNum + 1;
                    //循环插入数据
                    for (int i = i1; i < lastRowNum; i++) {
                        String mergedRegionValue1 = ExcelUtil.getMergedRegionValue(workbook.getSheetAt(j), i, 4);
                        if (NumberUtil.parseInt(mergedRegionValue1) == NumberUtil.parseInt(dataDesc)) {
                            Row row = workbook.getSheetAt(j).getRow(i);
                            ActivityAirLine activityAirLine = new ActivityAirLine();
                            if (row != null) {
                                Cell filghtNumber = row.getCell(0);//航班号
                                if (filghtNumber != null) {
                                    filghtNumber.setCellType(CellType.STRING);
                                    activityAirLine.setFilghtNumber((filghtNumber.getStringCellValue().trim()));
                                }
                                Cell cityCode = row.getCell(1);//航段信息
                                if (cityCode != null && cityCode.getCellType() != CellType.BLANK) {
                                    String[] split = cityCode.getStringCellValue().split("-");
                                    activityAirLine.setDepCityName(getCityInfo(split[0]));
                                    activityAirLine.setSendCode(split[0]);
                                    activityAirLine.setArrCode(split[1]);
                                    activityAirLine.setArrCityName(getCityInfo(split[1]));
                                }

                                Cell seckillCabin = row.getCell(3);//秒杀舱位
                                String mergedRegionValue = ExcelUtil.getMergedRegionValue(workbook.getSheetAt(j), i, 3);
                                if (seckillCabin != null) {
                                    seckillCabin.setCellType(CellType.STRING);
                                    activityAirLine.setSeckillCabin(mergedRegionValue);
                                }
                                Cell seckillPirce = row.getCell(4);//秒杀价格
                                if (seckillPirce != null) {
                                    seckillPirce.setCellType(CellType.NUMERIC);
                                    activityAirLine.setSeckillPrice(NumberUtil.parseInt(mergedRegionValue1));
                                }
                                activityAirLines.add(activityAirLine);
                            }

                        }
                    }
                }
                if (activityAirLines.size() > 0) {
                    return activityAirLines;
                } else {
                    throw new HoServiceException("请确保按照模板导入excel！");
                }

            } catch (Exception e) {
                log.error(e.getMessage());
            } finally {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error(e.getMessage());
                }
            }
        } catch (Exception e) {
            throw new HoServiceException("请确保按照模板导入excel！");
        }
        return null;
    }

    @Override
    public boolean toAddCoupon(ActivityCoupon activityCoupon) {
        if (activityCoupon.getCouponGiftName().length() > 9) {
            throw new HoServiceException("礼包名称不能超过9个字符！");
        }
        ActivityCouponsPO activityCouponsPO = new ActivityCouponsPO();
        BeanUtils.copyProperties(activityCoupon, activityCouponsPO);
        activityCouponsPO.setCreateDate(DateUtil.getDateStringAllDate(new Date()));
        activityCouponsPO.setLastUpdateDate(DateUtil.getDateStringAllDate(new Date()));
        activityCouponsPO.setCreateUser(SecurityUtils.getUsername());
        activityCouponsPO.setId(UUID.randomUUID().toString());
        activityCouponsPO.setLastUpdateUser(SecurityUtils.getUsername());
        try {
            return activityCouponsService.insertAll(activityCouponsPO);
        } catch (Exception e) {
            log.error("添加活动出错,错误信息为:", e);
            return false;
        }
    }

    @Override
    public boolean toDelCoupon(ActivityCoupon activityCoupon) {
        return activityCouponsService.removeById(activityCoupon.getId());
    }

    @Override
    public boolean toDelCouponDetail(ActivityCouponDetail activityCouponDetail) {
        return activityCouponDetailService.removeById(activityCouponDetail.getId());
    }

    @Override
    public boolean toUpdateCouponDetail(ActivityCouponDetail activityCouponDetail) {
        //校验礼包状态
        toCheckCouponStatus(activityCouponDetail);
        ActivityCouponDetailPO couponDetailPO = new ActivityCouponDetailPO();
        couponDetailPO.setCouponGiftId(activityCouponDetail.getCouponGiftId());
        QueryWrapper<ActivityCouponDetailPO> activityCouponDetailPOQueryWrapper = new QueryWrapper<>();
        activityCouponDetailPOQueryWrapper.eq("couponGiftId", activityCouponDetail.getCouponGiftId());
        int count = activityCouponDetailService.count(activityCouponDetailPOQueryWrapper);
        if (count >= 7) {
            throw new HoServiceException("该优惠券礼包已有过多优惠券，请做处理");
        }
        activityCouponDetailPOQueryWrapper.eq("couponId", activityCouponDetail.getCouponId());
        activityCouponDetailPOQueryWrapper.eq("Id", activityCouponDetail.getId());
        int couponIds = activityCouponDetailService.count(activityCouponDetailPOQueryWrapper);
        if (couponIds > 0) {
            throw new HoServiceException("一个优惠券礼包不能包含相同的优惠券ID");
        }
        ActivityCouponDetailPO couponPO = new ActivityCouponDetailPO();
        BeanUtils.copyProperties(activityCouponDetail, couponPO);
        couponPO.setLastUpdateUser(SecurityUtils.getUsername());
        couponPO.setLastUpdateDate(DateUtil.getDateStringAllDate(new Date()));
        boolean result = activityCouponDetailService.updateById(couponPO);
        if (!result) {
            throw new HoServiceException("更新状态出错");
        }
        return true;
    }

    private void toCheckCouponStatus(ActivityCouponDetail activityCouponDetail) {
        QueryWrapper<ActivityCouponsPO> activityCouponsPOQueryWrapper = new QueryWrapper<>();
        activityCouponsPOQueryWrapper.eq("Id", activityCouponDetail.getCouponGiftId());
        List<ActivityCouponsPO> activityCouponPOS = activityCouponsService.list(activityCouponsPOQueryWrapper);
        if (CollectionUtils.isEmpty(activityCouponPOS)) {
            throw new HoServiceException("查询无数据！");
        }
        if ("Y".equals(activityCouponPOS.get(0).getIsValid())) {
            throw new HoServiceException("请确定绑定的礼包处于已下线状态！");
        }
    }

    @Override
    public boolean toAddCouponDetail(ActivityCouponDetail activityCouponDetail) {
        //校验礼包状态
        toCheckCouponStatus(activityCouponDetail);
        QueryWrapper<ActivityCouponDetailPO> activityCouponDetailPOQueryWrapper = new QueryWrapper<>();
        activityCouponDetailPOQueryWrapper.eq("couponGiftId", activityCouponDetail.getCouponGiftId());
        activityCouponDetailPOQueryWrapper.eq("couponId", activityCouponDetail.getCouponId());
        int count = activityCouponDetailService.count(activityCouponDetailPOQueryWrapper);
        if (count > 0) {
            throw new HoServiceException("一个优惠券礼包不能包含相同的优惠券ID");
        }
        ActivityCouponDetailPO couponDetailPO = new ActivityCouponDetailPO();
        BeanUtils.copyProperties(activityCouponDetail, couponDetailPO);
        couponDetailPO.setId(UUID.randomUUID().toString());
        couponDetailPO.setCreateDate(DateUtil.getDateStringAllDate(new Date()));
        couponDetailPO.setLastUpdateDate(DateUtil.getDateStringAllDate(new Date()));
        couponDetailPO.setCreateUser(SecurityUtils.getUsername());
        couponDetailPO.setLastUpdateUser(SecurityUtils.getUsername());
        couponDetailPO.setCouponPrice(0);
        boolean save = activityCouponDetailService.save(couponDetailPO);
        if (!save) {
            throw new HoServiceException("添加活动出错！");
        }
        return true;
    }

    @Override
    public PhotoActivityJson toCatchType() {
        PhotoActivityJson pictureJson = new PhotoActivityJson();
        LambdaQueryWrapper<ActivityBaseInfoPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(ActivityBaseInfoPO::getCreateTime);
        List<ActivityBaseInfoPO> list = activityBaseInfoService.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            throw new HoServiceException("查询无数据");
        }
        List<ActivityBaseInfo> activityBaseInfos = new ArrayList<>();
        for (ActivityBaseInfoPO basePO : list
        ) {
            ActivityBaseInfo baseInfo = new ActivityBaseInfo();
            BeanUtils.copyProperties(basePO, baseInfo);
            activityBaseInfos.add(baseInfo);
        }
        List<Map<Object, Object>> listjson = new ArrayList<>();
        for (ActivityBaseInfo string : activityBaseInfos) {
            Map<Object, Object> map = new HashMap<Object, Object>();
            map.put("code", string.getId());
            map.put("name", string.getArea());
            listjson.add(map);
        }
        pictureJson.setActivityType(listjson);
        return pictureJson;
    }

    private String getCityInfo(String cityCode) {
        String redisData = primaryRedisService.get("cityInfoJson");
        if (StringUtils.isNotEmpty(redisData)) {
            Map<String, ArrCityJson> airportMap = JsonUtil.jsonToMap(redisData, new TypeToken<Map<String, ArrCityJson>>() {
            }.getType());
            for (Map.Entry<String, ArrCityJson> entry : airportMap.entrySet()) {
                if (entry.getKey().equals(cityCode)) {
                    return entry.getValue().getCityName();
                }
            }
        }
        return null;
    }


    /**
     * @return void
     * <AUTHOR>
     * @Description 刷新缓存操作
     * @Date 10:46 2023/11/17
     **/
    public void replaceRedis() {
        ActivityBaseInfoPO temp = new ActivityBaseInfoPO();
        temp.setNowTime(DateUtil.getDateStringAllDate(new Date()));
        List<AirLineJobParam> airLineJobParams = new ArrayList<>();
        List<ActivityBaseInfoPO> activityBaseInfos = activityBaseInfoService.toGainAllRecords(temp);
        if (CollectionUtils.isEmpty(activityBaseInfos)) {
            return;
        }
        for (ActivityBaseInfoPO activityBaseInfoPO : activityBaseInfos) {
            AirLineJobParam airLineJobParam = new AirLineJobParam();
            airLineJobParam.setArea(activityBaseInfoPO.getArea());
            airLineJobParam.setActivityParam(activityBaseInfoPO.getActivityParam());
            airLineJobParam.setJobStartDate(activityBaseInfoPO.getJobStartDate());
            airLineJobParam.setJobEndDate(activityBaseInfoPO.getJobEndDate());
            ActivityChildInfoPO childInfo = new ActivityChildInfoPO();
            childInfo.setBaseinfoId(activityBaseInfoPO.getId());
            childInfo.setDataType("0");
            List<ActivityChildInfoPO> allRecords = activityChildInfoService.toGainAllChildRecords(childInfo);
            List<AirLineChild> airLineChildren = new ArrayList<>();
            for (ActivityChildInfoPO activityChildInfoPO : allRecords) {
                AirLineChild airLineChild = new AirLineChild();
                airLineChild.setLinkName(activityChildInfoPO.getLinkName());
                airLineChild.setStartDate(activityChildInfoPO.getStartDate());
                airLineChild.setEndDate(activityChildInfoPO.getEndDate());
                airLineChild.setDataName(activityChildInfoPO.getDataName());
                airLineChild.setDataDesc(activityChildInfoPO.getDataDesc());
                ActivityAirlinePO airlinePO = new ActivityAirlinePO();
                airlinePO.setChildInfoId(activityChildInfoPO.getId());
                List<ActivityAirlinePO> activityAirlinePOS = activityAirlineService.toGainAllById(airlinePO);
                List<AirLine> tempAirLine = new ArrayList<>();
                for (ActivityAirlinePO activityAirLine : activityAirlinePOS) {
                    AirLine airLine = new AirLine();
                    airLine.setArrCode(activityAirLine.getArrCode());
                    airLine.setSendCode(activityAirLine.getSendCode());
                    tempAirLine.add(airLine);
                }
                airLineChild.setAirLine(tempAirLine);
                airLineChildren.add(airLineChild);
            }
            airLineJobParam.setAirline(airLineChildren);
            airLineJobParams.add(airLineJobParam);
        }
        log.info("获取有效的活动航线:" + JsonUtil.objectToJson(airLineJobParams));
        primaryRedisService.setJSON(NEW_ACTIVITY_SEGMENTS_INFO, JsonUtil.objectToJson(airLineJobParams), 24 * 3600L);
    }


}
