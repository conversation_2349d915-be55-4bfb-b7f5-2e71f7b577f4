package com.juneyaoair.ecs.manage.service.activity;

import com.juneyaoair.ecs.manage.dto.activity.redeem.signact.*;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.manage.b2c.entity.RedeemActSignPO;

import java.util.List;

public interface RedeemSignActService {
    PageResult addSignAct(SignAddActRequest request);

    void updateSignAct(SignUpdateActRequest request);

    List<SignActUser> signActUserList(SignActUserListRequest request);

    List<RedeemActSignPO> exportSignActExcel(SignExportRequest request);

    List<SignActBO> signActList(SignActListRequest request);

    List<SignRedeemRecordBO> redeemRecord(SignRedeemRecordRequest request);
}
