package com.juneyaoair.ecs.manage.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.message.request.MessageDto;
import com.juneyaoair.ecs.manage.enums.MessageTypeEnum;
import com.juneyaoair.ecs.manage.service.message.IMessageAggrService;
import com.juneyaoair.manage.b2c.entity.MessagePO;
import com.juneyaoair.manage.b2c.service.IMessageService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/4 16:34
 */
@RequestMapping("/message")
@RestController
@Api(value = "MessageController", tags = "消息公告管理")
public class MessageController extends HoBaseController {
    @Autowired
    private IMessageService messageService;
    @Autowired
    private IMessageAggrService messageAggrService;

    @PostMapping("/pageList")
    @ApiOperation(value = "公告列表分页查询", notes = "")
    public R<PageResult<MessagePO>> pageList(@RequestBody MessagePO message) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        List<MessagePO> list = messageService.selectMessageList(message);
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(i -> {
                if (StrUtil.isBlank(i.getMessageType())) {
                    return;
                }
                MessageTypeEnum enumByCode = MessageTypeEnum.getEnumByCode(i.getMessageType());
                i.setMessageType(enumByCode.getDesc() + "(" + enumByCode.getCode() + ")");
            });
        }
        return getPageData(list, pageDomain);
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加公告信息", notes = "")
    public R<Boolean> add(@RequestBody @Validated MessageDto messageDto) {
        return R.ok(messageAggrService.addMessage(messageDto));
    }

    @GetMapping("/get/{messageId}")
    @ApiOperation(value = "获取公告详情", notes = "")
    public R<MessageDto> get(@PathVariable String messageId) {
        if (StringUtils.isBlank(messageId)) {
            return R.fail("获取详情失败，缺少记录编号");
        }
        return R.ok(messageAggrService.messageDetail(messageId));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新公告信息", notes = "")
    public R<Boolean> update(@RequestBody @Validated MessageDto messageDto) {
        if (StringUtils.isBlank(messageDto.getMessageid())) {
            return R.fail("更新失败，缺少记录编号");
        }
        return R.ok(messageAggrService.updateMessage(messageDto));
    }

    @PostMapping("/publishOrOffline")
    @ApiOperation(value = "公告发布与下线", notes = "")
    public R<Boolean> publishOrOffline(@RequestBody MessageDto messageDto) {
        return R.ok(messageAggrService.publishOrOffline(messageDto));
    }

    @DeleteMapping("/delete/{messageId}")
    @ApiOperation(value = "删除公告信息", notes = "")
    public R<Boolean> delete(@PathVariable String messageId) {
        if (StringUtils.isBlank(messageId)) {
            return R.fail("删除失败，缺少记录编号");
        }
        QueryWrapper<MessagePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("MESSAGEID", messageId);
        return R.ok(messageService.remove(queryWrapper));
    }
}
