package com.juneyaoair.ecs.manage.service.syncfile.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.juneyaoair.ecs.manage.config.AdminConfig;
import com.juneyaoair.ecs.manage.constant.ManageConstant;
import com.juneyaoair.ecs.manage.dto.citymanage.CityInfoRespDTO;
import com.juneyaoair.ecs.manage.dto.citymanage.CityLabelInfo;
import com.juneyaoair.ecs.manage.dto.citymanage.CityLabelInfoDTO;
import com.juneyaoair.ecs.manage.dto.citymanage.SyncCityInfoDto;
import com.juneyaoair.ecs.manage.dto.config.CityGroup;
import com.juneyaoair.ecs.manage.dto.config.GroupInfo;
import com.juneyaoair.ecs.manage.dto.config.Item;
import com.juneyaoair.ecs.manage.enums.HK_MO_TWCityEnum;
import com.juneyaoair.ecs.manage.enums.LanguageEnum;
import com.juneyaoair.ecs.manage.enums.ResultEnum;
import com.juneyaoair.ecs.manage.service.syncfile.ICitySyncStaticFileService;
import com.juneyaoair.ecs.redis.service.PrimaryRedisService;
import com.juneyaoair.ecs.utils.*;
import com.juneyaoair.manage.b2c.service.ICityInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 城市同步静态资源
 * <AUTHOR>
 */
@Service
public class CitySyncStaticFileServiceImpl extends SyncStaticFileService<CityInfoRespDTO> implements ICitySyncStaticFileService {

    @Resource
    ICityInfoService ICityInfoService;
    private String contentName = "city";
    private final String cityGroup = "cityGroup";

    @Resource
    private AdminConfig config;
    @Resource
    PrimaryRedisService primaryRedisService;

    @Override
    protected void removeCacheInRedis(List<CityInfoRespDTO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<CityInfoRespDTO> list1 = list.stream().filter(i -> i != null && StrUtil.isNotBlank(i.cityCode)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list1)) {
            return;
        }
        list1.forEach(i ->
                primaryRedisService.removeHashData("API:" + SystemUtil.envRedisDir() + "cityInfoHash", i.cityCode)
        );
        primaryRedisService.delete("API:" + "airportInfoJson");
        primaryRedisService.delete("FlightBasic:CityInfo");
    }

    @Override
    protected void updateVersionNo() {
        updateVersionNo(SystemConstants.REDIS_CITY_INFO);
    }

    @Override
    protected String map2Js(Map<String, Object> resultMap) {
        return "var __cityInfo = " + JsonUtil.objectToJson(resultMap);
    }

    @Override
    protected String getContentName() {
        return contentName;
    }

    @Override
    public Object data2ObjData(List<CityInfoRespDTO> data) {
        List<SyncCityInfoDto> syncCityInfoDtos = data.stream().map(i -> {
            SyncCityInfoDto syncCityInfoDto = new SyncCityInfoDto();
            BeanUtil.copyProperties(i, syncCityInfoDto);
            syncCityInfoDto.countryName = i.countryDTO.countryName;
            syncCityInfoDto.provinceName = i.provinceName;
            syncCityInfoDto.cityPinYinAbb = HOStringUtil.capitalizeFirstLetter(i.cityPinYinAbb);
            syncCityInfoDto.region = changeRegion(i.getIsInternational(),i.getCountryCode());
            if (i.getFileInfo() != null && HOStringUtil.isNotBlank(i.getFileInfo().getImgserviceurl())) {
                syncCityInfoDto.setCityPic(i.getFileInfo().getImgserviceurl());
            }
            //城市标签处理
            List<CityLabelInfoDTO> listCityLabel = i.getListCityLabel();
            if (!ObjectUtil.isEmpty(listCityLabel)) {
                List<CityLabelInfo> cityLabelInfoDtoList = new ArrayList<>();
                for (CityLabelInfoDTO cityLabelInfoCore : listCityLabel) {
                    CityLabelInfo labelInfo = new CityLabelInfo();
                    labelInfo.setLabelName(cityLabelInfoCore.getCityLabelName());
                    //标签URL
                    labelInfo.setLabelUrl("");
                    //对应图片URL
                    labelInfo.setLabelIconUrl(cityLabelInfoCore.getCityLabelUrl());
                    cityLabelInfoDtoList.add(labelInfo);
                    syncCityInfoDto.setCityLabelInfoList(cityLabelInfoDtoList);
                }
            }
            Map<String, String> cityNameMap = Maps.newHashMap();
            cityNameMap.put(LanguageEnum.ZH_CN.name(), i.cityName);
            if (StringUtils.isNotBlank(i.cityEName)) {
                cityNameMap.put(LanguageEnum.EN_US.name(), i.cityEName);
            }
            if (StringUtils.isNotBlank(i.cityJpName)) {
                cityNameMap.put(LanguageEnum.JA_JP.name(), i.cityJpName);
            }
            if (StringUtils.isNotBlank(i.cityTcName)) {
                cityNameMap.put(LanguageEnum.ZH_HK.name(), i.cityTcName);
            }
            syncCityInfoDto.cityNameMap = cityNameMap;
            return syncCityInfoDto;
        }).collect(Collectors.toList());
        changeHKMOTWCountryName(syncCityInfoDtos);
        return syncCityInfoDtos;
    }

    private void changeHKMOTWCountryName(List<SyncCityInfoDto> syncCityInfoDtos) {
        if (CollUtil.isEmpty(syncCityInfoDtos)) {
            return;
        }
        for (SyncCityInfoDto dto : syncCityInfoDtos) {
            if (Arrays.stream(HK_MO_TWCityEnum.values()).anyMatch(i->i==HK_MO_TWCityEnum.getEnumByCode(dto.cityCode))) {
                dto.countryName = ManageConstant.CN_DESC;
            }
        }
    }

    @Override
    public List<CityInfoRespDTO> prepareData() {
        List<CityInfoRespDTO> cityInfoRespDTOS = ICityInfoService.searchList(null);
        //只保留启用状态数据
        cityInfoRespDTOS.removeIf(i -> i != null && !"1".equalsIgnoreCase(i.status));
        return cityInfoRespDTOS;
    }

    @Override
    protected Map<String, Object> getResultMapForGroup(List<CityInfoRespDTO> search) {
        Map<String, CityGroup> cityGroupMap = config.getCityGroupMap();
        if (CollUtil.isEmpty(cityGroupMap)) {
            return null;
        }
        HashMap<String, Object> ret = new HashMap<>();
        for (Map.Entry<String, CityGroup> entry : cityGroupMap.entrySet()) {
            CityGroup cityGroup = entry.getValue();
            if (CollUtil.isNotEmpty(cityGroup.getGroupInfoList())) {
                for (GroupInfo group : cityGroup.getGroupInfoList()) {
                    List<CityInfoRespDTO> cityPageDTOList = new ArrayList<>();
                    for (Item item : group.getGroupItem()) {
                        List<CityInfoRespDTO> cityList = null;
                        if ("city".equals(item.getType())) {
                            cityList = search.stream().filter(cityPageDTO -> item.getCode().equals(cityPageDTO.getCityCode()))
                                    .collect(Collectors.toList());
                        }
                        if ("area".equals(item.getType())) {
                            cityList = search.stream()
                                    .filter(cityPageDTO -> item.getCode().equals(cityPageDTO.getCountryCode()))
                                    .collect(Collectors.toList());
                        }
                        if ("region".equals(item.getType())) {
                            cityList = search.stream()
                                    .filter(cityPageDTO -> item.getCode().equals(Optional.ofNullable(cityPageDTO.getCountryDTO())
                                            .map(it -> it.regionCode)
                                            .orElse(null)))
                                    .collect(Collectors.toList());
                        }
                        if (CollectionUtils.isNotEmpty(cityList)) {
                            cityPageDTOList.addAll(cityList);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(cityPageDTOList)) {
                        List<String> cityListStr = cityPageDTOList.stream()
                                .sorted(Comparator.comparing((CityInfoRespDTO cityPageDTO) -> StringUtils.isBlank(cityPageDTO.getCityPinYin()) ? "" : cityPageDTO.getCityPinYin().toUpperCase().substring(0, 1)))
                                .map(CityInfoRespDTO::getCityCode)
                                .collect(Collectors.toList());
                        group.setItemDetail(cityListStr);
                    }
                }
            }
        }
        Timestamp timestamp = DateUtil.getTimestamp();
        ret.put("objData", cityGroupMap);
        ret.put("loginFlag", false);
        ret.put("timeStamp", timestamp.getTime());
        ret.put("resultCode", ResultEnum.S10001.getResultCode());
        ret.put("resultInfo", ResultEnum.S10001.getResultInfo());
        return ret;
    }

    @Override
    protected String groupMap2Js(Map<String, Object> groupMap) {
        return "var _cityGroup =" + JsonMapper.buildNormalMapper().toJson(groupMap);
    }

    @Override
    protected String groupMap2Json(Map<String, Object> groupMap) {
        return JsonMapper.buildNormalMapper().toJson(groupMap);
    }

    @Override
    protected String getGroupContentName() {
        return cityGroup;
    }

    @Override
    public boolean process() {
        mainProcess();
        return true;
    }
}
