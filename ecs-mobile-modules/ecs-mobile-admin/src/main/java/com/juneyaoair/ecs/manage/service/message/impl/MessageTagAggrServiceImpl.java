package com.juneyaoair.ecs.manage.service.message.impl;

import com.juneyaoair.ecs.manage.dto.message.request.MessageTagDto;
import com.juneyaoair.ecs.manage.service.message.IMessageTagAggrService;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.manage.b2c.entity.MessageTagPO;
import com.juneyaoair.manage.b2c.service.IMessageTagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/11 14:26
 */
@Service
public class MessageTagAggrServiceImpl implements IMessageTagAggrService {
    @Autowired
    private IMessageTagService messageTagService;
    @Override
    public boolean addMessageTag(MessageTagDto messageTagDto) {
        MessageTagPO messageTagPO = new MessageTagPO();
        BeanUtils.copyNotNullProperties(messageTagDto, messageTagPO);
        messageTagPO.setMtid(HOStringUtil.newGUID());
        return messageTagService.save(messageTagPO);
    }
}
