package com.juneyaoair.ecs.manage.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.news.response.News;
import com.juneyaoair.ecs.manage.dto.social.request.SocialReportRequest;
import com.juneyaoair.ecs.manage.dto.social.response.SocialResponsibilityReport;
import com.juneyaoair.ecs.manage.service.social.ISocialService;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName SocialResponsibilityReportController
 * @Description 社会责任报告
 * <AUTHOR>
 * @Date 2024/6/12 14:07
 * @Version 1.0
 */
@RequestMapping("social")
@RestController
@RequiredArgsConstructor
@Api(value = "SocialResponsibilityReportController", tags = "社会责任报告API")
@Slf4j
public class SocialResponsibilityReportController extends HoBaseController {

    @Autowired
    private ISocialService socialService;

    @PostMapping(value = "toCatchSocialResReports")
    @ApiOperation(value = "社会责任报告列表页", httpMethod = "POST")
    public R<PageResult<SocialResponsibilityReport>> toCatchSocialResReports(@RequestBody SocialReportRequest request) {
        try {
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(request));
            PageDomain pageDomain = TableSupport.buildPageRequest();
            startPage(pageDomain);
            Page<News> localPage = PageHelper.getLocalPage();
            List<SocialResponsibilityReport> socialReports = socialService.toCatchSocialReports(request);
            R<PageResult<SocialResponsibilityReport>> pageData = getPageData(socialReports, localPage);
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(pageData));
            return pageData;
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            Context.setContext(null);
        }
        return R.fail();
    }

    @PostMapping(value = "toIncreaseSocialReport")
    @ApiOperation(value = "新增社会责任报告", httpMethod = "POST")
    @SuppressWarnings("rawtypes")
    public R toIncreaseSocialReport(@RequestBody SocialResponsibilityReport socialReportRequest) {
        socialService.toIncreaseSocialReport(socialReportRequest);
        return R.ok();
    }

    @PostMapping(value = "toUpdateSocialReport")
    @ApiOperation(value = "修改社会责任报告", httpMethod = "POST")
    @SuppressWarnings("rawtypes")
    public R toUpdateSocialReport(@RequestBody SocialResponsibilityReport socialReportRequest) {
        socialService.toUpdateSocialReport(socialReportRequest);
        return R.ok();
    }

    @PostMapping(value = "toRemoveSocialReport")
    @ApiOperation(value = "删除社会责任报告", httpMethod = "POST")
    @SuppressWarnings("rawtypes")
    public R toRemoveSocialReport(@RequestBody SocialResponsibilityReport socialReportRequest) {
        socialService.toRemoveSocialReport(socialReportRequest);
        return R.ok();
    }

}
