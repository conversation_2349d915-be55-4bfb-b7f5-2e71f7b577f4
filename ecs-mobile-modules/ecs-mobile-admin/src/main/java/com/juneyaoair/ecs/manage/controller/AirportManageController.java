package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.aop.CityAopMethodAnno;
import com.juneyaoair.ecs.manage.dto.airport.*;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.airport.IAirPortAggrService;
import com.juneyaoair.manage.b2c.service.IAirportService;
import com.juneyaoair.ecs.utils.ExcelUtil;
import com.juneyaoair.manage.b2c.entity.AirportJoinCityPO;
import com.juneyaoair.manage.b2c.entity.AirportInfoPO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@RequestMapping("airportManage")
@RestController
@RequiredArgsConstructor
@Api(value = "AirportManageController", tags = "机场管理")
@Slf4j
public class AirportManageController extends HoBaseController {


    @Autowired
    private IAirportService IAirportService;
    @Autowired
    IAirPortAggrService airPortAggrService;

    @ApiOperation(value = "分页查询机场（包括标签、重要告示） searchByCode合并到此接口", notes = "", httpMethod = "POST")
    @PostMapping(value = "searchPage")
    public R<PageResult<AirportInfoDTO>> searchPage(@RequestBody ParamAirportPage param) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        try {
            startPage(pageDomain);
            return getPageData(IAirportService.searchPage(pageDomain, param), pageDomain);
        } catch (Exception e) {
            log.error("查询基础信息列表错误", e);
        }
        return R.fail();
    }

    @ApiOperation(value = "查询所有机场（包含城市信息）", notes = "", httpMethod = "POST")
    @RequestMapping(value = "searchAllAirport", method = RequestMethod.POST)
    public R<List<AirportJoinCityPO>> searchAllAirport(@RequestBody AirportJoinCityPO airPortInfo) {
        return R.ok(IAirportService.searchAirportJoinCity(airPortInfo));
    }


    @PostMapping(value = "exportExcel")
    public void exportExcel(String airportCode, String cityCode, HttpServletRequest request, HttpServletResponse response) {
        try {
            ParamAirportPage paramAirportPage = new ParamAirportPage();
            paramAirportPage.setCityCode(cityCode);
            paramAirportPage.setAirportCode(airportCode);

            List<AirportInfoExportDTO> sheetList = airPortAggrService.exportList(paramAirportPage);

            LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
            fieldMap.put("airportCode", "机场三字码");
            fieldMap.put("cityCode", "所属城市");
            fieldMap.put("airportName", "机场简称(简体)");
            fieldMap.put("airportEName", "机场简称(英文)");
            fieldMap.put("airportPinyin", "机场简称(拼音)");

            fieldMap.put("status", "状态");
            fieldMap.put("longitude", "经度");
            fieldMap.put("latitude", "纬度");
            fieldMap.put("terminalposition", "航站楼(国内)");
            fieldMap.put("iTerminalposition", "航站楼(国际)");
            fieldMap.put("ticketcounter", "售票处(国内)");
            fieldMap.put("iTicketcounter", "售票处(国际)");
            fieldMap.put("checkincounter", "值机柜台(国内)");
            fieldMap.put("iCheckincounter", "值机柜台(国际)");
            fieldMap.put("firstclasscheckincounter", "头等舱值机柜台(国内)");
            fieldMap.put("iFirstclasscheckincounter", "头等舱值机柜台(国际)");
            fieldMap.put("checkinbegintime", "值机开始时间");
            fieldMap.put("checkinendtime", "值机结束时间");
            fieldMap.put("viproom", "贵宾室(国内)");
            fieldMap.put("iViproom", "贵宾室(国际)");
            fieldMap.put("zonesLevel", "机场量级");
            fieldMap.put("gateCloseDate", "登机口关闭时间");
            String fileName = "机场管理导出表格";

            ExcelUtil.listToExcel(sheetList, fieldMap, fileName, response);
        } catch (Exception e) {
            log.error("导出城市信息异常" + e.getMessage(), e);
        }

    }

    private String convertString(String param) {
        String ret = param == null ? "" : param;
        return ret;
    }


    @ApiOperation(value = "插入机场", notes = "")
    @PostMapping(value = "add")
    @CityAopMethodAnno
    public R add(@RequestBody AirportInfoPO param) {
        try {
            if (IAirportService.add(param)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }

    @ApiOperation(value = "根据airportCode删除机场", notes = "")
    @GetMapping(value = "delete/{airportCode}")
    @CityAopMethodAnno
    public R delete(@PathVariable("airportCode") String airportCode) {
        try {
            if (IAirportService.delete(airportCode)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error("删除城市信息列表错误", e);
        }
        return R.fail();
    }

    @PostMapping(value = "update")
    @CityAopMethodAnno
    public R<Boolean> update(@RequestBody AirportInfoPO param) {
        try {
            if (IAirportService.update(param)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error("更新城市信息列表错误", e);
        }
        return R.fail();
    }

    @ApiOperation(value = "只更改机场状态")
    @PostMapping(value = "setStatus")
    @CityAopMethodAnno
    public R setStatus(@RequestBody AirportInfoPO param) {
        try {
            if (IAirportService.setStatus(param)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error("更新状态失败", e);
        }
        return R.fail();
    }

    /**
     * 修改机场标签
     *
     * @param param
     * @param airportCode
     * @return
     */
    @ApiOperation(value = "根据airportCode更改机场标签", notes = "")
    @PostMapping(value = "updateAirportLabel/{airportCode}")
    @CityAopMethodAnno
    public R updateAirportLabel(@RequestBody List<AirportLabelInfoDTO> param, @PathVariable("airportCode") String airportCode) {
        try {
            if (IAirportService.updateAirportLabel(param, airportCode)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error("维护机场标签错误！{}", e.getMessage());
        }
        return R.fail();
    }

    @ApiOperation(value = "以map格式返回所有机场信息，AirportCode为key 机场信息list为value", notes = "")
    @GetMapping(value = "searchAirportAndCity")
    public R<Map<String, List<AirportInfoPO>>> searchAirportAndCity() {
        try {
            return R.ok(IAirportService.searchAirport());
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }

    @ApiOperation(value = "根据airportCode更新机场公告", notes = "")
    @PostMapping(value = "updateAirportWarn/{airportCode}")
    @CityAopMethodAnno
    public R updateAirportWarn(@RequestBody List<AirportWarnDTO> param, @PathVariable("airportCode") String airportCode) {
        try {
            if (IAirportService.updateCityWarn(param, airportCode)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }
    @ResponseBody
    @GetMapping(value = "syncJs")
    @ApiOperation(value = "更新官网js文件", notes = "官网目前已经改版，作用不大")
    public R<Boolean> syncJs() {
        return R.ok(airPortAggrService.syncJs());
    }

    /**
     * 更新机场静态文件以及版本号
     */
    @ResponseBody
    @RequestMapping(value = "updateStaticFile", method = RequestMethod.GET)
    @ApiOperation(value = "更新APP以及网站JS文件", notes = "更新APP以及网站JS文件")
    public R<Boolean> updateAirportStaticFile() {
        airPortAggrService.syncJsonAndJs();
        return R.ok();
    }
}
