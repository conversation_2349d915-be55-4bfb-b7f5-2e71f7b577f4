package com.juneyaoair.ecs.manage.service.datadict.impl;

import com.juneyaoair.ecs.manage.dto.datadict.ModularVersion;
import com.juneyaoair.ecs.manage.dto.datadict.VersionManage;
import com.juneyaoair.ecs.manage.service.datadict.IVersionMangeService;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.mapper.VersionManageMapper;
import com.juneyaoair.manage.b2c.mapper.WxModularVersionManageMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @ClassName VersionMangeServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/24 8:46
 * @Version 1.0
 */

@Service
@RefreshScope
@Slf4j
public class VersionMangeServiceImpl implements IVersionMangeService {

    @Resource
    private VersionManageMapper versionManageMapper;

    @Resource
    private WxModularVersionManageMapper wxModularVersionManageMapper;

    @Override
    public List<VersionManage> getList(VersionManage versionManage) {
        List<VersionManage> list;
        StringBuffer iosVersion = new StringBuffer();
        StringBuffer andVersion = new StringBuffer();
        try {
            list = versionManageMapper.getList(versionManage);
            for (VersionManage vm : list) {
                List<ModularVersion> modularVersionList = wxModularVersionManageMapper.queryModularByModularId(vm.getVersionID().toString());
                if (null != modularVersionList && modularVersionList.size() > 0) {
                    for (ModularVersion mv : modularVersionList) {
                        if ("ios".equals(mv.getVersionType())) {
                            appendVersion(mv, iosVersion);
                        } else if ("android".equals(mv.getVersionType())) {
                            appendVersion(mv, andVersion);
                        }
                    }
                    vm.setIosVersion(iosVersion.toString());
                    vm.setAndVersion(andVersion.toString());
                }
                iosVersion.delete(0, iosVersion.length());
                andVersion.delete(0, andVersion.length());
            }
        } catch (Exception e) {
            log.debug("版本查询出错！！", e);
            return null;
        }
        return list;
    }

    @Override
    public int updateVersion(VersionManage versionManage) {
        int affectLine;
        List<VersionManage> versionManages = versionManageMapper.checkVersion(versionManage);
        if (versionManages != null && versionManages.size() > 0) {
            return -5;
        }
        try {
            affectLine = versionManageMapper.updateVersion(versionManage);
        } catch (Exception e) {
            log.debug("版本更新出错！!", e);
            return 0;
        }
        return affectLine;
    }

    @Override
    public boolean toAddVersion(VersionManage versionManage) {
        try {
            VersionManage vm = new VersionManage();
            vm.setChannelCode(versionManage.getChannelCode());
            vm.setVersionType(versionManage.getVersionType());
            List<VersionManage> versionManages = versionManageMapper.checkVersion(vm);
            if (CollectionUtils.isNotEmpty(versionManages)) {
                throw new HoServiceException("版本信息已存在");
            }
            int addVersion = versionManageMapper.toAddVersion(versionManage);
            if (addVersion > 0) {
                String ios = versionManage.getIosVersion();
                String and = versionManage.getAndVersion();
                insertModularVersion(ios, and, versionManage);
            }
            return true;
        } catch (HoServiceException e) {
            log.error("新增版本信息出错:", e);
            throw e;
        }
    }

    @Override
    public boolean toUpdateVersion(VersionManage versionManage) {
        try {
            if (StringUtils.isEmpty(versionManage.getVersionType())) {
                throw new HoServiceException("版本类型不能为空");
            }
            if (StringUtils.isEmpty(versionManage.getVersionNo())) {
                throw new HoServiceException("版本号不能为空");
            }
            VersionManage vm = new VersionManage();
            vm.setChannelCode(versionManage.getChannelCode());
            vm.setVersionType(versionManage.getVersionType());
            List<VersionManage> versionManages = versionManageMapper.checkVersion(vm);
            if (CollectionUtils.isNotEmpty(versionManages)) {
                Optional<VersionManage> versionExist = versionManages.stream().filter(el -> StringUtils.isNoneEmpty(el.getVersionType(), el.getChannelCode(), versionManage.getVersionType(), versionManage.getChannelCode()) &&
                        versionManage.getVersionType().equals(el.getVersionType()) && versionManage.getChannelCode().equals(el.getChannelCode()) && !Objects.equals(versionManage.getVersionID(), el.getVersionID())).findFirst();
                if (versionExist.isPresent()) {
                    throw new HoServiceException("版本信息已存在");
                }
            }
            int updateVersion = versionManageMapper.updateVersion(versionManage);
            if (updateVersion > 0) {
                ModularVersion mv = new ModularVersion();
                mv.setModularId((versionManage.getVersionID() == null) ? null : String.valueOf(versionManage.getVersionID()));
                wxModularVersionManageMapper.deleteModularVersion(mv);
                String ios = versionManage.getIosVersion();
                String and = versionManage.getAndVersion();
                insertModularVersion(ios, and, versionManage);
            }
            return true;
        } catch (HoServiceException e) {
            log.error("版本管理更新出错：", e);
            throw e;
        }
    }

    @Override
    public boolean toDeleteVersion(VersionManage versionManage) {
        if (versionManage.getVersionID() == null) {
            throw new HoServiceException("VersionID未指定");
        }
        int deleteVersion = versionManageMapper.deleteVersion(versionManage);
        if (deleteVersion > 0) {
            ModularVersion mv = new ModularVersion();
            mv.setModularId((versionManage.getVersionID() == null) ? null : versionManage.getVersionID().toString());
            int affectLineModularVersion = wxModularVersionManageMapper.deleteModularVersion(mv);
            if (affectLineModularVersion < 0) {
                throw new HoServiceException("新增失败");
            }
        }
        return true;
    }


    /**
     * @param mv
     * @param sb
     * <AUTHOR>
     * @Description 拼接支持的版本
     * @Date 9:45 2024/1/24
     **/
    private void appendVersion(ModularVersion mv, StringBuffer sb) {
        if (sb.length() > 0) {
            sb.append(",").append(mv.getVersionNo());
        } else {
            sb.append(mv.getVersionNo());
        }
    }

    /**
     * @param ios
     * @param and
     * @param versionManage
     * @return void
     * <AUTHOR>
     * @Description 往modular_version表中插入数据
     * @Date 13:27 2024/3/15
     **/
    public void insertModularVersion(String ios, String and, VersionManage versionManage) {
        ModularVersion mv = new ModularVersion();
        if (null != ios) {
            if (!ios.contains(",")) {
                mv.setId(HOStringUtil.newGUID());
                mv.setVersionType("ios");
                mv.setModularId((versionManage.getVersionID() == null) ? null : versionManage.getVersionID().toString());
                mv.setModularBm(versionManage.getVersionType());
                if (!"".equals(ios)) {
                    String[] noAndroidId = ios.split("##");
                    mv.setVersionNo(noAndroidId[0]);
                    mv.setInnerId(noAndroidId[1]);
                    wxModularVersionManageMapper.addModular(mv);
                }
            } else {
                String[] iosList = ios.split(",");
                for (String s : iosList) {
                    mv.setId(HOStringUtil.newGUID());
                    mv.setVersionType("ios");
                    mv.setModularId((versionManage.getVersionID() == null) ? null : versionManage.getVersionID().toString());
                    mv.setModularBm(versionManage.getVersionType());
                    String[] noAndroidId = s.split("##");
                    mv.setVersionNo(noAndroidId[0]);
                    mv.setInnerId(noAndroidId[1]);
                    wxModularVersionManageMapper.addModular(mv);
                }
            }
        }
        if (null != and) {
            if (!and.contains(",")) {
                mv.setId(HOStringUtil.newGUID());
                mv.setVersionType("android");
                mv.setModularId((versionManage.getVersionID() == null) ? null : versionManage.getVersionID().toString());
                mv.setModularBm(versionManage.getVersionType());
                if (!"".equals(and)) {
                    String[] noAndroidId = and.split("##");
                    mv.setVersionNo(noAndroidId[0]);
                    mv.setInnerId(noAndroidId[1]);
                    wxModularVersionManageMapper.addModular(mv);
                }
            } else {
                String[] androidList = and.split(",");
                for (String s : androidList) {
                    mv.setId(HOStringUtil.newGUID());
                    mv.setVersionType("android");
                    mv.setModularId((versionManage.getVersionID() == null) ? null : versionManage.getVersionID().toString());
                    mv.setModularBm(versionManage.getVersionType());
                    String[] noAndroidId = s.split("##");
                    mv.setVersionNo(noAndroidId[0]);
                    mv.setInnerId(noAndroidId[1]);
                    wxModularVersionManageMapper.addModular(mv);
                }
            }
        }
    }
}
