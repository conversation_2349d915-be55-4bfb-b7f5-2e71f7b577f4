package com.juneyaoair.ecs.manage.mapstruct;

import com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.bo.CoBrandCreditCardAdBO;
import com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.dto.AddOrUpdateAdDetailRequestDTO;
import com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.dto.AddOrUpdateAdRequestDTO;
import com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.dto.CoBrandCreditCardAdDTO;
import com.juneyaoair.manage.b2c.entity.CoBrandCreditCardAdPO;
import com.juneyaoair.manage.b2c.entity.CoBrandCreditCardContentPO;
import com.juneyaoair.manage.b2c.entity.CoBrandCreditCardDetailPO;
import com.juneyaoair.manage.b2c.entity.CoBrandCreditCardMainTextPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;


@Mapper
public interface CoBrandCreditCardAdMapStruct {

    CoBrandCreditCardAdMapStruct INSTANCE = org.mapstruct.factory.Mappers.getMapper(CoBrandCreditCardAdMapStruct.class);


    @Mappings({})
    CoBrandCreditCardAdDTO toCoBrandCreditCardAdDTO(CoBrandCreditCardAdBO bo);


    @Mappings({
            @Mapping(source = "startTime", target = "startTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(source = "endTime", target = "endTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(source = "sort", target = "sortNum"),
            @Mapping(target = "themeEnable", expression = "java(dto.isThemeEnable()? \"1\":\"0\")"),
    })
    CoBrandCreditCardAdPO toCoBrandCreditCardAdPO(AddOrUpdateAdRequestDTO dto);


    @Mappings({
            @Mapping(source = "sort", target = "sortNum"),
            @Mapping(target = "supportJump", expression = "java(detailDto.isEnable()? \"1\":\"0\")"),
    })
    CoBrandCreditCardDetailPO toCoBrandCreditCardDetailPo(AddOrUpdateAdDetailRequestDTO.AddOrUpdateAdDetailDTO detailDto);

    @Mappings({
            @Mapping(source = "sort", target = "sortNum"),
    })
    CoBrandCreditCardContentPO toCoBrandCreditCardContentPo(AddOrUpdateAdDetailRequestDTO.AddOrUpdateAdDetailDTO.AddOrUpdateAdContentDTO contentDTO);


    @Mappings({
            @Mapping(source = "sort", target = "sortNum"),
            @Mapping(source = "contentSubTitle", target = "subTitle")
    })
    CoBrandCreditCardMainTextPO toCoBrandCreditCardMainTextPo(AddOrUpdateAdDetailRequestDTO.AddOrUpdateAdDetailDTO.AddOrUpdateAdContentDTO.AddOrUpdateAdMainTextDTO mainTextDTO);
}
