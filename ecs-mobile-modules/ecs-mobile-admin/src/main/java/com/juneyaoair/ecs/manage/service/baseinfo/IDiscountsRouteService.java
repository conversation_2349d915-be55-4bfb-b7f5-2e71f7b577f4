package com.juneyaoair.ecs.manage.service.baseinfo;

import com.juneyaoair.ecs.manage.dto.discount.ro.DiscountsRouteActivityRO;
import com.juneyaoair.ecs.manage.dto.discount.vo.ChildNameVo;
import com.juneyaoair.ecs.manage.dto.discount.vo.DiscountsRouteActivityVo;
import com.juneyaoair.ecs.manage.dto.discount.vo.DiscountsSelectVo;
import com.juneyaoair.manage.b2c.entity.activity.DiscountsRouteActivityPO;

import java.util.List;

/**
 * @ClassName IDiscountsRouteSerivce
 * @Description
 * <AUTHOR>
 * @Date 2023/12/13 15:29
 * @Version 1.0
 */
public interface IDiscountsRouteService {

    boolean save(DiscountsRouteActivityRO discountsRouteActivityRO);

    List<DiscountsRouteActivityPO> toCatchList(DiscountsRouteActivityRO discountsRouteActivityRO);

    DiscountsRouteActivityVo getDetails(String id);

    boolean toUpdate(DiscountsRouteActivityRO discountsRouteActivityRO);

    boolean toUpdateValid(String id,String valid);

    boolean toUpdateStatus(String id,String status);

    List<DiscountsSelectVo> getSelectList();

    List<ChildNameVo> getChildNameList(String  activityId, String dataId);

}
