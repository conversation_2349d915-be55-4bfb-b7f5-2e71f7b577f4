package com.juneyaoair.ecs.manage.service.activity.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.juneyaoair.ecs.manage.dto.activity.request.CommonLotteryPoolReq;
import com.juneyaoair.ecs.manage.dto.activity.request.CommonLotteryPrizeReq;
import com.juneyaoair.ecs.manage.dto.activity.request.SubPrizeReq;
import com.juneyaoair.ecs.manage.service.activity.ICommonLotteryService;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.activity.*;
import com.juneyaoair.manage.b2c.service.*;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CommonLotteryServiceImpl
 * @Description
 * @createTime 2023年05月17日 14:52
 */
@Service
@RefreshScope
public class CommonLotteryServiceImpl implements ICommonLotteryService {
    @Autowired
    private CommonLotteryPoolService commonLotteryPoolService;

    @Autowired
    private CommonLotteryPrizeService commonLotteryPrizeService;

    @Autowired
    private CommonLotterySubPrizeService commonLotterySubPrizeService;

    @Autowired
    private CommonLotteryResultService commonLotteryResultService;

    private static final String NON_DELETED = "N"; //是否删除标志 未删除

    private static final String DELETED = "Y"; //是否删除标志 已删除


    @Override
    public boolean toAddPrizePoolRecord (CommonLotteryPoolReq commonLotteryPoolReq) {
        //校验是否存在同名或同编码的奖池
        CommonLotteryPoolPO commonLotteryPoolQueryPO = new CommonLotteryPoolPO();
        commonLotteryPoolQueryPO.setPrizePoolCode(commonLotteryPoolReq.getPrizePoolCode());
        commonLotteryPoolQueryPO.setDeleted(NON_DELETED);
        List<CommonLotteryPoolPO> poolRecords = commonLotteryPoolService.toGainAllPoolRecords(commonLotteryPoolQueryPO);

        if (CollectionUtils.isNotEmpty(poolRecords)) {
            throw new HoServiceException("奖池编码已存在");
        }
        CommonLotteryPoolPO commonLotteryPoolPO = new CommonLotteryPoolPO();

        commonLotteryPoolPO.setId(UUID.randomUUID().toString().replace("-",""));
        commonLotteryPoolPO.setPrizePoolCode(commonLotteryPoolReq.getPrizePoolCode());
        commonLotteryPoolPO.setPrizePoolName(commonLotteryPoolReq.getPrizePoolName());
        commonLotteryPoolPO.setDeleted(NON_DELETED);
        commonLotteryPoolPO.setCreateTime(new Date());
        commonLotteryPoolPO.setCreateUser(SecurityUtils.getUsername());
        return commonLotteryPoolService.save(commonLotteryPoolPO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toAddPrizeRecord(CommonLotteryPrizeReq commonLotteryPrizeReq) {

        if (CollectionUtils.isEmpty(commonLotteryPrizeReq.getSubPrizeReqList())) {
            throw new HoServiceException("子奖品信息不能为空");
        }

        CommonLotteryPoolPO commonLotteryPoolPO = new CommonLotteryPoolPO();
        commonLotteryPoolPO.setPrizePoolCode(commonLotteryPrizeReq.getPrizePoolCode());
        commonLotteryPoolPO.setDeleted(NON_DELETED);
        List<CommonLotteryPoolPO> poolRecords = commonLotteryPoolService.toGainAllPoolRecords(commonLotteryPoolPO);
        if (CollectionUtils.isEmpty(poolRecords)) {
            throw new HoServiceException("奖池不存在，无法添加！");
        }

        //0. 奖品概率及名称校验
        CommonLotteryPrizePO winningPrizePO = new CommonLotteryPrizePO();
        winningPrizePO.setDeleted(NON_DELETED);
        winningPrizePO.setPrizePoolCode(commonLotteryPrizeReq.getPrizePoolCode());
        List<CommonLotteryPrizePO> commonLotteryPrizePOS = commonLotteryPrizeService.toGainAllPrizeRecords(winningPrizePO);

        if (CollectionUtils.isNotEmpty(commonLotteryPrizePOS)) {
            commonLotteryPrizePOS.forEach(el -> {
                if (StringUtils.isNotEmpty(el.getPrizeCode()) && StringUtils.isNotEmpty(commonLotteryPrizeReq.getPrizeCode()) && el.getPrizeCode().equals(commonLotteryPrizeReq.getPrizeCode())) {
                    throw new HoServiceException("本奖池或其他奖池中已存在相同奖品编码，请替换");
                }
            });
        }

        CommonLotteryPrizePO prizePO = commonLotteryPrizePOS.stream().filter(e -> StringUtils.isNotEmpty(e.getPrizeCode()) && e.getPrizeCode().equals(commonLotteryPrizeReq.getPrizeCode())).findFirst().orElse(null);
        if (null != prizePO) {
            throw new HoServiceException("奖项编码已存在，无法添加！");
        }

        double totalWinningRate = 0.0;
        if (CollectionUtils.isNotEmpty(commonLotteryPrizePOS)) {
            for (CommonLotteryPrizePO commonLotteryPrizePO : commonLotteryPrizePOS) {
                totalWinningRate += commonLotteryPrizePO.getWinningRate();
            }
            if (totalWinningRate + commonLotteryPrizeReq.getWinningRate() > 1.0) {
                throw new HoServiceException("概率之和超出上限，无法添加！");
            }
        }

        //1. 往奖品表中插入新数据
        CommonLotteryPrizePO commonLotteryPrizePO = new CommonLotteryPrizePO();
        BeanUtils.copyProperties(commonLotteryPrizeReq, commonLotteryPrizePO);
        commonLotteryPrizePO.setId(HOStringUtil.newGUID());
        // 新增时奖品总数与奖品数一致
        commonLotteryPrizePO.setTotalAmount(commonLotteryPrizePO.getPrizeAmount());
        commonLotteryPrizePO.setCreateUser(SecurityUtils.getUsername());
        commonLotteryPrizePO.setCreateTime(new Date());
        commonLotteryPrizePO.setDeleted(NON_DELETED);
        boolean insertPrizeResult = commonLotteryPrizeService.save(commonLotteryPrizePO);

        if (!insertPrizeResult) {
            throw new HoServiceException("插入奖品库记录失败，程序终止！");
        }

        //2.往子表中插入记录
        boolean insertSubPrizeResult = false;
        List<SubPrizeReq> subPrizeReqList = commonLotteryPrizeReq.getSubPrizeReqList();
        for (SubPrizeReq subPrize : subPrizeReqList) {
            CommonLotterySubPrizePO commonLotterySubPrizePO = new CommonLotterySubPrizePO();
            BeanUtils.copyProperties(subPrize, commonLotterySubPrizePO);
            commonLotterySubPrizePO.setId(HOStringUtil.newGUID());
            commonLotterySubPrizePO.setPrizeCode(commonLotteryPrizeReq.getPrizeCode());
            commonLotterySubPrizePO.setCreateTime(new Date());
            commonLotterySubPrizePO.setCreateUser(SecurityUtils.getUsername());
            commonLotterySubPrizePO.setDeleted(NON_DELETED);
            insertSubPrizeResult = commonLotterySubPrizeService.save(commonLotterySubPrizePO);
        }

        if (!insertSubPrizeResult) {
            throw new HoServiceException("插入子奖品库记录失败，程序终止！");
        }
        return true;
    }

    @Override
    public boolean toUpdatePrizePoolRecord(CommonLotteryPoolReq commonLotteryPoolReq) {
        CommonLotteryPoolPO commonLotteryPoolPO = new CommonLotteryPoolPO();
        commonLotteryPoolPO.setPrizePoolCode(commonLotteryPoolReq.getPrizePoolCode());
        commonLotteryPoolPO.setDeleted(NON_DELETED);
        List<CommonLotteryPoolPO> commonLotteryPoolPOS = commonLotteryPoolService.toGainAllPoolRecords(commonLotteryPoolPO);

        if (CollectionUtils.isEmpty(commonLotteryPoolPOS)) {
            throw new HoServiceException("奖池不存在，程序终止！");
        }

        CommonLotteryPoolPO poolPOToUpdate = commonLotteryPoolPOS.get(0);
        poolPOToUpdate.setUpdateTime(new Date());
        poolPOToUpdate.setUpdateUser(SecurityUtils.getUsername());
        poolPOToUpdate.setPrizePoolName(commonLotteryPoolReq.getPrizePoolName());
        return commonLotteryPoolService.updateById(poolPOToUpdate);
    }

    @Override
    public List<CommonLotteryResponse> queryLotteryPoolResult(CommonLotteryPoolReq commonLotteryPoolReq) {
        List<CommonLotteryResponse> commonLotteryResponses = new ArrayList<>();
        CommonLotteryPoolPO commonLotteryPoolPO = new CommonLotteryPoolPO();
        commonLotteryPoolPO.setId(commonLotteryPoolReq.getId());
        commonLotteryPoolPO.setDeleted("N");
        commonLotteryPoolPO.setPrizePoolCode(commonLotteryPoolReq.getPrizePoolCode());
        commonLotteryPoolPO.setPrizePoolName(commonLotteryPoolReq.getPrizePoolName());
        List<CommonLotteryPoolPO> poolRecords = commonLotteryPoolService.toGainAllPoolRecords(commonLotteryPoolPO);

        if (CollectionUtils.isNotEmpty(poolRecords)) {
            poolRecords.forEach(poolPO -> {
                CommonLotteryResponse commonLotteryResponse = new CommonLotteryResponse();
                commonLotteryResponse.setId(poolPO.getId());
                commonLotteryResponse.setPrizePoolCode(poolPO.getPrizePoolCode());
                commonLotteryResponse.setPrizePoolName(poolPO.getPrizePoolName());
                commonLotteryResponses.add(commonLotteryResponse);
            });
        }
        return commonLotteryResponses;
    }

    @Override
    public CommonLotteryPrizeResp queryLotteryPrizeResult(CommonLotteryPrizeReq commonLotteryPrizeReq) {

        CommonLotteryPrizeResp result = new CommonLotteryPrizeResp();
        result.setPrizePoolCode(commonLotteryPrizeReq.getPrizePoolCode());
        if (StringUtils.isEmpty(commonLotteryPrizeReq.getPrizePoolCode())) {
            throw new HoServiceException("奖池编码不能为空！");
        }

        CommonLotteryPrizePO commonLotteryPrizePO = new CommonLotteryPrizePO();
        commonLotteryPrizePO.setDeleted(NON_DELETED);
        commonLotteryPrizePO.setPrizePoolCode(commonLotteryPrizeReq.getPrizePoolCode());

        List<CommonLotteryPrizePO> commonLotteryPrizePOS = commonLotteryPrizeService.toGainAllPrizeRecords(commonLotteryPrizePO);

        List<CommonLotteryPrize> commonLotteryPrizes = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(commonLotteryPrizePOS)) {
            for (CommonLotteryPrizePO prizePO : commonLotteryPrizePOS
            ) {
                CommonLotteryPrize commonLotteryPrize = new CommonLotteryPrize();
                commonLotteryPrize.setId(prizePO.getId());
                commonLotteryPrize.setPrizeAmount(prizePO.getPrizeAmount());
                commonLotteryPrize.setTotalAmount(prizePO.getTotalAmount());
                commonLotteryPrize.setPrizeName(prizePO.getPrizeName());
                commonLotteryPrize.setPrizeCode(prizePO.getPrizeCode());
                commonLotteryPrize.setIconUrl(prizePO.getIconUrl());
                commonLotteryPrize.setSort(prizePO.getSort());
                commonLotteryPrize.setWinningRate(prizePO.getWinningRate());
                commonLotteryPrizes.add(commonLotteryPrize);
            }
        }

        if (CollectionUtils.isNotEmpty(commonLotteryPrizes)) {
            for (CommonLotteryPrize prize : commonLotteryPrizes
            ) {
                CommonLotterySubPrizePO commonLotterySubPrizePO = new CommonLotterySubPrizePO();
                commonLotterySubPrizePO.setDeleted(NON_DELETED);
                commonLotterySubPrizePO.setPrizeCode(prize.getPrizeCode());
                List<CommonLotterySubPrizePO> commonLotterySubPrizePOS = commonLotterySubPrizeService.toGainAllSubPrizeRecords(commonLotterySubPrizePO);
                if (CollectionUtils.isNotEmpty(commonLotterySubPrizePOS)) {
                    List<CommonLotterySubPrize> subLotteryPrizes = new ArrayList<>();
                    for (CommonLotterySubPrizePO subPrizePO : commonLotterySubPrizePOS
                    ) {
                        CommonLotterySubPrize subPrize = new CommonLotterySubPrize();
                        subPrize.setId(subPrizePO.getId());
                        subPrize.setSubPrizeAmount(subPrizePO.getSubPrizeAmount());
                        subPrize.setSubPrizeCode(subPrizePO.getSubPrizeCode());
                        subPrize.setSubPrizeName(subPrizePO.getSubPrizeName());
                        subPrize.setSubPrizeCategory(subPrizePO.getSubPrizeCategory());
                        subLotteryPrizes.add(subPrize);
                    }
                    prize.setSubPrizeReqList(subLotteryPrizes);
                }
            }
        }
        result.setCommonLotteryPrizeList(commonLotteryPrizes);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toUpdateLotteryPrizeInfo(CommonLotteryPrizeReq commonLotteryPrizeReq) {

        if (CollectionUtils.isEmpty(commonLotteryPrizeReq.getSubPrizeReqList())) {
            throw new HoServiceException("子奖品列表不能为空！");
        }

        if (StringUtils.isEmpty(commonLotteryPrizeReq.getPrizeCode())) {
            throw new HoServiceException("奖品编码不能为空！");
        }

        if (StringUtils.isEmpty(commonLotteryPrizeReq.getPrizeName())) {
            throw new HoServiceException("奖品名称不能为空！");
        }

        Optional<SubPrizeReq> firstSubPrizeCode = commonLotteryPrizeReq.getSubPrizeReqList().stream().filter(e -> StringUtils.isEmpty(e.getSubPrizeCode())).findFirst();
        if (firstSubPrizeCode.isPresent()) {
            throw new HoServiceException("子奖品编码不能为空！");
        }

        Optional<SubPrizeReq> firstSubPrizeCate = commonLotteryPrizeReq.getSubPrizeReqList().stream().filter(e -> StringUtils.isEmpty(e.getSubPrizeCategory())).findFirst();

        if (firstSubPrizeCate.isPresent()) {
            throw new HoServiceException("子奖品类别不能为空！");
        }

        Optional<SubPrizeReq> firstSubPrizeName = commonLotteryPrizeReq.getSubPrizeReqList().stream().filter(e -> StringUtils.isEmpty(e.getSubPrizeName())).findFirst();

        if (firstSubPrizeName.isPresent()) {
            throw new HoServiceException("子奖品名称不能为空！");
        }

        //0. 奖品概率校验
        CommonLotteryPrizePO winningPrizePO = new CommonLotteryPrizePO();
        winningPrizePO.setDeleted(NON_DELETED);
        winningPrizePO.setPrizePoolCode(commonLotteryPrizeReq.getPrizePoolCode());
        List<CommonLotteryPrizePO> commonLotteryPrizePOS = commonLotteryPrizeService.toGainAllPrizeRecords(winningPrizePO);
        double totalWinningRate = 0.0;
        if (CollectionUtils.isNotEmpty(commonLotteryPrizePOS)) {
            for (CommonLotteryPrizePO commonLotteryPrizePO : commonLotteryPrizePOS) {
                if (StringUtils.isNotEmpty(commonLotteryPrizePO.getPrizeCode()) && commonLotteryPrizePO.getPrizeCode().equals(commonLotteryPrizeReq.getPrizeCode())) {
                    continue;
                }
                totalWinningRate += commonLotteryPrizePO.getWinningRate();
            }
            if (totalWinningRate + commonLotteryPrizeReq.getWinningRate() > 1.0) {
                throw new HoServiceException("概率之和超出上限，无法添加！");
            }
        }

        //1. 更新奖品库
        CommonLotteryPrizePO commonLotteryPrizePO = new CommonLotteryPrizePO();
        BeanUtils.copyProperties(commonLotteryPrizeReq, commonLotteryPrizePO);
        commonLotteryPrizePO.setUpdateUser(SecurityUtils.getUsername());
        commonLotteryPrizePO.setUpdateTime(new Date());

        int updatePrizeResult = commonLotteryPrizeService.toUpdatePrizeRecord(commonLotteryPrizePO);
        if (updatePrizeResult <= 0) {
            throw new HoServiceException("更新主奖品信息出错!");
        }

        //2. 更新子奖品库 为避免数据紊乱 此处将传入的子奖品统一删除（注意是物理删除） 然后通过新建的方式建立新的子奖品记录
        List<SubPrizeReq> subPrizeReqList = commonLotteryPrizeReq.getSubPrizeReqList();

        CommonLotterySubPrizePO subPrizePO = new CommonLotterySubPrizePO();
        subPrizePO.setPrizeCode(commonLotteryPrizeReq.getPrizeCode());
        int deleteSubPrizeRecord = commonLotterySubPrizeService.toDeleteSubPrizeRecords(subPrizePO);

        if (deleteSubPrizeRecord <= 0) {
            throw new HoServiceException("删除原子奖品信息出错！");
        }

        for (SubPrizeReq subPrize : subPrizeReqList) {
            CommonLotterySubPrizePO commonLotterySubPrizePO = new CommonLotterySubPrizePO();
            BeanUtils.copyProperties(subPrize, commonLotterySubPrizePO);
            commonLotterySubPrizePO.setId(HOStringUtil.newGUID());
            commonLotterySubPrizePO.setPrizeCode(commonLotteryPrizeReq.getPrizeCode());
            commonLotterySubPrizePO.setDeleted(NON_DELETED);
            commonLotterySubPrizePO.setCreateTime(new Date());
            commonLotterySubPrizePO.setCreateUser(SecurityUtils.getUsername());
            boolean updateSubPrizeResult = commonLotterySubPrizeService.save(commonLotterySubPrizePO);
            if (!updateSubPrizeResult) {
                throw new HoServiceException("更新子奖品信息出错！");
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toDeletePoolOrPrize(CommonLotteryReq commonLotteryReq) {

        if (StringUtils.isEmpty(commonLotteryReq.getPrizePoolCode())) {
            throw new HoServiceException("奖池编码不能为空！");
        }

        //删除整个奖池
        if (StringUtils.isNotEmpty(commonLotteryReq.getPrizePoolId())) {
            //1. 删除奖池
            CommonLotteryPoolPO commonLotteryPoolPO = new CommonLotteryPoolPO();
            commonLotteryPoolPO.setId(commonLotteryReq.getPrizePoolId());
            commonLotteryPoolPO.setDeleted(DELETED);
            commonLotteryPoolPO.setUpdateUser(SecurityUtils.getUsername());
            commonLotteryPoolPO.setUpdateTime(new Date());
            boolean updatePoolResult = commonLotteryPoolService.updateById(commonLotteryPoolPO);

            if (!updatePoolResult) {
                throw new HoServiceException("删除奖池失败！");
            }

            if (CollectionUtils.isNotEmpty(commonLotteryReq.getPrizeCodeList())) {
                //奖池非空才进行2.3.操作
                //2. 删除奖品信息
                toDeletePrizeInfo(commonLotteryReq, null);

                //3. 删除子奖品信息
                toDeleteSubPrizeInfo(commonLotteryReq);
            }
        } else if (StringUtils.isEmpty(commonLotteryReq.getPrizePoolId()) && CollectionUtils.isNotEmpty(commonLotteryReq.getPrizeCodeList())) {
            //只删除单一奖品
            List<String> prizeCodeList = commonLotteryReq.getPrizeCodeList();
            if (CollectionUtils.isEmpty(prizeCodeList)) {
                throw new HoServiceException("奖品编码不能为空！");
            }
            //1. 删除奖品信息
            toDeletePrizeInfo(commonLotteryReq, prizeCodeList);
            //2. 删除全部子奖品信息
            toDeleteSubPrizeInfo(commonLotteryReq);
        }
        return true;
    }

    @Override
    public List<CommonLotteryResultResp> queryCommonLotteryResultPage(CommonLotteryResultReq commonLotteryResultReq) {
        return commonLotteryResultService.toGainAllPrizeRecords(commonLotteryResultReq);
    }


    private void toDeletePrizeInfo(CommonLotteryReq commonLotteryPrizeReq, List<String> prizeCodeList) {
        CommonLotteryPrizePO updatePrizePO = new CommonLotteryPrizePO();
        updatePrizePO.setDeleted(DELETED);
        updatePrizePO.setUpdateTime(new Date());
        updatePrizePO.setUpdateUser(SecurityUtils.getUsername());
        if(StringUtils.isNotEmpty(commonLotteryPrizeReq.getPrizePoolCode())){
            updatePrizePO.setPrizePoolCode(commonLotteryPrizeReq.getPrizePoolCode());

        }
        if(null != prizeCodeList && StringUtils.isNotEmpty(prizeCodeList.get(0))){
            updatePrizePO.setPrizeCode(prizeCodeList.get(0));
        }
        commonLotteryPrizeService.toUpdatePrizeInfo(updatePrizePO);
    }

    private void toDeleteSubPrizeInfo(CommonLotteryReq commonLotteryPrizeReq) {
        CommonLotterySubPrizeUpPO commonLotterySubPrizeUpPO = new CommonLotterySubPrizeUpPO();
        commonLotterySubPrizeUpPO.setDeleted(DELETED);
        commonLotterySubPrizeUpPO.setUpdateTime(new Date());
        commonLotterySubPrizeUpPO.setUpdateUser(SecurityUtils.getUsername());
        if (CollectionUtils.isNotEmpty(commonLotteryPrizeReq.getPrizeCodeList())) {
            commonLotterySubPrizeUpPO.setPrizeCodes(commonLotteryPrizeReq.getPrizeCodeList());
        }
        commonLotterySubPrizeService.toUpdateSubPrizeRecords(commonLotterySubPrizeUpPO);
    }

}
