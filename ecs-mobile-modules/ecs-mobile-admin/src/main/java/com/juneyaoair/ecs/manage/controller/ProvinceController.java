package com.juneyaoair.ecs.manage.controller;


import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.manage.b2c.service.IProvinceService;
import com.juneyaoair.manage.b2c.entity.ProvincePO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RequestMapping("province")
@RestController
@RequiredArgsConstructor
@Api(value = "ProvinceController", tags = "省份管理")
@Slf4j
public class ProvinceController extends HoBaseController {

    @Autowired
    private IProvinceService IProvinceService;

    @PostMapping(value = "searchPageList")
    @ApiOperation(value = "分页查询省份", notes = "", httpMethod = "POST")
    public R<PageResult<ProvincePO>> searchForPage(@RequestBody ProvincePO param) {
        try {
            PageDomain pageDomain = TableSupport.buildPageRequest();
            startPage(pageDomain);
            return getPageData(IProvinceService.searchForPage(param), pageDomain);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }

    @PostMapping(value = "add")
    @ApiOperation(value = "添加省份", notes = "", httpMethod = "POST")
    public R addProvince(@RequestBody ProvincePO param) {
        try {
            if (IProvinceService.addProvince(param)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }


    @PostMapping(value = "update")
    @ApiOperation(value = "更新省份", notes = "", httpMethod = "POST")
    public R updateProvince(@RequestBody ProvincePO param) {
        try {
            if (IProvinceService.updateProvince(param)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }

    @PostMapping(value = "delete")
    @ApiOperation(value = "删除省份", notes = "", httpMethod = "POST")
    public R deleteProvince(@RequestBody ProvincePO param) {
        try {
            if (IProvinceService.deleteProvince(param)) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }

    @PostMapping(value = "search")
    @ApiOperation(value = "按字段搜索省", notes = "", httpMethod = "POST")
    public R<List<ProvincePO>> search(@RequestBody ProvincePO param) {
        try {
            return R.ok(IProvinceService.search(param));
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }
}
