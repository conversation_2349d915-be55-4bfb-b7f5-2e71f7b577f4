package com.juneyaoair.ecs.manage.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.dto.activity.request.CommonLotteryPoolReq;
import com.juneyaoair.ecs.manage.dto.activity.request.CommonLotteryPrizeReq;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.activity.ICommonLotteryService;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.ecs.utils.ExcelUtil;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.manage.b2c.entity.activity.*;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActivityRelateController
 * @Description 通用抽奖相关接口
 * @createTime 2023年05月17日 11:16
 */
@RequestMapping("activity")
@RestController
@RequiredArgsConstructor
@Api(value = "ActivityRelateController", tags = "通用抽奖相关API")
@Slf4j
public class ActivityRelateController extends HoBaseController {

    @Autowired
    private ICommonLotteryService commonLotteryService;

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "toAddPrizePoolRecord", method = RequestMethod.POST)
    @ApiOperation(value = "新增奖池记录", httpMethod = "POST")
    public R toAddPrizePoolRecord(@RequestBody CommonLotteryPoolReq param) {
        return R.ok(commonLotteryService.toAddPrizePoolRecord(param));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "toAddPrizeRecord", method = RequestMethod.POST)
    @ApiOperation(value = "新增奖品记录", httpMethod = "POST")
    public R toAddPrizeRecord(@RequestBody CommonLotteryPrizeReq param) {
        return R.ok(commonLotteryService.toAddPrizeRecord(param));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "toUpdatePrizePoolRecord", method = RequestMethod.POST)
    @ApiOperation(value = "更新奖池记录", httpMethod = "POST")
    public R toUpdatePrizePoolRecord(@RequestBody CommonLotteryPoolReq param) {
        return R.ok(commonLotteryService.toUpdatePrizePoolRecord(param));
    }

    @ResponseBody
    @RequestMapping(value = "queryLotteryPoolResult", method = RequestMethod.POST)
    @ApiOperation(value = "获取奖池列表", httpMethod = "POST")
    public R<PageResult<CommonLotteryResponse>> queryLotteryPoolResult(@RequestBody CommonLotteryPoolReq param) {
        initContext();
        log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(param));
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        Page<CommonLotteryResponse> localPage = PageHelper.getLocalPage();
        List<CommonLotteryResponse> commonLotteryResponses = commonLotteryService.queryLotteryPoolResult(param);
        R<PageResult<CommonLotteryResponse>> pageData = getPageData(commonLotteryResponses, localPage);
        log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(pageData));
        return pageData;
    }

    @ResponseBody
    @RequestMapping(value = "queryLotteryPrizeResult", method = RequestMethod.POST)
    @ApiOperation(value = "获取奖品列表", httpMethod = "POST")
    public R<CommonLotteryPrizeResp> queryLotteryPrizeResult(@RequestBody CommonLotteryPrizeReq param) {
        return R.ok(commonLotteryService.queryLotteryPrizeResult(param));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "toUpdateLotteryPrizeInfo", method = RequestMethod.POST)
    @ApiOperation(value = "更新奖品信息", httpMethod = "POST")
    public R toUpdateLotteryPrizeInfo(@RequestBody CommonLotteryPrizeReq param) {
        return R.ok(commonLotteryService.toUpdateLotteryPrizeInfo(param));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "toDeletePoolOrPrize", method = RequestMethod.POST)
    @ApiOperation(value = "删除奖品/奖池", httpMethod = "POST")
    public R toDeletePoolOrPrize(@RequestBody CommonLotteryReq param) {
        return R.ok(commonLotteryService.toDeletePoolOrPrize(param));
    }

    @ResponseBody
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "queryLotteryResult", method = RequestMethod.POST)
    @ApiOperation(value = "查询中奖结果", httpMethod = "POST")
    public R<PageResult<CommonLotteryResultResp>> queryLotteryResult(@RequestBody CommonLotteryResultReq param) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        List<CommonLotteryResultResp> commonLotteryResultResps = commonLotteryService.queryCommonLotteryResultPage(param);
        return getPageData(commonLotteryResultResps, pageDomain);
    }

    @RequestMapping(value = "exportLotteryResultExcel", method = RequestMethod.GET)
    @ResponseBody
    public void exportLotteryResultExcel(HttpServletRequest request, HttpServletResponse response) {
        String lotteryPoolName = convertString(request.getParameter("lotteryPoolName"));
        String lotteryPoolCode = convertString(request.getParameter("lotteryPoolCode"));
        String startDate = convertString(request.getParameter("startDate"));
        String endDate = convertString(request.getParameter("endDate"));

        try {

            CommonLotteryResultReq commonLotteryResultReq = new CommonLotteryResultReq();
            commonLotteryResultReq.setLotteryPoolCode(lotteryPoolCode);
            commonLotteryResultReq.setLotteryPoolName(lotteryPoolName);

            if (StringUtils.isNotEmpty(startDate)) {
                commonLotteryResultReq.setStartDate(startDate);
            }
            if (StringUtils.isNotEmpty(endDate)) {
                commonLotteryResultReq.setEndDate(endDate);
            }
            List<CommonLotteryResultResp> commonLotteryResultResp = commonLotteryService.queryCommonLotteryResultPage(commonLotteryResultReq);
            LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
            fieldMap.put("createDate", "中奖时间");
            fieldMap.put("poolName", "奖池名");
            fieldMap.put("poolCode", "奖池编码");
            fieldMap.put("ffpId", "会员卡号");
            fieldMap.put("prizeName", "奖品名");
            fieldMap.put("prizeContent", "奖品内容");
            fieldMap.put("sendStatus", "发放状态");
            fieldMap.put("failedReason", "失败原因");
            String fileName = "通用抽奖中奖记录";

            ExcelUtil.listToExcel(commonLotteryResultResp, fieldMap, fileName, response);
        } catch (Exception e) {
            log.error("导出符合条件的中奖结果出现异常，异常信息为:{}", e.getMessage());
        }
    }

    private String convertString(String param) {
        return param == null ? "" : param;
    }


}
