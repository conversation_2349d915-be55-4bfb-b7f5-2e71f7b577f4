package com.juneyaoair.ecs.manage.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.datadict.VersionManage;
import com.juneyaoair.ecs.manage.dto.version.request.VersionControlRequest;
import com.juneyaoair.ecs.manage.service.datadict.IVersionMangeService;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName VersionController
 * @Description 版本管理
 * <AUTHOR>
 * @Date 2024/3/15 9:05
 * @Version 1.0
 */

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("version")
@Api(value = "version", tags = "版本控制相关接口")
public class VersionController extends HoBaseController {


    @Resource
    private IVersionMangeService versionMangeService;


    /**
     * @param versionControlRequest
     * @return com.ruoyi.common.core.domain.R<com.juneyaoair.ecs.manage.dto.base.PageResult < com.juneyaoair.ecs.manage.dto.datadict.VersionManage>>
     * <AUTHOR>
     * @Description 查询版本列表
     * @Date 10:31 2024/3/15
     **/

    @PostMapping("toCatchVersionList")
    public R<PageResult<VersionManage>> selectNoticeInfo(@RequestBody @Validated VersionControlRequest versionControlRequest) {
        try {
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(versionControlRequest));
            PageDomain pageDomain = TableSupport.buildPageRequest();
            startPage(pageDomain);
            Page<VersionManage> localPage = PageHelper.getLocalPage();
            VersionManage versionManage = new VersionManage();
            BeanUtils.copyNotNullProperties(versionControlRequest, versionManage);
            List<VersionManage> versionManageList = versionMangeService.getList(versionManage);
            R<PageResult<VersionManage>> pageData = getPageData(versionManageList, localPage);
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(pageData));
            return pageData;
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            Context.setContext(null);
        }
        return R.fail();
    }

    @PostMapping("addVersion")
    public R<Boolean> addVersion(@RequestBody VersionManage versionManage) {
        if (null == versionManage) {
            return R.fail("数据不可为空！");
        }
        return R.ok(versionMangeService.toAddVersion(versionManage));
    }

    @PostMapping("updateVersion")
    public R<Boolean> updateVersion(@RequestBody VersionManage versionManage) {
        if (null == versionManage) {
            return R.fail("数据不可为空！");
        }
        return R.ok(versionMangeService.toUpdateVersion(versionManage));
    }

    @PostMapping("deletedVersion")
    public R<Boolean> deletedVersion(@RequestBody VersionManage versionManage) {
        if (null == versionManage) {
            return R.fail("数据不可为空！");
        }
        return R.ok(versionMangeService.toDeleteVersion(versionManage));
    }

    @PostMapping("toCatchVersionDetail")
    public R<VersionManage> toCatchVersionDetail(@RequestBody VersionManage versionManage) {
        if (null == versionManage) {
            return R.fail("数据不可为空！");
        }
        VersionManage response = null;
        List<VersionManage> versionManageList = versionMangeService.getList(versionManage);
        if (CollectionUtils.isNotEmpty(versionManageList)) {
            response = versionManageList.get(0);
        }
        return R.ok(response);
    }

}
