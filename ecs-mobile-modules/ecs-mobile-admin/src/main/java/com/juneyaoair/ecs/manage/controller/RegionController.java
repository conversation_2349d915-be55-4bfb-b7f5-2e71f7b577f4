package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.region.RegionReqDTO;
import com.juneyaoair.manage.b2c.service.IRegionService;
import com.juneyaoair.manage.b2c.entity.RegionPO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 国家所属洲管理
 */
@Api(value = "region", tags = {"国家所属洲管理"})
@RequestMapping("region")
@RestController
@RequiredArgsConstructor
@Slf4j
public class RegionController extends HoBaseController {
    @Autowired
    private IRegionService IRegionService;

    @ApiOperation(value = "查询洲", notes = "", httpMethod = "POST")
    @PostMapping(value = "searchList")
    public R<List<RegionPO>> searchList(@RequestBody RegionReqDTO regionReqDTO) {
        try {
            List<RegionPO> regionPOS = IRegionService.searchList(regionReqDTO);
            return R.ok(regionPOS);
        } catch (Exception e) {
            log.error("查询洲错误！{}", e.getMessage());
        }
        return R.fail();
    }



    @ApiOperation(value = "分页查询洲", notes = "", httpMethod = "POST")
    @PostMapping(value = "searchForPage")
    public R<PageResult<RegionPO>> searchForPage(@RequestBody RegionReqDTO regionReqDTO) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        return getPageData(IRegionService.searchList(regionReqDTO), pageDomain);
    }


    @ApiOperation(value = "添加洲", notes = "", httpMethod = "POST")
    @PostMapping(value = "add")
    public R add(@RequestBody RegionReqDTO param) {
        try {
            if (IRegionService.add(param) > 0) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }

    @ApiOperation(value = "更新洲", notes = "", httpMethod = "POST")
    @PostMapping(value = "update")
    public R update(@RequestBody RegionReqDTO param) {
        try {
            if (IRegionService.update(param) > 0) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }

    @ApiOperation(value = "根据regionCode删除", notes = "", httpMethod = "GET")
    @GetMapping(value = "delete/{regionCode}")
    public R delete(@PathVariable("regionCode") String regionCode) {
        try {
            if (IRegionService.delete(regionCode) > 0) {
                return R.ok();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.fail();
    }
}
