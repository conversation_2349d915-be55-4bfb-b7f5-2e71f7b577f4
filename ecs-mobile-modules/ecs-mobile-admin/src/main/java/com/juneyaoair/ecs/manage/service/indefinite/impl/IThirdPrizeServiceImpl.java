package com.juneyaoair.ecs.manage.service.indefinite.impl;

import com.juneyaoair.ecs.manage.config.AdminConfig;
import com.juneyaoair.ecs.manage.dto.activity.request.coupon.ThirdPartPrizeRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.commonprizesend.*;
import com.juneyaoair.ecs.manage.dto.config.ThirdPartActivityInfo;
import com.juneyaoair.ecs.manage.dto.config.ThirdPartMerchantInfo;
import com.juneyaoair.ecs.manage.dto.config.ThirdPartPrizeTotalConfig;
import com.juneyaoair.ecs.manage.service.indefinite.IThirdPartPrizeService;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.activity.ThirdPrizeRecordPO;
import com.juneyaoair.manage.b2c.service.IThirdPrizeRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName IndefiniteQualityServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/4/1 20:41
 * @Version 1.0
 */
@Service
@SuppressWarnings("all")
public class IThirdPrizeServiceImpl implements IThirdPartPrizeService {
    @Autowired
    private AdminConfig adminConfig;

    private static final String VALID_STATUS = "Y";//有效状态

    @Resource
    private IThirdPrizeRecordService thirdPrizeRecordService;

    @Override
    public ThirdPartPrizeConfigInformationResponse toCatchThirdPartPrizeConfigInformation() {
        ThirdPartPrizeConfigInformationResponse indefiniteQuantityResponse = new ThirdPartPrizeConfigInformationResponse();
        ThirdPartPrizeTotalConfig thirdPartPrizeSendConfig = adminConfig.toCatchThirdPartPrizeSendConfig();
        if (null == thirdPartPrizeSendConfig) {
            return indefiniteQuantityResponse;
        }
        if (null != thirdPartPrizeSendConfig.getThirdPartActivityInfoConfig() && CollectionUtils.isNotEmpty(thirdPartPrizeSendConfig.getThirdPartActivityInfoConfig().getThirdPartActivityInfoList())) {
            List<ThirdPartActivityInfo> validCommonActivityList = thirdPartPrizeSendConfig.getThirdPartActivityInfoConfig().getThirdPartActivityInfoList().stream().filter(el -> StringUtils.isNotEmpty(el.getActivityStatus()) && VALID_STATUS.equals(el.getActivityStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(validCommonActivityList)) {
                List<ThirdPartActivityInfoRes> indefiniteQuantityActivityInfos = new ArrayList<>();
                ThirdPartActivityInformation indefiniteQuantityActivityInformation = new ThirdPartActivityInformation();
                validCommonActivityList.forEach(elem -> {
                    indefiniteQuantityActivityInfos.add(ThirdPartActivityInfoRes.builder().activityCode(elem.getActivityCode()).activityName(elem.getActivityName()).build());
                });
                indefiniteQuantityActivityInformation.setThirdPartActivityInfoList(indefiniteQuantityActivityInfos);
                indefiniteQuantityResponse.setThirdPartActivityInformation(indefiniteQuantityActivityInformation);
            }
        }
        if (null != thirdPartPrizeSendConfig.getThirdPartMerchantInfoConfig() && CollectionUtils.isNotEmpty(thirdPartPrizeSendConfig.getThirdPartMerchantInfoConfig().getThirdPartMerchantInfoList())) {
            List<ThirdPartMerchantInfo> validCommonMerchantList = thirdPartPrizeSendConfig.getThirdPartMerchantInfoConfig().getThirdPartMerchantInfoList().stream().filter(el -> StringUtils.isNotEmpty(el.getMerchantStatus()) && VALID_STATUS.equals(el.getMerchantStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(validCommonMerchantList)) {
                List<ThirdPartMerchantInfoRes> indefiniteQuantityMerchantInfos = new ArrayList<>();
                ThirdPartMerchantInformation indefiniteQuantityMerchantInformation = new ThirdPartMerchantInformation();
                validCommonMerchantList.forEach(elem -> {
                    indefiniteQuantityMerchantInfos.add(ThirdPartMerchantInfoRes.builder().merchantCode(elem.getMerchantCode()).merchantName(elem.getMerchantName()).build());
                });
                indefiniteQuantityMerchantInformation.setThirdPartMerchantInfoList(indefiniteQuantityMerchantInfos);
                indefiniteQuantityResponse.setThirdPartMerchantInformation(indefiniteQuantityMerchantInformation);
            }
        }
        if (null != thirdPartPrizeSendConfig.getThirdPartPrizeTypeConfig() && CollectionUtils.isNotEmpty(thirdPartPrizeSendConfig.getThirdPartPrizeTypeConfig().getThirdPartPrizeTypeList())) {
            List<ThirdPartPrizeInfoRes> indefiniteQuantityPrizeInfos = new ArrayList<>();
            ThirdPartPrizeInformation indefiniteQuantityPrizeInformation = new ThirdPartPrizeInformation();
            thirdPartPrizeSendConfig.getThirdPartPrizeTypeConfig().getThirdPartPrizeTypeList().forEach(elem -> {
                indefiniteQuantityPrizeInfos.add(ThirdPartPrizeInfoRes.builder().prizeType(elem.getPrizeType()).prizeTypeDesc(elem.getPrizeDesc()).build());
            });
            indefiniteQuantityPrizeInformation.setThirdPartPrizeInfoList(indefiniteQuantityPrizeInfos);
            indefiniteQuantityResponse.setThirdPartPrizeInformation(indefiniteQuantityPrizeInformation);
        }
        return indefiniteQuantityResponse;
    }

    @Override
    public void toBatchPreserveThirdPartPrizes(List<ThirdPrizeRecordPO> thirdPrizeRecordPOList) {
        toCheckForDuplicatePrizeCodes(thirdPrizeRecordPOList);
        thirdPrizeRecordService.saveBatch(thirdPrizeRecordPOList);
    }

    @Override
    public List<ThirdPartPrizeResponse> toCatchThirdPartPrizeList(ThirdPartPrizeRequest thirdCouponRequest) {
        return thirdPrizeRecordService.toCatchThirdPrizeList(thirdCouponRequest);
    }

    /**
     * @param thirdPrizeRecordPOList
     * @return void
     * <AUTHOR>
     * @Description 校验是否存在重复的奖品编码 针对同一活动号下的同一商户号
     * @Date 15:04 2025/4/2
     **/
    public static void toCheckForDuplicatePrizeCodes(List<ThirdPrizeRecordPO> thirdPrizeRecordPOList) {
        Set<String> seenIds = new HashSet<>();
        for (ThirdPrizeRecordPO thirdPrizeRecordPO : thirdPrizeRecordPOList) {
            if (!seenIds.add(thirdPrizeRecordPO.getPrizeCode())) { // 如果 add 返回 false，说明已经存在
                throw new HoServiceException("存在重复的奖品编码: " + thirdPrizeRecordPO.getPrizeCode());
            }
        }
    }
}
