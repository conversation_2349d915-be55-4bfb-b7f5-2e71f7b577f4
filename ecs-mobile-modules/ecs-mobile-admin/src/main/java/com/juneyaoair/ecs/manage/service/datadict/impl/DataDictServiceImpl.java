package com.juneyaoair.ecs.manage.service.datadict.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.juneyaoair.ecs.manage.dto.datadict.DICTType;
import com.juneyaoair.ecs.manage.dto.datadict.VersionManage;
import com.juneyaoair.ecs.manage.dto.notice.DICTValue;
import com.juneyaoair.ecs.manage.service.datadict.IDataDictService;
import com.juneyaoair.ecs.manage.service.datadict.IVersionMangeService;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.StrLengthUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.mapper.DictMapper;
import com.juneyaoair.manage.b2c.mapper.DictTypesMapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

/**
 * @ClassName DataDictServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/23 14:37
 * @Version 1.0
 */

@Service
@RefreshScope
@Slf4j
public class DataDictServiceImpl implements IDataDictService {

    @Resource
    private DictTypesMapper dictTypesMapper;

    @Resource
    private DictMapper dictMapper;

    @Resource
    private IVersionMangeService versionMangeService;

    @Override
    public List<DICTType> toCatchDICTTypeList(DICTType dICTType) {
        /*toCheckAdmin();*/
        return dictTypesMapper.toCatchDictTypeList(dICTType);
    }

    @Override
    public void toAddDICTType(DICTType dICTType) {
        /*toCheckAdmin();*/
        toCheckParams(dICTType,"add");
        dICTType.setDtid(UUID.randomUUID().toString());
        int result = dictTypesMapper.toAddType(dICTType);
        if (result <= 0) {
            throw new HoServiceException("保存出错");
        }
    }

    @Override
    public void toUpdateDICTType(DICTType dICTType) {
        /*toCheckAdmin();*/
        toCheckParams(dICTType,"update");
        int update = dictTypesMapper.toUpdateType(dICTType);
        if (update <= 0) {
            throw new HoServiceException("更新出错");
        }
        if ("N".equals(dICTType.getEnable())) {
            DICTValue dICTValue = new DICTValue();
            dICTValue.setEnable("N");
            dICTValue.setDtid(dICTType.getDtid());
            dictMapper.updateDictValue(dICTValue);
        }
    }

    @Override
    public void toDeleteDICTType(String ids) {
        /*toCheckAdmin();*/
        //根据ids做更新操作，不真正删除，把数据类型设置为无效，对应的数据项设置无效
        if (StringUtils.isEmpty(ids)) {
            throw new HoServiceException("id不能为空");
        }
        String[] idArr = ids.split(",");
        DICTType dICTType = new DICTType();
        DICTValue dICTValue = new DICTValue();
        for (String id : idArr) {
            if (StringUtils.isNotEmpty(id)) {
                dICTType.setEnable("N");
                dICTType.setDtid(id);
                dictTypesMapper.toUpdateType(dICTType);

                dICTValue.setEnable("N");
                dICTValue.setDtid(id);
                dictMapper.updateDictValue(dICTValue);
            }
        }

    }

    @Override
    public List<DICTValue> toCatchDICTValueList(DICTValue value) {
        DICTValue dictValue = new DICTValue();
        dictValue.setDtCode(value.getDtCode());
        dictValue.setDtid(value.getDtid());
        dictValue.setDvName(value.getDvName());
        return dictMapper.findDICTValue(dictValue);
    }

    @Override
    public void toAddDICTValue(DICTValue dictValue) {
        /*toCheckAdmin();*/
        toCheckValueParams(dictValue);

        //编号不能重复
        DICTValue dICT = new DICTValue();
        dICT.setDvCode(dictValue.getDvCode());
        dICT.setDtid(dictValue.getDtid());
        List<DICTValue> dictValueList = dictMapper.findDICTValueList(dICT);
        if (CollectionUtils.isNotEmpty(dictValueList)) {
            throw new HoServiceException("编号不能重复");
        }
        dictValue.setDvid(UUID.randomUUID().toString());
        int result = dictMapper.addDictValue(dictValue);
        if (result <= 0) {
            throw new HoServiceException("新增失败");
        }
    }

    private void toCheckValueParams(DICTValue dictValue) {
        if (StringUtils.isBlank(dictValue.getDvCode())) {
            throw new HoServiceException("编号不能为空");
        }
        if (StringUtils.isBlank(dictValue.getDvDescription())) {
            throw new HoServiceException("描述不能为空");
        }
        if (StringUtils.isBlank(dictValue.getDvName())) {
            throw new HoServiceException("名称不能为空");
        }
        if (StrLengthUtil.StrLengthValidate(dictValue.getDvCode(), 30)) {
            throw new HoServiceException("编号长度超出15个字");// 将异常抛出
        }
        if (StrLengthUtil.StrLengthValidate(dictValue.getDvDescription(), 200)) {
            throw new HoServiceException("描述长度超出100个字");// 将异常抛出
        }
        if ("Y".equals(dictValue.getEnable())) {
            DICTType dictType = new DICTType();
            dictType.setDtid(dictValue.getDtid());
            dictType.setEnable("Y");
            dictTypesMapper.toUpdateType(dictType);
        }
    }

    @Override
    public void toUpdateDICTValue(DICTValue dictValue) {
        /*toCheckAdmin();*/
        toCheckValueParams(dictValue);
        DICTValue dICT = new DICTValue();
        dICT.setDvCode(dictValue.getDvCode());
        dICT.setDtid(dictValue.getDtid());
        List<DICTValue> dictValueList = dictMapper.findDICTValueList(dICT);

        if (dictValueList != null && dictValueList.size() > 0) {
            if (!dictValueList.get(0).getDvid().equals(dictValue.getDvid())) {
                throw new HoServiceException("修改出错！编号不能重复");
            }
        }

        int result = dictMapper.updateDictValue(dictValue);
        if (result <= 0) {
            throw new HoServiceException("更新DICTValue版本出错");
        }
        updDICTValueVersion(dictValue);


    }

    @Override
    public void toDeleteDICTValue(String ids) {
        /*toCheckAdmin();*/
        //根据ids做更新操作，不真正删除数据，把数据项设置为无效
        if (StringUtils.isEmpty(ids)) {
            throw new HoServiceException("参数不可为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            if (id != null && !id.equals("")) {
                DICTValue dICTValue = new DICTValue();
                dICTValue.setEnable("N");
                dICTValue.setDvid(id);
                try {
                    dictMapper.updateDictValue(dICTValue);
                } catch (Exception e) {
                    throw new HoServiceException("删除出错");
                }
            }
        }


    }

    /**
     * @param dValue
     * @return void
     * <AUTHOR>
     * @Description 更新DICTValue的版本信息
     * @Date 8:42 2024/1/24
     **/
    private void updDICTValueVersion(DICTValue dValue) {
        VersionManage versionManage = new VersionManage();
        versionManage.setVersionType(dValue.getDvCode());
        List<VersionManage> versionList = versionMangeService.getList(versionManage);
        if (CollectionUtils.isNotEmpty(versionList)) {
            VersionManage curVersion = versionList.get(0);
            curVersion.setVersionNo(DateUtil.getCurrentTimeStr());
            int result = versionMangeService.updateVersion(curVersion);
            if (result <= 0) {
                throw new HoServiceException("版本更新出错");
            }
        }
    }

    /**
     * @param dICTType
     * @return void
     * <AUTHOR>
     * @Description 校验数据格式
     * @Date 15:58 2024/1/23
     **/

    private void toCheckParams(DICTType dICTType,String opType) {
        if (StringUtils.isEmpty(dICTType.getDtCode())) {
            throw new HoServiceException("编号不能为空");
        }
        if (StringUtils.isEmpty(dICTType.getDtDescription())) {
            throw new HoServiceException("描述不能为空");
        }
        if (StringUtils.isEmpty(dICTType.getDtName())) {
            throw new HoServiceException("名称不能为空");
        }
        if (StrLengthUtil.StrLengthValidate(dICTType.getDtCode(), 30)) {
            throw new HoServiceException("编号长度超出15个字");
        }
        if (StrLengthUtil.StrLengthValidate(dICTType.getDtDescription(), 30)) {
            throw new HoServiceException("描述长度超出100个字");
        }
        if (StrLengthUtil.StrLengthValidate(dICTType.getDtName(), 60)) {
            throw new HoServiceException("名称长度超出30个字");
        }
        DICTType dICT = new DICTType();
        dICT.setDtCode(dICTType.getDtCode());
        List<DICTType> catchDictTypeList = dictTypesMapper.toCatchDictTypeList(dICT);
        if ("add".equals(opType)) {
            if (CollectionUtils.isNotEmpty(catchDictTypeList)) {
                throw new HoServiceException("编号不能重复");
            }
        } else if ("update".equals(opType)){
            if (CollectionUtils.isNotEmpty(catchDictTypeList) && !catchDictTypeList.get(0).getDtid().equals(dICTType.getDtid())) {
                throw new HoServiceException("编号不能重复");
            }
        }

    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 校验是否为管理员角色
     * @Date 15:43 2024/1/23
     **/
    /*private void toCheckAdmin() {
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            throw new HoServiceException("权限不足，需要超级管理员权限");
        }
    }*/
}
