package com.juneyaoair.ecs.manage.service.airline.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.juneyaoair.ecs.manage.dto.airline.routelabel.*;
import com.juneyaoair.ecs.manage.dto.airline.routelabel.bo.RouteLabelBO;
import com.juneyaoair.ecs.manage.mapstruct.RouteLabelMapStruct;
import com.juneyaoair.ecs.manage.service.airline.IRouteLabelService;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.RouteLabelChannelPO;
import com.juneyaoair.manage.b2c.entity.RouteLabelPO;
import com.juneyaoair.manage.b2c.entity.RouteLabelRulePO;
import com.juneyaoair.manage.b2c.mapper.RouteLabelChannelPOMapper;
import com.juneyaoair.manage.b2c.mapper.RouteLabelPOMapper;
import com.juneyaoair.manage.b2c.mapper.RouteLabelRulePOMapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RouteLabelServiceImpl implements IRouteLabelService {


    @Resource
    private RouteLabelPOMapper labelPoMapper;

    @Resource
    private RouteLabelRulePOMapper rulePoMapper;

    @Resource
    private RouteLabelChannelPOMapper channelPoMapper;

    @Override
    public List<RouteLabelDTO> list(ListRouteLabelRequestDTO request) {
        List<RouteLabelBO> boList = labelPoMapper.selectListBO(request);

        // RouteLabelBO convert to RouteLabelDTO
        List<RouteLabelDTO> routeLabelDTOS = boList.stream().map(RouteLabelMapStruct.INSTANCE::dtoToRouteLabelDTO)
                // sort
                .sorted(Comparator.comparingInt(RouteLabelDTO::getSortNum)).collect(Collectors.toList());


        // sort ruleDTOList
        routeLabelDTOS.forEach(routeLabelDTO -> {
            List<RouteLabelDTO.RouteLabelRuleDTO> ruleDTOS = routeLabelDTO.getRuleDTOList().stream()
                    .sorted(Comparator.comparingInt(RouteLabelDTO.RouteLabelRuleDTO::getSortNum))
                    .collect(Collectors.toList());
            routeLabelDTO.setRuleDTOList(ruleDTOS);
        });

        return routeLabelDTOS;
    }

    @Override
    @DSTransactional
    public void addRouteLabel(AddOrUpdateRouteLabelRequestDTO dto) {

        //
        Date date = new Date();
        String user = SecurityUtils.getUsername();

        //
        String routeLabelId = IdUtil.randomUUID();
        RouteLabelPO po = RouteLabelMapStruct.INSTANCE.requestDtoToRouteLabelPO(dto);


        po.setId(routeLabelId);
        po.setCreatedBy(user);
        po.setUpdatedBy(user);
        po.setCreatedTime(date);
        po.setUpdatedTime(date);
        labelPoMapper.insert(po);


        dto.getChannelList().forEach(routeLabelChannelDTO -> {
            String channelId = IdUtil.randomUUID();
            RouteLabelChannelPO channelPo = RouteLabelMapStruct.INSTANCE.requestDtoToChannelPO(routeLabelChannelDTO);
            channelPo.setId(channelId);
            channelPo.setLabelId(routeLabelId);
            channelPo.setCreatedBy(user);
            channelPo.setUpdatedBy(user);
            channelPo.setCreatedTime(date);
            channelPo.setUpdatedTime(date);
            channelPoMapper.insert(channelPo);
        });
    }

    @Override
    @DSTransactional
    public void updateRouteLabel(AddOrUpdateRouteLabelRequestDTO dto) {
        RouteLabelPO po = labelPoMapper.selectById(dto.getId());
        if (po == null) {
            throw new HoServiceException("航线标签不存在");
        }

        //
        RouteLabelPO updatePo = RouteLabelMapStruct.INSTANCE.requestDtoToRouteLabelPO(dto);
        //
        String updateUser = SecurityUtils.getUsername();

        updatePo.setCreatedBy(po.getCreatedBy());
        updatePo.setUpdatedBy(updateUser);
        updatePo.setCreatedTime(po.getCreatedTime());
        updatePo.setUpdatedTime(new Date());
        labelPoMapper.updateById(updatePo);

        List<RouteLabelChannelPO> channelPoS = channelPoMapper.selectByLabelId(po.getId());
        channelPoS.forEach(channelPo -> channelPoMapper.deleteById(channelPo.getId()));

        dto.getChannelList().forEach(routeLabelChannelDTO -> {
            String channelId = IdUtil.randomUUID();
            RouteLabelChannelPO channelPo = RouteLabelMapStruct.INSTANCE.requestDtoToChannelPO(routeLabelChannelDTO);
            channelPo.setId(channelId);
            channelPo.setLabelId(po.getId());
            channelPo.setCreatedBy(updateUser);
            channelPo.setUpdatedBy(updateUser);
            channelPo.setCreatedTime(new Date());
            channelPo.setUpdatedTime(new Date());
            channelPoMapper.insert(channelPo);
        });

    }

    @Override
    @DSTransactional
    public void deleteRouteLabel(RouteLabelIdRequestDTO dto) {
        RouteLabelPO po = labelPoMapper.selectById(dto.getId());

        if (po != null) {
            labelPoMapper.deleteById(dto.getId());
            //  关联删除rule
            List<RouteLabelRulePO> rulePoS = rulePoMapper.selectByLabelId(po.getId());
            rulePoS.forEach(rulePo -> rulePoMapper.deleteById(rulePo.getId()));

            //  关联删除channel
            List<RouteLabelChannelPO> channelPoS = channelPoMapper.selectByLabelId(po.getId());
            channelPoS.forEach(channelPo -> channelPoMapper.deleteById(channelPo.getId()));
        }
    }

    @Override
    public void publishRouteLabel(RouteLabelIdRequestDTO dto) {
        RouteLabelPO po = labelPoMapper.selectById(dto.getId());
        po.setEnableStatus("1".equals(po.getEnableStatus()) ? "0" : "1");
        labelPoMapper.updateById(po);
    }

    @Override
    public void addRule(AddOrUpdateRuleRequestDTO dto) {
        String ruleId = IdUtil.randomUUID();

        RouteLabelPO po = labelPoMapper.selectById(dto.getLabelId());
        if (po == null) {
            throw new HoServiceException("航线标签不存在");
        }

        // 处理中转航线的适用机型逻辑
        processTransitAircraftType(dto);

        //
        RouteLabelRulePO rulePo = RouteLabelMapStruct.INSTANCE.requestDtoToRulePO(dto);
        //
        rulePo.setId(ruleId);
        rulePo.setCreatedBy(SecurityUtils.getUsername());
        rulePo.setUpdatedBy(SecurityUtils.getUsername());
        rulePo.setCreatedTime(po.getCreatedTime());
        rulePo.setUpdatedTime(new Date());
        rulePoMapper.insert(rulePo);

    }

    @Override
    public void updateRule(AddOrUpdateRuleRequestDTO dto) {
        RouteLabelRulePO po = rulePoMapper.selectById(dto.getRuleId());
        if (po == null) {
            throw new HoServiceException("航线标签规则不存在");
        }

        // 处理中转航线的适用机型逻辑
        processTransitAircraftType(dto);

        //
        RouteLabelRulePO updatePo = RouteLabelMapStruct.INSTANCE.requestDtoToRulePO(dto);
        //
        updatePo.setId(po.getId());
        updatePo.setLabelId(po.getLabelId());
        //
        updatePo.setCreatedBy(po.getCreatedBy());
        updatePo.setUpdatedBy(SecurityUtils.getUsername());
        updatePo.setCreatedTime(po.getCreatedTime());
        updatePo.setUpdatedTime(new Date());
        rulePoMapper.updateById(updatePo);
    }

    @Override
    public void deleteRule(RouteLabelIdRequestDTO dto) {
        RouteLabelRulePO po = rulePoMapper.selectById(dto.getId());
        if (po != null) {
            rulePoMapper.deleteById(dto.getId());
        }
    }

    @Override
    public void publishRule(RouteLabelIdRequestDTO dto) {
        RouteLabelRulePO po = rulePoMapper.selectById(dto.getId());
        po.setEnableStatus("1".equals(po.getEnableStatus()) ? "0" : "1");
        rulePoMapper.updateById(po);
    }

    /**
     * 处理中转航线的适用机型逻辑
     * 如果是否中转选项为是，则适用机型内的配置不生效，同时清空其中配置
     */
    private void processTransitAircraftType(AddOrUpdateRuleRequestDTO dto) {
        if ("1".equals(dto.getTransit())) {
            // 如果是中转航线，清空适用机型配置
            dto.setApplyAircraftType("");
        }
    }
}
