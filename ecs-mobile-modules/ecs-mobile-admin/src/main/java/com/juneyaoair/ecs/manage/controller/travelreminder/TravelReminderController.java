package com.juneyaoair.ecs.manage.controller.travelreminder;

import com.juneyaoair.ecs.manage.dto.travelreminder.TravelReminderQueryDTO;
import com.juneyaoair.ecs.manage.dto.travelreminder.TravelReminderSaveDTO;
import com.juneyaoair.ecs.manage.dto.travelreminder.TravelReminderListDTO;
import com.juneyaoair.ecs.manage.dto.travelreminder.TravelReminderDeleteDTO;
import com.juneyaoair.ecs.manage.service.travelreminder.TravelReminderService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.controller.HoBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "出行提醒管理")
@RestController
@RequestMapping("/travelReminder")
@Slf4j
public class TravelReminderController extends HoBaseController {

    @Resource
    private  TravelReminderService travelReminderService;

    @ApiOperation("查询出行提醒列表")
    @PostMapping("/list")
    public R<PageResult<TravelReminderListDTO>> queryList(@RequestBody TravelReminderQueryDTO queryDTO) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        List<TravelReminderListDTO> list = travelReminderService.queryList(queryDTO);
        return getPageData(list, pageDomain);
    }

    @ApiOperation("删除出行提醒")
    @PostMapping("/delete")
    public R<Void> delete(@RequestBody TravelReminderDeleteDTO deleteDTO) {
        travelReminderService.delete(deleteDTO.getId());
        return R.ok();
    }

    @ApiOperation("保存或提交出行提醒")
    @PostMapping("/save")
    public R<Void> saveOrSubmit(@RequestBody TravelReminderSaveDTO saveDTO) {
        travelReminderService.saveOrSubmit(saveDTO);
        return R.ok();
    }
} 