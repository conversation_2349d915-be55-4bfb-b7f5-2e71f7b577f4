package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.flight.request.FightRequestDto;
import com.juneyaoair.manage.flight.entity.Flightinfo;
import com.juneyaoair.manage.flight.service.IFlightinfoService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 *
 * <AUTHOR>
 * @date 2023/4/20  9:37
 * 航班基础信息管理 包含航线，航班等基础信息管理
 */
@RequestMapping("/flightbase")
@RestController
@RequiredArgsConstructor
@Api(value = "FlightBaseController", tags = "航班基础信息管理")
public class FlightBaseController extends HoBaseController {


    private final IFlightinfoService iFlightinfoService;


    @ApiOperation(value = "航班分页查询列表",notes = "")
    @PostMapping("/pageList")
    public R<PageResult<Flightinfo>> pageList(@RequestBody @Validated FightRequestDto fightRequestDto)
    {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        List<Flightinfo> list =iFlightinfoService.selectFlightList(fightRequestDto);
        return getPageData(list,pageDomain);

    }


    @ApiOperation(value = "添加航班信息",notes = "")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody @Validated Flightinfo flightinfo)
    {
        return toAjax(iFlightinfoService.save(flightinfo));
    }


    @ApiOperation(value = "更新航班信息",notes = "")
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody @Validated Flightinfo flightinfo)
    {
        return toAjax(iFlightinfoService.updateById(flightinfo));
    }

}
