package com.juneyaoair.ecs.manage.external.flightbaisc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2020-3-12 13:17
 * @description： 航线航距信息
 * @modified By：
 * @version: $
 */
@Data
public class AirlineMileageInfo {

    @ApiModelProperty("出发城市")
    private String depCity;

    @ApiModelProperty("到达城市")
    private String arrCity;

    @ApiModelProperty("航距")
    private String mileage;

    @ApiModelProperty("出发机场")
    private String depAirport;

    @ApiModelProperty("出发机场")
    private String arrAirport;

    @ApiModelProperty("是否国际航线")
    private String isInternationalAirline;

}
