package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.activity.common.HolidayCalendar;
import com.juneyaoair.ecs.manage.dto.activity.response.HolidayCalenderResponse;
import com.juneyaoair.ecs.manage.service.holiday.IHolidayCalenderService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @ClassName HolidayManagerController
 * @Description
 * <AUTHOR>
 * @Date 2024/1/22 13:14
 * @Version 1.0
 */


@RequestMapping("holidayManager")
@RestController
@RequiredArgsConstructor
@Api(value = "HolidayManagerController", tags = "假日管理相关API")
@Slf4j
public class HolidayManagerController extends HoBaseController{

    @Autowired
    private IHolidayCalenderService holidayService;

    @RequestMapping(value = "getHolidayList",method = RequestMethod.GET)
    @ApiOperation(value = "假日列表查询", notes = "", httpMethod = "GET")
    public R<List<HolidayCalendar>> getHolidayList(HttpServletRequest request) {
        return R.ok(holidayService.toCatchHolidayList(request));
    }

    @RequestMapping(value = "optHoliday",method = RequestMethod.POST)
    @ApiOperation(value = "节假日操作", notes = "", httpMethod = "POST")
    public R<HolidayCalenderResponse> optHoliday(@RequestBody HolidayCalendar holidayCalendar, HttpServletRequest request) {
        holidayService.optHoliday(holidayCalendar,request);
        return R.ok();
    }

    @RequestMapping(value = "toClearCache",method = RequestMethod.GET)
    @ApiOperation(value = "删除节假日缓存", notes = "", httpMethod = "GET")
    @SuppressWarnings("rawtypes")
    public R toClearCache() {
        holidayService.toClearCache();
        return R.ok();
    }

}
