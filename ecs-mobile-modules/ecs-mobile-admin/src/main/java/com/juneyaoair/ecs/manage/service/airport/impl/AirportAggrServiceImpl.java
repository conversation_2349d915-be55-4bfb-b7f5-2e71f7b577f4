package com.juneyaoair.ecs.manage.service.airport.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.juneyaoair.ecs.manage.dto.airport.AirportInfoExportDTO;
import com.juneyaoair.ecs.manage.dto.airport.ParamAirportPage;
import com.juneyaoair.ecs.manage.service.airport.IAirPortAggrService;
import com.juneyaoair.ecs.manage.service.syncfile.IAirportSyncJsFileService;
import com.juneyaoair.ecs.manage.service.syncfile.IAirportSyncStaticFileService;
import com.juneyaoair.manage.b2c.entity.AirportInfoPO;
import com.juneyaoair.manage.b2c.service.IAirportService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AirportAggrServiceImpl implements IAirPortAggrService {
    @Resource
    private IAirportService airportService;
    @Resource
    IAirportSyncJsFileService airportSyncJsFileService;
    @Resource
    IAirportSyncStaticFileService airportSyncStaticFileService;

    @Override
    public List<AirportInfoExportDTO> exportList(ParamAirportPage param) {
        LambdaQueryWrapper<AirportInfoPO> queryChainWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getAirportCode())) {
            queryChainWrapper.eq(AirportInfoPO::getAirportCode, param.getAirportCode());
        }
        if (StringUtils.isNotBlank(param.getCityCode())) {
            queryChainWrapper.eq(AirportInfoPO::getCityCode, param.getCityCode());
        }
        if (StringUtils.isNotBlank(param.getAirportName())) {
            queryChainWrapper.like(AirportInfoPO::getAirportName, param.getAirportName());
        }
        List<AirportInfoPO> records = airportService.list(queryChainWrapper);
        if (CollectionUtil.isEmpty(records)) {
            return null;
        }
        return records.stream().map(i -> {
            AirportInfoExportDTO exportDTO = new AirportInfoExportDTO();
            exportDTO.setStatus("1".equals(i.getStatus()) ? "启用" : "禁用");
            BeanUtil.copyProperties(i, exportDTO);
            return exportDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public boolean syncJs() {
        airportSyncJsFileService.process();
        return true;
    }

    @Override
    public boolean syncJsonAndJs() {
        return airportSyncStaticFileService.process();
    }

}
