package com.juneyaoair.ecs.utils;

import com.juneyaoair.esc.manage.exception.HoServiceException;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;

/**
 * @Author: caolei
 * @Description: ValidatorUtils
 * @Date: 2023/02/21 15:35
 * @Modified by:
 */
public class ValidatorUtils {

    private static final Validator validator;

    static {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    public static <T> Set<ConstraintViolation<T>> validate(T obj){
        return validator.validate(obj);
    }

    /**
     * 参数校验
     * @param obj
     * @param <T>
     */
    public static <T> void valid(T obj){
        Set<ConstraintViolation<T>> violations = validator.validate(obj);
        if (!CollectionUtils.isEmpty(violations)) {
            throw new HoServiceException(violations.iterator().next().getMessage());
        }
    }
}
