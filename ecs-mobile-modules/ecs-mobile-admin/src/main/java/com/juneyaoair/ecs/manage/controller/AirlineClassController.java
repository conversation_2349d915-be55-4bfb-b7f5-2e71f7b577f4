package com.juneyaoair.ecs.manage.controller;

import com.juneyaoair.ecs.manage.dto.airline.airlineclass.AddOrUpDateAirlineClassDTO;
import com.juneyaoair.ecs.manage.dto.airline.airlineclass.AirlineClassDTO;
import com.juneyaoair.ecs.manage.dto.airline.airlineclass.AirlineClassIdRequestDTO;
import com.juneyaoair.ecs.manage.dto.airline.airlineclass.QueryAirlineClassRequestDTO;
import com.juneyaoair.ecs.manage.service.airline.IAirlineClassService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("airlineClass")
@RestController
@RequiredArgsConstructor
@Api(value = "AirlineClassController", tags = "航空公司舱位")
@Slf4j
public class AirlineClassController extends HoBaseController {

    @Resource
    private IAirlineClassService airlineClassService;

    @ApiOperation(value = "查询航空公司舱位", notes = "可根据航司代码进行查询", httpMethod = "POST")
    @PostMapping(value = "list")
    public R<List<AirlineClassDTO>> list(@RequestBody QueryAirlineClassRequestDTO request) {
        List<AirlineClassDTO> list = airlineClassService.list(request);
        return R.ok(list);
    }

    @ApiOperation(value = "新增航空公司舱位", notes = "", httpMethod = "POST")
    @PostMapping(value = "add")
    public R add(@RequestBody AddOrUpDateAirlineClassDTO request) {
        airlineClassService.add(request);
        return R.ok();
    }

    @ApiOperation(value = "编辑航空公司舱位", notes = "", httpMethod = "POST")
    @PostMapping(value = "update")
    public R update(@RequestBody AddOrUpDateAirlineClassDTO request) {
        airlineClassService.update(request);
        return R.ok();
    }

    @ApiOperation(value = "删除航空公司舱位", notes = "", httpMethod = "POST")
    @PostMapping(value = "delete")
    public R delete(@RequestBody AirlineClassIdRequestDTO request) {
        airlineClassService.delete(request);
        return R.ok();
    }


    @ApiOperation(value = "查询航空公司舱位操作日志", notes = "", httpMethod = "POST")
    @PostMapping(value = "logList")
    public R logList(@RequestBody AirlineClassIdRequestDTO request) {
        return R.ok(airlineClassService.logList(request));
    }
}
