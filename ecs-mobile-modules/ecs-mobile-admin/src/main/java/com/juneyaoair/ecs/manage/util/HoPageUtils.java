package com.juneyaoair.ecs.manage.util;

import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.dto.base.BasePageDto;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.sql.SqlUtil;
import com.ruoyi.common.core.web.page.PageDomain;

/**
 * <AUTHOR>
 * @description 分页工具类
 * @date 2023/5/4 16:58
 */
public class HoPageUtils extends PageUtils {
    public static void startPageBy(PageDomain pageDomain) {
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        Boolean reasonable = pageDomain.getReasonable();
        PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
    }

    public static PageDomain getPageDomain(BasePageDto basePageDto) {
        PageDomain pageDomain = new PageDomain();
        pageDomain.setPageNum(basePageDto.getPageNum()==null?1:basePageDto.getPageNum());
        pageDomain.setPageSize(basePageDto.getPageSize()==null?10:basePageDto.getPageSize());
        pageDomain.setOrderByColumn(basePageDto.getOrderByColumn());
        pageDomain.setIsAsc(basePageDto.getAsc());
        pageDomain.setReasonable(basePageDto.getReasonable()==null?true:basePageDto.getReasonable());
        return pageDomain;
    }
}
