package com.juneyaoair.ecs.manage.service.resource.impl;

import com.github.pagehelper.Page;
import com.juneyaoair.ecs.manage.dto.activity.common.ResourceManaDTO;
import com.juneyaoair.ecs.manage.dto.activity.common.ResourcePageReq;
import com.juneyaoair.ecs.manage.dto.base.PageDataResponse;
import com.juneyaoair.ecs.manage.dto.file.HoFile;
import com.juneyaoair.ecs.manage.service.file.IFileUploadService;
import com.juneyaoair.ecs.manage.service.resource.ResourceManagerService;
import com.juneyaoair.ecs.manage.util.HoPageUtils;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.ResourcePO;
import com.juneyaoair.manage.b2c.mapper.ResourceManagerMapper;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ResourceManagerServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/7/23 10:04
 * @Version 1.0
 */
@Service
@Slf4j
public class ResourceManagerServiceImpl implements ResourceManagerService {

    @Autowired
    ResourceManagerMapper resourceManagerMapper;

    @Autowired
    private IFileUploadService iFileService;

    public static final String uploadResourceFileModuleName = "resourceManage";
    public static final String defaultDeleteFlag = "N";


    /**
     * 分页查询
     * @param resourcePageReq
     * @return
     */
    @Override
    public PageDataResponse resourceListPageQuery(ResourcePageReq resourcePageReq) {

        PageDataResponse pageDataResponse = new PageDataResponse();
        PageDomain pageDomain = new PageDomain();

        try{
            //设置分页
            pageDomain.setPageNum(resourcePageReq.getPageNum());
            pageDomain.setPageSize(resourcePageReq.getPageSize());
            HoPageUtils.startPageBy(pageDomain);

            Page<ResourcePO> page = resourceManagerMapper.resourceListPageQuery(resourcePageReq);

            long total = page.getTotal();
            List<ResourcePO> result = page.getResult();
            pageDataResponse.setTotal(total);
            pageDataResponse.setData(result);
            return pageDataResponse;
        }catch (Exception e){
            log.error("查询资源列表发生错误:{}", e.getMessage());
            throw new HoServiceException("查询资源列表发生错误",e.getMessage());
        }
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @Override
    public ResourcePO getResourceById(String id) {
        if(StringUtils.isEmpty(id)) {
            throw new HoServiceException("缺少指定的目标ID");
        }
        try{
            ResourcePO resourcePO = resourceManagerMapper.getResourceById(id);
            return resourcePO;
        }catch (Exception e){
            log.error("查询资源信息发生错误:{}", e.getMessage());
            throw new HoServiceException("查询资源信息发生错误",e.getMessage());
        }
    }

    /**
     * 根据id修改资源信息
     * @param resourceManaDTO
     */
    @Override
    public void updateResourceById(ResourceManaDTO resourceManaDTO) {
        if(StringUtils.isEmpty(resourceManaDTO.getRecordID())) {
            throw new HoServiceException("缺少指定的目标ID");
        }

        ResourcePO resourcePO = new ResourcePO();
        BeanUtils.copyProperties(resourceManaDTO,resourcePO);

        resourcePO.setUpdateer(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(resourcePO.getUpdateer())) {
            throw new HoServiceException("更新人不可为空");
        }
        resourcePO.setUpdateDate(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(resourcePO.getUpdateDate().toString())) {
            throw new HoServiceException("更新时间不可为空");
        }

        updateMapper(resourcePO);
    }

    /**
     * 根据id修改资源状态
     * @param flag
     * @param id
     */
    @Override
    public void updateFlagById(String flag, String id) {
        if(StringUtils.isEmpty(id)) {
            throw new HoServiceException("缺少指定的目标ID");
        }
        if(StringUtils.isEmpty(flag)) {
            throw new HoServiceException("缺少指定的目标状态");
        }

        ResourcePO resourcePO = new ResourcePO();
        resourcePO.setRecordID(id);
        resourcePO.setDeleteFlag(flag);

        resourcePO.setUpdateer(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(resourcePO.getUpdateer())) {
            throw new HoServiceException("更新人不可为空");
        }
        resourcePO.setUpdateDate(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(resourcePO.getUpdateDate().toString())) {
            throw new HoServiceException("更新时间不可为空");
        }

        updateMapper(resourcePO);
    }

    /**
     * 新增资源
     * @param platform
     * @param moduleName
     * @param minVer
     * @param maxVer
     * @param file
     * @return
     * @throws IOException
     * @throws NoSuchAlgorithmException
     */
    @Override
    public ResourcePO addResource(String platform,String moduleName,String minVer,String maxVer, MultipartFile file) throws IOException, NoSuchAlgorithmException {

        HoFile hoFile = new HoFile();
        try{
            hoFile = iFileService.uploadFile(file, uploadResourceFileModuleName, null, true);
        }catch (Exception e){
            throw new HoServiceException("文件上传失败");
        }

        String fileId = hoFile.getFileId();
        String url = hoFile.getUrl();
        String recordID = HOStringUtil.newGUID();

        ResourcePO resourcePO = new ResourcePO();

        //计算MD5
        String zipMD5 = calculateZipMD5(file);

        resourcePO.setPlatform(platform);
        resourcePO.setModuleName(moduleName);
        resourcePO.setMinVer(minVer);
        resourcePO.setMaxVer(maxVer);
        resourcePO.setFileIDS(fileId);
        resourcePO.setURL(url);
        resourcePO.setZipMD5(zipMD5);
        resourcePO.setRecordID(recordID);
        resourcePO.setDeleteFlag(defaultDeleteFlag);

        resourcePO.setCreater(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(resourcePO.getCreater())) {
            throw new HoServiceException("创建人不可为空");
        }
        resourcePO.setCreateDate(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(resourcePO.getCreateDate().toString())) {
            throw new HoServiceException("创建时间不可为空");
        }
        resourcePO.setUpdateer(SecurityUtils.getUsername());
        if(StringUtils.isEmpty(resourcePO.getUpdateer())) {
            throw new HoServiceException("更新人不可为空");
        }
        resourcePO.setUpdateDate(DateUtil.StringToDate(DateUtil.getDateStringAllDate(new Date()), DateUtil.DATE_FORMATE_YYYY_MM_DD_HH_MM_SS));
        if(StringUtils.isEmpty(resourcePO.getUpdateDate().toString())) {
            throw new HoServiceException("更新时间不可为空");
        }

        int effectLine = resourceManagerMapper.addResource(resourcePO);
        if(effectLine>0){
            return resourcePO;
        }else {
            throw new HoServiceException("新增资源失败");
        }
    }

    /**
     * 修改资源
     * @param resourcePO
     */
    private void updateMapper(ResourcePO resourcePO) {
        try{
            int effectLine =  resourceManagerMapper.updateResourceById(resourcePO);
            if(effectLine>0) {
                return;
            }else if(effectLine==0){
                throw new HoServiceException("资源不存在，请检查id是否正确");
            } else {
                throw new HoServiceException("更新资源信息失败");
            }
        }catch (Exception e){
            log.error("更新失败:{}", e.getMessage());
            throw new HoServiceException("更新资源信息失败",e.getMessage());
        }
    }

    /**
     * 文件MD5计算
     * @param file
     * @return
     * @throws IOException
     * @throws NoSuchAlgorithmException
     */
    public String calculateZipMD5(MultipartFile file) throws IOException, NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");

        // 使用文件流计算MD5
        byte[] buffer = new byte[8192];
        int bytesRead;
        try (InputStream is = file.getInputStream()) {
            while ((bytesRead = is.read(buffer)) != -1) {
                md.update(buffer, 0, bytesRead);
            }
        }

        // 转换为十六进制字符串
        byte[] digest = md.digest();
        StringBuilder hexString = new StringBuilder();
        for (byte b : digest) {
            hexString.append(String.format("%02X", b));
        }
        return hexString.toString();
    }

}
