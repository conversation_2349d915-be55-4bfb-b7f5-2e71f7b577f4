package com.juneyaoair.ecs.manage.service.activity.impl;

import com.juneyaoair.ecs.manage.dto.activity.common.CityLabelInfo;
import com.juneyaoair.ecs.manage.dto.activity.common.CityLabelInfoPOO;
import com.juneyaoair.ecs.manage.dto.activity.common.DstWtInfo;
import com.juneyaoair.ecs.manage.dto.activity.common.FileInfo;
import com.juneyaoair.ecs.manage.dto.activity.request.CityInfo;
import com.juneyaoair.ecs.manage.service.activity.ICityInfoServices;
import com.juneyaoair.ecs.utils.BeanUtils;
import com.juneyaoair.manage.b2c.entity.CityInfoPO;
import com.juneyaoair.manage.b2c.entity.DstWtInfoPO;
import com.juneyaoair.manage.b2c.entity.FileInfoPOO;
import com.juneyaoair.manage.b2c.mapper.TCityInfoMapper;
import com.juneyaoair.manage.b2c.mapper.TCityLabelInfoMapper;
import com.juneyaoair.manage.b2c.mapper.TDstWtInfoMapper;
import com.juneyaoair.manage.b2c.mapper.TpFileInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName CityInfoServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/12/28 10:31
 * @Version 1.0
 */
@Service
@RefreshScope
@Slf4j
public class CityInfoServiceImpli implements ICityInfoServices {

    @Resource
    private TCityInfoMapper cityInfoMapper;

    @Resource
    TCityLabelInfoMapper tCityLabelInfoMapper;

    @Resource
    TpFileInfoMapper tpFileInfoMapper;

    @Resource
    TDstWtInfoMapper tDstWtInfoMapper;

    @Override
    public List<CityInfo> selectCityList(CityInfo cityInfo, HttpServletRequest request) {
        List<CityInfo> cityInfoList = new ArrayList<>();
        String path = request == null ? "" : request.getContextPath();
        try {
            CityInfoPO cityInfoPO = new CityInfoPO();
            cityInfoPO.setCityCode(cityInfo.getCityCode());
            cityInfoPO.setCityName(cityInfo.getCityName());
            List<CityInfoPO> cityInfoPOS = cityInfoMapper.selectAllByCityCodeAndCityName(cityInfoPO);
            if (CollectionUtils.isEmpty(cityInfoPOS)) {
                return null;
            }
            List<CityLabelInfoPOO> cityLabelInfoPOS = tCityLabelInfoMapper.selectAllRecords();
            List<FileInfoPOO> fileInfoPOS = tpFileInfoMapper.selectALL();
            List<DstWtInfoPO> dstWtInfoPOS = tDstWtInfoMapper.selectAll();
            for (CityInfoPO ci : cityInfoPOS
            ) {
                CityInfo city = new CityInfo();
                BeanUtils.copyProperties(ci, city);
                cityInfoList.add(city);
            }
            for (CityInfo temp : cityInfoList
            ) {
                List<CityLabelInfo> clInfoList = new ArrayList<>();
                List<FileInfo> fileInfos = new ArrayList<>();
                String code = temp.getCityCode();
                if (StringUtils.isNotEmpty(code) && CollectionUtils.isNotEmpty(fileInfoPOS)) {
                    List<FileInfoPOO> fileInfoPOList = fileInfoPOS.stream().filter(e -> StringUtils.isNotEmpty(e.getLinkId()) && code.equals(e.getLinkId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(fileInfoPOList)) {
                        fileInfoPOList.forEach(e -> {
                            FileInfo fileInformation = new FileInfo();
                            BeanUtils.copyProperties(e, fileInformation);
                            fileInfos.add(fileInformation);
                        });
                    }
                    if (CollectionUtils.isNotEmpty(fileInfos)) {
                        for (FileInfo info : fileInfos) {
                            info.setServerPath(path);
                        }
                        temp.setFileInfos(fileInfos);
                    }
                    if (CollectionUtils.isNotEmpty(cityLabelInfoPOS)) {
                        List<CityLabelInfoPOO> lablePOList = cityLabelInfoPOS.stream().filter(e -> StringUtils.isNotEmpty(e.getCityCode()) && e.getCityCode().equals(code)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(lablePOList)) {
                            List<CityLabelInfo> cityLabelInfos = new ArrayList<>();
                            for (CityLabelInfoPOO labelPO : lablePOList
                            ) {
                                CityLabelInfo cityLabelInfo = new CityLabelInfo();
                                BeanUtils.copyProperties(labelPO, cityLabelInfo);
                                cityLabelInfos.add(cityLabelInfo);
                            }
                            for (CityLabelInfo cityLabel : cityLabelInfos) {
                                CityLabelInfo cityLabelInfo = new CityLabelInfo();
                                cityLabelInfo.setCityLabelName(cityLabel.getCityLabelName());
                                cityLabelInfo.setCityLabelUrl(cityLabel.getCityLabelUrl());
                                cityLabelInfo.setCityLabelId(cityLabel.getCityLabelId());
                                cityLabelInfo.setCityCode(cityLabel.getCityCode());
                                cityLabelInfo.setLabelIntroduce(cityLabel.getLabelIntroduce());
                                clInfoList.add(cityLabelInfo);
                            }
                            temp.setCityLabelInfos(clInfoList);
                        }
                    }
                }
                //查询封装夏令时信息
                if (StringUtils.isNotBlank(temp.getDstWtId()) && CollectionUtils.isNotEmpty(dstWtInfoPOS)) {
                    DstWtInfoPO dstWtInfoPO = dstWtInfoPOS.stream().filter(e -> StringUtils.isNotEmpty(e.getId()) && e.getId().equals(temp.getDstWtId())).findFirst().orElse(null);
                    if (null != dstWtInfoPO) {
                        DstWtInfo dstWtInfo = new DstWtInfo();
                        BeanUtils.copyProperties(dstWtInfoPO, dstWtInfo);
                        temp.setDstWtInfo(dstWtInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("城市信息查询出错{}", e.getMessage());
            return null;
        }
        return cityInfoList;
    }
}
