package com.juneyaoair.ecs.manage.service.syncfile.impl;

import cn.hutool.core.util.StrUtil;
import com.juneyaoair.ecs.manage.service.file.IFileUploadService;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;


/**
 * 同步js文件
 */
public abstract class SyncJsFileService extends AbstractSyncService<String> {

    @Lazy
    @Resource
    IFileUploadService fileUploadService;

    protected final static String relativeRemotePath = "flightBasic";
    protected final static String fileSuffix = ".js";
    protected abstract String getContentName();

    /**
     * 上传（因为本地文件创建失败，与旧逻辑相比 去掉创建本地js文件和删除本地js文件步骤，
     * 但是如果使用new ClassPathResource见SyncStaticFileService.processGroupJsFile或许可创建成功）
     */
    @Override
    public void processFile(String data) {
        if (StrUtil.isBlank(data)) {
            return;
        }
        String fileFormat = getContentName() + fileSuffix;
        fileUploadService.uploadByte(data.getBytes(), fileFormat, relativeRemotePath);
    }

    @Override
    public abstract String prepareData();
}
