package com.juneyaoair.ecs.manage.service.activity.impl;

import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ImportPrizeEntry;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ImportPrizePoolInfo;
import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.ImportPrizeSubEntry;
import com.juneyaoair.ecs.manage.service.activity.ActivityPrizePoolService;
import com.juneyaoair.manage.b2c.entity.activity.ActivityPrizeEntityPO;
import com.juneyaoair.manage.b2c.entity.activity.ActivityPrizePoolPO;
import com.juneyaoair.manage.b2c.entity.activity.ActivityPrizeSubEntityPO;
import com.juneyaoair.manage.b2c.mapper.ActivityPrizeEntityMapper;
import com.juneyaoair.manage.b2c.mapper.ActivityPrizePoolMapper;
import com.juneyaoair.manage.b2c.mapper.ActivityPrizeSubEntityMapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.UUID;

/**
 * 活动奖池服务
 * <AUTHOR>
 */
@Service
public class ActivityPrizePoolServiceImpl implements ActivityPrizePoolService {

    @Autowired
    private ActivityPrizePoolMapper activityPrizePoolMapper;
    @Autowired
    private ActivityPrizeEntityMapper activityPrizeEntityMapper;
    @Autowired
    private ActivityPrizeSubEntityMapper activityPrizeSubEntityMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importPrizePool(Map<String, ImportPrizePoolInfo> pricePoolInfoMap) {
        String operator = SecurityUtils.getUsername();
        for (Map.Entry<String, ImportPrizePoolInfo> entry : pricePoolInfoMap.entrySet()) {
            String prizePoolCode = entry.getKey();
            ImportPrizePoolInfo importPrizePoolInfo = entry.getValue();
            // 保存奖池信息
            ActivityPrizePoolPO activityPrizePool = new ActivityPrizePoolPO();
            activityPrizePool.setPrizePoolCode(prizePoolCode);
            activityPrizePool.setPrizePoolName(importPrizePoolInfo.getPrizePoolName());
            activityPrizePool.setStartTime(importPrizePoolInfo.getStartTime());
            activityPrizePool.setEndTime(importPrizePoolInfo.getEndTime());
            activityPrizePool.setStatus("N");
            activityPrizePool.initInsert(operator);
            activityPrizePoolMapper.insert(activityPrizePool);
            Map<String, ImportPrizeEntry> prizeEntryMap = importPrizePoolInfo.getPrizeEntryMap();
            for (Map.Entry<String, ImportPrizeEntry> prizeEntry : prizeEntryMap.entrySet()) {
                String prizeCode = prizeEntry.getKey();
                ImportPrizeEntry importPrizeEntry = prizeEntry.getValue();
                // 保存奖品信息
                ActivityPrizeEntityPO activityPrizeEntity = new ActivityPrizeEntityPO();
                activityPrizeEntity.setPrizePoolCode(prizePoolCode);
                activityPrizeEntity.setPrizeCode(prizeCode);
                activityPrizeEntity.setPrizeName(importPrizeEntry.getPrizeName());
                activityPrizeEntity.setTotalAmount(importPrizeEntry.getTotalAmount());
                activityPrizeEntity.setSendAmount(0);
                activityPrizeEntity.setStatus("N");
                activityPrizeEntity.initInsert(operator);
                activityPrizeEntityMapper.insert(activityPrizeEntity);
                // 保存子奖品
                for (ImportPrizeSubEntry importPrizeSubEntry : importPrizeEntry.getPrizeSubEntityList()) {
                    ActivityPrizeSubEntityPO activityPrizeSubEntity = new ActivityPrizeSubEntityPO();
                    activityPrizeSubEntity.setPrizeSubEntityId(UUID.randomUUID().toString());
                    activityPrizeSubEntity.setPrizeCode(prizeCode);
                    activityPrizeSubEntity.setSubPrizeCode(importPrizeSubEntry.getSubPrizeCode());
                    activityPrizeSubEntity.setSubPrizeName(importPrizeSubEntry.getSubPrizeName());
                    activityPrizeSubEntity.setSubPrizeType(importPrizeSubEntry.getSubPrizeCategory());
                    activityPrizeSubEntity.setSubPrizeAmount(importPrizeSubEntry.getSubPrizeAmount());
                    activityPrizeSubEntity.setStatus("N");
                    activityPrizeSubEntity.initInsert(operator);
                    activityPrizeSubEntityMapper.insert(activityPrizeSubEntity);
                }
            }
        }
    }

}
