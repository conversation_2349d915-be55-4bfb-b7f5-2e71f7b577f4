package com.juneyaoair.ecs.manage.service.activity.impl;

import com.juneyaoair.ecs.manage.dto.activity.request.event.EventMemberQuery;
import com.juneyaoair.ecs.manage.dto.activity.response.event.EventMemberRecord;
import com.juneyaoair.ecs.manage.service.activity.EventInfoService;
import com.juneyaoair.manage.b2c.mapper.EventInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 事件服务
 * <AUTHOR>
 */
@Service
public class EventInfoServiceImpl implements EventInfoService {

    @Autowired
    private EventInfoMapper eventInfoMapper;

    @Override
    public List<EventMemberRecord> getEventMemberRecord(EventMemberQuery eventMemberQuery) {
        return eventInfoMapper.getEventMemberRecord(eventMemberQuery);
    }

}
