package com.juneyaoair.ecs.manage.service.apppictureconsole;

import com.juneyaoair.ecs.manage.dto.activity.response.AppPicture;

import javax.servlet.http.HttpServletRequest;

public interface IAppPictureConsoleAggrService {

    AppPicture getPicture(AppPicture req, HttpServletRequest request);

    boolean addPicture(AppPicture appPicture);

    boolean delPicture(AppPicture appPicture);

    boolean releaseAndOfflinePicture(AppPicture appPicture);

    boolean updatePicture(AppPicture picture);
}
