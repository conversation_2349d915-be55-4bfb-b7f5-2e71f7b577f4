package com.juneyaoair.ecs.manage.service.social;

import com.juneyaoair.ecs.manage.dto.social.request.SocialReportRequest;
import com.juneyaoair.ecs.manage.dto.social.response.SocialResponsibilityReport;

import java.util.List;

/**
 * @ClassName ISocialService
 * @Description
 * <AUTHOR>
 * @Date 2024/6/12 16:45
 * @Version 1.0
 */
public interface ISocialService {

    /**
     * @param socialReportRequest
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.social.response.SocialReportResponse>
     * <AUTHOR>
     * @Description 获取社会责任报告列表
     * @Date 16:48 2024/6/12
     **/
    List<SocialResponsibilityReport> toCatchSocialReports(SocialReportRequest socialReportRequest);

    /**
     * @param socialResponsibilityReport
     * @return void
     * <AUTHOR>
     * @Description 新增社会责任报告
     * @Date 10:05 2024/6/13
     **/

    void toIncreaseSocialReport(SocialResponsibilityReport socialResponsibilityReport);

    /**
     * @param socialResponsibilityReport
     * @return void
     * <AUTHOR>
     * @Description 修改社会责任报告
     * @Date 10:43 2024/6/13
     **/

    void toUpdateSocialReport(SocialResponsibilityReport socialResponsibilityReport);

    /**
     * @param socialResponsibilityReport
     * @return void
     * <AUTHOR>
     * @Description 删除社会责任报告
     * @Date 10:58 2024/6/13
     **/

    void toRemoveSocialReport(SocialResponsibilityReport socialResponsibilityReport);
}
