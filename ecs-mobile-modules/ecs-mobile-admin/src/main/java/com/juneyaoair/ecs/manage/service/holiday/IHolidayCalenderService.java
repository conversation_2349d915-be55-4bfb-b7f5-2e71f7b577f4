package com.juneyaoair.ecs.manage.service.holiday;

import com.juneyaoair.ecs.manage.dto.activity.common.HolidayCalendar;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @ClassName IHolidayService
 * @Description
 * <AUTHOR>
 * @Date 2024/1/22 13:26
 * @Version 1.0
 */
public interface IHolidayCalenderService {

    /**
     * <AUTHOR>
     * @Description 获取节假日列表
     * @Date 13:28 2024/1/22
     * @param request
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.activity.common.HolidayCalendar>
     **/
    List<HolidayCalendar> toCatchHolidayList(HttpServletRequest request);

    /**
     * <AUTHOR>
     * @Description 操作节假日信息
     * @Date 15:16 2024/1/22
     * @param holidayCalendar
     * @param request
     * @return com.juneyaoair.ecs.manage.dto.activity.response.HolidayCalenderResponse
     **/
    void optHoliday(HolidayCalendar holidayCalendar, HttpServletRequest request);

    /**
     * <AUTHOR>
     * @Description 删除节假日缓存信息
     * @Date 11:08 2024/4/11
     * @return void
     **/
    void toClearCache();
}
