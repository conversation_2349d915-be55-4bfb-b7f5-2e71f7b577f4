package com.juneyaoair.ecs.manage.service.paymethod;

import com.juneyaoair.ecs.manage.dto.activity.request.paymethod.PayMethodActivityDTO;
import com.juneyaoair.ecs.manage.dto.activity.request.paymethod.PayMethodDTO;
import com.juneyaoair.ecs.manage.dto.activity.request.paymethod.PayMethodPageReq;
import com.juneyaoair.ecs.manage.dto.base.PageDataResponse;
import com.juneyaoair.manage.b2c.entity.PayMethodPO;
import com.juneyaoair.manage.b2c.entity.activity.PayMethodActivityPO;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;

/**
 * @ClassName PayMethodService
 * @Description 支付方式配置相关API
 * <AUTHOR>
 * @Date 2025/7/31 15:47
 * @Version 1.0
 */
public interface PayMethodService {


    /**
     * 支付方式配置列表分页查询
     * @param payMethodPageReq
     * @return
     */
    PageDataResponse payMethodListPageQuery(PayMethodPageReq payMethodPageReq);

    /**
     * 根据id查询支付方式信息
     * @param id
     * @return
     */
    PayMethodPO getPayMethodById(String id);

    /**
     * 根据id查询活动信息
     * @param id
     * @return
     */
    PayMethodActivityDTO getPayMethodActivityById(String id);

    void updatePayMethodById(PayMethodDTO payMethodDTO);

    void updatePayMethodActivityById(PayMethodActivityDTO payMethodActivityDTO);

    String checkChannelAndProduct(PayMethodDTO payMethodDTO);

    String parseImage(MultipartFile file);

    void changePayMethodStatus(String status, String id);

    void changePayMethodActivityStatus(String status, String id);

    void deletePayMethodActivityById(String id);

    PayMethodPO addPayMethod(PayMethodDTO payMethodDTO);

    PayMethodActivityPO addPayMethodActivity(PayMethodActivityDTO payMethodActivityDTO);

    List<PayMethodActivityPO> getActivityByPayMethodId(String payMethodId);

    void refreshRedis(String merchantPayment, String channel, String payProductType);

    Boolean checkAuth();
}
