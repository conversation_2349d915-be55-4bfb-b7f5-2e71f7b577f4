package com.juneyaoair.ecs.excel.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/3 13:01
 */
@Data
public class Traveller {
    @ExcelProperty(index = 0, value = "证件号")
    private String cert;
    @ExcelProperty(index = 1, value = "航班号")
    private String flightNo;
    @ExcelProperty(index = 2, value = "航班日期")
    private String flightDate;
    @ExcelProperty(index = 3, value = "姓名")
    private String name;
    @ExcelProperty(index = 4, value = "票号")
    private String tktNo;
    @ExcelProperty(index = 5, value = "出票office")
    private String officeNo;
    @ExcelProperty(index = 6, value = "数据航班号")
    private String resultFlightNo;
    @ExcelProperty(index = 7, value = "出票时间")
    private String ticketOutTime;
}
