package com.juneyaoair.ecs.manage.service.activity;

import com.juneyaoair.ecs.manage.dto.activity.request.event.EventMemberQuery;
import com.juneyaoair.ecs.manage.dto.activity.response.event.EventMemberRecord;

import java.util.List;

/**
 * 事件服务
 * <AUTHOR>
 */
public interface EventInfoService {

    /**
     * 查询事件奖品发放清单
     * @param eventMemberQuery
     * @return
     */
    List<EventMemberRecord> getEventMemberRecord(EventMemberQuery eventMemberQuery);

}
