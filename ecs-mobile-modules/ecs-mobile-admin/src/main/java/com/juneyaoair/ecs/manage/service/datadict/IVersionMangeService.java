package com.juneyaoair.ecs.manage.service.datadict;

import com.juneyaoair.ecs.manage.dto.datadict.VersionManage;

import java.util.List;

/**
 * @ClassName IVersionMangeService
 * @Description
 * <AUTHOR>
 * @Date 2024/1/24 8:46
 * @Version 1.0
 */
public interface IVersionMangeService {

    List<VersionManage> getList(VersionManage versionManage);
    int updateVersion(VersionManage versionManage);

    boolean toAddVersion(VersionManage versionManage);
    boolean toUpdateVersion(VersionManage versionManage);

    boolean toDeleteVersion(VersionManage versionManage);

}
