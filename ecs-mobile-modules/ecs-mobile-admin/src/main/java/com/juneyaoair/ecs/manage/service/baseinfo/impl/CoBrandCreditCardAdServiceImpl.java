package com.juneyaoair.ecs.manage.service.baseinfo.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.bo.CoBrandCreditCardAdBO;
import com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.dto.*;
import com.juneyaoair.ecs.manage.mapstruct.CoBrandCreditCardAdMapStruct;
import com.juneyaoair.ecs.manage.service.baseinfo.CoBrandCreditCardAdService;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.CoBrandCreditCardAdPO;
import com.juneyaoair.manage.b2c.entity.CoBrandCreditCardContentPO;
import com.juneyaoair.manage.b2c.entity.CoBrandCreditCardDetailPO;
import com.juneyaoair.manage.b2c.entity.CoBrandCreditCardMainTextPO;
import com.juneyaoair.manage.b2c.mapper.CoBrandCreditCardAdPOMapper;
import com.juneyaoair.manage.b2c.mapper.CoBrandCreditCardContentPOMapper;
import com.juneyaoair.manage.b2c.mapper.CoBrandCreditCardDetailPOMapper;
import com.juneyaoair.manage.b2c.mapper.CoBrandCreditCardMainTextPOMapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CoBrandCreditCardAdServiceImpl implements CoBrandCreditCardAdService {

    @Resource
    private CoBrandCreditCardAdPOMapper adPOMapper;
    @Resource
    private CoBrandCreditCardDetailPOMapper detailPOMapper;
    @Resource
    private CoBrandCreditCardContentPOMapper contentPOMapper;
    @Resource
    private CoBrandCreditCardMainTextPOMapper mainTextMapper;


    @Override
    public List<CoBrandCreditCardAdDTO> list(ListCoBrandCreditCardAdRequestDTO request) {

        List<CoBrandCreditCardAdBO> boList = adPOMapper.selectBO(null, request.getTheme(), request.getStatus());

        // CoBrandCreditCardAdBO convert to CoBrandCreditCardAdDTO
        List<CoBrandCreditCardAdDTO> dtos = boList.stream().map(CoBrandCreditCardAdMapStruct.INSTANCE::toCoBrandCreditCardAdDTO).collect(Collectors.toList());

        dtos.forEach(ad -> {
            // sort content
            ad.getDetail().forEach(detail -> {
                detail.getContents().sort(Comparator.comparingInt(CoBrandCreditCardAdDTO.CoBrandCreditCardAdDetailDTO.CoBrandCreditCardAdContentDTO::getSort));
            });

            // sort detail
            ad.getDetail().sort(Comparator.comparingInt(CoBrandCreditCardAdDTO.CoBrandCreditCardAdDetailDTO::getSort));

        });

        // sort by sort integer
        dtos.sort(Comparator.comparingInt(CoBrandCreditCardAdDTO::getSort));
        return dtos;
    }



    @Override
    public void addCreditCardAD(AddOrUpdateAdRequestDTO dto) {

        List<CoBrandCreditCardAdBO> boList = adPOMapper.selectBO(null, null, null);
        if (boList.stream().anyMatch(v -> v.getSort().equals(dto.getSort()))) {
            throw new HoServiceException("排序号重复");
        }

        String themeId = IdUtil.randomUUID();
        //
        CoBrandCreditCardAdPO po = CoBrandCreditCardAdMapStruct.INSTANCE.toCoBrandCreditCardAdPO(dto);
        //
        po.setId(themeId);
        po.setCreatedBy(SecurityUtils.getUsername());
        po.setUpdatedBy(SecurityUtils.getUsername());
        po.setCreatedTime(new Date());
        po.setUpdatedTime(new Date());
        adPOMapper.insert(po);
    }

    @Override
    public void updateCreditCardAD(AddOrUpdateAdRequestDTO dto) {

        List<CoBrandCreditCardAdBO> boList = adPOMapper.selectBO(null, null, null);

        if (boList.stream().filter(v -> !v.getId().equals(dto.getId())).anyMatch(v -> v.getSort().equals(dto.getSort()))) {
            throw new HoServiceException("排序号重复");
        }

        // check id exist
        CoBrandCreditCardAdPO po = adPOMapper.selectById(dto.getId());
        if (po == null) {
            throw new HoServiceException("主题不存在");
        }

        //
        CoBrandCreditCardAdPO updatePO = CoBrandCreditCardAdMapStruct.INSTANCE.toCoBrandCreditCardAdPO(dto);
        //
        updatePO.setCreatedBy(po.getCreatedBy());
        updatePO.setUpdatedBy(SecurityUtils.getUsername());
        updatePO.setCreatedTime(po.getCreatedTime());
        updatePO.setUpdatedTime(new Date());
        adPOMapper.updateById(updatePO);
    }


    @Override
    public void publishCreditCardAD(CreditCardAdIdRequestDTO request) {
        CoBrandCreditCardAdPO ad = adPOMapper.selectById(request.getId());
        ad.setThemeEnable("1".equals(ad.getThemeEnable()) ? "0" : "1");
        adPOMapper.updateById(ad);
    }

    @Override
    @DSTransactional
    public void deleteCreditCardAD(CreditCardAdIdRequestDTO request) {
        CoBrandCreditCardAdBO bo = adPOMapper.selectBoById(request.getId());
        bo.getDetail().forEach(detail -> {
            detail.getContents().forEach(content -> {
                content.getMainText().forEach(mainText -> mainTextMapper.deleteById(mainText.getId()));
                contentPOMapper.deleteById(content.getId());
            });
            detailPOMapper.deleteById(detail.getId());
        });
        adPOMapper.deleteById(request.getId());
    }


    @Override
    @DSTransactional
    public void addDetail(AddOrUpdateAdDetailRequestDTO request) {

        CoBrandCreditCardAdBO bo = adPOMapper.selectBoById(request.getId());

        if (CollectionUtils.isEmpty(request.getDetail())) {
            throw new HoServiceException("详情不能为空");
        }
        if (bo == null) {
            throw new HoServiceException("主题不存在");
        }
        if (bo.getDetail().stream().anyMatch(v -> v.getFrontEndType().equals(request.getDetail().get(0).getFrontEndType()))) {
            throw new HoServiceException("该类型已存在");
        }


        // 填充默认序号
        request.getDetail().forEach(v -> {

            // detail sort default 1
            if (v.getSort() == null) {
                v.setSort(1);
            }

            // content sort default 1 to n
            if (CollUtil.isNotEmpty(v.getContents())) {
                for (int i = 1; i <= v.getContents().size(); i++) {
                    v.getContents().get(i - 1).setSort(i);
                    // main text sort default 1
                    if (CollUtil.isNotEmpty(v.getContents().get(i - 1).getMainText())) {
                        for (int j = 1; j <= v.getContents().get(i - 1).getMainText().size(); j++) {
                            v.getContents().get(i - 1).getMainText().get(j - 1).setSort(j);
                        }
                    }
                }
            }
        });


        //
        AddOrUpdateAdDetailRequestDTO.AddOrUpdateAdDetailDTO detailDTO = request.getDetail().get(0);
        String detailId = IdUtil.randomUUID();
        // insert detail
        if (detailDTO != null) {
            CoBrandCreditCardDetailPO detailPo = CoBrandCreditCardAdMapStruct.INSTANCE.toCoBrandCreditCardDetailPo(detailDTO);
            detailPo.setId(detailId);
            detailPo.setThemeId(request.getId());
            detailPo.setCreatedBy(SecurityUtils.getUsername());
            detailPo.setUpdatedBy(SecurityUtils.getUsername());
            detailPo.setCreatedTime(new Date());
            detailPo.setUpdatedTime(new Date());
            detailPOMapper.insert(detailPo);

            // insert content
            if (CollUtil.isNotEmpty(detailDTO.getContents())) {
                request.getDetail().get(0).getContents().forEach(v -> {
                    String contentId = IdUtil.randomUUID();
                    CoBrandCreditCardContentPO contentPO = CoBrandCreditCardAdMapStruct.INSTANCE.toCoBrandCreditCardContentPo(v);
                    contentPO.setId(contentId);
                    contentPO.setDetailId(detailId);
                    contentPO.setCreatedBy(SecurityUtils.getUsername());
                    contentPO.setUpdatedBy(SecurityUtils.getUsername());
                    contentPO.setCreatedTime(new Date());
                    contentPO.setUpdatedTime(new Date());
                    contentPOMapper.insert(contentPO);
                    // insert mainText
                    if (CollUtil.isNotEmpty(v.getMainText())) {
                        v.getMainText().forEach(text -> {
                            String mainTextId = IdUtil.randomUUID();
                            CoBrandCreditCardMainTextPO mainTextPO = CoBrandCreditCardAdMapStruct.INSTANCE.toCoBrandCreditCardMainTextPo(text);
                            mainTextPO.setId(mainTextId);
                            mainTextPO.setContentId(contentId);
                            mainTextMapper.insert(mainTextPO);
                        });
                    }
                });
            }
        }


    }

    @Override
    @DSTransactional
    public void updateDetail(AddOrUpdateAdDetailRequestDTO request) {

        // check id exist
        CoBrandCreditCardAdBO bo = adPOMapper.selectBoById(request.getId());
        if (bo == null) {
            throw new HoServiceException("主题不存在");
        }
        ((CoBrandCreditCardAdService) AopContext.currentProxy()).deleteDetail(request);
        ((CoBrandCreditCardAdService) AopContext.currentProxy()).addDetail(request);

    }

    @Override
    @DSTransactional
    public void deleteDetail(AddOrUpdateAdDetailRequestDTO request) {

        CoBrandCreditCardAdBO bo = adPOMapper.selectBoById(request.getId());
        if (CollUtil.isNotEmpty(bo.getDetail())) {
            bo.getDetail().forEach(detail -> {
                // just delete current frontEndType detail
                if (request.getDetail().size() > 1) {
                    throw new HoServiceException("仅更新单个详情");
                }
                if (request.getDetail().get(0).getFrontEndType().equals(detail.getFrontEndType())) {
                    if (CollUtil.isNotEmpty(detail.getContents())) {
                        detail.getContents().forEach(content -> {
                            if (CollUtil.isNotEmpty(content.getMainText())) {
                                content.getMainText().forEach(mainText -> mainTextMapper.deleteById(mainText.getId()));
                            }
                            contentPOMapper.deleteById(content.getId());
                        });
                    }
                    detailPOMapper.deleteById(detail.getId());
                }
            });
        }
    }
}
