package com.juneyaoair.ecs.manage.service.syncfile.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.juneyaoair.ecs.manage.constant.ManageConstant;
import com.juneyaoair.ecs.manage.dto.citymanage.CityAirportInfoDTO;
import com.juneyaoair.ecs.manage.dto.citymanage.CityInfoRespDTO;
import com.juneyaoair.ecs.manage.dto.citymanage.CityLabelInfo;
import com.juneyaoair.ecs.manage.enums.HK_MO_TWCityEnum;
import com.juneyaoair.ecs.manage.enums.LanguageEnum;
import com.juneyaoair.ecs.manage.service.syncfile.IAirportSyncStaticFileService;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.ecs.utils.SystemConstants;
import com.juneyaoair.manage.b2c.entity.AirportJoinLabelWarnPO;
import com.juneyaoair.manage.b2c.service.IAirportService;
import com.juneyaoair.manage.b2c.service.ICityInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AirportSyncStaticFileServiceImpl extends SyncStaticFileService<CityAirportInfoDTO> implements IAirportSyncStaticFileService {
    private static final String DOMESTIC = "D";
    private final String INTERNATIONAL = "I";
    @Resource
    private IAirportService airportService;
    @Resource
    private ICityInfoService cityInfoService;

    @Override
    protected void removeCacheInRedis(List<CityAirportInfoDTO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        //老代码里没有更新缓存过程
    }

    @Override
    protected void updateVersionNo() {
        //老代码里没有更新版本过程 但是应该有
        updateVersionNo(SystemConstants.REDIS_AIRPORT_INFO);
        updateVersionNo(SystemConstants.REDIS_CITY_INFO);

    }

    @Override
    protected String getContentName() {
        return "airport";
    }

    @Override
    public Object data2ObjData(List<CityAirportInfoDTO> data) {
        return data;
    }

    @Override
    public List<CityAirportInfoDTO> prepareData() {
        //优先级默认处理
        Map<String, Integer> map = new HashMap<>();
        map.put(DOMESTIC, 1);
        map.put(INTERNATIONAL, 2);
        //城市信息处理
        List<CityInfoRespDTO> cityInfoRespDTOS = cityInfoService.searchCity(null);
        cityInfoRespDTOS.removeIf(i -> i == null || !"1".equalsIgnoreCase(i.status));
        List<CityAirportInfoDTO> cityAirportInfoDTOS = getCityAirportDTOFromCity(cityInfoRespDTOS, map);
        changeCountryInfo(cityAirportInfoDTOS);
        Map<String, CityAirportInfoDTO> cityAirpotyMap = cityAirportInfoDTOS.stream().collect(Collectors.toMap(i -> i.cityCode, i -> i));
        Map<String, CityInfoRespDTO> cityMap = cityInfoRespDTOS.stream().collect(Collectors.toMap(i -> i.cityCode, i -> i));
        //机场信息处理
        List<AirportJoinLabelWarnPO> airportPOs = airportService.searchAirportJoinLabelAndWarn(null);
        airportPOs.removeIf(i -> i == null || !"1".equalsIgnoreCase(i.status));
        List<CityAirportInfoDTO> cityAirportInfoDTOS1 = getCityAirportDTOFromAirport(airportPOs, map, cityAirpotyMap, cityMap);
        changeCountryInfo(cityAirportInfoDTOS1);
        //判断城市信息有无机场数据
        for (CityAirportInfoDTO tmp : cityAirportInfoDTOS) {
            boolean exist = cityAirportInfoDTOS1.stream().anyMatch(cityAirportInfoDTO -> {
                if ("airport".equals(cityAirportInfoDTO.getCityOrAirportType()) && cityAirportInfoDTO.getCityCode().equals(tmp.getCityCode())) {
                    return true;
                }
                return false;
            });
            tmp.setHasAirport(exist);
        }
        ArrayList<CityAirportInfoDTO> result = new ArrayList<>();
        result.addAll(cityAirportInfoDTOS);
        result.addAll(cityAirportInfoDTOS1);
        return result;
    }

    private void changeCountryInfo(List<CityAirportInfoDTO> cityAirportInfoDTOS) {
        if (CollUtil.isEmpty(cityAirportInfoDTOS)) {
            return;
        }
        cityAirportInfoDTOS.forEach(i -> {
            if (Arrays.stream(HK_MO_TWCityEnum.values()).noneMatch(it -> it == HK_MO_TWCityEnum.getEnumByCode(i.cityCode))) {
                return;
            }
            i.countryName = ManageConstant.CN_DESC;
        });
    }

    private List<CityAirportInfoDTO> getCityAirportDTOFromCity(List<CityInfoRespDTO> cityInfoRespDTOS,
                                                                      Map<String, Integer> map) {
        List<CityAirportInfoDTO> cityAirportInfoDTOS = cityInfoRespDTOS.stream().sorted(Comparator.comparing(i -> map.get(i.isInternational))).map(cityInfoDto -> {
            CityAirportInfoDTO cityAirportInfo = new CityAirportInfoDTO();
            cityAirportInfo.cityOrAirportType = "city";
            cityAirportInfo.cityCode = cityInfoDto.cityCode;
            cityAirportInfo.cityName = cityInfoDto.cityName;
            cityAirportInfo.nameAbb = cityInfoDto.nameAbb;
            cityAirportInfo.nearbyAirport = StringUtils.isNotBlank(cityInfoDto.nearbyAirport) ? Arrays.asList(cityInfoDto.nearbyAirport.split(";")) : null;
            Map<String, String> cityNameMap = Maps.newHashMap();
            cityNameMap.put(LanguageEnum.ZH_CN.name(), cityInfoDto.cityName);
            if (StringUtils.isNotBlank(cityInfoDto.cityEName)) {
                cityNameMap.put(LanguageEnum.EN_US.name(), cityInfoDto.cityEName);
            }
            if (StringUtils.isNotBlank(cityInfoDto.cityJpName)) {
                cityNameMap.put(LanguageEnum.JA_JP.name(), cityInfoDto.cityJpName);
            }
            if (StringUtils.isNotBlank(cityInfoDto.cityTcName)) {
                cityNameMap.put(LanguageEnum.ZH_HK.name(), cityInfoDto.cityTcName);
            }
            cityAirportInfo.cityNameMap = cityNameMap;
            cityAirportInfo.cityPinYin = cityInfoDto.cityPinYin;
            cityAirportInfo.cityPinYinAbb = cityInfoDto.cityPinYinAbb;
            cityAirportInfo.isInternational = cityInfoDto.isInternational;
            cityAirportInfo.cityNameKeyword = cityInfoDto.cityKeyWords;
            cityAirportInfo.cityLabelInfoList = Optional.ofNullable(cityInfoDto.listCityLabel).map(i -> i.stream().map(it -> {
                CityLabelInfo cityLabelInfo = new CityLabelInfo();
                cityLabelInfo.labelName = it.cityLabelName;
                cityLabelInfo.labelUrl = "";
                cityLabelInfo.labelIconUrl = it.cityLabelUrl;
                return cityLabelInfo;

            }).collect(Collectors.toList())).orElse(null);
            cityAirportInfo.countryName = Optional.ofNullable(cityInfoDto.countryDTO).map(i -> i.countryName).orElse(null);
            cityAirportInfo.provinceName = cityInfoDto.provinceName;
            cityAirportInfo.region = changeRegion(cityInfoDto.getIsInternational(),cityInfoDto.getCountryCode());
            return cityAirportInfo;
        }).collect(Collectors.toList());
        return cityAirportInfoDTOS;
    }

    private List<CityAirportInfoDTO> getCityAirportDTOFromAirport(List<AirportJoinLabelWarnPO> airportPOs,
                                                                         Map<String, Integer> map,
                                                                         Map<String, CityAirportInfoDTO> cityAirpotyMap,
                                                                         Map<String, CityInfoRespDTO> cityMap) {
        List<CityAirportInfoDTO> cityAirportInfoDTOS1 = airportPOs.stream().sorted(Comparator.comparing(i ->
                        map.get(Optional.ofNullable(i.cityInfoPO).map(it -> it.isInternational).orElse(DOMESTIC/*默认国内*/))
                )).filter(
                        i -> i.cityInfoPO != null
                )
                .map(airPortInfoDto -> {
                    CityAirportInfoDTO cityAirportInfo = new CityAirportInfoDTO();
                    cityAirportInfo.setCityOrAirportType("airport");
                    cityAirportInfo.setAirportCode(airPortInfoDto.airportCode);
                    cityAirportInfo.setAirportPinYin(airPortInfoDto.airportPinyin);
                    cityAirportInfo.setAirportPinYinAbb(airPortInfoDto.pinyinAbb);
                    if (StringUtils.isNotBlank(airPortInfoDto.latitude)) {
                        cityAirportInfo.setLatitude(airPortInfoDto.latitude);
                    }
                    if (StringUtils.isNotBlank(airPortInfoDto.getLongitude())) {
                        cityAirportInfo.setLongitude(airPortInfoDto.getLongitude());
                    }
                    cityAirportInfo.setAirportName(airPortInfoDto.airportName);
                    String airPortName = airPortInfoDto.airportName;
                    if (StringUtils.isNotBlank(airPortName) && !airPortName.endsWith("机场")) {
                        cityAirportInfo.setAirportName(airPortName + "机场");
                    }
                    //语言格式处理
                    Map<String, String> airportNameMap = Maps.newHashMap();
                    airportNameMap.put(LanguageEnum.ZH_CN.name(), cityAirportInfo.getAirportName());
                    if (StringUtils.isNotBlank(airPortInfoDto.airportEName)) {
                        airportNameMap.put(LanguageEnum.EN_US.name(), airPortInfoDto.airportEName);
                    }
                    if (StringUtils.isNotBlank(airPortInfoDto.airportJpName)) {
                        airportNameMap.put(LanguageEnum.JA_JP.name(), airPortInfoDto.airportJpName);
                    }
                    if (StringUtils.isNotBlank(airPortInfoDto.airportTcName)) {
                        airportNameMap.put(LanguageEnum.ZH_HK.name(), airPortInfoDto.airportTcName);
                    }
                    cityAirportInfo.airportNameMap = airportNameMap;
                    cityAirportInfo.setAirportNameAbb(StringUtils.isNotBlank(airPortInfoDto.nameAbb) ? Arrays.asList(airPortInfoDto.nameAbb.split(";")) : null);
                    cityAirportInfo.setCityCode(airPortInfoDto.cityCode);
                    cityAirportInfo.setCityName(airPortInfoDto.cityInfoPO.cityName);
                    cityAirportInfo.setNameAbb(airPortInfoDto.cityInfoPO.nameAbb);
                    cityAirportInfo.setCityPinYin(airPortInfoDto.cityInfoPO.cityPinYin);
                    cityAirportInfo.setIsInternational(airPortInfoDto.cityInfoPO.isInternational);
                    // 设置城市名称
                    CityAirportInfoDTO cityAirportInfoDTO = cityAirpotyMap.get(airPortInfoDto.cityCode);
                    if (null != cityAirportInfoDTO) {
                        cityAirportInfo.setCityNameMap(cityAirportInfoDTO.cityNameMap);
                    }
                    //获取对应的城市标签
                    CityInfoRespDTO cityInfoRespDTO = cityMap.get(airPortInfoDto.cityCode);
                    if(cityInfoRespDTO != null){
                        cityAirportInfo.region = changeRegion(cityInfoRespDTO.getIsInternational(),cityInfoRespDTO.getCountryCode());
                        //标签处理
                        if (CollectionUtil.isNotEmpty(cityInfoRespDTO.listCityLabel)) {
                            cityAirportInfo.cityLabelInfoList = cityInfoRespDTO.listCityLabel.stream().map(it -> {
                                CityLabelInfo cityLabelInfo = new CityLabelInfo();
                                cityLabelInfo.labelName = it.cityLabelName;
                                cityLabelInfo.labelUrl = "";
                                cityLabelInfo.labelIconUrl = it.cityLabelUrl;
                                return cityLabelInfo;
                            }).collect(Collectors.toList());
                        }
                    }
                    cityAirportInfo.provinceName = Optional.ofNullable(cityInfoRespDTO).map(it -> it.provinceName).orElse(null);
                    cityAirportInfo.countryName = Optional.ofNullable(cityInfoRespDTO).map(it -> it.countryDTO).map(it -> it.countryName).orElse(null);
                    return cityAirportInfo;
                }).collect(Collectors.toList());
        return cityAirportInfoDTOS1;
    }

    @Override
    protected String map2Js(Map<String, Object> resultMap) {
        return "var __cityAirportInfo = " + JsonUtil.objectToJson(resultMap);
    }

    @Override
    public boolean process() {
        mainProcess();
        return true;
    }
}
