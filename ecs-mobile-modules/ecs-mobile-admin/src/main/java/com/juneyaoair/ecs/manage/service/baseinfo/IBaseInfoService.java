package com.juneyaoair.ecs.manage.service.baseinfo;

import com.juneyaoair.ecs.manage.dto.activity.common.ActivityAirLine;
import com.juneyaoair.ecs.manage.dto.activity.common.PhotoActivityJson;
import com.juneyaoair.ecs.manage.dto.activity.request.ActivityBaseInfo;
import com.juneyaoair.ecs.manage.dto.activity.request.ActivityChildInfo;
import com.juneyaoair.ecs.manage.dto.activity.request.ActivityCoupon;
import com.juneyaoair.ecs.manage.dto.activity.request.ActivityCouponDetail;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.ruoyi.common.core.web.page.PageDomain;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * @ClassName IBaseInfoService
 * @Description
 * <AUTHOR>
 * @Date 2023/11/16 11:04
 * @Version 1.0
 */
public interface IBaseInfoService {

    PageResult<ActivityBaseInfo> toCatchList(ActivityBaseInfo activityBaseInfo, PageDomain pageDomain);

    List<ActivityChildInfo> toCatchChildList(ActivityChildInfo activityChildInfo);


    /**
     * @param activityBaseInfo
     * @return boolean
     * <AUTHOR>
     * @Description 新增活动基础信息配置
     * @Date 13:38 2023/11/16
     **/
    boolean toAddBaseInfo(ActivityBaseInfo activityBaseInfo);

    /**
     * @param activityBaseInfo
     * @return boolean
     * <AUTHOR>
     * @Description 修改活动基础信息配置
     * @Date 13:38 2023/11/16
     **/
    boolean toUpdateBaseInfo(ActivityBaseInfo activityBaseInfo);

    /**
     * @param childInfo
     * @return boolean
     * <AUTHOR>
     * @Description 新增子基础信息配置
     * @Date 14:52 2023/11/17
     **/
    boolean toAddChildInfo(ActivityChildInfo childInfo);

    /**
     * <AUTHOR>
     * @Description 更新子基础信息配置
     * @Date 8:34 2023/12/11
     * @param childInfo
     * @return boolean
     **/
    boolean toUpdateChildInfo(ActivityChildInfo childInfo);

    /**
     * <AUTHOR>
     * @Description 根据id获取图片信息
     * @Date 9:29 2023/12/11
     * @param activityChildInfo
     * @return com.juneyaoair.ecs.manage.dto.activity.request.ActivityChildInfo
     **/
    ActivityChildInfo toCatchChildDetailById(ActivityChildInfo activityChildInfo);

    /**
     * <AUTHOR>
     * @Description 修改状态
     * @Date 9:58 2023/12/11
     * @param activityChildInfo
     * @return boolean
     **/
    boolean toUpdateChildStatus(ActivityChildInfo activityChildInfo);

    /**
     * <AUTHOR>
     * @Description 查询图片信息
     * @Date 10:06 2023/12/11
     * @param activityBaseInfo
     * @return com.juneyaoair.ecs.manage.dto.activity.request.ActivityBaseInfo
     **/
    ActivityBaseInfo toCatchCouponById(ActivityBaseInfo activityBaseInfo);

    /**
     * <AUTHOR>
     * @Description 导入EXCEL表格
     * @Date 10:17 2023/12/11
     * @param file
     * @param dataDesc
     * @return java.util.List<com.juneyaoair.ecs.manage.dto.activity.common.ActivityAirLine>
     **/
    List<ActivityAirLine> importExcel(MultipartFile file, String dataDesc) throws IOException;

    /**
     * <AUTHOR>
     * @Description 添加活动
     * @Date 10:35 2023/12/11
     * @param activityCoupon
     * @return void
     **/
    boolean toAddCoupon(ActivityCoupon activityCoupon);

    /**
     * <AUTHOR>
     * @Description 删除活动
     * @Date 10:52 2023/12/11
     * @param activityCoupon
     * @return boolean
     **/
    boolean toDelCoupon(ActivityCoupon activityCoupon);

    /**
     * <AUTHOR>
     * @Description
     * @Date 10:56 2023/12/11
     * @param activityCouponDetail
     * @return boolean
     **/
    boolean toDelCouponDetail(ActivityCouponDetail activityCouponDetail);

    /**
     * <AUTHOR>
     * @Description 审核作品
     * @Date 11:08 2023/12/11
     * @param activityCouponDetail
     * @return boolean
     **/
    boolean toUpdateCouponDetail(ActivityCouponDetail activityCouponDetail);


    /**
     * <AUTHOR>
     * @Description 新增券详情
     * @Date 13:07 2023/12/11
     * @param activityCouponDetail
     * @return boolean
     **/
    boolean toAddCouponDetail(ActivityCouponDetail activityCouponDetail);

    /**
     * <AUTHOR>
     * @Description 获取类型
     * @Date 13:25 2023/12/11
     * @return PhotoActivityJson
     **/
    PhotoActivityJson toCatchType();


}
