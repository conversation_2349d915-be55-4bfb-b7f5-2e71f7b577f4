package com.juneyaoair.ecs.manage.service.cityinfo.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.ecs.manage.dto.citymanage.*;
import com.juneyaoair.ecs.manage.service.cityinfo.ICityInfoAggrService;
import com.juneyaoair.ecs.manage.service.file.IFileUploadService;
import com.juneyaoair.ecs.manage.service.syncfile.ICitySyncStaticFileService;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.manage.b2c.entity.CityInfoPO;
import com.juneyaoair.manage.b2c.entity.DstWtInfoPO;
import com.juneyaoair.manage.b2c.entity.CityLabelInfoPO;
import com.juneyaoair.manage.b2c.mapper.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CityInfoAggrServiceImpl implements ICityInfoAggrService {
    @Resource
    TCityInfoMapper cityInfoMapper;
    @Resource
    TDstWtInfoMapper tDstWtInfoMapper;
    @Resource
    TCityLabelInfoMapper tCityLabelInfoMapper;
    @Resource
    ICitySyncStaticFileService citySyncStaticFileService;

    @Override
    public List<CityExportDTO> exportList(CityInfoReqDTO dto) {
        LambdaQueryWrapper<CityInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(CityInfoPO::getCityHotOrder);
        queryWrapper.orderByAsc(CityInfoPO::getZoneHotOrder);
        if (StringUtils.isNotBlank(dto.getCityCode())) {
            queryWrapper.eq(CityInfoPO::getCityCode, dto.getCityCode());
        }
        if (StringUtils.isNotBlank(dto.getCityName())) {
            queryWrapper.like(CityInfoPO::getCityName, dto.getCityName());
        }
        if (StringUtils.isNotBlank(dto.getIsInternational())) {
            queryWrapper.eq(CityInfoPO::getIsInternational, dto.getIsInternational());
        }
        if (StringUtils.isNotBlank(dto.getIsHotCity())) {
            queryWrapper.eq(CityInfoPO::getIsHotCity, dto.getIsHotCity());
        }
        if (StringUtils.isNotBlank(dto.getIsOftenCity())) {
            queryWrapper.eq(CityInfoPO::getIsOftenCity, dto.getIsOftenCity());
        }
        if (StringUtils.isNotBlank(dto.getIsHotRegion())) {
            queryWrapper.eq(CityInfoPO::getIsHotRegion, dto.getIsHotRegion());
        }
        List<CityInfoPO> records = cityInfoMapper.selectList(queryWrapper);
        Map<String, List<CityLabelInfoPO>> labelMap = tCityLabelInfoMapper.selectByCityCodeIn(records.stream().map(i -> i.cityCode).collect(Collectors.toList())).stream().collect(Collectors.groupingBy(
                (i -> i.cityCode), Collectors.toList()));
        Map<String, List<DstWtInfoPO>> dstMap = tDstWtInfoMapper.selectbyidIn(records.stream().map(i -> i.dstWtId).collect(Collectors.toList())).stream()
                .collect(Collectors.groupingBy((i -> i.id), Collectors.toList()));
        return records.stream().map(
                i -> {
                    CityExportDTO exportDTO = new CityExportDTO();
                    BeanUtil.copyProperties(i, exportDTO);
                    exportDTO.setIsInternational("D".equals(i.getIsInternational()) ? "国内" : "国际");
                    exportDTO.setStatus("1".equals(i.getStatus()) ? "启用" : "禁用");
                    List<CityLabelInfoPO> cityLabelInfoPOS = labelMap.get(i.cityCode);
                    if (CollectionUtil.isNotEmpty(cityLabelInfoPOS)) {
                        exportDTO.cityLabelName = cityLabelInfoPOS.stream().map(it ->
                                it.cityLabelName
                        ).collect(Collectors.joining(","));
                    }
                    DstWtInfoPO dstWtInfoPO = dstMap.getOrDefault(i.cityCode, new ArrayList<>()).stream().findFirst().orElse(null);
                    if (dstWtInfoPO != null) {
                        new TypeToken<List<DstWtInfoDTO>>() {
                        }.getType();
                        List<DstWtInfoDTO> dstWtInfoDTOS = JsonUtil.jsonToList(dstWtInfoPO.getDstMsg(),
                                new TypeToken<List<DstWtInfoDTO>>() {}.getType());
                        exportDTO.dstWtInfo = Optional.ofNullable(dstWtInfoDTOS).map(dst -> dst.stream().map(it -> it.dstStart + "->" + it.dstEnd).collect(Collectors.joining(","))).orElse(null);
                    }
                    return exportDTO;
                }
        ).collect(Collectors.toList());
    }

    @Override
    public boolean syncJsonAndJs() {
        citySyncStaticFileService.process();
        return true;
    }

    @Autowired
    IFileUploadService fileUploadService;
}
