package com.juneyaoair.ecs.manage.service.dih.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.dih.InventoryHistoryExcel;
import com.juneyaoair.ecs.manage.enums.OpTypeEnum;
import com.juneyaoair.ecs.manage.mapstruct.InventoryHistoryMapStruct;
import com.juneyaoair.ecs.manage.service.dih.IDIHDataService;
import com.juneyaoair.ecs.mongo.MongoUtil;
import com.juneyaoair.ecs.mongo.entity.InventoryHistoryPO;
import com.juneyaoair.ecs.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/8 10:56
 */
@Slf4j
@Service
public class DIHDataService implements IDIHDataService {
    @Autowired
    private MongoUtil mongoUtil;
    /**
     * 本地文件临时存放目录
     */
    @Value("${file.tmpPath:/juneyaoair/tmp/}")
    private String tmpPath;
    // 创建一个固定大小的线程池
    private final ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());


    @Override
    public PageResult<InventoryHistoryPO> queryInventoryHistoryByPage(String flightDate, int pageNo, int pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("flightDate", flightDate);
        Query query = mongoUtil.buildQuery(paramMap);
        //总数量
        long totalCount = mongoUtil.count(query, InventoryHistoryPO.class);
        List<InventoryHistoryPO> inventoryHistoryPOList = mongoUtil.findByPage(query, InventoryHistoryPO.class, pageNo, pageSize);
        PageResult pageResult = new PageResult();
        pageResult.setPageNum(pageNo);
        pageResult.setPageSize(pageSize);
        pageResult.setRows(inventoryHistoryPOList);
        pageResult.setTotal(totalCount);
        return pageResult;
    }

    @Override
    public void downloadInventoryHistory(String flightDateStart, String flightDateEnd, HttpServletResponse response) throws IOException {
        Query query = new Query();
        query.addCriteria(Criteria.where("flightDate").gte(flightDateStart).lte(flightDateEnd));
        // 添加optype的限制条件
        List<String> allowedOptypes = Arrays.asList(OpTypeEnum.OrBkPNR.name(), OpTypeEnum.limitSaleAndOn.name(), OpTypeEnum.permanentRequest.name());
        query.addCriteria(Criteria.where("opType").in(allowedOptypes));
        List<InventoryHistoryPO> inventoryHistoryPOList = mongoUtil.find(query, InventoryHistoryPO.class);
        List<InventoryHistoryExcel> inventoryHistoryExcelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inventoryHistoryPOList)) {
            for (InventoryHistoryPO inventoryHistoryPO : inventoryHistoryPOList) {
                InventoryHistoryExcel inventoryHistoryExcel = InventoryHistoryMapStruct.INSTANCE.toInventoryHistoryExcel(inventoryHistoryPO);
                String opTypeName = inventoryHistoryPO.getOpType();
                try {
                    opTypeName = OpTypeEnum.valueOf(inventoryHistoryPO.getOpType()).getDesc();
                } catch (Exception e) {
                    log.error("OpTypeEnum类型枚举转换失败:", e);
                }
                inventoryHistoryExcel.setOpType(opTypeName);
                inventoryHistoryExcel.setOpTime("'" + inventoryHistoryPO.getOpTime());
                if (StringUtils.isNotBlank(inventoryHistoryPO.getOperation())) {
                    //处理特殊的操作类型
                    if (OpTypeEnum.OrBkPNR.name().equalsIgnoreCase(inventoryHistoryPO.getOpType())) {
                        dealOrBkPNR(inventoryHistoryExcel, inventoryHistoryPO.getOperation());
                    }
                    if (OpTypeEnum.limitSaleAndOn.name().equalsIgnoreCase(inventoryHistoryPO.getOpType())) {
                        dealLimitSaleAndOn(inventoryHistoryExcel, inventoryHistoryPO.getOperation());
                    }
                    if (OpTypeEnum.permanentRequest.name().equalsIgnoreCase(inventoryHistoryPO.getOpType())) {
                        dealLimitPermanentRequest(inventoryHistoryExcel, inventoryHistoryPO.getOperation());
                    }
                }
                inventoryHistoryExcelList.add(inventoryHistoryExcel);
            }
            Map<String, List<InventoryHistoryExcel>> groupedByFlightDate = inventoryHistoryExcelList.stream()
                    .collect(Collectors.groupingBy(InventoryHistoryExcel::getFlightDate));
            List<File> fileList = new ArrayList<>();
            CountDownLatch latch = new CountDownLatch(groupedByFlightDate.size());
            groupedByFlightDate.entrySet().forEach(entry -> {
                String flightDate = entry.getKey();
                List<InventoryHistoryExcel> flightInventoryHistoryExcelList = entry.getValue();
                //调整为代码排序
                flightInventoryHistoryExcelList.stream().sorted(
                        Comparator.comparing(InventoryHistoryExcel::getFlightDate)
                                .thenComparing(InventoryHistoryExcel::getFlightNo)
                                .thenComparing(InventoryHistoryExcel::getOpTime));
                String fileName = flightDate.replaceAll("-", "") + "_dih.csv";
                String filePath = tmpPath + fileName;
                File file = new File(filePath);
                // 判断父级目录是否存在，不存在则创建
                if (!file.getParentFile().exists()) {
                    file.getParentFile().mkdirs();
                }
                executorService.submit(() -> {
                    try {
                        EasyExcel.write(file, InventoryHistoryExcel.class)
                                .excelType(ExcelTypeEnum.CSV)
                                .sheet(fileName)
                                .doWrite(flightInventoryHistoryExcelList);
                        fileList.add(file);
                    } catch (Exception e) {
                        log.error("生成CSV文件时出错: {}", file.getAbsolutePath(), e);
                    } finally {
                        latch.countDown();
                    }
                });
            });

            try {
                // 等待所有任务完成
                latch.await();
            } catch (InterruptedException e) {
                log.error("等待任务完成时被中断", e);
                Thread.currentThread().interrupt();
            }
            //输出压缩文件
            String currentDate = DateUtil.getCurrentDateStr();
            // 设置响应头
            response.setContentType("application/zip");
            response.setCharacterEncoding("utf-8");
            String zipFileName = "dih_" + currentDate + ".zip";
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(zipFileName, "UTF-8"));
            // 创建 ZIP 文件并写入响应输出流
            try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
                for (File file : fileList) {
                    ZipEntry zipEntry = new ZipEntry(file.getName());
                    zos.putNextEntry(zipEntry);
                    try (FileInputStream fis = new FileInputStream(file)) {
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = fis.read(buffer)) > 0) {
                            zos.write(buffer, 0, length);
                        }
                    }
                    zos.closeEntry();
                }
            } finally {
                // 删除临时文件
                for (File file : fileList) {
                    if (file.exists()) {
                        if (file.delete()) {
                            log.info("临时文件已删除: {}", file.getAbsolutePath());
                        } else {
                            log.warn("无法删除临时文件: {}", file.getAbsolutePath());
                        }
                    }
                }
            }
        }
    }

    @Override
    public void cancelInventoryHistory(String flightDateStart, String flightDateEnd) {
        Query query = new Query();
        query.addCriteria(Criteria.where("flightDate").gte(flightDateStart).lte(flightDateEnd));
        mongoUtil.delete(query, InventoryHistoryPO.class);
    }

    private void dealLimitPermanentRequest(InventoryHistoryExcel inventoryHistoryExcel, String operation) {
        StringBuilder desc = new StringBuilder();
        try {
            JSONObject jsonObject = JSON.parseObject(operation);
            inventoryHistoryExcel.setSegment(jsonObject.getString("origin") + jsonObject.getString("destination"));
            List<JSONObject> jsonList = jsonObject.getList("classes", JSONObject.class);
            if (CollectionUtils.isNotEmpty(jsonList)) {
                for (JSONObject json : jsonList) {
                    String permanentRequest = json.getString("permanentRequest");
                    desc.append(json.getString("classId"))
                            .append(" Value:");
                    if ("1".equals(permanentRequest)) {
                        desc.append(" 0->1 ");
                    } else {
                        desc.append(" 1->0 ");
                    }
                }
            }
        } catch (Exception e) {
            desc.append("异常数据，联系技术处理");
        }
        inventoryHistoryExcel.setDesc(desc.toString());
    }

    private void dealLimitSaleAndOn(InventoryHistoryExcel inventoryHistoryExcel, String operation) {
        StringBuilder desc = new StringBuilder();
        try {
            JSONObject jsonObject = JSON.parseObject(operation);
            inventoryHistoryExcel.setSegment(jsonObject.getString("origin") + jsonObject.getString("destination"));
            List<JSONObject> jsonList = jsonObject.getList("classes", JSONObject.class);
            if (CollectionUtils.isNotEmpty(jsonList)) {
                for (JSONObject json : jsonList) {
                    desc.append(json.getString("classId"))
                            .append(" Num:")
                            .append(json.getString("limitSaleOn"))
                            .append("->")
                            .append(json.getString("limit"))
                            .append(" ");
                }
            }
        } catch (Exception e) {
            desc.append("异常数据，联系技术处理");
        }
        inventoryHistoryExcel.setDesc(desc.toString());
    }

    private void dealOrBkPNR(InventoryHistoryExcel inventoryHistoryExcel, String operation) {
        StringBuilder desc = new StringBuilder();
        try {
            List<JSONObject> jsonArray = JSON.parseArray(operation, JSONObject.class);
            if (CollectionUtils.isNotEmpty(jsonArray)) {
                JSONObject jsonObject = jsonArray.get(0);
                inventoryHistoryExcel.setSegment(jsonObject.getString("originLeg"));
                for (JSONObject json : jsonArray) {
                    desc.append(json.getString("compartmentDesigner"))
                            .append(" Num:")
                            .append(json.getString("oldOverBookNumber"))
                            .append("->")
                            .append(json.getString("newOverBookNumber"))
                            .append(" ")
                            .append(json.getString("pnrlocator"));
                }
            }
        } catch (Exception e) {
            desc.append("异常数据，联系技术处理");
        }
        inventoryHistoryExcel.setDesc(desc.toString());
    }
}
