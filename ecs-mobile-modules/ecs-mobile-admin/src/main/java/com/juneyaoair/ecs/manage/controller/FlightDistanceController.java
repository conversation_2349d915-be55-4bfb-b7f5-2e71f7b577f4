package com.juneyaoair.ecs.manage.controller;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.flightdistance.FlightDistancePricedDTO;
import com.juneyaoair.ecs.manage.dto.flightdistance.FlightDistancePricedLogsDTO;
import com.juneyaoair.ecs.manage.service.flightinfo.FlightInfoDistanceService;
import com.juneyaoair.manage.flight.entity.TFlightDistance;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RequestMapping("/flightDistance")
@RestController
@RequiredArgsConstructor
@Api(value = "FlightDistanceController", tags = "航距管理")
@Slf4j
public class FlightDistanceController extends HoBaseController {


    @Resource
    private FlightInfoDistanceService flightInfoDistanceService;


    @ApiOperation(value = "查询", notes = "")
    @PostMapping(value = "query")
    public R<PageResult<FlightDistancePricedDTO>> select(@RequestBody FlightDistancePricedDTO param, HttpServletRequest request) {
        log.info("请求地址{},请求参数：{}", request.getRequestURI(), JSON.toJSONString(param));
        //
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        //
        Page<TFlightDistance> pageInfo = PageHelper.getLocalPage();
        List<FlightDistancePricedDTO> distancePricedDTOS = flightInfoDistanceService.select(param);
        return getPageData(distancePricedDTOS, pageInfo);
    }

    @ApiOperation(value = "添加", notes = "")
    @PostMapping(value = "add")
    public R addPrice(@RequestBody FlightDistancePricedDTO param, HttpServletRequest request) {
        log.info("请求地址{},请求参数：{}", request.getRequestURI(), JSON.toJSONString(param));
        flightInfoDistanceService.add(param);
        return R.ok();
    }


    @ApiOperation(value = "更新", notes = "")
    @PostMapping(value = "update")
    public R updatePrice(@RequestBody FlightDistancePricedDTO param, HttpServletRequest request) {
        log.info("请求地址{},请求参数：{}", request.getRequestURI(), JSON.toJSONString(param));
        flightInfoDistanceService.update(param);
        return R.ok();
    }


    @ApiOperation(value = "启用/禁用", notes = "")
    @PostMapping(value = "pause")
    public R pause(@RequestBody @Validated FlightDistancePricedDTO param, HttpServletRequest request) {
        log.info("请求地址{},请求参数：{}", request.getRequestURI(), JSON.toJSONString(param));
        flightInfoDistanceService.pause(param);
        return R.ok();
    }


    @ApiOperation(value = "查询日志", notes = "")
    @PostMapping(value = "queryLog")
    public R<PageResult<FlightDistancePricedLogsDTO>> queryPrice(@RequestBody FlightDistancePricedDTO param, HttpServletRequest request) {
        log.info("请求地址{},请求参数：{}", request.getRequestURI(), JSON.toJSONString(param));
        //
        PageDomain pageDomain = TableSupport.buildPageRequest();
        startPage(pageDomain);
        //

        List<FlightDistancePricedLogsDTO> result = flightInfoDistanceService.queryLog(param);
        return getPageData(result, pageDomain);
    }


}
