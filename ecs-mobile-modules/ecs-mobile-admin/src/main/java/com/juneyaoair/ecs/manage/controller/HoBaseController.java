package com.juneyaoair.ecs.manage.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.util.HoPageUtils;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.ecs.utils.TraceUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableDataInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 公共controller
 * @date 2023/5/4 16:59
 */
@Slf4j
public class HoBaseController extends BaseController {



    /**
     * 设置请求分页数据
     */
    protected void startPage(PageDomain pageDomain) {
        HoPageUtils.startPageBy(pageDomain);
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected <T> R<PageResult<T>> getPageData(List<T> list, PageDomain pageDomain) {
        TableDataInfo tableDataInfo = getDataTable(list);
        PageResult pageResult = new PageResult();
        pageResult.setRows(tableDataInfo.getRows());
        pageResult.setTotal(tableDataInfo.getTotal());
        pageResult.setPageNum(pageDomain.getPageNum());
        pageResult.setPageSize(pageDomain.getPageSize());
        return R.ok(pageResult);
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected <T> R<PageResult<T>> getPageData(List<T> list, Page pageInfo) {
        PageResult pageResult = new PageResult();
        pageResult.setRows(list);
        pageResult.setTotal(pageInfo.getTotal());
        pageResult.setPageNum(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        return R.ok(pageResult);
    }

    /**
     * @param pageInfo
     * @param pageDomain
     * @return
     */
    protected <T> R<PageResult<T>> getPageData(PageInfo<T> pageInfo, PageDomain pageDomain) {
        PageResult pageResult = new PageResult();
        pageResult.setRows(pageInfo.getList());
        pageResult.setTotal(pageInfo.getTotal());
        pageResult.setPageNum(pageDomain.getPageNum());
        pageResult.setPageSize(pageDomain.getPageSize());
        return R.ok(pageResult);
    }


    /**
     * list 手动分页，模拟分页效果
     *
     * @param currentPage
     * @param pageSize
     * @param list
     * @param <T>
     * @return
     */
    public <T> PageInfo<T> dealPageInfo(int currentPage, int pageSize, List<T> list) {
        int total = list.size();
        //总数量超过每页记录数
        if (total >= pageSize) {
            int toIndex = currentPage * pageSize;
            int startIndex = (currentPage - 1) * pageSize;
            if (startIndex > total) {
                list = new ArrayList<>();
            } else {
                if (toIndex > total) {
                    toIndex = total;
                }
                list = list.subList(startIndex, toIndex);
            }
        }
        Page<T> page = new Page<>(currentPage, pageSize);
        page.addAll(list);
        //page.setPages((total + pageSize - 1) / pageSize);
        page.setTotal(total);
        PageInfo<T> pageInfo = new PageInfo<>(page);
        return pageInfo;
    }

    protected void initContext() {
        Context.setContext(new Context(TraceUtil.getTraceId()));
    }
}
