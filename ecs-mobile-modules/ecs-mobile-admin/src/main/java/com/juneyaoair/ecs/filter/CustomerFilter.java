package com.juneyaoair.ecs.filter;

import com.alibaba.fastjson.JSON;
import com.juneyaoair.ecs.utils.MdcUtils;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.esc.manage.util.IPUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CustomerFilter implements Filter {

    @Autowired
    private ExcludeFilterUrlBean excludeFilterUrlBean;

    //Filter初始化
    @Override
    public void init(FilterConfig arg0) {

    }

    //Filter销毁
    @Override
    public void destroy() {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            // 对request、response进行一些预处理
            request.setCharacterEncoding("UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setContentType("text/html;charset=UTF-8");
            HttpServletRequest servletRequest = (HttpServletRequest) request;
            String servletPath = servletRequest.getServletPath();
            if (servletRequest.getServletPath().contains("swagger") ||
                servletRequest.getServletPath().contains("api-docs") ||
                excludeFilterUrlBean.getExcludeFilterUrl().contains(servletPath)) {
                chain.doFilter(request, response);
                return;
            }
            //获取方法
            String method = servletRequest.getMethod();
            //获取IP地址
            String clientIp = IPUtil.getIpAddr(servletRequest);

            String requestId = StringUtils.isBlank(servletRequest.getHeader("bizNo")) ? UUID.randomUUID().toString() : servletRequest.getHeader("bizNo");
            MdcUtils.setRequestId(requestId);
            long startTime = System.currentTimeMillis();

            // 下载操作
            if (servletPath.contains("download") || servletPath.contains("export") ||
                servletPath.contains("import") || servletPath.contains("upload") ||
                servletPath.contains("Excel") || servletPath.contains("excel") ||
                servletPath.contains("parseImage") || servletPath.contains("addResource")) {
                log.info("【方法】:{} 操作人：{} 来源IP：{} 处理ID：{}", servletPath, SecurityUtils.getUsername(), clientIp, MdcUtils.getRequestId());
                chain.doFilter(request, response);
                long endTime = System.currentTimeMillis();
                long interval = endTime - startTime;
                log.info("【方法】:{} 处理ID：{} 【响应时间】:{}", servletRequest.getServletPath(), MdcUtils.getRequestId(), interval);
                return;
            }
            //请求体
            String requestBody = getRequestBody(servletRequest);
            //记录日志
            log.info("【方法】:{} 操作人：{} 来源IP：{} 处理ID：{} 【请求信息】:{}", servletPath, SecurityUtils.getUsername(), clientIp, MdcUtils.getRequestId(), requestBody);
            WrapperedRequest wrapRequest = new WrapperedRequest(servletRequest, requestBody);
            WrapperedResponse wrapResponse = new WrapperedResponse((HttpServletResponse) response);
            //设置允许跨域的配置
            //这里填写你允许进行跨域的主机ip（正式上线时可以动态配置具体允许的域名和IP）
            wrapResponse.setHeader("Access-Control-Allow-Origin", "*");
            // 允许的访问方法
            wrapResponse.setHeader("Access-Control-Allow-Methods", "POST, GET, PUT, OPTIONS, DELETE, PATCH");
            // Access-Control-Max-Age 用于 CORS 相关配置的缓存
            wrapResponse.setHeader("Access-Control-Max-Age", "3600");
            wrapResponse.setHeader("Access-Control-Allow-Headers", "token,Origin, X-Requested-With, Content-Type, Accept");
            if ("OPTIONS".equals(method)) {
                wrapResponse.setStatus(HttpServletResponse.SC_OK);
            } else {
                String responseBody = "";
                try {
                    // 执行目标资源，放行
                    chain.doFilter(wrapRequest, wrapResponse);
                    //请求返回结果日志记录
                    byte[] data = wrapResponse.getResponseData();
                    responseBody = new String(data, "utf-8");
                } catch (HoServiceException he) {
                    R<Object> r = null != he.getCode() ? R.fail(he.getCode(), he.getMessage()) : R.fail(he.getMessage());
                    if (he.getDetailError() != null) {
                        r.setData(he.getDetailError());
                    }
                    responseBody = JSON.toJSONString(r);
                }
                long endTime = System.currentTimeMillis();
                long interval = endTime - startTime;
                writeResponse(response, responseBody);
                log.info("【方法】:{} 处理ID：{}【返回信息】:{}【响应时间】:{}", servletRequest.getServletPath(), MdcUtils.getRequestId(), responseBody, interval);
            }
        } finally {
            MdcUtils.clear();
        }
    }

    /**
     * @param req
     * @return
     */
    private String getRequestBody(HttpServletRequest req) {
        try {
            BufferedReader reader = req.getReader();
            StringBuffer sb = new StringBuffer();
            String line = null;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            String json = sb.toString();
            return json;
        } catch (IOException e) {

        }
        return "";
    }

    //输出信息
    private void writeResponse(ServletResponse response, String responseString) throws IOException {
        PrintWriter out = response.getWriter();
        out.print(responseString);
        out.flush();
        out.close();
    }
}