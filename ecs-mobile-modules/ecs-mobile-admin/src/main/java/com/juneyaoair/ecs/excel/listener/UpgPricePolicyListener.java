package com.juneyaoair.ecs.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.google.common.collect.Sets;
import com.juneyaoair.ecs.excel.bean.UpgPricePolicyUpload;
import com.juneyaoair.ecs.upg.enums.UPG_FLIGHT_TIME_ENUM;
import com.juneyaoair.ecs.utils.ValidatorUtils;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @created 2023/8/21 13:18
 */
public class UpgPricePolicyListener extends AnalysisEventListener<UpgPricePolicyUpload> {

    /** 星期 */
    private final static Set<String> WEEK_DAY_SET = Sets.newHashSet("1", "2", "3", "4", "5", "6", "7");

    @Getter
    private Set<UpgPricePolicyUpload> createUpgPricePolicyParamList = Sets.newHashSet();

    public UpgPricePolicyListener() {
        super();
        createUpgPricePolicyParamList.clear();
    }

    @Override
    public void invoke(UpgPricePolicyUpload upgPricePolicyUpload, AnalysisContext analysisContext) {
        ValidatorUtils.valid(upgPricePolicyUpload);
        if (StringUtils.isNotBlank(upgPricePolicyUpload.getWeekDays())) {
            Set<String> weekDaySet = Sets.newHashSet(upgPricePolicyUpload.getWeekDays().split(","));
            for (String weekDay : weekDaySet) {
                if (!WEEK_DAY_SET.contains(weekDay)) {
                    throw new HoServiceException("生效周期：1~7表示周一到周日(多个用,分割)");
                }
            }
        }
        if (StringUtils.isNotBlank(upgPricePolicyUpload.getFlightTimes())) {
            Set<String> flightTimeSet = Sets.newHashSet(upgPricePolicyUpload.getFlightTimes().split(","));
            for (String flightTime : flightTimeSet) {
                try {
                    UPG_FLIGHT_TIME_ENUM.valueOf(flightTime);
                } catch (Exception e) {
                    throw new HoServiceException("航班时刻 A:0-6点 B:6-12点 C:12-18点 D:18-24(多个用,分割)");
                }
            }
        }
        createUpgPricePolicyParamList.add(upgPricePolicyUpload);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
