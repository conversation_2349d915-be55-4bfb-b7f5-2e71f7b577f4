package com.juneyaoair.ecs.manage.service.syncfile.impl;

import com.juneyaoair.ecs.manage.service.syncfile.IAirportSyncJsFileService;
import com.juneyaoair.manage.b2c.entity.AirportInfoPO;
import com.juneyaoair.manage.b2c.mapper.TAirportInfoMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 机场同步js文件
 */
@Service
public class AirportSyncJsFileServiceImpl extends SyncJsFileService implements IAirportSyncJsFileService {
    @Resource
    private TAirportInfoMapper airportInfoMapper;
    private final static String contentName = "airport";

    @Override
    protected String getContentName() {
        return contentName;
    }

    @Override
    public String prepareData() {
        List<AirportInfoPO> airportPOs = airportInfoMapper.selectByAll(null);
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("var allAirport=new Array();\r\n");
        for (int i = 0; i < airportPOs.size(); i++) {
            AirportInfoPO airportInfoPO = airportPOs.get(i);
            String airportCode = airportInfoPO.getAirportCode();
            String airportName = airportInfoPO.getAirportName();
            String checkincounter = StringUtils.isNotBlank(airportInfoPO.getCheckincounter()) ? airportInfoPO.getCheckincounter() : "";
            String firstclasscheckincounter = StringUtils.isNotBlank(airportInfoPO.getFirstclasscheckincounter()) ? airportInfoPO.getFirstclasscheckincounter() : "";
            String checkinbegintime = StringUtils.isNotBlank(airportInfoPO.getCheckinbegintime()) ? airportInfoPO.getCheckinbegintime() : "";
            String checkinendtime = StringUtils.isNotBlank(airportInfoPO.getCheckinendtime()) ? airportInfoPO.getCheckinendtime() : "";
            stringBuffer.append("allAirport[" + i + "]=new Array('" + airportCode + "','" + airportName + "','" + checkincounter + "','" + firstclasscheckincounter + "','" + checkinbegintime + "','" + checkinendtime + "');\r\n");
        }
        return stringBuffer.toString();
    }

    @Override
    public boolean process() {
        mainProcess();
        return true;
    }
}
