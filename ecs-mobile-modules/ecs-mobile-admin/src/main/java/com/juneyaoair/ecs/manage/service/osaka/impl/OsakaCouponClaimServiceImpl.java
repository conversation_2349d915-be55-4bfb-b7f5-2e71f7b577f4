package com.juneyaoair.ecs.manage.service.osaka.impl;

import com.juneyaoair.ecs.manage.dto.activity.request.osaka.OsakaCouponClaimQueryRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.osaka.OsakaCouponClaimResponse;
import com.juneyaoair.ecs.manage.service.osaka.IOsakaCouponClaimService;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.service.OsakaTransferPOService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName OsakaCouponClaimServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/6/23 14:07
 * @Version 1.0
 */

@Service
@SuppressWarnings("all")
public class OsakaCouponClaimServiceImpl implements IOsakaCouponClaimService {

    @Autowired
    private OsakaTransferPOService osakaTransferPOService;

    @Override
    public List<OsakaCouponClaimResponse> doCouponClaimListQuery(OsakaCouponClaimQueryRequest osakaCouponClaimQueryRequest) {
        //查询时间间隔是否正确 是否小于0或者大于3个月
        int signUpDiffDays = DateUtil.dateDiff(osakaCouponClaimQueryRequest.getClaimStartTime(), osakaCouponClaimQueryRequest.getClaimEndTime(), DateUtil.DATE_FORMATE);
        if (signUpDiffDays <= 0) {
            throw new HoServiceException("查询时间间隔非法");
        }
        if (signUpDiffDays > 90) {
            throw new HoServiceException("查询时间间隔过大(已超过90个自然日),请缩小查询范围.");
        }
        List<OsakaCouponClaimResponse> osakaCouponClaimResponses = osakaTransferPOService.doCouponClaimListQuery(osakaCouponClaimQueryRequest);
        if (CollectionUtils.isEmpty(osakaCouponClaimResponses)) {
            return null;
        }
        osakaCouponClaimResponses.forEach(el -> {
            el.setFfpCardNo(toDecrypFfpCardNo(el.getFfpCardNo()));
            el.setTicketNo(toDecrypTicketNo(el.getTicketNo()));
            el.setPhoneNumber(toDecrypPhoneNumber(el.getPhoneNumber()));
            el.setCouponCode(toDecrypCouponCode(el.getCouponCode()));
        });
        return osakaCouponClaimResponses;
    }

    /**
     * <AUTHOR>
     * @Description 券码脱敏
     * @Date 11:02 2025/6/24
     * @param couponCode 券码脱敏
     * @return java.lang.String
     **/
    private String toDecrypCouponCode(String couponCode) {
        // 处理空值或长度不为10的情况
        if (couponCode == null || couponCode.length() != 10) {
            return couponCode;
        }

        // 使用StringBuilder构建结果
        return new StringBuilder(couponCode)
                .replace(2, 8, "******")  // 替换索引3到8的字符（共6位）
                .toString();
    }

    /**
     * @param ticketNo
     * @return java.lang.String
     * <AUTHOR>
     * @Description 票号脱敏
     * @Date 10:58 2025/6/24
     **/
    private String toDecrypTicketNo(String ticketNo) {
        // 处理空值或长度不为13的情况
        if (ticketNo == null || ticketNo.length() != 13) {
            return ticketNo;
        }

        // 使用StringBuilder构建结果
        return new StringBuilder(ticketNo)
                .replace(3, 9, "******")  // 替换索引3到8的字符（共6位）
                .toString();
    }

    /**
     * @param ffpCardNo
     * @return java.lang.String
     * <AUTHOR>
     * @Description 会员卡号脱敏
     * @Date 10:50 2025/6/24
     **/
    private String toDecrypFfpCardNo(String ffpCardNo) {
        if (StringUtils.isEmpty(ffpCardNo) || ffpCardNo.length() <= 6) {
            return ffpCardNo;
        }
        int length = ffpCardNo.length();
        int middleLength = length - 6; // 计算需要替换的中间部分长度
        // 使用StringBuilder构建结果
        StringBuilder masked = new StringBuilder();
        masked.append(ffpCardNo, 0, 3);           // 添加前三位
        for (int i = 0; i < middleLength; i++) {
            masked.append('*');               // 添加中间的星号
        }
        masked.append(ffpCardNo, length - 3, length); // 添加后三位
        return masked.toString();
    }

    /**
     * @param phoneNumber 原始手机号
     * @return java.lang.String
     * <AUTHOR>
     * @Description 手机号脱敏
     * @Date 15:37 2025/6/23
     **/
    private String toDecrypPhoneNumber(String phoneNumber) {
        if (StringUtils.isEmpty(phoneNumber)) {
            return phoneNumber;
        }
        if (phoneNumber == null || phoneNumber.length() != 11) {
            return phoneNumber; // 非标准手机号不处理
        }
        return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(7);
    }
}
