package com.juneyaoair.ecs.manage.service.pet;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.juneyaoair.ecs.manage.dto.pet.PetCounterDto;
import com.juneyaoair.ecs.manage.dto.pet.PetFlightPlanDto;
import com.juneyaoair.ecs.manage.dto.pet.PetFlightPlanParam;
import com.juneyaoair.ecs.manage.enums.YorNEnum;
import com.juneyaoair.ecs.manage.mapstruct.PetCounterToPOMapStruct;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.ecs.utils.HOStringUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.I18nDictionaryValuePO;
import com.juneyaoair.manage.b2c.entity.pet.PetCounterPO;
import com.juneyaoair.manage.b2c.entity.pet.PetFlightPlanPO;
import com.juneyaoair.manage.b2c.service.IPetCounterService;
import com.juneyaoair.manage.b2c.service.IPetFlightPlanService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/12 16:09
 */
@Service
public class PetBizService {
    @Autowired
    private IPetCounterService petCounterService;
    @Autowired
    private IPetFlightPlanService petFlightPlanService;

    public void addPetCounter(PetCounterDto petCounterDto) {
        if (StringUtils.isBlank(petCounterDto.getCityName())) {
            throw new HoServiceException("城市名称不能为空");
        }
        if (StringUtils.isBlank(petCounterDto.getAirportCode())) {
            throw new HoServiceException("机场三字码不能为空");
        }
        if (StringUtils.isBlank(petCounterDto.getAirportCounter())) {
            throw new HoServiceException("受理柜台信息不能为空");
        }
        LambdaQueryWrapper<PetCounterPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PetCounterPO::getAirportCode, petCounterDto.getAirportCode());
        PetCounterPO existPetCounter = petCounterService.getOne(wrapper);
        if (existPetCounter != null) {
            throw new HoServiceException("当前机场柜台信息已维护，请进行编辑操作");
        }
        PetCounterPO petCounterPO = PetCounterToPOMapStruct.INSTANCE.toPetCounterToPO(petCounterDto);
        Date now = new Date();
        petCounterPO.setId(HOStringUtil.newGUID());
        //默认禁用状态
        petCounterPO.setDisableFlag(YorNEnum.Y.name());
        petCounterPO.setCreatedBy(StringUtils.isBlank(SecurityUtils.getUsername()) ? "system" : SecurityUtils.getUsername());
        petCounterPO.setCreatedTime(now);
        petCounterPO.setUpdatedBy(StringUtils.isBlank(SecurityUtils.getUsername()) ? "system" : SecurityUtils.getUsername());
        petCounterPO.setUpdatedTime(now);
        petCounterService.save(petCounterPO);
    }

    public List<PetCounterPO> queryList(PetCounterDto petCounterDto) {
        LambdaQueryWrapper<PetCounterPO> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(petCounterDto.getCityName())) {
            wrapper.like(PetCounterPO::getCityName, petCounterDto.getCityName());
        }
        wrapper.orderByDesc(PetCounterPO::getUpdatedTime);
        return petCounterService.list(wrapper);
    }

    public PetCounterPO findPetCounter(PetCounterDto petCounterDto) {
        if (StringUtils.isBlank(petCounterDto.getId())) {
            throw new HoServiceException("记录编号不可为空");
        }
        LambdaQueryWrapper<PetCounterPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PetCounterPO::getId, petCounterDto.getId());
        PetCounterPO petCounterPO = petCounterService.getOne(wrapper);
        if (petCounterPO == null) {
            throw new HoServiceException("未查询到相关信息");
        }
        return petCounterPO;
    }

    public void disablePetCounter(PetCounterDto petCounterDto) {
        if (petCounterDto.getDisableFlag() == null) {
            throw new HoServiceException("状态不可为空");
        }
        PetCounterPO petCounterPO = findPetCounter(petCounterDto);
        if (petCounterPO.getDisableFlag().equalsIgnoreCase(petCounterDto.getDisableFlag().name())) {
            if (YorNEnum.Y.equals(petCounterDto.getDisableFlag())) {
                throw new HoServiceException("信息已处于禁用状态");
            } else {
                throw new HoServiceException("信息已处于启用状态");
            }
        }
        petCounterPO.setDisableFlag(petCounterDto.getDisableFlag().name());
        petCounterPO.setUpdatedBy(StringUtils.isBlank(SecurityUtils.getUsername()) ? "system" : SecurityUtils.getUsername());
        petCounterPO.setUpdatedTime(new Date());
        boolean flag = petCounterService.updateById(petCounterPO);
        if (!flag) {
            throw new HoServiceException("数据更新失败");
        }
    }

    public void editPetCounter(PetCounterDto petCounterDto) {
        if (StringUtils.isBlank(petCounterDto.getId())) {
            throw new HoServiceException("记录编号不可为空");
        }
        PetCounterPO existPetCounterPO = findPetCounter(petCounterDto);
        if (YorNEnum.N.name().equalsIgnoreCase(existPetCounterPO.getDisableFlag())) {
            throw new HoServiceException("请先下线后再编辑");
        }
        // 构造更新条件
        LambdaUpdateWrapper<PetCounterPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PetCounterPO::getId, petCounterDto.getId());
        PetCounterPO petCounterPO = PetCounterToPOMapStruct.INSTANCE.toPetCounterToPO(petCounterDto);
        petCounterPO.setUpdatedBy(StringUtils.isBlank(SecurityUtils.getUsername()) ? "system" : SecurityUtils.getUsername());
        petCounterPO.setUpdatedTime(new Date());
        // 指定需要更新的字段（可选多个）
        updateWrapper
                .set(StringUtils.isNotBlank(petCounterDto.getCityName()), PetCounterPO::getCityName, petCounterDto.getCityName())
                .set(StringUtils.isNotBlank(petCounterDto.getAirportCode()), PetCounterPO::getAirportCode, petCounterDto.getAirportCode())
                .set(StringUtils.isNotBlank(petCounterDto.getAirportName()), PetCounterPO::getAirportName, petCounterDto.getAirportName())
                .set(StringUtils.isNotBlank(petCounterDto.getAirportCounter()), PetCounterPO::getAirportCounter, petCounterDto.getAirportCounter())
                .set(petCounterDto.getDisableFlag() != null, PetCounterPO::getDisableFlag, petCounterDto.getDisableFlag())
                .set(PetCounterPO::getUpdatedBy, petCounterPO.getUpdatedBy())
                .set(PetCounterPO::getUpdatedTime, petCounterPO.getUpdatedTime());
        boolean flag = petCounterService.update(updateWrapper);
        if (!flag) {
            throw new HoServiceException("数据更新失败");
        }
    }


    public void addPetFlightPlan(PetFlightPlanDto petFlightPlanDto) {
        PetFlightPlanPO petFlightPlanPO = PetCounterToPOMapStruct.INSTANCE.toPetFlightPlanPO(petFlightPlanDto);
        Date now = new Date();
        petFlightPlanPO.setId(HOStringUtil.newGUID());
        petFlightPlanPO.setDeleteFlag(YorNEnum.N.name());
        petFlightPlanPO.setCreatedBy(StringUtils.isBlank(SecurityUtils.getUsername()) ? "system" : SecurityUtils.getUsername());
        petFlightPlanPO.setCreatedTime(now);
        petFlightPlanPO.setUpdatedBy(StringUtils.isBlank(SecurityUtils.getUsername()) ? "system" : SecurityUtils.getUsername());
        petFlightPlanPO.setUpdatedTime(now);
        petFlightPlanService.save(petFlightPlanPO);
    }

    public List<PetFlightPlanPO> queryPetFlightPlanList(PetFlightPlanParam petFlightPlanDto) {
        LambdaQueryWrapper<PetFlightPlanPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PetFlightPlanPO::getDeleteFlag,YorNEnum.N.name())
                .eq(StringUtils.isNotBlank(petFlightPlanDto.getDepAirport()), PetFlightPlanPO::getDepAirport, petFlightPlanDto.getDepAirport())
                .eq(StringUtils.isNotBlank(petFlightPlanDto.getArrAirport()), PetFlightPlanPO::getArrAirport, petFlightPlanDto.getArrAirport())
                .ge(StringUtils.isNotBlank(petFlightPlanDto.getFlightStart()), PetFlightPlanPO::getFlightEnd, DateUtil.toDate(petFlightPlanDto.getFlightStart(), DateUtil.DATE_FORMATE_YYYY_MM_DD))
                .le(StringUtils.isNotBlank(petFlightPlanDto.getFlightEnd()), PetFlightPlanPO::getFlightStart, DateUtil.toDate(petFlightPlanDto.getFlightEnd(), DateUtil.DATE_FORMATE_YYYY_MM_DD));
        if (StringUtils.isNotBlank(petFlightPlanDto.getFlightNo())) {
            wrapper.and(wrapperOr -> {
                wrapperOr.like(PetFlightPlanPO::getFlightNo, petFlightPlanDto.getFlightNo())
                        .or()
                        .eq(PetFlightPlanPO::getFlightNo, "ALL");
            });
        }
        wrapper.orderByDesc(PetFlightPlanPO::getUpdatedTime)
                .orderByDesc(PetFlightPlanPO::getFlightEnd)
                .orderByDesc(PetFlightPlanPO::getFlightStart);
        return petFlightPlanService.list(wrapper);
    }

    public PetFlightPlanPO findPetFlightPlan(PetFlightPlanParam petFlightPlanParam) {
        if (StringUtils.isBlank(petFlightPlanParam.getId())) {
            throw new HoServiceException("记录编号不可为空");
        }
        LambdaQueryWrapper<PetFlightPlanPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PetFlightPlanPO::getId, petFlightPlanParam.getId());
        PetFlightPlanPO petFlightPlanPO = petFlightPlanService.getOne(wrapper);
        if (petFlightPlanPO == null) {
            throw new HoServiceException("未查询到相关信息");
        }
        return petFlightPlanPO;
    }

    public void switchPetFlightPlanStatus(PetFlightPlanParam petFlightPlanParam) {
        if (petFlightPlanParam.getDeleteFlag() == null) {
            throw new HoServiceException("状态不可为空");
        }
        PetFlightPlanPO petFlightPlanPO = findPetFlightPlan(petFlightPlanParam);
        if (petFlightPlanPO.getDeleteFlag().equalsIgnoreCase(petFlightPlanParam.getDeleteFlag().name())) {
            if (YorNEnum.Y.equals(petFlightPlanParam.getDeleteFlag())) {
                throw new HoServiceException("信息已处于禁用状态");
            } else {
                throw new HoServiceException("信息已处于启用状态");
            }
        }
        petFlightPlanPO.setDeleteFlag(petFlightPlanParam.getDeleteFlag().name());
        petFlightPlanPO.setUpdatedBy(StringUtils.isBlank(SecurityUtils.getUsername()) ? "system" : SecurityUtils.getUsername());
        petFlightPlanPO.setUpdatedTime(new Date());
        boolean flag = petFlightPlanService.updateById(petFlightPlanPO);
        if (!flag) {
            throw new HoServiceException("数据更新失败");
        }
    }

    public void editPetFlightPlan(PetFlightPlanDto petFlightPlanDto) {
        if (StringUtils.isBlank(petFlightPlanDto.getId())) {
            throw new HoServiceException("记录编号不可为空");
        }
        // 构造更新条件
        LambdaUpdateWrapper<PetFlightPlanPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PetFlightPlanPO::getId, petFlightPlanDto.getId());
        PetFlightPlanPO petFlightPlanPO = PetCounterToPOMapStruct.INSTANCE.toPetFlightPlanPO(petFlightPlanDto);
        petFlightPlanPO.setUpdatedBy(StringUtils.isBlank(SecurityUtils.getUsername()) ? "system" : SecurityUtils.getUsername());
        petFlightPlanPO.setUpdatedTime(new Date());
        // 指定需要更新的字段（可选多个）
        updateWrapper
                .set(StringUtils.isNotBlank(petFlightPlanDto.getFlightStart()), PetFlightPlanPO::getFlightStart, DateUtil.toDate(petFlightPlanDto.getFlightStart(), DateUtil.DATE_FORMATE_YYYY_MM_DD))
                .set(StringUtils.isNotBlank(petFlightPlanDto.getFlightEnd()), PetFlightPlanPO::getFlightEnd, DateUtil.toDate(petFlightPlanDto.getFlightEnd(), DateUtil.DATE_FORMATE_YYYY_MM_DD))
                .set(StringUtils.isNotBlank(petFlightPlanDto.getDepAirport()), PetFlightPlanPO::getDepAirport, petFlightPlanDto.getDepAirport())
                .set(StringUtils.isNotBlank(petFlightPlanDto.getDepAirportName()), PetFlightPlanPO::getDepAirportName, petFlightPlanDto.getDepAirportName())
                .set(StringUtils.isNotBlank(petFlightPlanDto.getArrAirport()), PetFlightPlanPO::getArrAirport, petFlightPlanDto.getArrAirport())
                .set(StringUtils.isNotBlank(petFlightPlanDto.getArrAirportName()), PetFlightPlanPO::getArrAirportName, petFlightPlanDto.getArrAirportName())
                .set(StringUtils.isNotBlank(petFlightPlanDto.getFlightNo()), PetFlightPlanPO::getFlightNo, petFlightPlanDto.getFlightNo())
                .set(petFlightPlanDto.getFlightQuota() != null, PetFlightPlanPO::getFlightQuota, petFlightPlanDto.getFlightQuota())
                .set(petFlightPlanDto.getPrice() != null, PetFlightPlanPO::getPrice, petFlightPlanDto.getPrice())
                .set(PetFlightPlanPO::getUpdatedBy, petFlightPlanPO.getUpdatedBy())
                .set(PetFlightPlanPO::getUpdatedTime, petFlightPlanPO.getUpdatedTime());
        boolean flag = petFlightPlanService.update(updateWrapper);
        if (!flag) {
            throw new HoServiceException("数据更新失败");
        }
    }
}
