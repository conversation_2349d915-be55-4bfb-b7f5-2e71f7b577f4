# Tomcat
server:
  port: 9202
# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.juneyaoair.**.entity
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  #开启sql日志
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
# Spring
spring:
  application:
    # 应用名称
    name: ecs-mobile-admin-uat
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false
  config:
    activate:
      on-profile: prd
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        #server-addr: 10.12.254.11:8848
        server-addr: m-nacos.juneyaoair.com:8848
        # 命名空间
        namespace: ecs-prd
        username: ecsmaster
        password: gJPbGh5f
      config:
        # 服务注册地址
        #server-addr: 10.12.254.11:8848
        server-addr: m-nacos.juneyaoair.com:8848
        # 命名空间
        namespace: ecs-prd
        username: ecsmaster
        password: gJPbGh5f
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
