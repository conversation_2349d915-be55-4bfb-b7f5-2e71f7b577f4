<!DOCTYPE html>
<html lang="en" xmlns:th="http://wwww.thymeleaf.org">

<head>
    <meta charset="utf-8" />
    <title>吉祥航空</title>
    <meta name="description" content="国内机票预订|特价机票预订|网上机票预订" />
    <meta name="keywords" content="上海吉祥航空有限公司是著名民营企业均瑶集团的全资子公司。吉祥航空以全新飞机、精确定位、标准服务享誉业界。吉祥航空官方购票网站为旅客提供国内机票预订、电子客票销售、特价机票销售等多种优惠促销活动。"
    />
    <meta name="viewport" content="target-densitydpi=device-dpi, wicontainermain
  dth=device-width, initial-scale=1, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <style>
        @charset "UTF-8";
        body {
          -webkit-box-sizing: border-box;
          -ms-box-sizing: border-box
        }

        body,
        pre {
          font-family: "Microsoft Yahei", "PingFang SC", SFUIDisplay, "Hiragino Sans GB", "Source Han Sans", "WenQuanYi Micro Hei", sans-serif
        }

        .bdBottom, .bdLeft, .bdRight, .bdTop, .border, .border-1px, body {
          position: relative
        }

        body, html {
          height: 100%;
          width: 100%
        }

        html {
          -webkit-text-size-adjust: none;
          -webkit-tap-highlight-color: transparent;
          -webkit-user-select: none;
          -webkit-touch-callout: none;
          -webkit-font-smoothing: antialiased
        }

        body {
          background: #f5f5f5;
          color: #000;
          overflow-x: hidden;
          -moz-box-sizing: border-box;
          -o-box-sizing: border-box;
          box-sizing: border-box
        }
        a {
          text-decoration: none
        }

        pre {
          word-break: break-word;
          text-align: justify;
          white-space: pre-wrap
        }

        body, dd, dl, dt, h1, h2, h3, h4, h5, h6, html, li, p, ul {
          padding: 0;
          margin: 0;
          outline: 0
        }

        h1, h2, h3, h4, h5, h6, strong {
          font-weight: 400
        }

        li, ul {
          list-style: none
        }

        img {
          vertical-align: middle
        }
        body {
          width: 100%;
          background: #F6F6F6;
          display: flex;
          flex-direction: column;
          align-items: center;
          line-height: 1.5;
        }
        .notice-main {
          width: 100%;
          max-width: 1360px;
          background: #fff;
          margin: 0 auto;
          padding: 30px;
          box-sizing: border-box;
        }
        .notice-title {
          font-weight: bold;
          font-size: 24px;
          color: #333333;
        }
        .notice-date {
          font-size: 14px;
          margin-top: 16px;
          color: #666666;
        }
        .notice-line {
          margin-top: 20px;
          border-bottom: 1px solid #E6E6E6;
        }
        .notice-con {
          margin-top: 28px;
          font-size: 14px;
          color: #333333;
          line-height: 1.5;
        }
        .notice-normal:not(:first-child) {
          text-indent: 2ch;
        }
        .textRight {
          text-align: right;
        }
        p {
          margin-bottom: 30px;
        }
        @media (max-width: 599px) {
          .notice-main {
            padding: 16px;
          }
          .notice-title {
            font-size: 18px;
          }
          .notice-date {
            margin-top: 4px;
          }
          .notice-con {
            margin-top: 16px;
            font-size: 12px;
          }
          p {
            margin-bottom: 16px;
          }
        }
    </style>
</head>
<body>
<div class="notice-main">
    <div class="notice-title" th:text="${title}"></div>
    <div class="notice-date" th:text="${time}"></div>
    <div class="notice-line"></div>
    <div class="notice-con" th:utext="${body}"></div>
</div>
</body>
</html>