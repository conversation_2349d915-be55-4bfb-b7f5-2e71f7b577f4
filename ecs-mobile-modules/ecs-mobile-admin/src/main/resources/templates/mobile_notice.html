<!DOCTYPE html>
<html lang="en" xmlns:th="http://wwww.thymeleaf.org">

<head>
  <meta charset="utf-8" />
	<title>吉祥航空</title>
	<meta name="description" content="国内机票预订|特价机票预订|网上机票预订" />
	<meta name="keywords" content="上海吉祥航空有限公司是著名民营企业均瑶集团的全资子公司。吉祥航空以全新飞机、精确定位、标准服务享誉业界。吉祥航空官方购票网站为旅客提供国内机票预订、电子客票销售、特价机票销售等多种优惠促销活动。"
	/>
  <meta name="viewport" content="target-densitydpi=device-dpi, wicontainermain
  dth=device-width, initial-scale=1, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0, viewport-fit=cover">
	<meta name="apple-mobile-web-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-status-bar-style" content="black" />
	<meta name="format-detection" content="telephone=no" />
  <style>
    @charset "UTF-8";
    .container,
    body {
      -webkit-box-sizing: border-box;
      -ms-box-sizing: border-box
    }

    body,
    pre {
      font-family: "Microsoft Yahei", "PingFang SC", SFUIDisplay, "Hiragino Sans GB", "Source Han Sans", "WenQuanYi Micro Hei", sans-serif
    }

    .bdBottom, .bdLeft, .bdRight, .bdTop, .border, .border-1px, body {
      position: relative
    }

    body, html {
      height: 100%;
      width: 100%
    }

    html {
      -webkit-text-size-adjust: none;
      -webkit-tap-highlight-color: transparent;
      -webkit-user-select: none;
      -webkit-touch-callout: none;
      -webkit-font-smoothing: antialiased
    }

    body {
      background: #f5f5f5;
      color: #000;
      overflow-x: hidden;
      -moz-box-sizing: border-box;
      -o-box-sizing: border-box;
      box-sizing: border-box
    }

    em {
      font-style: normal
    }

    a {
      text-decoration: none
    }

    pre {
      word-break: break-word;
      text-align: justify;
      white-space: pre-wrap
    }

    body, dd, dl, dt, h1, h2, h3, h4, h5, h6, html, li, p, ul {
      padding: 0;
      margin: 0;
      outline: 0
    }

    h1, h2, h3, h4, h5, h6, strong {
      font-weight: 400
    }

    li, ul {
      list-style: none
    }

    img {
      vertical-align: middle
    }

    textarea {
      border: border(1) solid #ccc;
      border-radius: .04rem;
      outline: 0;
      resize: none
    }

    .container {
      padding: .3rem .3rem .6rem;
      box-sizing: border-box
    }

    .border-1px:after {
      display: block;
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      border-bottom: 1px solid rgba(7, 17, 27, .1);
      content: ' '
    }

    @media (-webkit-min-device-pixel-ratio:1.5),
    (min-device-pixel-ratio:1.5) {
      .border-1px-scale::after {
        -webkit-transform: scaleY(.7);
        transform: scaleY(.7)
      }
    }

    @media (-webkit-min-device-pixel-ratio:2),
    (min-device-pixel-ratio:2) {
      .border-1px-scale::after {
        -webkit-transform: scaleY(.5);
        transform: scaleY(.5)
      }
    }

    .g-btn,
    header.g-header .title-wp .u-txt {
      color: #25292F;
      font-size: .32rem;
      text-align: center
    }

    .main {
      width: 100%;
      position: absolute;
      background: #FFFFFF;
      top: 0rem;
      bottom: 1.2rem;
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch
    }

    @supports (top:constant(safe-area-inset-top)) or (top:env(safe-area-inset-top)) {
      .safe-top-pt {
        padding-top: env(safe-area-inset-top)
      }

      .main,
      header.g-header,
      header.g-header .f-fx {
        padding-top: env(safe-area-inset-top) !important
      }

      .safe-top-mt {
        margin-top: env(safe-area-inset-top)
      }
    }

    @supports (bottom:constant(safe-area-inset-bottom)) or (bottom:env(safe-area-inset-bottom)) {

      .main,
      .safe-bottom-pt {
        padding-bottom: env(safe-area-inset-bottom)
      }

      .safe-bottom-mt {
        margin-bottom: env(safe-area-inset-bottom)
      }
    }

    .bd-top,
    .contact-person li,
    .content li,
    .post-wrapper li,
    .score-wrapper li {
      position: relative
    }

    .com-border-bottom::after,
    .com-dotted-border-bottom::after {
      position: absolute;
      content: '';
      width: 100%;
      display: block;
      left: 0
    }

    .bd-top::before,
    .contact-person li::before,
    .content li::before,
    .post-wrapper li::before,
    .score-wrapper li::before {
      position: absolute;
      top: 0;
      left: 0;
      display: block;
      content: '';
      width: 100%;
      height: 1px;
      background: #f4f4f4
    }

    .contact-person li:first-child::before,
    .content li:first-child::before,
    .post-wrapper li:first-child::before,
    .score-wrapper li:first-child::before {
      background: #fff
    }

    @media screen and (-webkit-min-device-pixel-ratio:2),
    (min--moz-device-pixel-ratio:2),
    (min-resolution:2dppx) {

      .IOS7 .bd-top::before,
      .IOS7 .contact-person li::before,
      .IOS7 .content li::before,
      .IOS7 .post-wrapper li::before,
      .IOS7 .score-wrapper li::before {
        position: absolute;
        top: -1px;
        left: 0;
        display: block;
        content: '';
        width: 100%;
        height: 1px;
        background: #f4f4f4;
        -webkit-transform: scaleY(.5);
        -moz-transform: scaleY(.5);
        -ms-transform: scaleY(.5);
        -o-transform: scaleY(.5);
        transform: scaleY(.5)
      }
    }

    .main.mainTop {
      top: 0 !important;
      padding-top: 0 !important
    }

    body.IOS7 .main,
    body.IOS7 .search-header,
    body.IOS7 .search-header .f-fx,
    body.IOS7 header.g-header,
    body.IOS7 header.g-header .f-fx {
      padding-top: .4rem
    }

    .main.mainBottom {
      bottom: 0 !important;
      padding-bottom: 0 !important
    }

    .com-border-bottom::after {
      bottom: 0;
      height: 1px;
      background: #f4f4f4
    }

    .com-dotted-border-bottom::after {
      bottom: 0;
      border-bottom: 1px dotted #EBECEE
    }

    @media screen and (-webkit-min-device-pixel-ratio:2),
    (min--moz-device-pixel-ratio:2),
    (min-resolution:2dppx) {

      .IOS7 .com-border-bottom::after,
      .IOS7 .com-dotted-border-bottom::after {
        position: absolute;
        bottom: 0;
        left: 0;
        display: block;
        content: '';
        width: 100%
      }

      .IOS7 .com-border-bottom::after {
        height: 1px;
        background: #f4f4f4;
        -webkit-transform: scaleY(.5);
        -moz-transform: scaleY(.5);
        -ms-transform: scaleY(.5);
        -o-transform: scaleY(.5);
        transform: scaleY(.5)
      }

      .IOS7 .com-dotted-border-bottom::after {
        border-bottom: 1px dotted #EBECEE;
        -webkit-transform: scaleY(.5);
        -moz-transform: scaleY(.5);
        -ms-transform: scaleY(.5);
        -o-transform: scaleY(.5);
        transform: scaleY(.5)
      }
    }

    @media screen and (-webkit-min-device-pixel-ratio:3),
    (min--moz-device-pixel-ratio:3),
    (min-resolution:3dppx) {

      .IOS7 .com-border-bottom::after,
      .IOS7 .com-dotted-border-bottom::after {
        position: absolute;
        bottom: 0;
        left: 0;
        display: block;
        content: '';
        width: 100%
      }

      .IOS7 .com-border-bottom::after {
        height: 1px;
        background: #f4f4f4;
        -webkit-transform: scaleY(.33);
        -moz-transform: scaleY(.33);
        -ms-transform: scaleY(.33);
        -o-transform: scaleY(.33);
        transform: scaleY(.33)
      }

      .IOS7 .com-dotted-border-bottom::after {
        border-bottom: 1px dotted #EBECEE;
        -webkit-transform: scaleY(.33);
        -moz-transform: scaleY(.33);
        -ms-transform: scaleY(.33);
        -o-transform: scaleY(.33);
        transform: scaleY(.33)
      }
    }

    header.g-header {
      position: relative;
      z-index: 2;
      width: 100%;
      height: .88rem
    }

    header.g-header .f-fx {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 99;
      width: 100%;
      height: .88rem;
      line-height: .88rem;
      background: #fff;
      -webkit-box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, .1);
      -moz-box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, .1);
      -ms-box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, .1);
      -o-box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, .1);
      box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, .1)
    }

    header.g-header .goBack {
      position: relative;
      z-index: 5;
      float: left;
      display: block;
      width: .95rem;
      height: .88rem;
      margin-right: -.95rem;
      /* background: url(../../imgs/back.png) center no-repeat; */
      background-size: auto .35rem
    }


    header.g-header .title-wp {
      float: left;
      position: relative;
      width: 100%
    }

    header.g-header .title-wp .u-txt {
      margin: 0 1rem
    }

    @supports (top:constant(safe-area-inset-top)) or (top:env(safe-area-inset-top)) {

      body.IOS7 .search-header,
      body.IOS7 .search-header .f-fx {
        padding-top: env(safe-area-inset-top)
      }
    }
  </style>
  <style>
    .notice-title {
      padding: 0 .4rem;
    }
    .notice-title .title {
      padding-top: 0.42rem;
      font-size: 0.32rem;
      line-height: 0.48rem;
      color: #3A3D50;
      font-weight: bold;
    }
    .notice-title .date {
      font-size: 0.22rem;
      line-height: 0.48rem;
      color: #D0D0D0;
      height: 0.48rem;
      padding-bottom: 0.1rem;
    }
    .notice-con {
      padding: 0.4rem;
      font-size: 0.28rem;
      line-height: 1.5;
      color: #3A3D50;
    }
    p {
      min-height: 0.42rem;
      line-height: 1.5;
    }
    .textRight {
      text-align: right;
    }
  </style>
</head>
<body>

<!--  <header class="g-header">
    <div class="f-fx clearfix">
      <a class="goBack" href="javascript:back();"></a>
      <div class="title-wp">
        <div class="u-txt">吉祥公告</div>
      </div>
    </div>
  </header>-->
  <div class="main mainBottom">
    <div class="notice-title" >
      <div class="title" th:text="${title}"></div>
      <div class="date border-1px" th:text="${time}"></div>
    </div>
    <div class="notice-con" th:utext="${body}"></div>
  </div>
  
</body>
<script>
 // rem
  ;
  (function (win, lib) {
    var doc = win.document
    var docEl = doc.documentElement
    var metaEl = doc.querySelector('meta[name="viewport"]')
    var flexibleEl = doc.querySelector('meta[name="flexible"]')
    var dpr = 0
    var scale = 0
    var tid
    var flexible = lib.flexible || (lib.flexible = {})

    if (metaEl) {
      var match = metaEl.getAttribute('content').match(/initial-scale=([\d.]+)/)
      if (match) {
        scale = parseFloat(match[1])
        dpr = parseInt(1 / scale)
      }
    } else if (flexibleEl) {
      var content = flexibleEl.getAttribute('content')
      if (content) {
        var initialDpr = content.match(/initial-dpr=([\d.]+)/)
        var maximumDpr = content.match(/maximum-dpr=([\d.]+)/)
        if (initialDpr) {
          dpr = parseFloat(initialDpr[1])
          scale = parseFloat((1 / dpr).toFixed(2))
        }
        if (maximumDpr) {
          dpr = parseFloat(maximumDpr[1])
          scale = parseFloat((1 / dpr).toFixed(2))
        }
      }
    }

    if (!dpr && !scale) {
      // var isAndroid = win.navigator.appVersion.match(/android/gi)
      var isIPhone = win.navigator.appVersion.match(/iphone/gi)
      var devicePixelRatio = win.devicePixelRatio
      if (isIPhone) {
        // iOS下，对于2和3的屏，用2倍的方案，其余的用1倍方案
        if (devicePixelRatio >= 3 && (!dpr || dpr >= 3)) {
          dpr = 3
        } else if (devicePixelRatio >= 2 && (!dpr || dpr >= 2)) {
          dpr = 2
        } else {
          dpr = 1
        }
      } else {
        // 其他设备下，仍旧使用1倍的方案
        dpr = 1
      }
      scale = 1 / dpr
    }

    docEl.setAttribute('data-dpr', dpr)
    if (!metaEl) {
      metaEl = doc.createElement('meta')
      metaEl.setAttribute('name', 'viewport')
      metaEl.setAttribute('content', 'initial-scale=' + scale + ', maximum-scale=' + scale + ', minimum-scale=' + scale + ', user-scalable=no')
      if (docEl.firstElementChild) {
        docEl.firstElementChild.appendChild(metaEl)
      } else {
        var wrap = doc.createElement('div')
        wrap.appendChild(metaEl)
        doc.write(wrap.innerHTML)
      }
    }

    // px:rem == 100:1
    function refreshRem() {
      var width = docEl.getBoundingClientRect().width
      // 适配平板
      if (width / dpr > 750) {
        width = 750 * dpr
      }
      var rem = 100 * (width / 750)
      docEl.style.fontSize = rem + 'px'
      flexible.rem = win.rem = rem
    }

    win.addEventListener('resize', function () {
      clearTimeout(tid)
      tid = setTimeout(refreshRem, 300)
    }, false)
    win.addEventListener('pageshow', function (e) {
      if (e.persisted) {
        clearTimeout(tid)
        tid = setTimeout(refreshRem, 300)
      }
    }, false)

    if (doc.readyState === 'complete') {
      doc.body.style.fontSize = 12 * dpr + 'px'
    } else {
      doc.addEventListener('DOMContentLoaded', function (e) {
        doc.body.style.fontSize = 12 * dpr + 'px'
      }, false)
    }

    refreshRem()

    flexible.dpr = win.dpr = dpr
    flexible.refreshRem = refreshRem
    flexible.rem2px = function (d) {
      var val = parseFloat(d) * this.rem
      if (typeof d === 'string' && d.match(/rem$/)) {
        val += 'px'
      }
      return val
    }
    flexible.px2rem = function (d) {
      var val = parseFloat(d) / this.rem
      if (typeof d === 'string' && d.match(/px$/)) {
        val += 'rem'
      }
      return val
    }
  })(window, window['lib'] || (window['lib'] = {}))

</script>
</html>