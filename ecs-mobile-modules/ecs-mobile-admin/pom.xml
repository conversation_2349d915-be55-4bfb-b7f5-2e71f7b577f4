<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>ecs-mobile-modules</artifactId>
        <groupId>com.juneyaoair.ecs</groupId>
        <version>${revision}</version>
    </parent>
    <artifactId>ecs-mobile-admin</artifactId>
    <description>基础信息管理模块</description>
    <dependencies>
        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!-- 线程传递值 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jta-atomikos</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!-- juneyaoair Common Swagger -->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common-swagger</artifactId>
        </dependency>

        <!-- Swagger UI -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${swagger.fox.version}</version>
        </dependency>

        <!-- juneyaoair Common DataScope -->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common-datascope</artifactId>
        </dependency>

        <!-- juneyaoair Common DataSource -->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common-datasource</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdbc7</artifactId>
        </dependency>

        <!--Thymeleaf 启动器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-b2c-datasource</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-flight-datasource</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-b2c2014-datasource</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-cki-datasource</artifactId>
        </dependency>

        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-dto</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-redis</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-mongo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-http</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-exception</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-log</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-third-api</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.dsop</groupId>
            <artifactId>sdk-java</artifactId>
            <version>5.0.2</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>