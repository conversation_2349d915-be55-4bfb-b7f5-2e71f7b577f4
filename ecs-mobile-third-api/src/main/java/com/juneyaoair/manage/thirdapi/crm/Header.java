package com.juneyaoair.manage.thirdapi.crm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2025/8/14  10:27.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class Header {
    //客户端IP
    private String ClientIP;

    //客户端版本
    private String ClientVersion;

    //登录凭证
    private String Token;

    //登录会员Id
    private Long MemberId;

    //时间戳（默认为系统当前时间）
    private Long Timestamp;
}
