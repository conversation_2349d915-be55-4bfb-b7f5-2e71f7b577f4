package com.juneyaoair.manage.thirdapi.crm;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class CrmRightInfoDetailDTO {

    /**
     * RightsRecordId : 1
     * RightsNameCn : sample string 2
     * RightsDescCn : sample string 3
     * RightsNameEn : sample string 4
     * RightsDescEn : sample string 5
     * OnlyDisplay : true
     * Number : 7
     */
    @SerializedName("RightsRecordId")
    private int rightsRecordId;
    @SerializedName("RightsNameCn")
    private String rightsNameCn;
    @SerializedName("RightsDescCn")
    private String rightsDescCn;
    @SerializedName("RightsNameEn")
    private String rightsNameEn;
    @SerializedName("RightsDescEn")
    private String rightsDescEn;
    @SerializedName("OnlyDisplay")
    private boolean onlyDisplay;
    @SerializedName("Number")
    private int number;


}
