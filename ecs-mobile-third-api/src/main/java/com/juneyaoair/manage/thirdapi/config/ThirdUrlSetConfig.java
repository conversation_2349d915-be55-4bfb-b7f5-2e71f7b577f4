package com.juneyaoair.manage.thirdapi.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/18 10:00
 */
@Configuration
@RefreshScope
@Data
public class ThirdUrlSetConfig {
    /**
     * 航班任务调用地址
     */
    @Value("${thirdAppUrl.oneorder.service.fare.url:}")
    private String oneorderServiceFareUrl;
    /**
     * 基础服务consumer调用地址
     */
    @Value("${thirdAppUrl.flightbasicUrl:}")
    private String flightbasicUrl;
}
