package com.juneyaoair.manage.thirdapi.constant;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/18 10:10
 */
public interface UrlConstant {
    /**
     * AV缓存刷新
     */
    String FARE_FRESH_NOTICE = "/fareFreshNotice";
    /**
     * 清理flightbasic服务缓存  航线
     */
    String CONSUMER_CLEAR_AIRLINE = "/cache/clearAirlineCache";
    /**
     * 清理flightbasic服务缓存  城市
     */
    String CONSUMER_CLEAR_CITY = "/cache/clearCityCache";
    /**
     * 清理flightbasic服务缓存  机场
     */
    String CONSUMER_CLEAR_AIRPORT = "/cache/clearAirportCache";
    /**
     * 清理flightbasic服务省份信息
     **/
    String CONSUMER_CLEAR_PROVINCE = "/cache/clearProvinceCache";
    /**
     * 清理消息缓存
     */
    String CLEAR_MESS_CACHE = "/indexHandle/clearMessCache";
    /**
     * 清除模块管理缓存
     */
    String CLEAR_MODULE_TAB_CACHE = "/cache/clearModuleTabCache";


}
