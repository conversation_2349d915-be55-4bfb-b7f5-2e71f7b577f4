package com.juneyaoair.manage.thirdapi.crm;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class PtCRMResponse<T> {
    @SerializedName("Data")
    private T data;
    @SerializedName("IsSuccess")
    private Boolean isSuccess;
    @SerializedName("Code")
    private Integer code;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("InnerMsg")
    private String innerMsg;
}
