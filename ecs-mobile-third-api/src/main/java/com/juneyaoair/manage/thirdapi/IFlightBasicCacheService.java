package com.juneyaoair.manage.thirdapi;

import com.juneyaoair.manage.thirdapi.dto.FareFreshNotice;

/**
 * <AUTHOR>
 * @description 基础服务缓存统一处理服务
 * @date 2024/12/17 19:24
 */
public interface IFlightBasicCacheService {
    /**
     * 清除航线缓存
     **/
    boolean clearAirlineCache(String ip);
    /**
     * 清除城市缓存
     **/
    boolean clearCityCache(String ip);
    /**
     * 清除机场缓存
     **/
    boolean clearAirportCache(String ip);
    /**
     * 清除省份缓存
     **/
    boolean clearProvinceCache(String ip);
    /**
     * 清理公告缓存
     **/
    void clearNoticeCache();
    
    /**
     * 清理模块管理缓存
     **/
    void clearModuleTabCache();
}
