package com.juneyaoair.manage.thirdapi.impl;

import com.juneyaoair.ecs.http.service.HttpClientService;
import com.juneyaoair.ecs.manage.dto.base.BaseRequest;
import com.juneyaoair.ecs.manage.dto.base.BaseRequestUtil;
import com.juneyaoair.ecs.manage.dto.base.BasicBaseReq;
import com.juneyaoair.manage.thirdapi.IFlightBasicCacheService;
import com.juneyaoair.manage.thirdapi.config.ThirdUrlSetConfig;
import com.juneyaoair.manage.thirdapi.constant.UrlConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/17 19:24
 */
@Service
public class FlightBasicCacheService implements IFlightBasicCacheService {
    @Autowired
    private ThirdUrlSetConfig thirdUrlSetConfig;
    @Autowired
    private HttpClientService httpClientService;
    @Override
    public boolean clearAirlineCache(String ip) {
        BasicBaseReq basicBaseReq = new BasicBaseReq(ip);
        String url = thirdUrlSetConfig.getFlightbasicUrl() + UrlConstant.CONSUMER_CLEAR_AIRLINE;
        httpClientService.doPostJson(basicBaseReq, url, null);
        return true;
    }

    @Override
    public boolean clearCityCache(String ip) {
        BasicBaseReq basicBaseReq = new BasicBaseReq(ip);
        String url = thirdUrlSetConfig.getFlightbasicUrl() + UrlConstant.CONSUMER_CLEAR_CITY;
        httpClientService.doPostJson(basicBaseReq, url, null);
        return true;
    }

    @Override
    public boolean clearAirportCache(String ip) {
        BasicBaseReq basicBaseReq = new BasicBaseReq(ip);
        String url = thirdUrlSetConfig.getFlightbasicUrl() + UrlConstant.CONSUMER_CLEAR_AIRPORT;
        httpClientService.doPostJson(basicBaseReq, url, null);
        return true;
    }

    @Override
    public boolean clearProvinceCache(String ip) {
        BasicBaseReq basicBaseReq = new BasicBaseReq(ip);
        String url = thirdUrlSetConfig.getFlightbasicUrl() + UrlConstant.CONSUMER_CLEAR_PROVINCE;
        httpClientService.doPostJson(basicBaseReq, url, null);
        return true;
    }

    @Override
    public void clearNoticeCache() {
        BaseRequest request = BaseRequestUtil.createRequest(null);
        httpClientService.doPostJson(request, thirdUrlSetConfig.getFlightbasicUrl() + UrlConstant.CLEAR_MESS_CACHE, null);
    }

    @Override
    public void clearModuleTabCache() {
        BaseRequest request = BaseRequestUtil.createRequest(null);
        httpClientService.doPostJson(request, thirdUrlSetConfig.getFlightbasicUrl() + UrlConstant.CLEAR_MODULE_TAB_CACHE, null);
    }
}
