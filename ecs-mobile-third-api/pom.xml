<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.juneyaoair.ecs</groupId>
        <artifactId>ecs-mobile-manage</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ecs-mobile-third-api</artifactId>
    <description>第三方服务接口</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.ecs</groupId>
            <artifactId>ecs-mobile-common-core</artifactId>
        </dependency>
    </dependencies>

</project>