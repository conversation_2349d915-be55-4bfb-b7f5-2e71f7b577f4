CREATE TABLE PET_FLIGHT_PLAN(
    ID VARCHAR2(32) NOT NULL,
    FLIGHT_START DATE NOT NULL,
    FLIGHT_END DATE NOT NULL,
    DEP_AIRPORT_NAME VARCHAR2(20) NOT NULL,
    DEP_AIRPORT VARCHAR2(5) NOT NULL,
    ARR_AIRPORT_NAME VARCHAR2(20) NOT NULL,
    ARR_AIRPORT VARCHAR2(5) NOT NULL,
    FLIGHT_NO VARCHAR2(150) NOT NULL,
    FLIGHT_QUOTA INT NOT NULL,
    PRICE DECIMAL NOT NULL,
    DELETE_FLAG VARCHAR2(1) NOT NULL,
    CREATED_BY VARCHAR2(32) NOT NULL,
    CREATED_TIME DATE NOT NULL,
    UPDATED_BY VARCHAR2(32) NOT NULL,
    UPDATED_TIME DATE NOT NULL,
    PRIMARY KEY (ID)
);

CREATE INDEX idx_start ON PET_FLIGHT_PLAN(FLIGHT_START);
CREATE INDEX idx_end ON PET_FLIGHT_PLAN(FLIGHT_END);
CREATE INDEX idx_dep ON PET_FLIGHT_PLAN(DEP_AIRPORT);
CREATE INDEX idx_arr ON PET_FLIGHT_PLAN(ARR_AIRPORT);

COMMENT ON TABLE PET_FLIGHT_PLAN IS '萌宠飞航班计划';
COMMENT ON COLUMN PET_FLIGHT_PLAN.ID IS '记录ID';
COMMENT ON COLUMN PET_FLIGHT_PLAN.FLIGHT_START IS '航班计划开始日期;包含开始日期';
COMMENT ON COLUMN PET_FLIGHT_PLAN.FLIGHT_END IS '航班计划结束日期;包含结束日期';
COMMENT ON COLUMN PET_FLIGHT_PLAN.DEP_AIRPORT_NAME IS '出发机场名称';
COMMENT ON COLUMN PET_FLIGHT_PLAN.DEP_AIRPORT IS '出发机场三字码';
COMMENT ON COLUMN PET_FLIGHT_PLAN.ARR_AIRPORT_NAME IS '到达机场名称';
COMMENT ON COLUMN PET_FLIGHT_PLAN.ARR_AIRPORT IS '到达机场三字码';
COMMENT ON COLUMN PET_FLIGHT_PLAN.FLIGHT_NO IS '航班号;多个航班将以英文分号;分隔，ALL-表示所有';
COMMENT ON COLUMN PET_FLIGHT_PLAN.FLIGHT_QUOTA IS '航班配额';
COMMENT ON COLUMN PET_FLIGHT_PLAN.PRICE IS '价格';
COMMENT ON COLUMN PET_FLIGHT_PLAN.DELETE_FLAG IS '数据状态;Y-删除，N-正常';
COMMENT ON COLUMN PET_FLIGHT_PLAN.CREATED_BY IS '创建人';
COMMENT ON COLUMN PET_FLIGHT_PLAN.CREATED_TIME IS '创建时间';
COMMENT ON COLUMN PET_FLIGHT_PLAN.UPDATED_BY IS '更新人';
COMMENT ON COLUMN PET_FLIGHT_PLAN.UPDATED_TIME IS '更新时间';
