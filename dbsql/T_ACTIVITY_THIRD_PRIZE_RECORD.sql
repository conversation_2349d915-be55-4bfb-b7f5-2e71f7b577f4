-- Create table 第三方奖品发放流水表
create table "T_ACTIVITY_THIRD_PRIZE_RECORD"
(
    id            VARCHAR2(50) not null,
    activity_code VARCHAR2(50) not null,
    merchant_code VARCHAR2(50) not null,
    prize_code    VARCHAR2(200) not null,
    prize_name    VARCHAR2(200) not null,
    prize_type    VARCHAR2(10) not null,
    prize_amount  NUMERIC not null,
    prize_status  VARCHAR2(10) DEFAULT 'INIT' not null,
    ffp_card_no     VARCHAR2(20),--如奖品未被领取，则无领取人。故可为空
    create_time   DATE    not null,
    create_user   VARCHAR2(20) not null,
    update_time   DATE,--如未遇更新，则无更新时间。故可为空
    update_user   VARCHAR2(50)--如未遇更新，则无更新人。故可为空
)
-- Add comment to the table
comment on table T_ACTIVITY_THIRD_PRIZE_RECORD is '第三方奖品发放流水表';
-- Add comments to the columns
comment
on column T_ACTIVITY_THIRD_PRIZE_RECORD.activity_code is '活动号';
comment
on column T_ACTIVITY_THIRD_PRIZE_RECORD.merchant_code is '商户号';
comment
on column T_ACTIVITY_THIRD_PRIZE_RECORD.prize_code is '奖品编码';
comment
on column T_ACTIVITY_THIRD_PRIZE_RECORD.prize_name is '奖品名称';
comment
on column T_ACTIVITY_THIRD_PRIZE_RECORD.prize_type is '奖品类型';
comment
on column T_ACTIVITY_THIRD_PRIZE_RECORD.prize_amount is '奖品发放数量';
comment
on column T_ACTIVITY_THIRD_PRIZE_RECORD.prize_status is '奖品领取状态';
comment
on column T_ACTIVITY_THIRD_PRIZE_RECORD.ffp_card_no is '领取卡号';
comment
on column T_ACTIVITY_THIRD_PRIZE_RECORD.create_user is '创建人';
comment
on column T_ACTIVITY_THIRD_PRIZE_RECORD.update_time is '更新时间';
comment
on column T_ACTIVITY_THIRD_PRIZE_RECORD.update_user is '更新人';

CREATE UNIQUE INDEX ux_amcp_at_atpr ON T_ACTIVITY_THIRD_PRIZE_RECORD (ACTIVITY_CODE, MERCHANT_CODE, PRIZE_CODE)

