CREATE TABLE T_PAY_METHOD_ROUTE(
	ID VARCHAR2(32),
	ROUTE_TYPE VARCHAR2(2),
	PAY_ACTIVITY_ID VARCHAR2(32),
	DEP_AIRPORT VARCHAR2(10),
	ARR_AIRPORT VARCHAR2(10),
	CREATE_USER VARCHAR2(100),
	UPDATE_USER DATE,
	CREATE_TIME DATE,
	UPDATE_TIME VARCHAR2(100)
   ) SEGMENT CREATION IMMEDIATE
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "HOORDER" ;

COMMENT ON TABLE T_PAY_METHOD_ROUTE IS '支付宝立即减航线数据表';
COMMENT ON COLUMN T_PAY_METHOD_ROUTE.ROUTE_TYPE IS '航线类型  I 国际 D国内';
COMMENT ON COLUMN T_PAY_METHOD_ROUTE.PAY_ACTIVITY_ID IS '支付活动id';
COMMENT ON COLUMN T_PAY_METHOD_ROUTE.DEP_AIRPORT IS '出发机场三字码';
COMMENT ON COLUMN T_PAY_METHOD_ROUTE.ARR_AIRPORT IS '到达机场三字码';
COMMENT ON COLUMN T_PAY_METHOD_ROUTE.CREATE_USER IS '创建人';
COMMENT ON COLUMN T_PAY_METHOD_ROUTE.UPDATE_USER IS '创建时间';
COMMENT ON COLUMN T_PAY_METHOD_ROUTE.CREATE_TIME IS '更新时间';
COMMENT ON COLUMN T_PAY_METHOD_ROUTE.UPDATE_TIME IS '更新人';