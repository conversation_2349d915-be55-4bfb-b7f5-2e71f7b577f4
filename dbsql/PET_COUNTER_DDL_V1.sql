CREATE TABLE PET_COUNTER(
    ID VARCHAR2(32) NOT NULL,
    CITY_NAME VARCHAR2(50) NOT NULL,
    AIRPORT_NAME VARCHAR2(50) NOT NULL,
    AIRPORT_CODE VARCHAR2(10) NOT NULL,
    AIRPORT_COUNTER VARCHAR2(200) NOT NULL,
    DISABLE_FLAG VARCHAR2(1) NOT NULL,
    CREATED_BY VARCHAR2(32) NOT NULL,
    CREATED_TIME DATE NOT NULL,
    UPDATED_BY VARCHAR2(32) NOT NULL,
    UPDATED_TIME DATE NOT NULL,
    PRIMARY KEY (ID)
);

CREATE UNIQUE INDEX uk_airport ON PET_COUNTER(AIRPORT_CODE);
CREATE INDEX idx_status ON PET_COUNTER(DISABLE_FLAG);

COMMENT ON TABLE PET_COUNTER IS '萌宠飞受理柜台';
COMMENT ON COLUMN PET_COUNTER.ID IS '记录ID';
COMMENT ON COLUMN PET_COUNTER.CITY_NAME IS '城市名称';
COMMENT ON COLUMN PET_COUNTER.AIRPORT_NAME IS '机场名称';
COMMENT ON COLUMN PET_COUNTER.AIRPORT_CODE IS '机场三字码';
COMMENT ON COLUMN PET_COUNTER.AIRPORT_COUNTER IS '受理柜台信息';
COMMENT ON COLUMN PET_COUNTER.DISABLE_FLAG IS '柜台状态;Y-禁用；N-启动';
COMMENT ON COLUMN PET_COUNTER.CREATED_BY IS '创建人';
COMMENT ON COLUMN PET_COUNTER.CREATED_TIME IS '创建时间';
COMMENT ON COLUMN PET_COUNTER.UPDATED_BY IS '更新人';
COMMENT ON COLUMN PET_COUNTER.UPDATED_TIME IS '更新时间';
