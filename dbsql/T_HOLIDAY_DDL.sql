CREATE TABLE T_HOLIDAY(
    ID VARCHAR2(32) NOT NULL,
    HOLIDAY_DATE VARCHAR2(10) NOT NULL,
    REST_OR_WORK VARCHAR2(5) NOT NULL,
    HOLIDAY_NAME VARCHAR2(10),
    REMARK VARCHAR2(20),
    CREATED_BY VARCHAR2(32) NOT NULL,
    CREATED_TIME DATE NOT NULL,
    UPDATED_BY VARCHAR2(32) NOT NULL,
    UPDATED_TIME DATE NOT NULL,
    PRIMARY KEY (ID)
);

COMMENT ON TABLE T_HOLIDAY IS '假日管理';
COMMENT ON COLUMN T_HOLIDAY.ID IS '记录编号';
COMMENT ON COLUMN T_HOLIDAY.HOLIDAY_DATE IS '日期;yyyy-MM-dd';
COMMENT ON COLUMN T_HOLIDAY.REST_OR_WORK IS '休息/上班';
COMMENT ON COLUMN T_HOLIDAY.HOLIDAY_NAME IS '假日名称';
COMMENT ON COLUMN T_HOLIDAY.REMARK IS '备注';
COMMENT ON COLUMN T_HOLIDAY.CREATED_BY IS '创建人';
COMMENT ON COLUMN T_HOLIDAY.CREATED_TIME IS '创建时间';
COMMENT ON COLUMN T_HOLIDAY.UPDATED_BY IS '更新人';
COMMENT ON COLUMN T_HOLIDAY.UPDATED_TIME IS '更新时间';

--索引创建
CREATE UNIQUE INDEX IDX_HD ON T_HOLIDAY(HOLIDAY_DATE);

